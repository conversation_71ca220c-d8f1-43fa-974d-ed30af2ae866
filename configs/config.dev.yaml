port: "8080"
mode: "development"
db:
  host: "127.0.0.1"
  port: 3306
  username: "root"
  password: "970824.."
  charset: "utf8mb4"
  database: "devdb"

# Redis配置
redis:
  host: "127.0.0.1"
  port: 6379
  password: ""
  db: 0
  ttl: 3600

# JWT 配置
jwt:
  secretKey: "your-very-complex-secret-key-here"  # 生产环境请使用复杂的随机字符串
  expires: 14400                                   # 默认 24 小时（24小时 * 60分钟）
  issuer: "backend-api"                           # JWT 的颁发者

# 新增日志配置部分
logger:
  level: "debug"  # 开发环境使用debug级别，显示更详细的日志
  output_paths:   # 使用下划线风格统一配置
    - "stdout"
    - "./logs/server.log"  # 存放在dev子目录下
    - "./logs/temporal.log"  # 添加worker专用日志文件
  error_paths:    # 使用下划线风格统一配置
    - "stderr"
    - "./logs/error.log"   # 存放在dev子目录下
  encoding: "console"      # 控制台友好的格式
  max_size: 50             # 单个文件最大尺寸(MB)
  max_age: 7               # 开发环境日志保留7天
  max_backups: 3           # 最大备份数
  compress: true           # 是否压缩
  development: true        # 开发环境标志
  enable_sampling: false   # 不启用采样，记录所有日志
  console: true            # 在控制台输出
  gorm_console: false      # 开发环境显示数据库日志

superAdmin:
  username: admin
  password: Admin@123
  realName: 超级管理员
  email: <EMAIL>
  roles:
    - super
  accessCodes:
    - "*"


temporal:
  address: "localhost:7233"  # Temporal前端服务端口
  namespace: "default"

# 飞书机器人配置
feishu:
  webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/6a21973d-1456-4065-9db6-852aa85fe08d"
  secret: "7BiHDK9oNjm7O8JDf6Wevf"
  enabled: true
  ticket_detail_url_template: "http://localhost:5666/fault-report-management/detail/%s"
  # 维修单项目专用webhook映射配置（仅用于维修单待接单通知）
  repair_project_webhooks:
    "cloud17":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/f0622c72-701a-4eec-a880-87b88ad47a47"
      secret: "JEYx81Iq4k1SMAMXUeM6q"
    "cloud27":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/298cfae5-e1e3-4a5a-8c6e-6f915287b9df"
      secret: "itghARXHCCLDrXmsP99fLc"
    "default":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/f0622c72-701a-4eec-a880-87b88ad47a47"
      secret: "JEYx81Iq4k1SMAMXUeM6q"

  repair_ticket_detail_url_template: "http://localhost:5666/hardware-order/%s"
  # 采购通知专用配置
  purchase_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/298cfae5-e1e3-4a5a-8c6e-6f915287b9df"
  purchase_secret: "itghARXHCCLDrXmsP99fLc"
  purchase_url_templates:
    default: "http://localhost:5666/purchase-management/"
    request: "http://localhost:5666/purchase-management/purchase-request/detail/"
    inquiry: "http://localhost:5666/purchase-management/purchase-inquiry/detail/"
    contract: "http://localhost:5666/purchase-management/purchase-contract/detail/"
    payment: "http://localhost:5666/purchase-management/payment-request/detail/"
    arrival: "http://localhost:5666/purchase-management/arrival-management/detail/"
    shipment: "http://localhost:5666/purchase-management/shipment/detail/"
    invoice: "http://localhost:5666/purchase-management/invoice/"
  purchase_enabled: true


  inbound_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/533cde91-3d53-4ee1-87a3-d1e5bb69c360"
  inbound_secret: "U8HCuyg413f4GwkPulRPTd"
  inbound_ticket_detail_url_template: "http://cloud17.cnhancloud.com/asset-storage"
  soft_sche_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/6cfbed3a-c2b9-48f9-81bd-85be4f75bf70" #测试机器人
  soft_sche_secret: "PlsD7ZgUN74P2yBzd65Sgc"
  security_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/85eea222-d6f2-45c0-99ae-fb8a42ede460"
  security_guard_secret: "qaeCwYB9a3OLcuuj7U8hJb"
  soft_sche_template: "AAqd3ZpVIYPF2"
  soft_sche_enabled: true  # 开发环境禁用排班表通知

  hard_sche_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/3931eec1-db24-45b7-8eeb-35ca0e16806d" #测试机器人
  hard_sche_secret: "JjvXhrK6Pq5n0rz8jW5XNc"
  hard_sche_morning_template: "AAqdzlSDNo9gd"
  hard_sche_evening_template: "AAqd9MkSDcRWk"
  hard_sche_enabled: true  # 开发环境禁用排班表通知

# 邮件服务配置
email:
  senderAccounts:
    default:
      host: "smtp.qiye.aliyun.com"                         # SMTP 服务器地址
      port: 25                                             # SMTP 端口号
      username: "<EMAIL>"                   # 登录用户名
      password: "WSsIVcM4smZl9kyw"                         # 登录密码
      from: "<EMAIL>"    # 发件人信息
    marketing:
      host: "smtp.sendgrid.net"                        # 营销用的 SMTP 服务地址
      port: 587
      username: "apikey"                               # SendGrid 推荐使用 API key 作为用户名
      password: "sendgrid_api_key"                     # API key 作为密码
      from: "Marketing Team <<EMAIL>>"   # 营销发件人信息
