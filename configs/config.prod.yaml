port: "8080"
mode: "production"
db:
  host: "127.0.0.1"
  port: 3306
  username: "root"
  password: "970824.."
  charset: "utf8mb4"
  database: "devdb"

# Redis配置
redis:
  host: "redis"
  port: 6379
  password: ""
  db: 0
  ttl: 3600

# JWT 配置
jwt:
  secretKey: "your-very-complex-secret-key-here"  # 生产环境请使用复杂的随机字符串
  expires: 1440                                   # 默认 24 小时（24小时 * 60分钟）
  issuer: "backend-api"                           # JWT 的颁发者

# 生产环境日志配置
logger:
  level: "info"   # 生产环境使用warn级别，减少日志量
  output_paths:
    - "./logs/server.log"  # 存放在prod子目录下
    - "./logs/temporal.log"  # 添加worker专用日志文件
  error_paths:
    - "./logs/error.log"   # 存放在prod子目录下
  encoding: "json"           # 使用json格式便于解析
  max_size: 100              # 单个文件最大尺寸(MB)
  max_age: 30                # 保留天数
  max_backups: 10            # 最大备份数
  compress: true             # 是否压缩
  development: false         # 非开发环境
  enable_sampling: true      # 启用采样减少日志量
  console: true              # 在控制台输出关键日志
  gorm_console: false        # 不在控制台输出GORM日志

superAdmin:
  username: admin
  password: Admin@123
  realName: 超级管理员
  email: <EMAIL>
  roles:
    - super
  accessCodes:
    - "*"

# 飞书机器人配置
feishu:
  webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/3ef49db8-b0a4-42d9-af55-79f669be1ad8"
  secret: "iE2CgLy5WMYrFZncy9cxjh"
  enabled: true
  ticket_detail_url_template: "http://cloud17.cnhancloud.com/fault-report-management/detail/%s"

  # 维修单项目专用webhook映射配置（仅用于维修单待接单通知）
  repair_project_webhooks:
    "cloud17":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/cloud17-repair-webhook"
      secret: "cloud17-repair-secret"
    "cloud27":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/cloud27-repair-webhook"
      secret: "cloud27-repair-secret"
    "default":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/default"
      secret: "default"

  repair_ticket_detail_url_template: "http://cloud17.cnhancloud.com/hardware-order/%s"
  # 采购通知专用配置
  purchase_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/298cfae5-e1e3-4a5a-8c6e-6f915287b9df"
  purchase_secret: "itghARXHCCLDrXmsP99fLc"
  purchase_url_templates:
    default: "http://cloud17.cnhancloud.com/purchase-management/"
    request: "http://cloud17.cnhancloud.com/purchase-management/purchase-request/detail/"
    inquiry: "http://cloud17.cnhancloud.com/purchase-management/purchase-inquiry/detail/"
    contract: "http://cloud17.cnhancloud.com/purchase-management/purchase-contract/detail/"
    payment: "http://cloud17.cnhancloud.com/purchase-management/payment-request/detail/"
    arrival: "http://cloud17.cnhancloud.com/purchase-management/arrival-management/detail/"
    shipment: "http://cloud17.cnhancloud.com/purchase-management/shipment/detail/"
    invoice: "http://cloud17.cnhancloud.com/purchase-management/invoice/"
  purchase_enabled: true

  inbound_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/f75e05cb-4fc1-41aa-a9e7-b8a13d8b611c"
  inbound_secret: "zxz97xT5sHIfbUhP9Vn7Uc"
  inbound_ticket_detail_url_template: "http://cloud17.cnhancloud.com/asset-storage"
  # 保安群配置
  security_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/eb9b26ce-12c5-4756-a17a-f5071990a781"
  security_guard_secret: "8BUw0OMbWjubDotjeG3kf"

  # 软件排班表配置
  #soft_sche_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/6cfbed3a-c2b9-48f9-81bd-85be4f75bf70" #目前是最初的机器人
  #soft_sche_secret: "PlsD7ZgUN74P2yBzd65Sgc"
  #soft_sche_template: "AAqd3ZpVIYPF2"
  #soft_sche_enabled: true  # 开发环境禁用排班表通知
  hard_sche_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/b2e13027-35f2-44a3-b6fc-fad33086e6de" #目前是最初的机器人
  hard_sche_secret: "gPKTf6SeTtBmLkZJP05BGe"
  hard_sche_morning_template: "AAqdzlSDNo9gd"
  hard_sche_evening_template: "AAqd9MkSDcRWk"
  hard_sche_enabled: true  # 开发环境禁用排班表通知
