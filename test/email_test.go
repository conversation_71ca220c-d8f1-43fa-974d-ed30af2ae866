package test

import (
	"backend/internal/common/utils/email"
	"context"
	"fmt"
	"testing"

	"go.uber.org/zap"
)

func ExampleEmailUtils() {
	// 初始化配置
	config := &email.EmailConfig{
		Host:     "smtp.qiye.aliyun.com",
		Port:     25,
		Username: "<EMAIL>",
		Password: "WSsIVcM4smZl9kyw",
		From:     "<EMAIL>",
	}

	logger, _ := zap.NewProduction()
	defer func(logger *zap.Logger) {
		err := logger.Sync()
		if err != nil {
			fmt.Println(err)
		}
	}(logger)

	// 创建邮件工具实例
	emailUtils := email.NewEmailUtils(config, logger)

	// 创建上下文
	ctx := context.Background()

	// 示例1: 发送简单邮件
	err := emailUtils.SendSimpleEmail(ctx, []string{"<EMAIL>"}, "测试邮件", "这是一封测试邮件")
	if err != nil {
		logger.Error("发送邮件失败", zap.Error(err))
	}

	// 示例2: 发送欢迎邮件
	err = emailUtils.SendWelcomeEmail(ctx, []string{"<EMAIL>"}, "张三")
	if err != nil {
		logger.Error("发送欢迎邮件失败", zap.Error(err))
	}

	// 示例3: 发送通知邮件
	details := map[string]string{
		"工单编号": "INC-2024-001",
		"处理人":  "李四",
		"状态":   "已处理",
	}
	err = emailUtils.SendNotificationEmail(ctx, []string{"<EMAIL>"}, "工单处理完成", "工单已处理完成", details)
	if err != nil {
		logger.Error("发送通知邮件失败", zap.Error(err))
	}

	// 示例4: 发送警告邮件
	alertDetails := map[string]string{
		"错误类型": "系统异常",
		"影响范围": "用户登录",
		"紧急程度": "高",
	}
	err = emailUtils.SendAlertEmail(ctx, []string{"<EMAIL>"}, "系统异常警告", "检测到系统异常，请立即处理", alertDetails)
	if err != nil {
		logger.Error("发送警告邮件失败", zap.Error(err))
	}
}

func TestEmailSent(t *testing.T) {
	logger, _ := zap.NewProduction()
	defer func(logger *zap.Logger) {
		err := logger.Sync()
		if err != nil {
			fmt.Println(err)
		}
	}(logger)

	// 初始化配置
	config := &email.EmailConfig{
		Host:     "smtp.163.com",
		Port:     25,
		Username: "<EMAIL>",
		Password: "WSsIVcM4smZl9kyw",
		From:     "<EMAIL>",
	}

	logger, _ = zap.NewProduction()
	defer func(logger *zap.Logger) {
		err := logger.Sync()
		if err != nil {
			fmt.Println(err)
		}
	}(logger)

	// 创建邮件工具实例
	emailUtils := email.NewEmailUtils(config, logger)

	// 创建上下文
	ctx := context.Background()

	// 示例1: 发送简单邮件
	err := emailUtils.SendSimpleEmail(ctx, []string{"<EMAIL>"}, "测试邮件", "这是一封测试邮件")
	if err != nil {
		logger.Error("发送邮件失败", zap.Error(err))
	}

	// 示例2: 发送欢迎邮件
	err = emailUtils.SendWelcomeEmail(ctx, []string{"<EMAIL>"}, "张三")
	if err != nil {
		logger.Error("发送欢迎邮件失败", zap.Error(err))
	}

	// 示例3: 发送通知邮件
	details := map[string]string{
		"工单编号": "INC-2024-001",
		"处理人":  "李四",
		"状态":   "已处理",
	}
	err = emailUtils.SendNotificationEmail(ctx, []string{"<EMAIL>"}, "工单处理完成", "工单已处理完成", details)
	if err != nil {
		logger.Error("发送通知邮件失败", zap.Error(err))
	}

	// 示例4: 发送警告邮件
	alertDetails := map[string]string{
		"错误类型": "系统异常",
		"影响范围": "用户登录",
		"紧急程度": "高",
	}
	err = emailUtils.SendAlertEmail(ctx, []string{"<EMAIL>"}, "系统异常警告", "检测到系统异常，请立即处理", alertDetails)
	if err != nil {
		logger.Error("发送警告邮件失败", zap.Error(err))
	}

}

func TestEmailValidation(t *testing.T) {
	logger, _ := zap.NewProduction()
	defer func(logger *zap.Logger) {
		err := logger.Sync()
		if err != nil {
			fmt.Println(err)
		}
	}(logger)

	emailUtils := email.NewEmailUtils(&email.EmailConfig{}, logger)

	// 测试邮箱验证
	testCases := []struct {
		email   string
		valid   bool
		message string
	}{
		{"<EMAIL>", true, "有效邮箱"},
		{"invalid-email", false, "无效邮箱"},
		{"", false, "空邮箱"},
		{"user@", false, "不完整邮箱"},
		{"@example.com", false, "不完整邮箱"},
	}

	for _, tc := range testCases {
		result := emailUtils.ValidateEmail(tc.email)
		if result != tc.valid {
			t.Errorf("邮箱验证失败: %s - 期望: %v, 实际: %v", tc.message, tc.valid, result)
		}
	}
}

func TestMultipleEmailValidation(t *testing.T) {
	logger, _ := zap.NewProduction()
	defer func(logger *zap.Logger) {
		err := logger.Sync()
		if err != nil {
			fmt.Println(err)
		}
	}(logger)

	emailUtils := email.NewEmailUtils(&email.EmailConfig{}, logger)

	emails := []string{"<EMAIL>", "invalid-email", "<EMAIL>", "bad-email"}
	invalidEmails := emailUtils.ValidateEmails(emails)

	expectedInvalid := []string{"invalid-email", "bad-email"}
	if len(invalidEmails) != len(expectedInvalid) {
		t.Errorf("期望无效邮箱数量: %d, 实际: %d", len(expectedInvalid), len(invalidEmails))
	}
}
