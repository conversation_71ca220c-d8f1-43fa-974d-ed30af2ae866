package feishuapp

import (
	"context"
	"errors"
	"fmt"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
)
import lark "github.com/larksuite/oapi-sdk-go/v3"

type FeiShuAppBot struct {
	client *lark.Client
}

func NewFeiShuAppBot(AppID, AppSecret string, options ...lark.ClientOptionFunc) (*FeiShuAppBot, error) {
	if len(AppID) == 0 || len(AppSecret) == 0 {
		return nil, errors.New("AppID或AppSecret为空")
	}
	client := lark.NewClient(AppID, AppSecret, options...) // HY 机器人
	if client == nil {
		return nil, fmt.Errorf("FeishuAppBot初始化失败,AppID: %s", AppID)
	}
	return &FeiShuAppBot{client: client}, nil
}

// GetUserIDByMobilesAndEmails 通过手机号和邮箱获取id,返回的UserId为查询的IDType类型的id
func (f *FeiShuAppBot) GetUserIDByMobilesAndEmails(ctx context.Context, idType IDType, mobiles, emails []string) ([]*larkcontact.UserContactInfo, error) {
	req := larkcontact.NewBatchGetIdUserReqBuilder().
		UserIdType(string(idType)).
		Body(larkcontact.NewBatchGetIdUserReqBodyBuilder().
			Emails(emails).
			Mobiles(mobiles).
			IncludeResigned(true).
			Build()).
		Build()

	// 发起请求
	resp, err := f.client.Contact.V3.User.BatchGetId(ctx, req) //nolint:staticcheck
	// 处理错误
	if err != nil {
		return nil, err
	}

	// 服务端错误处理
	if !resp.Success() {
		return nil, fmt.Errorf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
	}
	return resp.Data.UserList, nil
}

// SendMsgToUser 给单人发送消息
// MsgContent 构建参考 https://open.feishu.cn/document/server-docs/im-v1/message-content-description/create_json
func (f *FeiShuAppBot) SendMsgToUser(ctx context.Context, msgType MsgType, receiveIDType IDType, receivedID string, content string) error {

	sendReq := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(string(receiveIDType)).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(receivedID).
			MsgType(string(msgType)).
			Content(content).
			Build()).
		Build()
	sendResp, err := f.client.Im.V1.Message.Create(ctx, sendReq) //nolint:staticcheck
	if err != nil {
		return err
	}

	if !sendResp.Success() {
		return fmt.Errorf("logId: %s, error response: \n%s", sendResp.RequestId(), larkcore.Prettify(sendResp.CodeError))
	}

	return nil
}
