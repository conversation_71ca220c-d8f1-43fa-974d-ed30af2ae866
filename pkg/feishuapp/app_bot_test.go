package feishuapp

import (
	"context"
	"encoding/json"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"testing"
	"time"
)

// 消息内容填充请参考：https://open.feishu.cn/document/server-docs/im-v1/message-content-description/create_json
var (
	testAppBotID     = "xxxx" // 公司test机器人应用ID
	testAppBotSecret = "xxxx" // 公司test机器人应用Secret
	testMobiles      = []string{"136xxxxxxxx", "12315465789"}
	testEmails       = []string{"<EMAIL>", "<EMAIL>"}

	testUserID = "**********"
	testOpenID = "**********"
	//testUnionID = "**********"

	testRichText = "{\"zh_cn\":{\"title\":\"我是一个标题\",\"content\":[[{\"tag\":\"text\",\"text\":\"第一行 :\"},{\"tag\":\"a\",\"href\":\"http://www.feishu.cn\",\"text\":\"超链接\"},{\"tag\":\"at\",\"user_id\":\"ou_1avnmsbv3k45jnk34j5\",\"user_name\":\"tom\"}],[{\"tag\":\"img\",\"image_key\":\"img_7ea74629-9191-4176-998c-2e603c9c5e8g\"}],[{\"tag\":\"text\",\"text\":\"第二行:\"},{\"tag\":\"text\",\"text\":\"文本测试\"}],[{\"tag\":\"img\",\"image_key\":\"img_7ea74629-9191-4176-998c-2e603c9c5e8g\"}]]}}"
)

func TestNewFeiShuAppBot(t *testing.T) {
	tests := []struct {
		name      string
		appID     string
		appSecret string
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "正常创建",
			appID:     "test_app_id",
			appSecret: "test_app_secret",
			wantErr:   false,
		},
		{
			name:      "AppID为空",
			appID:     "",
			appSecret: "test_app_secret",
			wantErr:   true,
			errMsg:    "AppID或AppSecret为空",
		},
		{
			name:      "AppSecret为空",
			appID:     "test_app_id",
			appSecret: "",
			wantErr:   true,
			errMsg:    "AppID或AppSecret为空",
		},
		{
			name:      "AppID和AppSecret都为空",
			appID:     "",
			appSecret: "",
			wantErr:   true,
			errMsg:    "AppID或AppSecret为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bot, err := NewFeiShuAppBot(tt.appID, tt.appSecret)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, bot)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, bot)
				assert.NotNil(t, bot.client)
			}
		})
	}
}

func TestFeiShuAppBot_GetUserIDByMobilesAndEmails(t *testing.T) {
	bot, err := NewFeiShuAppBot(testAppBotID, testAppBotSecret)
	require.NoError(t, err)
	require.NotNil(t, bot)

	ctx := context.Background()

	tests := []struct {
		name    string
		idType  IDType
		mobiles []string
		emails  []string
	}{
		{
			name:    "通过手机号获取用户ID",
			idType:  IDType_User_ID,
			mobiles: testMobiles,
			emails:  []string{},
		},
		{
			name:    "通过邮箱获取用户ID",
			idType:  IDType_Open_ID,
			mobiles: testMobiles,
			emails:  testEmails,
		},
		{
			name:    "通过手机号和邮箱获取用户ID",
			idType:  IDType_Union_ID,
			mobiles: []string{},
			emails:  testEmails,
		},
		{
			name:    "空参数",
			idType:  IDType_User_ID,
			mobiles: []string{},
			emails:  []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			userList, err := bot.GetUserIDByMobilesAndEmails(ctx, tt.idType, tt.mobiles, tt.emails)
			if err != nil {
				t.Logf("获取用户ID失败（可能是网络或认证问题）: %v", err)
			} else {
				t.Logf("成功获取用户列表，数量: %d", len(userList))
				t.Logf("用户信息列表：%s", larkcore.Prettify(userList))
			}
		})
	}
}

func TestFeiShuAppBot_SendMsgToUser(t *testing.T) {
	bot, err := NewFeiShuAppBot(testAppBotID, testAppBotSecret)
	require.NoError(t, err)
	require.NotNil(t, bot)

	ctx := context.Background()

	testTemplateContentData := map[string]interface{}{
		"template_id":           "AAqd3ZpVIYPF2",
		"template_version_name": "1.0.6",
		"template_variable": map[string]interface{}{
			"date":         time.Now().Format(time.DateOnly),
			"primary_name": "张三",
			"second_name":  "李四",
		},
	}
	testTemplateContent := map[string]interface {
	}{
		"type": "template",
		"data": testTemplateContentData,
	}

	templateTestContentStr, err := json.Marshal(testTemplateContent)
	if err != nil {
		t.Logf("json.Marshal(testTemplateContentData) Fail, err: %s", err.Error())
	}
	tests := []struct {
		name          string
		msgType       MsgType
		receiveIDType IDType
		receivedID    string
		content       string
	}{
		{
			name:          "发送文本消息",
			msgType:       MsgType_Text,
			receiveIDType: IDType_User_ID,
			receivedID:    testUserID,
			content:       `{"text":"测试消息"}`,
		},
		{
			name:          "发送富文本消息",
			msgType:       MsgType_Post,
			receiveIDType: IDType_Open_ID,
			receivedID:    testOpenID,
			content:       testRichText,
		},
		// 测试卡片模板
		{
			name:          "发送卡片消息",
			msgType:       MsgType_Interactice,
			receiveIDType: IDType_User_ID,
			receivedID:    testUserID,
			content:       string(templateTestContentStr),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := bot.SendMsgToUser(ctx, tt.msgType, tt.receiveIDType, tt.receivedID, tt.content)
			if err != nil {
				t.Logf("发送消息失败（可能是网络或认证问题）: %v", err)
			}
		})
	}
}
