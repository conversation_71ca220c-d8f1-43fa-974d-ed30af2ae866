package utils

import (
	"encoding/json"
	"fmt"
)

// ToJSONString 将对象转换为JSON字符串
func ToJSONString(obj interface{}) (string, error) {
	if obj == nil {
		return "", nil
	}

	data, err := json.Marshal(obj)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("JSON序列化失败: %w", err)
	}

	return string(data), nil
}

// FromJSONString 从JSON字符串解析对象
func FromJSONString(jsonStr string, obj interface{}) error {
	if jsonStr == "" {
		return nil
	}

	err := json.Unmarshal([]byte(jsonStr), obj)
	if err != nil {
		return fmt.Errorf("JSON反序列化失败: %w", err)
	}

	return nil
}
