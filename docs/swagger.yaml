basePath: /api/v1
definitions:
  asset.AssetSpare:
    properties:
      asset_status:
        example: idle
        type: string
      batch_number:
        example: B202401
        type: string
      created_at:
        example: "2023-01-01T00:00:00Z"
        type: string
      firmware_version:
        example: 1.2.3
        type: string
      hardware_status:
        example: normal
        type: string
      id:
        example: 1
        type: integer
      location:
        example: 架位A-1-2
        type: string
      price:
        example: 1200.5
        type: number
      product:
        $ref: '#/definitions/product.Product'
      product_id:
        example: 1
        type: integer
      purchase_date:
        example: "2023-01-01T00:00:00Z"
        type: string
      related_asset_id:
        example: 0
        type: integer
      related_asset_sn:
        example: ""
        type: string
      remark:
        example: 从服务器SN12345拆下的CPU
        type: string
      sn:
        example: SP123456
        type: string
      source_type:
        example: 新购
        type: string
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
      warehouse:
        $ref: '#/definitions/asset.Warehouse'
      warehouse_id:
        example: 1
        type: integer
      warranty_expire:
        example: "2026-01-01T00:00:00Z"
        type: string
    type: object
  asset.ChangeAssetStatusRequest:
    properties:
      assetID:
        type: integer
      newStatus:
        type: string
      reason:
        type: string
    required:
    - assetID
    - newStatus
    - reason
    type: object
  asset.ChangeBizStatusRequest:
    properties:
      newStatus:
        type: string
      reason:
        type: string
      resourceID:
        type: integer
    required:
    - newStatus
    - reason
    - resourceID
    type: object
  asset.Device:
    type: object
  asset.NetworkDevice:
    properties:
      created_at:
        type: string
      device:
        $ref: '#/definitions/asset.Device'
      deviceID:
        description: 关联设备ID (外键)
        type: integer
      firmwareVersion:
        type: string
      id:
        example: 1
        type: integer
      layer:
        type: integer
      loopbackAddress:
        type: string
      managementAddress:
        type: string
      portSpeed:
        type: string
      ports:
        description: 其他网络设备特有属性
        type: integer
      role:
        description: 网络设备特有属性
        type: string
      routingProtocols:
        type: string
      stackID:
        type: integer
      stackRole:
        type: string
      stackSupport:
        type: boolean
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
    type: object
  asset.NetworkDeviceWithDeviceInfo:
    properties:
      assetStatus:
        type: string
      assetType:
        type: string
      azID:
        type: integer
      azName:
        type: string
      bizStatus:
        type: string
      bmcIP:
        type: string
      bondType:
        type: string
      brand:
        type: string
      cabinetColumn:
        type: string
      cabinetID:
        type: integer
      cabinetName:
        type: string
      cabinetRow:
        description: 机柜信息
        type: string
      cabinetType:
        type: string
      cluster:
        type: string
      cpuModel:
        type: string
      createdAt:
        type: string
      dataCenterAddress:
        type: string
      dataCenterID:
        type: integer
      dataCenterName:
        type: string
      deliveryTime:
        type: string
      deviceID:
        description: 基本设备信息
        type: integer
      diskType:
        type: string
      firmwareVersion:
        type: string
      gpuModel:
        type: string
      hardwareStatus:
        type: string
      id:
        description: 网络设备信息
        type: integer
      isBackup:
        type: boolean
      layer:
        type: integer
      loopbackAddress:
        type: string
      managementAddress:
        type: string
      memoryCapacity:
        type: integer
      model:
        type: string
      networkEnvironment:
        type: string
      portSpeed:
        type: string
      ports:
        type: integer
      price:
        type: number
      project:
        type: string
      purchaseDate:
        type: string
      purchaseOrder:
        description: 财务信息
        type: string
      rackPosition:
        type: integer
      rackingTime:
        type: string
      regionID:
        type: integer
      regionName:
        type: string
      remark:
        type: string
      resStatus:
        type: string
      residualValue:
        type: number
      resourceID:
        description: 资源信息
        type: integer
      resourceRemark:
        type: string
      role:
        type: string
      roomID:
        description: 位置信息
        type: integer
      roomName:
        type: string
      routingProtocols:
        type: string
      sn:
        type: string
      stackID:
        type: integer
      stackRole:
        type: string
      stackSupport:
        type: boolean
      templateID:
        description: 套餐模板信息
        type: integer
      templateName:
        description: 添加套餐模板名称
        type: string
      totalPower:
        type: number
      unitHeight:
        type: integer
      updatedAt:
        type: string
      vpcIP:
        type: string
      warrantyExpire:
        type: string
    type: object
  asset.ProcessAssetDeliveryRequest:
    properties:
      assetID:
        type: integer
      project:
        type: string
    required:
    - assetID
    - project
    type: object
  asset.ProcessAssetMaintenanceRequest:
    properties:
      assetID:
        type: integer
      reason:
        type: string
    required:
    - assetID
    - reason
    type: object
  asset.ProcessAssetOutboundRequest:
    properties:
      approverID:
        type: integer
      approverName:
        type: string
      assetID:
        type: integer
    required:
    - approverID
    - approverName
    - assetID
    type: object
  asset.ProcessAssetRackingRequest:
    properties:
      assetID:
        type: integer
      cabinetID:
        type: integer
      rackPosition:
        type: integer
    required:
    - assetID
    - cabinetID
    - rackPosition
    type: object
  asset.ProcessAssetScrapRequest:
    properties:
      approverID:
        type: integer
      approverName:
        type: string
      assetID:
        type: integer
      reason:
        type: string
    required:
    - approverID
    - approverName
    - assetID
    - reason
    type: object
  asset.ProcessAssetStorageRequest:
    properties:
      approverID:
        type: integer
      approverName:
        type: string
      assetID:
        type: integer
    required:
    - approverID
    - approverName
    - assetID
    type: object
  asset.Resource:
    properties:
      assetID:
        type: integer
      bizStatus:
        type: string
      bmcIP:
        type: string
      cabinet:
        $ref: '#/definitions/location.Cabinet'
      cabinetID:
        type: integer
      cluster:
        type: string
      created_at:
        type: string
      deliveryTime:
        type: string
      device:
        $ref: '#/definitions/asset.Device'
      height:
        type: integer
      hostname:
        type: string
      id:
        example: 1
        type: integer
      isBackup:
        type: boolean
      lastBizStatusChange:
        type: string
      project:
        type: string
      rackPosition:
        type: integer
      rackingTime:
        type: string
      remark:
        type: string
      resStatus:
        type: string
      room:
        $ref: '#/definitions/location.Room'
      roomID:
        type: integer
      sn:
        type: string
      tenantIP:
        type: string
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
      vpcIP:
        type: string
    type: object
  asset.Warehouse:
    properties:
      code:
        example: WH-001
        type: string
      created_at:
        example: "2023-01-01T00:00:00Z"
        type: string
      description:
        example: 存放CPU和内存备件
        type: string
      id:
        example: 1
        type: integer
      name:
        example: 主数据中心备件仓
        type: string
      room:
        $ref: '#/definitions/location.Room'
      room_id:
        example: 1
        type: integer
      status:
        example: active
        type: string
      type:
        example: 备件仓
        type: string
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
    type: object
  component.ComponentStatistics:
    properties:
      by_server:
        additionalProperties:
          type: integer
        type: object
      by_status:
        additionalProperties:
          type: integer
        type: object
      by_type:
        additionalProperties:
          type: integer
        type: object
      inventory:
        $ref: '#/definitions/component.InventoryStatistics'
      total_components:
        type: integer
    type: object
  component.InventoryStatistics:
    properties:
      allocated_count:
        description: 已分配数量
        type: integer
      allocated_stock:
        description: 已分配库存
        type: integer
      defect_count:
        type: integer
      good_count:
        type: integer
      idle_count:
        type: integer
      in_use_count:
        type: integer
      total_stock:
        type: integer
    type: object
  component.ServerComponent:
    type: object
  controller.NewInbondReq:
    properties:
      file_id:
        type: integer
      new_inbound_id:
        type: integer
    required:
    - file_id
    - new_inbound_id
    type: object
  inbound.CreateNewInboundResponse:
    properties:
      new_inbound_id:
        type: integer
      new_inbound_no:
        type: string
      submitter_time:
        type: string
    type: object
  inbound.DismantledInbound:
    type: object
  inbound.DismantledInboundDTO:
    type: object
  inbound.DismantledInboundDTOV1:
    properties:
      dismantled_inbounds:
        $ref: '#/definitions/inbound.DismantledInbound'
      inbound_title:
        type: string
      project:
        type: string
    required:
    - dismantled_inbounds
    - inbound_title
    - project
    type: object
  inbound.InboudWholeInfo:
    properties:
      inbound_info: {}
      inboundType:
        type: string
      ticket_info: {}
    type: object
  inbound.InboundList:
    properties:
      asset_type:
        type: string
      completed_at:
        type: string
      create_by:
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      creater_id:
        type: integer
      id:
        example: 1
        type: integer
      inbound_no:
        type: string
      inbound_reason:
        type: string
      inbound_title:
        type: string
      inbound_type:
        type: string
      project:
        type: string
      stage:
        type: string
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
    required:
    - inbound_reason
    - inbound_title
    - project
    type: object
  inbound.NewInbound:
    properties:
      amount:
        description: 数量
        type: integer
      comment:
        type: string
      create_by:
        type: string
      create_id:
        type: integer
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      inbound_no:
        type: string
      inbound_title:
        type: string
      lock:
        type: boolean
      may_arrive_at:
        type: string
      new_details:
        items:
          $ref: '#/definitions/inbound.NewInboundDetail'
        type: array
      new_info:
        items:
          $ref: '#/definitions/inbound.NewInboundInfo'
        type: array
      project:
        type: string
      purchase_order_id:
        description: 采购合同ID
        type: integer
      purchase_order_no:
        description: 采购合同编号
        type: string
      supplier_name:
        description: 供应商名称
        type: string
      tracking_info:
        type: string
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
    required:
    - new_info
    - purchase_order_no
    - supplier_name
    type: object
  inbound.NewInboundDetail:
    properties:
      asset_status:
        example: idle
        type: string
      batch_number:
        example: B202401
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      data_center_id:
        type: integer
      data_center_name:
        type: string
      firmware_version:
        example: 1.2.3
        type: string
      hardware_status:
        example: normal
        type: string
      id:
        example: 1
        type: integer
      location:
        example: 架位A-1-2
        type: string
      new_inbound_id:
        type: integer
      price:
        example: 1200.5
        type: number
      product:
        $ref: '#/definitions/product.Product'
      product_id:
        type: integer
      purchase_date:
        example: "2023-01-01T00:00:00Z"
        type: string
      related_asset_id:
        example: 0
        type: integer
      related_asset_sn:
        example: ""
        type: string
      remark:
        example: 从服务器SN12345拆下的CPU
        type: string
      room_id:
        type: integer
      room_name:
        type: string
      sn:
        example: SP123456
        type: string
      source_type:
        description: 目前不关注
        example: 新购
        type: string
      try_count:
        example: 0
        type: integer
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      warehouse_id:
        example: 1
        type: integer
      warehouse_name:
        type: string
      warranty_expire:
        example: "2026-01-01T00:00:00Z"
        type: string
    type: object
  inbound.NewInboundInfo:
    properties:
      amount:
        type: integer
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      new_inbound_id:
        type: integer
      product:
        $ref: '#/definitions/product.Product'
      product_id:
        type: integer
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
    required:
    - amount
    - product_id
    type: object
  inbound.PartInbound:
    properties:
      component_id:
        type: string
      component_sn:
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      engineer_id:
        type: integer
      engineer_name:
        type: string
      fault_description:
        type: string
      fault_level:
        type: string
      handling_suggestion:
        type: string
      hardware_status:
        type: string
      id:
        example: 1
        type: integer
      inbound_no:
        type: string
      inbound_notes:
        type: string
      inbound_time:
        type: string
      inspection_result:
        type: string
      lock:
        type: boolean
      manufacturer_return_date:
        type: string
      manufacturer_return_status:
        type: string
      recipient_id:
        type: integer
      recipient_name:
        type: string
      repair_ticket_id:
        type: integer
      requires_rma:
        type: boolean
      rma_number:
        type: string
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      warehouse:
        $ref: '#/definitions/asset.Warehouse'
      warehouse_id:
        type: integer
      warehouse_location:
        type: string
    required:
    - component_id
    - inbound_no
    - repair_ticket_id
    type: object
  inbound.RepairInbound:
    properties:
      create_by:
        type: string
      create_id:
        type: integer
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      inbound_no:
        type: string
      inbound_title:
        type: string
      project:
        type: string
      repair_details:
        items:
          $ref: '#/definitions/inbound.RepairInboundDetails'
        type: array
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
    required:
    - repair_details
    type: object
  inbound.RepairInboundDTO:
    properties:
      inbound_title:
        type: string
      project:
        type: string
      repair_inbound:
        $ref: '#/definitions/inbound.RepairInbound'
    required:
    - inbound_title
    - project
    - repair_inbound
    type: object
  inbound.RepairInboundDetails:
    properties:
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      data_center_id:
        type: integer
      data_center_name:
        type: string
      id:
        example: 1
        type: integer
      pn:
        example: B202401
        type: string
      repair_inbound_id:
        type: integer
      repair_type:
        example: 维修 | 换新===repaired | renew
        type: string
      replace_pn:
        example: B202401
        type: string
      replace_sn:
        example: ""
        type: string
      sn:
        example: SP123456
        type: string
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      warehouse_id:
        type: integer
      warehouse_name:
        type: string
    required:
    - repair_type
    - replace_sn
    - sn
    - warehouse_id
    - warehouse_name
    type: object
  inbound.RepairInboundResult:
    properties:
      inbound_no:
        type: string
    type: object
  inbound.inboundDTO:
    properties:
      inbound_title:
        type: string
      may_arrive_at:
        type: string
      new_info:
        items:
          $ref: '#/definitions/inbound.NewInboundInfo'
        type: array
      project:
        type: string
      purchase_order_no:
        description: 采购合同编号
        type: string
      supplier_name:
        description: 供应商名称
        type: string
      tracking_info:
        type: string
      warehouse_id:
        type: integer
      warehouse_name:
        type: string
    required:
    - inbound_title
    - may_arrive_at
    - new_info
    - project
    - purchase_order_no
    - supplier_name
    type: object
  inventory.InventoryDetail:
    properties:
      allocated_stock:
        example: 3
        type: integer
      available_stock:
        example: 7
        type: integer
      batch_number:
        example: B20230615
        type: string
      created_at:
        example: "2023-01-01T00:00:00Z"
        type: string
      current_stock:
        example: 10
        type: integer
      id:
        example: 1
        type: integer
      inbound_date:
        example: "2023-01-01T00:00:00Z"
        type: string
      new_inbound_id:
        example: 1
        type: integer
      outbound_date:
        example: "2023-01-01T00:00:00Z"
        type: string
      outbound_id:
        description: NewInbound        inbound.NewInbound `json:"new_inbound" gorm:"foreignKey:NewInboundID"`
        example: 1
        type: integer
      part_inbound_id:
        example: 1
        type: integer
      product:
        $ref: '#/definitions/product.Product'
      product_id:
        example: 1
        type: integer
      status:
        example: active
        type: string
      unit_price:
        example: 1200.5
        type: number
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
      warehouse:
        example: 主仓库
        type: string
      warehouse_id:
        example: 1
        type: integer
      warehouse_location:
        description: WarehouseModel    asset.Warehouse `json:"warehouseModel" gorm:"foreignKey:WarehouseID"`
        example: A区-01-02
        type: string
      warranty_end:
        example: "2026-01-01T00:00:00Z"
        type: string
      warranty_start:
        example: "2023-01-01T00:00:00Z"
        type: string
    type: object
  location.AZ:
    properties:
      created_at:
        type: string
      description:
        type: string
      id:
        example: 1
        type: integer
      name:
        type: string
      region:
        $ref: '#/definitions/location.Region'
      regionId:
        type: integer
      status:
        type: string
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
    type: object
  location.Cabinet:
    properties:
      bondType:
        type: string
      cabinetType:
        type: string
      capacityUnits:
        type: integer
      column:
        type: string
      created_at:
        type: string
      description:
        type: string
      id:
        example: 1
        type: integer
      name:
        type: string
      networkEnvironment:
        type: string
      room:
        $ref: '#/definitions/location.Room'
      roomID:
        type: integer
      row:
        type: string
      status:
        type: string
      totalPower:
        type: number
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
    type: object
  location.DataCenter:
    properties:
      address:
        type: string
      az:
        $ref: '#/definitions/location.AZ'
      azId:
        type: integer
      created_at:
        type: string
      description:
        type: string
      id:
        example: 1
        type: integer
      name:
        type: string
      status:
        type: string
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
    type: object
  location.Region:
    properties:
      created_at:
        example: "2023-01-01T00:00:00Z"
        type: string
      description:
        type: string
      id:
        example: 1
        type: integer
      name:
        type: string
      status:
        type: string
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
    type: object
  location.Room:
    properties:
      created_at:
        type: string
      dataCenterID:
        type: integer
      description:
        type: string
      id:
        example: 1
        type: integer
      name:
        type: string
      status:
        type: string
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
    type: object
  model.APIResponse:
    properties:
      code:
        description: 状态码 0:成功 1:失败
        type: integer
      data:
        description: 数据
      msg:
        description: 消息
        type: string
    type: object
  model.AuthResponse:
    properties:
      accessToken:
        type: string
      expires_at:
        type: integer
      user_id:
        type: integer
    type: object
  model.BatchCreateTicketRequest:
    properties:
      tickets:
        description: 工单列表，至少需要一个
        items:
          $ref: '#/definitions/model.CustomerTicketRequest'
        minItems: 1
        type: array
    required:
    - tickets
    type: object
  model.BatchCreateTicketResponse:
    properties:
      failCount:
        description: 失败数量
        type: integer
      results:
        description: 详细结果
        items:
          $ref: '#/definitions/model.BatchCreateTicketResult'
        type: array
      successCount:
        description: 成功创建数量
        type: integer
    type: object
  model.BatchCreateTicketResult:
    properties:
      detail:
        allOf:
        - $ref: '#/definitions/model.CustomerTicketRequest'
        description: 原始请求详情
      error:
        description: 失败原因
        type: string
      success:
        description: 是否成功
        type: boolean
      ticketId:
        description: 成功时的工单ID，格式为i+数字
        type: string
      ticketVmIP:
        description: 虚机IP
        type: string
    type: object
  model.ChangePasswordRequest:
    properties:
      newPassword:
        maxLength: 20
        minLength: 8
        type: string
      oldPassword:
        type: string
    required:
    - newPassword
    - oldPassword
    type: object
  model.ColdMigration:
    properties:
      backup_device_sn:
        type: string
      comments:
        type: string
      created_at:
        type: string
      duration:
        type: integer
      execution_time:
        type: string
      fault_device_sn:
        type: string
      id:
        type: integer
      operator_id:
        type: integer
      operator_name:
        type: string
      status:
        type: string
      tenant_ip:
        type: string
      ticket_id:
        type: integer
      updated_at:
        type: string
    type: object
  model.CreateUserRequest:
    properties:
      department:
        type: string
      email:
        type: string
      password:
        maxLength: 20
        minLength: 8
        type: string
      realName:
        type: string
      roleName:
        type: string
      roles:
        items:
          type: string
        type: array
      status:
        type: string
      telephone:
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - password
    - roleName
    - roles
    - status
    - username
    type: object
  model.CustomerApproval:
    properties:
      comments:
        type: string
      created_at:
        type: string
      customer_id:
        type: integer
      customer_name:
        type: string
      id:
        type: integer
      response_time:
        type: string
      status:
        type: string
      ticket_id:
        type: integer
      updated_at:
        type: string
    type: object
  model.CustomerTicketAuthorityRequest:
    properties:
      ticketId:
        description: 事件单ID
        type: string
      ticketVmIp:
        description: 虚机IP
        type: string
    type: object
  model.CustomerTicketQueryResponse:
    properties:
      data:
        description: 数据
        items:
          $ref: '#/definitions/model.TicketDetail'
        type: array
      page:
        description: 页码
        type: integer
      page_size:
        description: 每页条数
        type: integer
      total:
        description: 总数
        type: integer
    type: object
  model.CustomerTicketRequest:
    properties:
      ticketContent:
        description: 报障内容
        type: string
      ticketVmIP:
        description: 虚机IP
        type: string
    required:
    - ticketContent
    - ticketVmIP
    type: object
  model.CustomerTicketResponse:
    properties:
      existingTicket:
        description: 是否为已存在的工单
        type: boolean
      ticketId:
        description: 事件单ID，格式为i+数字
        type: string
    type: object
  model.ExportRequest:
    properties:
      condition:
        description: 额外的查询条件
        type: string
      fields:
        description: 导出字段
        items:
          $ref: '#/definitions/model.Field'
        type: array
      filename:
        description: 文件名
        type: string
      ids:
        description: 选定的ID列表
        items:
          type: integer
        type: array
      isHeader:
        description: 是否包含表头
        type: boolean
      mode:
        description: 导出模式：all/selected
        type: string
      sheetName:
        description: 工作表名称
        type: string
      tableName:
        description: 表名
        type: string
    type: object
  model.ExportResponse:
    properties:
      expired:
        description: 过期时间
        type: string
      fileUrl:
        description: 文件下载URL
        type: string
    type: object
  model.FaultTicket:
    properties:
      acknowledgeTime:
        type: string
      actualFixTime:
        type: string
      assignedTo:
        type: string
      assignmentTime:
        type: string
      business_impact:
        type: string
      business_impact_time:
        type: integer
      closeTime:
        type: string
      componentSN:
        type: string
      componentType:
        type: string
      count_in_sla:
        type: boolean
      created_at:
        type: string
      creationTime:
        type: string
      current_waiting_stage:
        type: string
      customerApprovalTime:
        type: string
      deviceSN:
        type: string
      diagnosis_duration:
        type: integer
      expectedFixTime:
        type: string
      fault_detail_type:
        type: string
      faultDescription:
        type: string
      faultSummary:
        type: string
      faultType:
        type: string
      hardware_repair_duration:
        type: integer
      id:
        type: integer
      immediate_repair:
        type: boolean
      is_duplicate_fault:
        type: boolean
      is_false_alarm:
        type: boolean
      is_frequent_fault:
        type: boolean
      lastWaitingTime:
        type: string
      lastWorkflowRetryTime:
        type: string
      needsWorkflowRetry:
        description: 工作流相关字段
        type: boolean
      preventionMeasures:
        type: string
      priority:
        type: string
      related_ticket_id:
        type: integer
      remarks:
        type: string
      repair_ticket_id:
        description: 关联的维修单ID
        type: integer
      repairMethod:
        type: string
      reporterID:
        type: integer
      reporterName:
        type: string
      require_approval:
        type: boolean
      resource:
        $ref: '#/definitions/asset.Resource'
      resource_identifier:
        type: string
      response_duration:
        type: integer
      sla_violation_reason:
        type: string
      slaStatus:
        type: string
      slotPosition:
        type: string
      software_fix_duration:
        type: integer
      source:
        type: string
      status:
        type: string
      symptom:
        type: string
      ticketNo:
        type: string
      title:
        type: string
      totalDowntime:
        type: integer
      triageCompleteTime:
        type: string
      updated_at:
        type: string
      verificationEndTime:
        type: string
      verificationStartTime:
        type: string
      waiting_manual_trigger:
        description: 手动触发相关字段
        type: boolean
      workflow_retry_count:
        type: integer
    type: object
  model.FaultTicketAssignment:
    properties:
      engineer_id:
        type: integer
    required:
    - engineer_id
    type: object
  model.Field:
    properties:
      field:
        type: string
      title:
        type: string
    type: object
  model.File:
    properties:
      created_at:
        type: string
      description:
        description: 文件描述
        type: string
      file_name:
        description: 原始文件名
        type: string
      file_size:
        description: 文件大小(字节)
        type: integer
      file_type:
        allOf:
        - $ref: '#/definitions/model.FileType'
        description: 文件类型
      id:
        example: 1
        type: integer
      mime_type:
        description: MIME类型
        type: string
      module_id:
        description: 关联模块ID
        type: integer
      module_type:
        description: 关联模块类型
        type: string
      storage_path:
        description: 存储路径
        type: string
      tags:
        description: 文件标签
        type: string
      thumbnail_url:
        description: 缩略图URL(图片类型)
        type: string
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
      uploaded_by:
        description: 上传用户ID
        type: integer
      url:
        description: 访问URL
        type: string
    type: object
  model.FileBatchResponse:
    properties:
      error_count:
        description: 失败数量
        type: integer
      errors:
        description: 失败的文件及原因
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      files:
        description: 成功上传的文件
        items:
          $ref: '#/definitions/model.FileResponse'
        type: array
      success_count:
        description: 成功上传数量
        type: integer
    type: object
  model.FileResponse:
    properties:
      file_name:
        description: 文件名
        type: string
      file_size:
        description: 文件大小
        type: integer
      file_type:
        allOf:
        - $ref: '#/definitions/model.FileType'
        description: 文件类型
      id:
        description: 文件ID
        type: integer
      thumbnail_url:
        description: 缩略图URL
        type: string
      url:
        description: 访问URL
        type: string
    type: object
  model.FileType:
    enum:
    - image
    - doc
    - zip
    - csv
    - other
    type: string
    x-enum-comments:
      FileTypeCSV: CSV文件
      FileTypeDoc: 文档
      FileTypeImage: 图片
      FileTypeOther: 其他
      FileTypeZip: 压缩文件
    x-enum-varnames:
    - FileTypeImage
    - FileTypeDoc
    - FileTypeZip
    - FileTypeCSV
    - FileTypeOther
  model.GpuServer:
    type: object
  model.HardSchedule:
    type: object
  model.HardwareReplaceRequest:
    properties:
      spare_id:
        type: integer
    required:
    - spare_id
    type: object
  model.InboundTrigger:
    properties:
      comments:
        type: string
      data:
        additionalProperties: true
        type: object
      operator:
        type: integer
      operator_name:
        type: string
      require_verified:
        type: boolean
      required_approval:
        type: boolean
      stage:
        type: string
      status:
        type: string
    required:
    - status
    type: object
  model.LoginRequest:
    properties:
      password:
        type: string
      username:
        type: string
    required:
    - password
    - username
    type: object
  model.Menu:
    type: object
  model.MenuListResponse:
    properties:
      authCode:
        type: string
      children:
        items:
          $ref: '#/definitions/model.MenuListResponse'
        type: array
      component:
        type: string
      id:
        type: integer
      meta:
        $ref: '#/definitions/model.MenuMeta'
      name:
        type: string
      order:
        type: integer
      path:
        type: string
      pid:
        type: integer
      status:
        type: boolean
      type:
        type: string
    type: object
  model.MenuMeta:
    properties:
      activeIcon:
        type: string
      activePath:
        type: string
      affixTab:
        type: boolean
      affixTabOrder:
        type: integer
      badge:
        type: string
      badgeType:
        type: string
      badgeVariants:
        type: string
      hideChildrenInMenu:
        type: boolean
      hideInBreadcrumb:
        type: boolean
      hideInMenu:
        type: boolean
      hideInTab:
        type: boolean
      icon:
        type: string
      iframeSrc:
        type: string
      ignoreAccess:
        type: boolean
      keepAlive:
        type: boolean
      link:
        type: string
      menuVisibleWithForbidden:
        type: boolean
      noBasicLayout:
        type: boolean
      openInNewWindow:
        type: boolean
      order:
        type: integer
      title:
        type: string
    type: object
  model.MenuResponse:
    properties:
      children:
        items:
          $ref: '#/definitions/model.MenuResponse'
        type: array
      component:
        type: string
      meta:
        $ref: '#/definitions/model.MenuMeta'
      name:
        type: string
      path:
        type: string
      redirect:
        type: string
    type: object
  model.PartInboundTicket:
    type: object
  model.RegisterRequest:
    properties:
      password:
        maxLength: 20
        minLength: 8
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - password
    - username
    type: object
  model.RepairSelection:
    properties:
      comments:
        type: string
      created_at:
        type: string
      diagnosis:
        type: string
      fault_detail_type:
        type: string
      id:
        type: integer
      operator_id:
        type: integer
      operator_name:
        type: string
      repair_type:
        type: string
      slot_position:
        type: string
      ticket_id:
        type: integer
      updated_at:
        type: string
    type: object
  model.RepairTicket:
    properties:
      arrive_time:
        type: string
      assigned_engineer_id:
        type: integer
      assigned_engineer_name:
        type: string
      assigned_time:
        type: string
      attempt_number:
        type: integer
      complete_time:
        type: string
      created_at:
        type: string
      created_time:
        type: string
      fault_ticket:
        $ref: '#/definitions/model.FaultTicket'
      fault_ticket_id:
        type: integer
      hardware_operation_duration:
        type: integer
      hardware_replace_end:
        type: string
      hardware_replace_start:
        type: string
      id:
        type: integer
      is_first_attempt:
        type: boolean
      repair_result:
        type: string
      repair_steps:
        type: string
      repair_type:
        type: string
      software_config_end:
        type: string
      software_config_start:
        type: string
      software_operation_duration:
        type: integer
      solution:
        type: string
      spare_id:
        type: integer
      spare_model:
        type: string
      spare_sn:
        type: string
      spare_type:
        type: string
      start_time:
        type: string
      status:
        type: string
      testing_end:
        type: string
      testing_start:
        type: string
      ticket_no:
        type: string
      total_repair_duration:
        type: integer
      updated_at:
        type: string
      verification_description:
        type: string
      verification_result:
        type: string
      waiting_duration:
        type: integer
    type: object
  model.RepairTicketAssignment:
    properties:
      engineer_id:
        type: integer
    required:
    - engineer_id
    type: object
  model.RepairTicketCreateRequest:
    properties:
      fault_ticket_id:
        type: integer
      repair_type:
        type: string
    required:
    - fault_ticket_id
    - repair_type
    type: object
  model.RepairTicketStatusHistory:
    properties:
      activity_category:
        type: string
      created_at:
        type: string
      duration:
        type: integer
      id:
        type: integer
      is_sla_pause:
        type: boolean
      new_status:
        type: string
      operation_time:
        type: string
      operator_id:
        type: integer
      operator_name:
        type: string
      pause_reason:
        type: string
      previous_status:
        type: string
      remarks:
        type: string
      repair_ticket_id:
        type: integer
      updated_at:
        type: string
    type: object
  model.RepairTicketStatusUpdate:
    properties:
      operator_id:
        type: integer
      operator_name:
        type: string
      status:
        type: string
    required:
    - operator_id
    - operator_name
    - status
    type: object
  model.ResetPasswordRequest:
    properties:
      newPassword:
        maxLength: 20
        minLength: 8
        type: string
      userId:
        type: integer
    required:
    - newPassword
    - userId
    type: object
  model.RoleCreateRequest:
    properties:
      name:
        type: string
      permissions:
        items:
          type: integer
        type: array
      remark:
        type: string
      status:
        type: integer
    required:
    - name
    type: object
  model.RoleListResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.RoleResponse'
        type: array
      total:
        type: integer
    type: object
  model.RoleResponse:
    properties:
      createTime:
        type: string
      id:
        type: string
      name:
        type: string
      permissions:
        items:
          type: integer
        type: array
      remark:
        type: string
      status:
        type: integer
    type: object
  model.RoleUpdateRequest:
    properties:
      id:
        type: string
      name:
        type: string
      permissions:
        items:
          type: integer
        type: array
      remark:
        type: string
      status:
        type: integer
    required:
    - id
    - name
    type: object
  model.SoftSchedule:
    type: object
  model.SpareMachineRequest:
    properties:
      template_id:
        type: integer
    required:
    - template_id
    type: object
  model.SpareRequest:
    properties:
      product_id:
        type: integer
      quantity:
        type: integer
    required:
    - product_id
    - quantity
    type: object
  model.TicketDetail:
    properties:
      isAuthorized:
        description: 是否已授权
        type: boolean
      isColdmigration:
        description: 是否冷迁移
        type: boolean
      requireApproval:
        description: 是否需要客户审批(从主系统直接映射)
        type: boolean
      ticketCategory:
        description: 事件类型
        type: string
      ticketClosedAt:
        description: 事件单关闭时间
        type: string
      ticketContent:
        description: 事件单详情
        type: string
      ticketCreateAt:
        description: 事件单创建时间
        type: string
      ticketFrom:
        description: 事件单来源
        type: string
      ticketId:
        description: 事件单ID
        type: string
      ticketRepairComment:
        description: 维修备注
        type: string
      ticketStatus:
        description: 事件单状态
        type: string
      ticketSubcategory:
        description: 事件子类型
        type: string
      ticketUpdatedAt:
        description: 事件单最近更新时间
        type: string
      ticketVmIp:
        description: 虚拟机IP
        type: string
    type: object
  model.UpdateUserRequest:
    properties:
      department:
        type: string
      email:
        type: string
      id:
        type: integer
      realName:
        type: string
      roleName:
        type: string
      roles:
        items:
          type: string
        type: array
      status:
        type: string
      telephone:
        type: string
      value:
        type: string
    required:
    - id
    type: object
  model.Verification:
    properties:
      comments:
        type: string
      created_at:
        type: string
      id:
        type: integer
      operator_id:
        type: integer
      operator_name:
        type: string
      success:
        type: boolean
      ticket_id:
        type: integer
      updated_at:
        type: string
      verification_time:
        type: string
    type: object
  product.CreateProductDTO:
    properties:
      brand:
        type: string
      material_type:
        type: string
      model:
        type: string
      pn:
        type: string
      product_category:
        type: string
      spec:
        type: string
    required:
    - brand
    - material_type
    - model
    - pn
    - product_category
    - spec
    type: object
  product.Product:
    properties:
      brand:
        example: Intel
        type: string
      created_at:
        example: "2023-01-01T00:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      material_type:
        example: CPU
        type: string
      model:
        example: Xeon 8280
        type: string
      pn:
        example: BX806958280
        type: string
      product_category:
        example: processor
        type: string
      spec:
        example: 28核56线程
        type: string
      updated_at:
        example: "2023-01-01T00:00:00Z"
        type: string
    type: object
  product.ProductListResult:
    properties:
      list:
        items:
          $ref: '#/definitions/product.Product'
        type: array
      total:
        type: integer
    type: object
  product.UpdateProductDTO:
    properties:
      brand:
        type: string
      id:
        type: integer
      material_type:
        type: string
      model:
        type: string
      pn:
        type: string
      product_category:
        type: string
      spec:
        type: string
    required:
    - brand
    - id
    - material_type
    - model
    - pn
    - product_category
    - spec
    type: object
  response.PageResult:
    properties:
      list: {}
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
    type: object
  response.Response:
    properties:
      code:
        description: 业务状态码
        type: integer
      data:
        description: 数据
      message:
        description: 提示信息
        type: string
    type: object
  response.ResponseStruct:
    properties:
      code:
        description: |-
          HTTP 状态码
          example: 200
        type: integer
      data:
        description: 响应数据
      message:
        description: |-
          响应消息
          example: 操作成功
        type: string
    type: object
  service.CreatePurchaseOrderDTO:
    type: object
  service.RepairResult:
    properties:
      repair_result:
        type: string
      repair_steps:
        type: string
      solution:
        type: string
    type: object
  template.MachineTemplate:
    type: object
  template.TemplateComponent:
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a sample server celler server.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Swagger  API
  version: "1.0"
paths:
  /api/v1/cmdb/asset/rack:
    get:
      consumes:
      - application/json
      description: 分页获取上架工单列表，支持筛选
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 工单编号
        in: query
        name: ticket_no
        type: string
      - description: 工单状态
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取上架工单列表
      tags:
      - 资产管理-上架工单
  /api/v1/cmdb/asset/rack/{id}:
    get:
      consumes:
      - application/json
      description: 根据ID获取上架工单详情
      parameters:
      - description: 上架工单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取上架工单详情
      tags:
      - 资产管理-上架工单
  /api/v1/cmdb/asset/rack/{id}/status:
    put:
      consumes:
      - application/json
      description: 更新上架工单的状态
      parameters:
      - description: 上架工单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 状态信息 (包含status字段)
        in: body
        name: status
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新上架工单状态
      tags:
      - 资产管理-上架工单
  /api/v1/import/data:
    post:
      consumes:
      - multipart/form-data
      description: 导入CSV/Excel文件数据
      parameters:
      - description: JWT token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 模型类型
        in: formData
        name: model
        required: true
        type: string
      - description: 要导入的文件
        in: formData
        name: file
        required: true
        type: file
      - description: '导入模式: overwrite(覆盖), append_bottom(底部追加), append_top(顶部追加)'
        enum:
        - overwrite
        - append_bottom
        - append_top
        in: formData
        name: import_mode
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 导入结果
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 导入数据
      tags:
      - 数据导入
  /audit/operation-logs:
    get:
      consumes:
      - application/json
      description: 分页获取操作日志列表
      parameters:
      - description: 操作模块
        in: query
        name: module
        type: string
      - description: 操作类型
        in: query
        name: operation
        type: string
      - description: 操作用户
        in: query
        name: username
        type: string
      - description: 开始时间 格式：2006-01-02 15:04:05
        in: query
        name: start_time
        type: string
      - description: 结束时间 格式：2006-01-02 15:04:05
        in: query
        name: end_time
        type: string
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取操作日志列表
      tags:
      - 审计-操作日志
  /auth/admin/create-user:
    post:
      consumes:
      - application/json
      description: 超级管理员创建新用户
      parameters:
      - description: 用户信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.CreateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 管理员创建用户
      tags:
      - 用户管理
  /auth/admin/reset-password:
    post:
      consumes:
      - application/json
      description: 超级管理员重置指定用户的密码
      parameters:
      - description: 重置密码信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.ResetPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 重置成功
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "403":
          description: 没有权限
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 超级管理员重置用户密码
      tags:
      - 用户管理
  /auth/change-password:
    post:
      consumes:
      - application/json
      description: 修改当前用户的密码
      parameters:
      - description: 密码修改信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 修改密码
      tags:
      - 用户管理
  /auth/check-permission-status:
    get:
      consumes:
      - application/json
      description: 检查用户所属角色的权限是否有更新
      produces:
      - application/json
      responses:
        "200":
          description: 返回是否需要更新权限
          schema:
            additionalProperties: true
            type: object
        "401":
          description: 未授权
          schema:
            type: string
      security:
      - Bearer: []
      summary: 检查用户权限是否需要更新（基于角色标志）
      tags:
      - 用户
  /auth/clear-permission-flag:
    post:
      consumes:
      - application/json
      description: 在前端获取到最新权限后，手动清除权限更新标志
      produces:
      - application/json
      responses:
        "200":
          description: 清除成功
          schema:
            additionalProperties: true
            type: object
        "401":
          description: 未授权
          schema:
            type: string
      security:
      - Bearer: []
      summary: 清除用户权限更新标志
      tags:
      - 用户
  /auth/codes:
    get:
      description: 获取按钮权限
      responses:
        "200":
          description: 获取按钮权限成功
          schema:
            type: string
        "401":
          description: 未授权
          schema:
            type: string
        "500":
          description: 获取按钮权限失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 获取按钮权限
      tags:
      - 用户
  /auth/login:
    post:
      consumes:
      - application/json
      description: 用户登录
      parameters:
      - description: 登录请求
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/model.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/model.AuthResponse'
        "400":
          description: 请求参数错误
          schema:
            type: string
        "401":
          description: 用户名或密码错误
          schema:
            type: string
      summary: 用户登录
      tags:
      - 用户
  /auth/logout:
    post:
      consumes:
      - application/json
      description: 用户登出
      produces:
      - application/json
      responses:
        "200":
          description: 登出成功
          schema:
            type: string
      security:
      - Bearer: []
      summary: 用户登出
      tags:
      - 用户
  /auth/register:
    post:
      consumes:
      - application/json
      description: 用户注册
      parameters:
      - description: 注册请求
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/model.RegisterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 注册成功
          schema:
            type: string
        "400":
          description: 请求参数错误
          schema:
            type: string
      summary: 用户注册
      tags:
      - 用户
  /auth/user-list:
    get:
      consumes:
      - application/json
      description: 分页获取用户列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: keyword
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取用户列表
      tags:
      - 用户管理
  /auth/user-update:
    put:
      consumes:
      - application/json
      description: 更新用户的基本信息
      parameters:
      - description: 用户信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 更新用户信息
      tags:
      - 用户管理
  /auth/user/{id}:
    delete:
      consumes:
      - application/json
      description: 将用户标记为已删除状态
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 软删除用户
      tags:
      - 用户管理
  /auth/userInfo:
    get:
      consumes:
      - application/json
      description: 获取用户信息
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户信息成功
          schema:
            type: string
        "401":
          description: 未授权
          schema:
            type: string
        "500":
          description: 获取用户信息失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 获取用户信息
      tags:
      - 用户
  /azs/cmdb/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的可用区
      parameters:
      - description: 可用区ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 删除可用区
      tags:
      - 位置管理-可用区
    get:
      consumes:
      - application/json
      description: 根据ID获取可用区详情
      parameters:
      - description: 可用区ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/location.AZ'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取可用区详情
      tags:
      - 位置管理-可用区
  /cmdb/asset-status/change-asset-status:
    post:
      consumes:
      - application/json
      description: 手动变更资产状态
      parameters:
      - description: 变更请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/asset.ChangeAssetStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 变更资产状态
      tags:
      - 资产管理-状态变更
  /cmdb/asset-status/change-biz-status:
    post:
      consumes:
      - application/json
      description: 手动变更资源业务状态
      parameters:
      - description: 变更请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/asset.ChangeBizStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 变更业务状态
      tags:
      - 资产管理-状态变更
  /cmdb/asset-status/history:
    get:
      consumes:
      - application/json
      description: 获取指定资产的状态变更历史记录
      parameters:
      - description: 资产ID
        in: query
        name: assetID
        required: true
        type: integer
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取资产状态变更历史
      tags:
      - 资产管理-状态变更
  /cmdb/asset-status/process-delivery:
    post:
      consumes:
      - application/json
      description: 处理资产交付流程
      parameters:
      - description: 交付请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/asset.ProcessAssetDeliveryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 处理资产交付
      tags:
      - 资产管理-状态变更
  /cmdb/asset-status/process-maintenance:
    post:
      consumes:
      - application/json
      description: 处理资产维修流程
      parameters:
      - description: 维修请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/asset.ProcessAssetMaintenanceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 处理资产维修
      tags:
      - 资产管理-状态变更
  /cmdb/asset-status/process-outbound:
    post:
      consumes:
      - application/json
      description: 处理资产出库流程
      parameters:
      - description: 出库请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/asset.ProcessAssetOutboundRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 处理资产出库
      tags:
      - 资产管理-状态变更
  /cmdb/asset-status/process-racking:
    post:
      consumes:
      - application/json
      description: 处理资产上架流程
      parameters:
      - description: 上架请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/asset.ProcessAssetRackingRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 处理资产上架
      tags:
      - 资产管理-状态变更
  /cmdb/asset-status/process-scrap:
    post:
      consumes:
      - application/json
      description: 处理资产报废流程
      parameters:
      - description: 报废请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/asset.ProcessAssetScrapRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 处理资产报废
      tags:
      - 资产管理-状态变更
  /cmdb/asset-status/process-storage:
    post:
      consumes:
      - application/json
      description: 处理资产入库流程
      parameters:
      - description: 入库请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/asset.ProcessAssetStorageRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 处理资产入库
      tags:
      - 资产管理-状态变更
  /cmdb/azs:
    get:
      consumes:
      - application/json
      description: 分页获取可用区列表
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取可用区列表
      tags:
      - 位置管理-可用区
    post:
      consumes:
      - application/json
      description: 创建新的可用区
      parameters:
      - description: 可用区信息
        in: body
        name: az
        required: true
        schema:
          $ref: '#/definitions/location.AZ'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 创建可用区
      tags:
      - 位置管理-可用区
  /cmdb/azs/{id}:
    put:
      consumes:
      - application/json
      description: 更新可用区信息
      parameters:
      - description: 可用区ID
        in: path
        name: id
        required: true
        type: integer
      - description: 可用区信息
        in: body
        name: az
        required: true
        schema:
          $ref: '#/definitions/location.AZ'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 更新可用区
      tags:
      - 位置管理-可用区
  /cmdb/azs/{id}/datacenters:
    get:
      consumes:
      - application/json
      description: 根据可用区ID获取机房列表
      parameters:
      - description: 可用区ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/location.DataCenter'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取可用区下的机房列表
      tags:
      - 位置管理-机房
  /cmdb/cabinets:
    get:
      consumes:
      - application/json
      description: 分页获取机柜列表
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 房间ID，按房间筛选
        in: query
        name: roomID
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取机柜列表
      tags:
      - 位置管理-机柜
    post:
      consumes:
      - application/json
      description: 创建新的机柜
      parameters:
      - description: 机柜信息
        in: body
        name: cabinet
        required: true
        schema:
          $ref: '#/definitions/location.Cabinet'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 创建机柜
      tags:
      - 位置管理-机柜
  /cmdb/cabinets/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的机柜
      parameters:
      - description: 机柜ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 删除机柜
      tags:
      - 位置管理-机柜
    get:
      consumes:
      - application/json
      description: 根据ID获取机柜详情
      parameters:
      - description: 机柜ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/location.Cabinet'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取机柜详情
      tags:
      - 位置管理-机柜
    put:
      consumes:
      - application/json
      description: 更新机柜信息
      parameters:
      - description: 机柜ID
        in: path
        name: id
        required: true
        type: integer
      - description: 机柜信息
        in: body
        name: cabinet
        required: true
        schema:
          $ref: '#/definitions/location.Cabinet'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 更新机柜
      tags:
      - 位置管理-机柜
  /cmdb/datacenters:
    get:
      consumes:
      - application/json
      description: 分页获取机房列表
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取机房列表
      tags:
      - 位置管理-机房
    post:
      consumes:
      - application/json
      description: 创建新的机房
      parameters:
      - description: 机房信息
        in: body
        name: dataCenter
        required: true
        schema:
          $ref: '#/definitions/location.DataCenter'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 创建机房
      tags:
      - 位置管理-机房
  /cmdb/datacenters/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的机房
      parameters:
      - description: 机房ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 删除机房
      tags:
      - 位置管理-机房
    get:
      consumes:
      - application/json
      description: 根据ID获取机房详情
      parameters:
      - description: 机房ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/location.DataCenter'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取机房详情
      tags:
      - 位置管理-机房
    put:
      consumes:
      - application/json
      description: 更新机房信息
      parameters:
      - description: 机房ID
        in: path
        name: id
        required: true
        type: integer
      - description: 机房信息
        in: body
        name: dataCenter
        required: true
        schema:
          $ref: '#/definitions/location.DataCenter'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 更新机房
      tags:
      - 位置管理-机房
  /cmdb/datacenters/{id}/rooms:
    get:
      consumes:
      - application/json
      description: 根据机房ID获取房间列表
      parameters:
      - description: 机房ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/location.Room'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取机房下的房间列表
      tags:
      - 位置管理-房间
  /cmdb/device-resources:
    get:
      consumes:
      - application/json
      description: 获取服务器资源列表，支持基于设备和资源属性的精确查询
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页数量
        in: query
        name: pageSize
        required: true
        type: integer
      - description: 搜索关键词(精确匹配SN/主机名)
        in: query
        name: query
        type: string
      - description: 品牌(精确匹配)
        in: query
        name: brand
        type: string
      - description: 型号(精确匹配)
        in: query
        name: model
        type: string
      - description: 资产状态
        in: query
        name: assetStatus
        type: string
      - description: 资产类型
        in: query
        name: assetType
        type: string
      - description: 业务状态
        in: query
        name: bizStatus
        type: string
      - description: 项目名称(精确匹配)
        in: query
        name: project
        type: string
      - description: VPC IP地址(精确匹配)
        in: query
        name: vpcIP
        type: string
      - description: BMC IP地址(精确匹配)
        in: query
        name: bmcIP
        type: string
      - description: 租户IP地址(精确匹配)
        in: query
        name: tenantIP
        type: string
      - description: 是否备机
        in: query
        name: isBackup
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/response.PageResult'
                  - properties:
                      list:
                        items:
                          $ref: '#/definitions/asset.Device'
                        type: array
                    type: object
              type: object
      summary: 获取服务器资源列表
      tags:
      - CMDB-服务器资源
    post:
      consumes:
      - application/json
      description: 创建新的服务器资源记录
      parameters:
      - description: 服务器资源数据
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/asset.Device'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建服务器资源
      tags:
      - CMDB-服务器资源
  /cmdb/device-resources/{id}:
    delete:
      consumes:
      - application/json
      description: 删除服务器资源记录
      parameters:
      - description: 服务器ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 删除服务器资源
      tags:
      - CMDB-服务器资源
    get:
      consumes:
      - application/json
      description: 根据ID获取服务器资源详情
      parameters:
      - description: 服务器ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取服务器资源详情
      tags:
      - CMDB-服务器资源
    put:
      consumes:
      - application/json
      description: 更新现有的服务器资源记录
      parameters:
      - description: 服务器ID
        in: path
        name: id
        required: true
        type: integer
      - description: 服务器资源数据
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/asset.Device'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新服务器资源
      tags:
      - CMDB-服务器资源
  /cmdb/devices:
    get:
      consumes:
      - application/json
      description: 分页获取资产设备列表
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 资产类型(server:服务器,network:网络设备,storage:存储设备)
        in: query
        name: assetType
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取资产设备列表
      tags:
      - 资产管理-设备
    post:
      consumes:
      - application/json
      description: 创建新的资产设备
      parameters:
      - description: 资产设备信息
        in: body
        name: device
        required: true
        schema:
          $ref: '#/definitions/asset.Device'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 创建资产设备
      tags:
      - 资产管理-设备
  /cmdb/devices/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的资产设备
      parameters:
      - description: 资产设备ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 删除资产设备
      tags:
      - 资产管理-设备
    get:
      consumes:
      - application/json
      description: 根据ID获取资产设备详情
      parameters:
      - description: 资产设备ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/asset.Device'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取资产设备详情
      tags:
      - 资产管理-设备
    put:
      consumes:
      - application/json
      description: 更新资产设备信息
      parameters:
      - description: 资产设备ID
        in: path
        name: id
        required: true
        type: integer
      - description: 资产设备信息
        in: body
        name: device
        required: true
        schema:
          $ref: '#/definitions/asset.Device'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 更新资产设备
      tags:
      - 资产管理-设备
  /cmdb/devices/{id}/machine-template:
    get:
      consumes:
      - application/json
      description: 根据设备ID获取关联的套餐模板信息
      parameters:
      - description: 设备ID
        in: path
        name: id
        required: true
        type: integer
      - description: 是否包含组件信息
        in: query
        name: with_components
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取设备关联的套餐模板
      tags:
      - CMDB-服务器资源
  /cmdb/devices/by-sn:
    get:
      consumes:
      - application/json
      description: 根据SN获取资产设备详情
      parameters:
      - description: 设备SN
        in: query
        name: sn
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/asset.Device'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 根据SN获取资产设备
      tags:
      - 资产管理-设备
  /cmdb/dict/brands:
    get:
      consumes:
      - application/json
      description: 获取所有品牌
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
      summary: 获取所有品牌
      tags:
      - 产品管理
  /cmdb/dict/material-types:
    get:
      consumes:
      - application/json
      description: 获取所有物料类型
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
      summary: 获取所有物料类型
      tags:
      - 产品管理
  /cmdb/dict/product-categories:
    get:
      consumes:
      - application/json
      description: 获取所有产品类别
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
      summary: 获取所有产品类别
      tags:
      - 产品管理
  /cmdb/dict/specs-by-material-type:
    get:
      consumes:
      - application/json
      description: 获取物料类型对应的规格列表
      parameters:
      - description: 物料类型
        in: query
        name: material_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 获取物料类型对应的规格列表
      tags:
      - 产品管理
  /cmdb/gpuServer:
    get:
      description: 根据各字段的查询条件过滤 GPU 服务器记录，同时支持分页查询；返回的数据包含记录列表（items）和总记录数（total），未传查询条件时返回所有记录
      parameters:
      - description: 项目
        in: query
        name: project
        type: string
      - description: 机房
        in: query
        name: data_center
        type: string
      - description: 包间
        in: query
        name: package
        type: string
      - description: 机柜
        in: query
        name: cabinet
        type: string
      - description: 主机名
        in: query
        name: hostname
        type: string
      - description: SN
        in: query
        name: sn
        type: string
      - description: 主责任人
        in: query
        name: primary_owner
        type: string
      - description: 备份责任人
        in: query
        name: backup_owner
        type: string
      - description: VPC_IP
        in: query
        name: vpc_ip
        type: string
      - description: VPC掩码
        in: query
        name: vpc_mask
        type: string
      - description: BMC_IP
        in: query
        name: bmc_ip
        type: string
      - description: BMC掩码
        in: query
        name: bmc_mask
        type: string
      - description: BMC网关
        in: query
        name: bmc_gateway
        type: string
      - description: CPU类型
        in: query
        name: cpu_type
        type: string
      - description: VPC_eth0_MAC
        in: query
        name: vpc_eth0_mac
        type: string
      - description: BMC_MAC
        in: query
        name: bmc_mac
        type: string
      - description: 设备类型
        in: query
        name: device_type
        type: string
      - description: 高度
        in: query
        name: height
        type: integer
      - description: GPU卡型号
        in: query
        name: gpu_model
        type: string
      - description: 集群
        in: query
        name: cluster
        type: string
      - description: 厂商
        in: query
        name: manufacturer
        type: string
      - description: 型号
        in: query
        name: model
        type: string
      - description: 业务状态
        in: query
        name: business_status
        type: string
      - description: 资产状态
        in: query
        name: asset_status
        type: string
      - description: 是否备机
        in: query
        name: is_backup
        type: boolean
      - description: 当前页码
        in: query
        name: page
        type: integer
      - description: 每页记录数
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: '查询成功，返回数据格式为 {items: [...], total: 数值}'
          schema:
            additionalProperties: true
            type: object
      security:
      - Bearer: []
      summary: 查询 GPU 服务器记录
      tags:
      - GPU服务器
    post:
      consumes:
      - application/json
      description: 创建一条新的 GPU 服务器记录
      parameters:
      - description: GPU服务器记录
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/model.GpuServer'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            $ref: '#/definitions/model.GpuServer'
        "400":
          description: 请求参数错误
          schema:
            type: string
      summary: 创建 GPU 服务器记录
      tags:
      - GPU服务器
  /cmdb/gpuServer/{id}:
    delete:
      description: 根据 ID 删除 GPU 服务器记录
      parameters:
      - description: GPU服务器记录ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            type: string
        "404":
          description: 记录未找到
          schema:
            type: string
      security:
      - Bearer: []
      summary: 删除 GPU 服务器记录
      tags:
      - GPU服务器
    get:
      description: 根据 ID 获取 GPU 服务器记录
      parameters:
      - description: GPU服务器记录ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            $ref: '#/definitions/model.GpuServer'
        "404":
          description: 记录未找到
          schema:
            type: string
      security:
      - Bearer: []
      summary: 获取 GPU 服务器记录
      tags:
      - GPU服务器
    put:
      consumes:
      - application/json
      description: 前端传递完整的记录数据进行更新
      parameters:
      - description: GPU服务器记录ID
        in: path
        name: id
        required: true
        type: integer
      - description: 完整的GPU服务器记录
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/model.GpuServer'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/model.GpuServer'
        "400":
          description: 请求参数错误或记录不存在
          schema:
            type: string
      security:
      - Bearer: []
      summary: 更新 GPU 服务器记录（全部更新）
      tags:
      - GPU服务器
  /cmdb/inbound:
    get:
      consumes:
      - application/json
      description: 获取入库单列表，支持分页和条件查询
      parameters:
      - description: 入库单号
        in: query
        name: inboundNo
        type: string
      - description: 入库类型
        in: query
        name: inboundType
        type: string
      - description: 创建人ID
        in: query
        name: createID
        type: integer
      - description: 状态
        in: query
        name: stage
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页数量
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/inbound.InboundList'
                      type: array
                    total:
                      type: integer
                  type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 获取入库单列表
      tags:
      - 入库管理
  /cmdb/inbound/{id}:
    delete:
      consumes:
      - application/json
      description: 根据ID删除指定的入库单
      parameters:
      - description: 入库单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除入库单成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 无效的ID
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 删除失败
          schema:
            $ref: '#/definitions/response.Response'
      summary: 删除入库单
      tags:
      - 入库管理
    get:
      consumes:
      - application/json
      description: 通过指定的 ID 获取新购入库的详细信息
      parameters:
      - description: 新购入库的 ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 无效的 ID
          schema:
            type: string
      summary: 根据 ID 获取新购入库信息
      tags:
      - 新购入库
  /cmdb/inbound/{inboundNo}:
    get:
      consumes:
      - application/json
      description: 根据工单号获取入库信息，支持新购入库、返修入库、拆机入库等类型
      parameters:
      - description: 工单号
        in: path
        name: inboundNo
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/inbound.InboudWholeInfo'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "404":
          description: 数据不存在
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 获取入库信息
      tags:
      - 入库管理
  /cmdb/inbound/Part/{id}:
    put:
      consumes:
      - application/json
      description: 根据ID更新入库单信息
      parameters:
      - description: 入库单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 入库单更新数据
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/inbound.PartInbound'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 更新失败
          schema:
            $ref: '#/definitions/response.Response'
      summary: 更新入库单
      tags:
      - 入库管理
  /cmdb/inbound/dismantled-input:
    post:
      consumes:
      - application/json
      description: 通过输入的方式创建拆机入库单及相关信息
      parameters:
      - description: 拆机入库单创建参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/inbound.DismantledInboundDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 创建拆机入库单
      tags:
      - 入库管理
  /cmdb/inbound/new:
    post:
      consumes:
      - application/json
      description: 创建一个新的入库单，包含采购合同ID、供应商ID和数量信息
      parameters:
      - description: 新入库单请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/inbound.NewInbound'
      produces:
      - application/json
      responses:
        "200":
          description: 创建新入库单成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 创建新入库单
      tags:
      - 入库管理
  /cmdb/inbound/new-import:
    post:
      consumes:
      - multipart/form-data
      description: |-
        上传Excel文件创建新入库单，文件格式要求：
        1. 必须包含以下列：project(项目)、inbound_title(入库标题)、purchase_order_no(采购合同编号)、supplier_name(供应商名称)
        2. 每行数据必须包含：product_id(产品ID)、amount(数量)
        3. 文件必须是.xlsx格式
      parameters:
      - description: Excel文件(.xlsx)
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 导入成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
              type: object
        "400":
          description: 请求参数错误，包括：文件格式错误、必要列缺失等
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "500":
          description: 服务器内部错误，包括：文件处理错误、数据验证失败等
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 通过Excel文件创建新入库单
      tags:
      - 入库管理
  /cmdb/inbound/new-input:
    post:
      consumes:
      - application/json
      description: 通过输入的形式创建新入库单，包含入库信息单和入库详情
      parameters:
      - description: 入库单创建请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/inbound.inboundDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/inbound.CreateNewInboundResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 创建新入库单及详情
      tags:
      - 入库管理
  /cmdb/inbound/new/{id}/upload:
    post:
      consumes:
      - multipart/form-data
      description: 上传产品和备件的CSV文件，解析并导入到系统中
      parameters:
      - description: 产品CSV文件
        in: formData
        name: product
        required: true
        type: file
      - description: 备件CSV文件
        in: formData
        name: spare
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 导入成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 上传CSV文件并导入数据
      tags:
      - 入库管理
  /cmdb/inbound/new/{inboundNo}:
    get:
      consumes:
      - application/json
      description: 根据入库单号获取新购入库单的详细信息
      parameters:
      - description: 入库单号
        in: path
        name: inboundNo
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/inbound.NewInbound'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 获取新购入库单信息
      tags:
      - 入库管理
  /cmdb/inbound/new/submittersList:
    get:
      consumes:
      - application/json
      description: 获取所有新维修入库单的提交人列表
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties:
                    type: integer
                  type: object
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 获取提交人列表
      tags:
      - 入库管理
  /cmdb/inbound/new/update-details-input:
    put:
      consumes:
      - application/json
      description: 通过输入的方式更新入库单的详细信息
      parameters:
      - description: 入库详情更新参数
        in: body
        name: request
        required: true
        schema:
          items:
            $ref: '#/definitions/inbound.NewInboundDetail'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 更新入库详情
      tags:
      - 入库管理
  /cmdb/inbound/part:
    post:
      consumes:
      - application/json
      description: 根据请求参数创建一个新的入库工单
      parameters:
      - description: 入库请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/inbound.PartInbound'
      produces:
      - application/json
      responses:
        "200":
          description: 创建入库工单成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 创建入库工单
      tags:
      - 入库管理
  /cmdb/inbound/repair-import:
    put:
      consumes:
      - multipart/form-data
      description: 通过Excel文件导入返修入库单及相关信息
      parameters:
      - description: Excel文件
        in: formData
        name: file
        required: true
        type: file
      - description: 入库标题
        in: formData
        name: inbound_title
        required: true
        type: string
      - description: 项目
        in: formData
        name: project
        required: true
        type: string
      - description: 是否需要返厂
        in: formData
        name: need_return
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: 导入成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/inbound.RepairInboundResult'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 通过Excel文件导入返修入库单
      tags:
      - 入库管理
  /cmdb/inbound/repair-input:
    post:
      consumes:
      - application/json
      description: 通过输入的方式创建返修入库单、返修入库工单和入库列表
      parameters:
      - description: 返修入库单创建参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/inbound.RepairInboundDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/inbound.RepairInboundResult'
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 创建返修入库单
      tags:
      - 入库管理
  /cmdb/inbound/trans/dismantled-import:
    post:
      consumes:
      - multipart/form-data
      description: 通过Excel文件导入拆机入库单及相关信息的V1版本
      parameters:
      - description: Excel文件
        in: formData
        name: file
        required: true
        type: file
      - description: 入库标题
        in: formData
        name: inbound_title
        required: true
        type: string
      - description: 项目
        in: formData
        name: project
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 导入成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 通过Excel文件导入拆机入库单
      tags:
      - 入库管理
  /cmdb/inbound/trans/dismantled-input:
    post:
      consumes:
      - application/json
      description: 通过输入的方式创建拆机入库单及相关信息的V1版本
      parameters:
      - description: 拆机入库单创建参数V1
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/inbound.DismantledInboundDTOV1'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                msg:
                  type: string
              type: object
      summary: 创建拆机入库单(V1)
      tags:
      - 入库管理
  /cmdb/inventory/product/{productID}/warehouse/{warehouseID}:
    get:
      consumes:
      - application/json
      description: 根据产品ID和仓库ID查询库存明细
      parameters:
      - description: 产品ID
        in: path
        name: productID
        required: true
        type: integer
      - description: 仓库ID
        in: path
        name: warehouseID
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/inventory.InventoryDetail'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.Response'
      summary: 根据产品ID和仓库ID查询库存
      tags:
      - 库存管理
  /cmdb/machine-templates:
    get:
      consumes:
      - application/json
      description: 分页查询套餐模板列表
      parameters:
      - description: 页码, 默认1
        in: query
        name: page
        type: integer
      - description: 每页数量, 默认10
        in: query
        name: pageSize
        type: integer
      - description: 查询关键字
        in: query
        name: query
        type: string
      - description: 模板类别
        in: query
        name: category
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 查询套餐模板列表
      tags:
      - CMDB-套餐模板
    post:
      consumes:
      - application/json
      description: 创建新的套餐模板
      parameters:
      - description: 套餐模板信息
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/template.MachineTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建套餐模板
      tags:
      - CMDB-套餐模板
  /cmdb/machine-templates/{id}:
    delete:
      consumes:
      - application/json
      description: 根据ID删除套餐模板
      parameters:
      - description: 模板ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 删除套餐模板
      tags:
      - CMDB-套餐模板
    get:
      consumes:
      - application/json
      description: 根据ID获取套餐模板详情
      parameters:
      - description: 模板ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取套餐模板详情
      tags:
      - CMDB-套餐模板
    put:
      consumes:
      - application/json
      description: 更新套餐模板信息
      parameters:
      - description: 模板ID
        in: path
        name: id
        required: true
        type: integer
      - description: 套餐模板信息
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/template.MachineTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新套餐模板
      tags:
      - CMDB-套餐模板
  /cmdb/machine-templates/{id}/components:
    get:
      consumes:
      - application/json
      description: 根据模板ID获取组件列表
      parameters:
      - description: 模板ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取模板组件列表
      tags:
      - CMDB-模板组件
    post:
      consumes:
      - application/json
      description: 创建模板组件
      parameters:
      - description: 模板ID
        in: path
        name: id
        required: true
        type: integer
      - description: 组件信息
        in: body
        name: component
        required: true
        schema:
          $ref: '#/definitions/template.TemplateComponent'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建模板组件
      tags:
      - CMDB-模板组件
  /cmdb/network-devices:
    get:
      consumes:
      - application/json
      description: 获取网络设备列表
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认20
        in: query
        name: pageSize
        type: integer
      - description: 查询关键字
        in: query
        name: query
        type: string
      - description: 网络设备角色
        in: query
        name: role
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/asset.NetworkDeviceWithDeviceInfo'
                  type: array
              type: object
      summary: 获取网络设备列表
      tags:
      - 网络设备
    post:
      consumes:
      - application/json
      description: 创建网络设备
      parameters:
      - description: 网络设备信息
        in: body
        name: networkDevice
        required: true
        schema:
          $ref: '#/definitions/asset.NetworkDevice'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建网络设备
      tags:
      - 网络设备
  /cmdb/network-devices/{id}:
    delete:
      consumes:
      - application/json
      description: 删除网络设备
      parameters:
      - description: 网络设备ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 删除网络设备
      tags:
      - 网络设备
    get:
      consumes:
      - application/json
      description: 根据ID获取网络设备
      parameters:
      - description: 网络设备ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/asset.NetworkDevice'
              type: object
      summary: 根据ID获取网络设备
      tags:
      - 网络设备
    put:
      consumes:
      - application/json
      description: 更新网络设备
      parameters:
      - description: 网络设备ID
        in: path
        name: id
        required: true
        type: integer
      - description: 网络设备信息
        in: body
        name: networkDevice
        required: true
        schema:
          $ref: '#/definitions/asset.NetworkDevice'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新网络设备
      tags:
      - 网络设备
  /cmdb/network-devices/detail/{id}:
    get:
      consumes:
      - application/json
      description: 获取网络设备详情（包含设备和资源信息）
      parameters:
      - description: 网络设备ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/asset.NetworkDeviceWithDeviceInfo'
              type: object
      summary: 获取网络设备详情（包含设备和资源信息）
      tags:
      - 网络设备
  /cmdb/network-devices/device/{deviceID}:
    get:
      consumes:
      - application/json
      description: 根据设备ID获取网络设备
      parameters:
      - description: 设备ID
        in: path
        name: deviceID
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/asset.NetworkDevice'
              type: object
      summary: 根据设备ID获取网络设备
      tags:
      - 网络设备
  /cmdb/network-devices/with-device:
    post:
      consumes:
      - application/json
      description: 一次请求同时创建设备和网络设备信息
      parameters:
      - description: 网络设备信息
        in: body
        name: data
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 一站式创建网络设备
      tags:
      - 网络设备
  /cmdb/network-devices/with-device/{id}:
    put:
      consumes:
      - application/json
      description: 一次请求同时更新设备和网络设备信息
      parameters:
      - description: 网络设备ID
        in: path
        name: id
        required: true
        type: integer
      - description: 网络设备信息
        in: body
        name: data
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 一站式更新网络设备
      tags:
      - 网络设备
  /cmdb/products:
    get:
      consumes:
      - application/json
      description: 获取产品列表，支持分页和筛选
      parameters:
      - description: PN号码
        in: query
        name: pn
        type: string
      - description: 物料类型
        in: query
        name: material_type
        type: string
      - description: 品牌
        in: query
        name: brand
        type: string
      - description: 型号
        in: query
        name: model
        type: string
      - description: 规格
        in: query
        name: spec
        type: string
      - description: 产品类别
        in: query
        name: product_category
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页数量
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/product.ProductListResult'
              type: object
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 获取产品列表
      tags:
      - 产品管理
    post:
      consumes:
      - application/json
      description: 创建新产品
      parameters:
      - description: 产品信息
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/product.CreateProductDTO'
      produces:
      - application/json
      responses:
        "201":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/product.Product'
              type: object
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 创建产品
      tags:
      - 产品管理
  /cmdb/products/{id}:
    delete:
      consumes:
      - application/json
      description: 删除产品
      parameters:
      - description: 产品ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: 产品不存在
          schema:
            $ref: '#/definitions/response.Response'
      summary: 删除产品
      tags:
      - 产品管理
    get:
      consumes:
      - application/json
      description: 根据ID获取产品详情
      parameters:
      - description: 产品ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/product.Product'
              type: object
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: 产品不存在
          schema:
            $ref: '#/definitions/response.Response'
      summary: 获取产品详情
      tags:
      - 产品管理
    put:
      consumes:
      - application/json
      description: 更新产品信息
      parameters:
      - description: 产品ID
        in: path
        name: id
        required: true
        type: integer
      - description: 产品信息
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/product.UpdateProductDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/product.Product'
              type: object
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: 产品不存在
          schema:
            $ref: '#/definitions/response.Response'
      summary: 更新产品
      tags:
      - 产品管理
  /cmdb/regions:
    get:
      consumes:
      - application/json
      description: 分页获取区域列表
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取区域列表
      tags:
      - 位置管理-区域
    post:
      consumes:
      - application/json
      description: 创建新的区域
      parameters:
      - description: 区域信息
        in: body
        name: region
        required: true
        schema:
          $ref: '#/definitions/location.Region'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 创建区域
      tags:
      - 位置管理-区域
  /cmdb/regions/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的区域
      parameters:
      - description: 区域ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 删除区域
      tags:
      - 位置管理-区域
    get:
      consumes:
      - application/json
      description: 根据ID获取区域详情
      parameters:
      - description: 区域ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/location.Region'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取区域详情
      tags:
      - 位置管理-区域
    put:
      consumes:
      - application/json
      description: 更新区域信息
      parameters:
      - description: 区域ID
        in: path
        name: id
        required: true
        type: integer
      - description: 区域信息
        in: body
        name: region
        required: true
        schema:
          $ref: '#/definitions/location.Region'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 更新区域
      tags:
      - 位置管理-区域
  /cmdb/regions/{id}/azs:
    get:
      consumes:
      - application/json
      description: 根据区域ID获取可用区列表
      parameters:
      - description: 区域ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/location.AZ'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取区域下的可用区列表
      tags:
      - 位置管理-可用区
  /cmdb/resources:
    get:
      consumes:
      - application/json
      description: 分页获取资源列表
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 业务状态(online:在线,offline:离线)
        in: query
        name: bizStatus
        type: string
      - description: 资源状态(allocated:已分配,unallocated:未分配)
        in: query
        name: resStatus
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取资源列表
      tags:
      - 资产管理-资源
    post:
      consumes:
      - application/json
      description: 创建新的资源
      parameters:
      - description: 资源信息
        in: body
        name: resource
        required: true
        schema:
          $ref: '#/definitions/asset.Resource'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 创建资源
      tags:
      - 资产管理-资源
  /cmdb/resources/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的资源
      parameters:
      - description: 资源ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 删除资源
      tags:
      - 资产管理-资源
    get:
      consumes:
      - application/json
      description: 根据ID获取资源详情
      parameters:
      - description: 资源ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/asset.Resource'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取资源详情
      tags:
      - 资产管理-资源
    put:
      consumes:
      - application/json
      description: 更新资源信息
      parameters:
      - description: 资源ID
        in: path
        name: id
        required: true
        type: integer
      - description: 资源信息
        in: body
        name: resource
        required: true
        schema:
          $ref: '#/definitions/asset.Resource'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 更新资源
      tags:
      - 资产管理-资源
  /cmdb/resources/available-backups:
    get:
      consumes:
      - application/json
      description: 根据项目、集群、硬件状态和业务状态获取符合条件的备机列表
      parameters:
      - description: 项目名称
        in: query
        name: project
        required: true
        type: string
      - description: 集群名称
        in: query
        name: cluster
        type: string
      - description: 硬件状态，默认为normal
        in: query
        name: hardwareStatus
        type: string
      - description: 业务状态，默认为maintaining
        in: query
        name: bizStatus
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/asset.Resource'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取可用备机列表
      tags:
      - 资产管理-资源
  /cmdb/resources/by-asset:
    get:
      consumes:
      - application/json
      description: 根据资产ID获取资源详情
      parameters:
      - description: 资产ID
        in: query
        name: assetID
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/asset.Resource'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 根据资产ID获取资源
      tags:
      - 资产管理-资源
  /cmdb/resources/by-sn:
    get:
      consumes:
      - application/json
      description: 根据设备SN获取资源详情
      parameters:
      - description: 设备SN
        in: query
        name: sn
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/asset.Resource'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 根据SN获取资源
      tags:
      - 资产管理-资源
  /cmdb/resources/clusters:
    get:
      consumes:
      - application/json
      description: 获取系统中所有唯一的集群名称，供前端下拉框使用
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取所有集群列表
      tags:
      - 资产管理-资源
  /cmdb/resources/projects:
    get:
      consumes:
      - application/json
      description: 获取系统中所有唯一的项目名称，供前端下拉框使用
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取所有项目列表
      tags:
      - 资产管理-资源
  /cmdb/rooms:
    get:
      consumes:
      - application/json
      description: 分页获取房间列表
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 机房ID，按机房筛选
        in: query
        name: dataCenterId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                ' total':
                  type: integer
                data:
                  items:
                    $ref: '#/definitions/location.Room'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取房间列表
      tags:
      - 位置管理-房间
    post:
      consumes:
      - application/json
      description: 创建新的房间
      parameters:
      - description: 房间信息
        in: body
        name: room
        required: true
        schema:
          $ref: '#/definitions/location.Room'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建房间
      tags:
      - 位置管理-房间
  /cmdb/rooms/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的房间
      parameters:
      - description: 房间ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 删除房间
      tags:
      - 位置管理-房间
    get:
      consumes:
      - application/json
      description: 根据ID获取房间详情
      parameters:
      - description: 房间ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/location.Room'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取房间详情
      tags:
      - 位置管理-房间
    put:
      consumes:
      - application/json
      description: 更新房间信息
      parameters:
      - description: 房间ID
        in: path
        name: id
        required: true
        type: integer
      - description: 房间信息
        in: body
        name: room
        required: true
        schema:
          $ref: '#/definitions/location.Room'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新房间
      tags:
      - 位置管理-房间
  /cmdb/rooms/{id}/cabinets:
    get:
      consumes:
      - application/json
      description: 根据房间ID获取机柜列表
      parameters:
      - description: 房间ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/location.Cabinet'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取房间下的机柜列表
      tags:
      - 位置管理-机柜
  /cmdb/server-components:
    get:
      consumes:
      - application/json
      description: 分页获取服务器组件列表
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 组件类型(CPU,GPU,Memory,Disk等)
        in: query
        name: componentType
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取服务器组件列表
      tags:
      - 资产管理-服务器组件
    post:
      consumes:
      - application/json
      description: 创建新的服务器组件
      parameters:
      - description: 服务器组件信息
        in: body
        name: component
        required: true
        schema:
          $ref: '#/definitions/component.ServerComponent'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 创建服务器组件
      tags:
      - 资产管理-服务器组件
  /cmdb/server-components/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的服务器组件
      parameters:
      - description: 服务器组件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 删除服务器组件
      tags:
      - 资产管理-服务器组件
    get:
      consumes:
      - application/json
      description: 根据ID获取服务器组件详情
      parameters:
      - description: 服务器组件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/component.ServerComponent'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取服务器组件详情
      tags:
      - 资产管理-服务器组件
    put:
      consumes:
      - application/json
      description: 更新服务器组件信息
      parameters:
      - description: 服务器组件ID
        in: path
        name: id
        required: true
        type: integer
      - description: 服务器组件信息
        in: body
        name: component
        required: true
        schema:
          $ref: '#/definitions/component.ServerComponent'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 更新服务器组件
      tags:
      - 资产管理-服务器组件
  /cmdb/server-components/{id}/details:
    get:
      consumes:
      - application/json
      description: 获取指定ID的组件详细信息（包含关联数据）
      parameters:
      - description: 组件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取组件详情
      tags:
      - 资产管理-服务器组件
  /cmdb/server-components/{id}/spares:
    get:
      consumes:
      - application/json
      description: 获取指定组件ID关联的备件列表
      parameters:
      - description: 组件ID
        in: path
        name: id
        required: true
        type: integer
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取组件相关的备件
      tags:
      - 资产管理-服务器组件
  /cmdb/server-components/by-server:
    get:
      consumes:
      - application/json
      description: 根据服务器ID获取该服务器的所有组件
      parameters:
      - description: 服务器ID
        in: query
        name: serverID
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/component.ServerComponent'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 根据服务器ID获取组件列表
      tags:
      - 资产管理-服务器组件
  /cmdb/server-components/by-sn:
    get:
      consumes:
      - application/json
      description: 根据SN获取服务器组件详情
      parameters:
      - description: 组件SN
        in: query
        name: sn
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/component.ServerComponent'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 根据SN获取服务器组件
      tags:
      - 资产管理-服务器组件
  /cmdb/server-components/details:
    get:
      consumes:
      - application/json
      description: 获取组件列表，包含关联信息，支持分页和过滤
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 组件类型(CPU,GPU,Memory,Disk等)
        in: query
        name: componentType
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取组件列表（包含关联信息）
      tags:
      - 资产管理-服务器组件
  /cmdb/server-components/statistics:
    get:
      consumes:
      - application/json
      description: 获取组件的统计信息，包括按类型、状态和服务器的统计
      parameters:
      - description: 组件类型
        in: query
        name: component_type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/component.ComponentStatistics'
              type: object
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取组件统计信息
      tags:
      - 资产管理-服务器组件
  /cmdb/server-components/type-spares:
    get:
      consumes:
      - application/json
      description: 获取指定组件类型的可用备件列表
      parameters:
      - description: 组件类型
        in: query
        name: component_type
        required: true
        type: string
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 根据组件类型获取可用备件
      tags:
      - 资产管理-服务器组件
  /cmdb/spares:
    get:
      consumes:
      - application/json
      description: 分页获取备件列表，支持多种精确查询条件
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 备件SN(精确匹配)
        in: query
        name: sn
        type: string
      - description: 备件PN号码(精确匹配)
        in: query
        name: pn
        type: string
      - description: 产品ID(精确匹配)
        in: query
        name: product_id
        type: integer
      - description: 来源类型(精确匹配)
        in: query
        name: source_type
        type: string
      - description: 资产状态(精确匹配)
        in: query
        name: asset_status
        type: string
      - description: 硬件状态(精确匹配)
        in: query
        name: hardware_status
        type: string
      - description: 仓库ID(精确匹配)
        in: query
        name: warehouse_id
        type: integer
      - description: 备件类型(精确匹配)
        in: query
        name: type
        type: string
      - description: 固件版本(精确匹配)
        in: query
        name: firmware_version
        type: string
      - description: 批次号(精确匹配)
        in: query
        name: batch_number
        type: string
      - description: 存放位置(精确匹配)
        in: query
        name: location
        type: string
      - description: 最低价格
        in: query
        name: min_price
        type: number
      - description: 最高价格
        in: query
        name: max_price
        type: number
      - description: '购买日期开始(格式: 2023-01-01)'
        in: query
        name: purchase_date_start
        type: string
      - description: '购买日期结束(格式: 2023-12-31)'
        in: query
        name: purchase_date_end
        type: string
      - description: '保修期开始(格式: 2023-01-01)'
        in: query
        name: warranty_expire_start
        type: string
      - description: '保修期结束(格式: 2023-12-31)'
        in: query
        name: warranty_expire_end
        type: string
      - description: 品牌(精确匹配)
        in: query
        name: brand
        type: string
      - description: 规格(精确匹配)
        in: query
        name: spec
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取备件列表
      tags:
      - 资产管理-备件
    post:
      consumes:
      - application/json
      description: 创建新的备件记录
      parameters:
      - description: 备件信息
        in: body
        name: spare
        required: true
        schema:
          $ref: '#/definitions/asset.AssetSpare'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建备件
      tags:
      - 资产管理-备件
  /cmdb/spares/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的备件
      parameters:
      - description: 备件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 删除备件
      tags:
      - 资产管理-备件
    get:
      consumes:
      - application/json
      description: 根据ID获取备件详情
      parameters:
      - description: 备件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/asset.AssetSpare'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取备件详情
      tags:
      - 资产管理-备件
    put:
      consumes:
      - application/json
      description: 更新备件信息
      parameters:
      - description: 备件ID
        in: path
        name: id
        required: true
        type: integer
      - description: 备件信息
        in: body
        name: spare
        required: true
        schema:
          $ref: '#/definitions/asset.AssetSpare'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新备件
      tags:
      - 资产管理-备件
  /cmdb/spares/{id}/assign:
    post:
      consumes:
      - application/json
      description: 将备件分配到指定设备
      parameters:
      - description: 备件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 分配备件到设备
      tags:
      - 资产管理-备件
  /cmdb/spares/{id}/details:
    get:
      consumes:
      - application/json
      description: 获取指定ID的备件详细信息，包含产品、仓库、库存等关联信息
      parameters:
      - description: 备件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取备件详情（包含关联信息）
      tags:
      - 资产管理-备件
  /cmdb/spares/{id}/remove:
    post:
      consumes:
      - application/json
      description: 将备件从当前安装的设备上移除
      parameters:
      - description: 备件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 从设备移除备件
      tags:
      - 资产管理-备件
  /cmdb/spares/{id}/status:
    post:
      consumes:
      - application/json
      description: 更改备件的状态，例如变更为闲置、使用中、维修中等
      parameters:
      - description: 备件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更改备件状态
      tags:
      - 资产管理-备件
  /cmdb/spares/{id}/transfer:
    post:
      consumes:
      - application/json
      description: 将备件转移到指定仓库的指定位置
      parameters:
      - description: 备件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 转移备件到其他仓库
      tags:
      - 资产管理-备件
  /cmdb/spares/by-component-type:
    get:
      consumes:
      - application/json
      description: 获取指定组件类型的可用备件列表，包含详细信息
      parameters:
      - description: 组件类型
        in: query
        name: component_type
        required: true
        type: string
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 根据组件类型获取可用备件列表
      tags:
      - 资产管理-备件
  /cmdb/spares/by-product/{productID}:
    get:
      consumes:
      - application/json
      description: 获取指定产品的备件列表，支持分页
      parameters:
      - description: 产品ID
        in: path
        name: productID
        required: true
        type: integer
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 根据产品ID获取备件列表
      tags:
      - 资产管理-备件
  /cmdb/spares/by-warehouse/{warehouseID}:
    get:
      consumes:
      - application/json
      description: 获取指定仓库中的备件列表，支持分页
      parameters:
      - description: 仓库ID
        in: path
        name: warehouseID
        required: true
        type: integer
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 根据仓库ID获取备件列表
      tags:
      - 资产管理-备件
  /cmdb/spares/product/{productID}/details:
    get:
      consumes:
      - application/json
      description: 获取指定产品的详细备件列表，包含产品、仓库、库存等关联信息
      parameters:
      - description: 产品ID
        in: path
        name: productID
        required: true
        type: integer
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 根据产品ID获取详细备件列表
      tags:
      - 资产管理-备件
  /cmdb/spares/statistics:
    get:
      consumes:
      - application/json
      description: 获取备件的统计信息，包括按类型、状态和仓库的统计
      parameters:
      - description: 备件类型
        in: query
        name: type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取备件统计信息
      tags:
      - 资产管理-备件
  /cmdb/template-components/{id}:
    delete:
      consumes:
      - application/json
      description: 根据ID删除模板组件
      parameters:
      - description: 组件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 删除模板组件
      tags:
      - CMDB-模板组件
    put:
      consumes:
      - application/json
      description: 更新模板组件信息
      parameters:
      - description: 组件ID
        in: path
        name: id
        required: true
        type: integer
      - description: 组件信息
        in: body
        name: component
        required: true
        schema:
          $ref: '#/definitions/template.TemplateComponent'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新模板组件
      tags:
      - CMDB-模板组件
  /cmdb/warehouses:
    get:
      consumes:
      - application/json
      description: 分页获取仓库列表，支持按类型和关键词筛选
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 仓库类型
        in: query
        name: type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取仓库列表
      tags:
      - 资产管理-仓库
    post:
      consumes:
      - application/json
      description: 创建新的仓库
      parameters:
      - description: 仓库信息
        in: body
        name: warehouse
        required: true
        schema:
          $ref: '#/definitions/asset.Warehouse'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建仓库
      tags:
      - 资产管理-仓库
  /cmdb/warehouses/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的仓库
      parameters:
      - description: 仓库ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 删除仓库
      tags:
      - 资产管理-仓库
    get:
      consumes:
      - application/json
      description: 根据ID获取仓库详情
      parameters:
      - description: 仓库ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取仓库详情
      tags:
      - 资产管理-仓库
    put:
      consumes:
      - application/json
      description: 更新仓库信息
      parameters:
      - description: 仓库ID
        in: path
        name: id
        required: true
        type: integer
      - description: 仓库信息
        in: body
        name: warehouse
        required: true
        schema:
          $ref: '#/definitions/asset.Warehouse'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新仓库
      tags:
      - 资产管理-仓库
  /cmdb/warehouses/{id}/inventory:
    get:
      consumes:
      - application/json
      description: 获取指定仓库内的库存明细
      parameters:
      - description: 仓库ID
        in: path
        name: id
        required: true
        type: integer
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取仓库内库存
      tags:
      - 资产管理-仓库
  /cmdb/warehouses/{id}/spares:
    get:
      consumes:
      - application/json
      description: 获取指定仓库内的备件列表
      parameters:
      - description: 仓库ID
        in: path
        name: id
        required: true
        type: integer
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取仓库内备件
      tags:
      - 资产管理-仓库
  /cmdb/warehouses/{id}/stats:
    get:
      consumes:
      - application/json
      description: 获取指定仓库的统计信息，包括产品数量、总库存、可用库存等
      parameters:
      - description: 仓库ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取仓库统计信息
      tags:
      - 资产管理-仓库
  /cmdb/warehouses/code/{code}:
    get:
      consumes:
      - application/json
      description: 根据仓库编码获取仓库详情
      parameters:
      - description: 仓库编码
        in: path
        name: code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/asset.Warehouse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 根据编码获取仓库
      tags:
      - 资产管理-仓库
  /cmdb/warehouses/types:
    get:
      consumes:
      - application/json
      description: 获取系统中所有的仓库类型
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取所有仓库类型
      tags:
      - 资产管理-仓库
  /dashboard/resources/brand-distribution:
    get:
      consumes:
      - application/json
      description: 获取所有设备的品牌分布数量统计
      parameters:
      - description: 项目名称，为空时返回所有项目数据
        in: query
        name: project
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取设备品牌分布统计
      tags:
      - 看板-资源统计
  /dashboard/resources/cluster-stats:
    get:
      consumes:
      - application/json
      description: 获取各集群类型的数量统计
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取集群统计信息
      tags:
      - 看板-资源统计
  /dashboard/resources/cluster-stats-by-project:
    get:
      consumes:
      - application/json
      description: 按项目获取集群数量统计
      parameters:
      - description: 项目名称，为空时返回所有项目数据
        in: query
        name: project
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 按项目获取集群统计信息
      tags:
      - 看板-资源统计
  /dashboard/resources/gpu-model-distribution:
    get:
      consumes:
      - application/json
      description: 获取GPU卡型号分布数量统计
      parameters:
      - description: 项目名称，为空时返回所有项目数据
        in: query
        name: project
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取GPU卡型号分布统计
      tags:
      - 看板-资源统计
  /dashboard/resources/room-distribution:
    get:
      consumes:
      - application/json
      description: 获取服务器在各机房的分布数量统计
      parameters:
      - description: 项目名称，为空时返回所有项目数据
        in: query
        name: project
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取服务器机房分布统计
      tags:
      - 看板-资源统计
  /dashboard/resources/server-stats:
    get:
      consumes:
      - application/json
      description: 获取服务器总数量及各状态数量统计，包括各资产类型(服务器、GPU服务器、网络设备、存储设备等)的数量
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取服务器统计信息
      tags:
      - 看板-资源统计
  /dashboard/resources/server-stats-by-project:
    get:
      consumes:
      - application/json
      description: 按项目获取服务器数量统计，包括各项目中不同资产类型(服务器、GPU服务器、网络设备、存储设备等)的数量
      parameters:
      - description: 项目名称，为空时返回所有项目数据
        in: query
        name: project
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 按项目获取服务器统计信息
      tags:
      - 看板-资源统计
  /dashboard/sla/calculate:
    get:
      consumes:
      - application/json
      description: 根据不同算法计算SLA
      parameters:
      - description: 年份，默认当前年
        in: query
        name: year
        type: integer
      - description: 月份，默认当前月
        in: query
        name: month
        type: integer
      - description: 算法类型(100/90/95)，默认100
        in: query
        name: algorithm
        type: string
      - description: 项目名称，为空时计算所有项目
        in: query
        name: project
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 计算SLA
      tags:
      - 看板-SLA
  /dashboard/sla/daily:
    get:
      consumes:
      - application/json
      description: 计算指定月份内每天三种算法(100/90/95)的SLA数据，用于前端图表展示
      parameters:
      - description: 年份，默认当前年
        in: query
        name: year
        type: integer
      - description: 月份，默认当前月
        in: query
        name: month
        type: integer
      - description: 项目名称，为空时计算所有项目
        in: query
        name: project
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 计算每日SLA数据
      tags:
      - 看板-SLA
  /dashboard/ticket/brand-distribution:
    get:
      consumes:
      - application/json
      description: 获取故障设备的厂商分布情况
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取故障设备厂商分布统计
      tags:
      - 看板-工单统计
  /dashboard/ticket/business-impact:
    get:
      consumes:
      - application/json
      description: 计算计入SLA且已完成的报障单的业务影响总时长
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取业务影响总时长
      tags:
      - 看板-工单统计
  /dashboard/ticket/daily-impact-trend:
    get:
      consumes:
      - application/json
      description: 获取每天故障处理总时长趋势及报障单数量趋势
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 分组方式(day/week/month)
        in: query
        name: group_by
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为false
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取每天故障处理总时长趋势统计
      tags:
      - 看板-工单统计
  /dashboard/ticket/duration-stats:
    get:
      consumes:
      - application/json
      description: 获取报障单的处理时长统计
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取处理时长统计
      tags:
      - 看板-工单统计
  /dashboard/ticket/engineer-stats:
    get:
      consumes:
      - application/json
      description: 获取软件修复分配人和硬件修复工程师的平均响应时长和修复时长统计
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取工程师响应和修复时长统计
      tags:
      - 看板-工单统计
  /dashboard/ticket/fault-detail-types:
    get:
      consumes:
      - application/json
      description: 获取报障单的具体故障类型(FaultDetailType)分布
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取具体故障类型分布统计
      tags:
      - 看板-工单统计
  /dashboard/ticket/fault-details:
    get:
      consumes:
      - application/json
      description: 获取指定项目的故障单详情，包括租户IP、故障原因、故障时长和是否冷迁移
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，必须指定项目
        in: query
        name: project
        required: true
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取故障单详情列表
      tags:
      - 看板-工单统计
  /dashboard/ticket/fault-types:
    get:
      consumes:
      - application/json
      description: 获取报障单的故障类型分布
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取故障类型分布统计
      tags:
      - 看板-工单统计
  /dashboard/ticket/gpu-fault-ratio:
    get:
      consumes:
      - application/json
      description: 计算指定项目的GPU卡故障比(GPU故障工单数/该项目的GPU服务器数*8)
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，必须指定项目
        in: query
        name: project
        required: true
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取GPU卡故障比
      tags:
      - 看板-工单统计
  /dashboard/ticket/hardware-fault-trend:
    get:
      consumes:
      - application/json
      description: 获取每天硬件故障次数趋势统计
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 分组方式(day/week/month)
        in: query
        name: group_by
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为false
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取每天硬件故障次数趋势统计
      tags:
      - 看板-工单统计
  /dashboard/ticket/sla-stats:
    get:
      consumes:
      - application/json
      description: 获取报障单的SLA达标情况
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取SLA达标情况
      tags:
      - 看板-工单统计
  /dashboard/ticket/source-stats:
    get:
      consumes:
      - application/json
      description: 获取报障单的故障来源分布，customer为客户报障，其他为内部监控
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取故障来源分布统计
      tags:
      - 看板-工单统计
  /dashboard/ticket/status-stats:
    get:
      consumes:
      - application/json
      description: 获取报障单的状态分布
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取报障单状态分布统计
      tags:
      - 看板-工单统计
  /dashboard/ticket/time-stats:
    get:
      consumes:
      - application/json
      description: 获取报障单的创建时间分布，支持按天、周、月、年统计
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 分组方式(day/week/month)
        in: query
        name: group_by
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取报障单时间分布统计
      tags:
      - 看板-工单统计
  /dashboard/ticket/total-devices:
    get:
      consumes:
      - application/json
      description: 获取根据不重复的device_sn统计的故障总台数
      parameters:
      - description: 时间范围(today/week/month/year/custom)
        in: query
        name: time_range
        type: string
      - description: 开始日期，格式YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期，格式YYYY-MM-DD
        in: query
        name: end_date
        type: string
      - description: 项目名称，为空时返回全部项目数据
        in: query
        name: project
        type: string
      - description: 是否只统计计入SLA的工单，默认为true
        in: query
        name: count_in_sla
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取故障总台数
      tags:
      - 看板-工单统计
  /event/api/batch_create:
    post:
      consumes:
      - application/json
      description: 一次提交多个客户报障工单，返回每个工单的创建结果
      parameters:
      - description: 批量报障请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.BatchCreateTicketRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/model.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/model.BatchCreateTicketResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/model.APIResponse'
      summary: 批量创建客户报障工单
      tags:
      - 客户API
  /event/api/create:
    post:
      consumes:
      - application/json
      description: 提交客户报障工单，默认影响SLA，提交时间为故障时间，授权时间为提交时间，一旦提交成功，默认客户已授权运维人员进行处理，不再单独申请客户授权
      parameters:
      - description: 报障请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.CustomerTicketRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/model.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/model.CustomerTicketResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/model.APIResponse'
      summary: 创建客户报障工单
      tags:
      - 客户API
  /event/api/query:
    get:
      consumes:
      - application/json
      description: 查询工单列表，支持多种筛选条件
      parameters:
      - description: 事件单ID，多个用逗号分隔
        in: query
        name: ticketId
        type: string
      - description: '状态: open, authority, in_progress, closed, all(默认)'
        in: query
        name: ticketStatus
        type: string
      - description: 创建时间，支持today, yesterday, thisweek, lastweek, thismonth, lastmonth,
          last7days或具体日期如2023-01-01
        in: query
        name: ticketCreateAt
        type: string
      - description: '来源: customer, cnhancloud, all(默认)'
        in: query
        name: ticketFrom
        type: string
      - description: 每页条数，默认20，最大100
        in: query
        name: pageSize
        type: integer
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 虚机IP，多个用逗号分隔
        in: query
        name: ticketVmIp
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/model.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/model.CustomerTicketQueryResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/model.APIResponse'
      summary: 查询工单列表
      tags:
      - 客户API
  /event/api/set_authority:
    post:
      consumes:
      - application/json
      description: 设置工单授权，可以通过工单ID或虚机IP授权
      parameters:
      - description: 授权请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.CustomerTicketAuthorityRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.APIResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/model.APIResponse'
      summary: 设置工单授权
      tags:
      - 客户API
  /export:
    post:
      consumes:
      - application/json
      description: 导出数据到Excel文件
      parameters:
      - description: 导出请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/model.ExportRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.ExportResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 导出数据
      tags:
      - 导出
  /export/download/{filename}:
    get:
      description: 下载已导出的Excel文件
      parameters:
      - description: 文件名
        in: path
        name: filename
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 文件内容
          schema:
            type: file
        "404":
          description: 文件不存在
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 下载导出文件
      tags:
      - 导出
  /file/{id}:
    delete:
      description: 删除指定ID的文件
      parameters:
      - description: 文件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "403":
          description: 无权限
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 删除文件
      tags:
      - 文件管理
  /file/list-by-module:
    get:
      description: 根据模块类型和ID获取关联的文件列表
      parameters:
      - description: 模块类型
        in: query
        name: module_type
        required: true
        type: string
      - description: 模块ID
        in: query
        name: module_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 文件列表
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/model.File'
                  type: array
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 获取模块关联的文件列表
      tags:
      - 文件管理
  /file/thumbnail/{filename}:
    get:
      description: 根据缩略图文件名查看缩略图
      parameters:
      - description: 缩略图文件名
        in: path
        name: filename
        required: true
        type: string
      produces:
      - image/*
      responses:
        "200":
          description: 缩略图内容
          schema:
            type: file
        "404":
          description: 缩略图不存在
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 查看图片缩略图
      tags:
      - 文件管理
  /file/upload:
    post:
      consumes:
      - multipart/form-data
      description: 上传单个文件并返回文件信息
      parameters:
      - description: 文件
        in: formData
        name: file
        required: true
        type: file
      - description: 关联模块类型
        in: formData
        name: module_type
        type: string
      - description: 关联模块ID
        in: formData
        name: module_id
        type: integer
      - description: 文件描述
        in: formData
        name: description
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 上传成功
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.FileResponse'
              type: object
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 上传单个文件
      tags:
      - 文件管理
  /file/upload/batch:
    post:
      consumes:
      - multipart/form-data
      description: 批量上传多个文件并返回文件信息
      parameters:
      - description: 多个文件
        in: formData
        name: files
        required: true
        type: file
      - description: 关联模块类型
        in: formData
        name: module_type
        type: string
      - description: 关联模块ID
        in: formData
        name: module_id
        type: integer
      - description: 文件描述
        in: formData
        name: description
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 上传成功
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.FileBatchResponse'
              type: object
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      security:
      - BearerAuth: []
      summary: 批量上传文件
      tags:
      - 文件管理
  /file/view/{filename}:
    get:
      description: 根据文件名查看或下载文件
      parameters:
      - description: 文件名
        in: path
        name: filename
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 文件内容
          schema:
            type: file
        "404":
          description: 文件不存在
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 查看或下载文件
      tags:
      - 文件管理
  /hard-sche:
    get:
      consumes:
      - application/json
      description: 根据日期（如：2006-02-01T19:00:00+08:00）查询排版信息
      parameters:
      - description: 查询日期
        in: body
        name: date
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回当天值班人信息（主/副值班人姓名）
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.HardSchedule'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取指定日期的值班人信息
      tags:
      - 排班管理
    post:
      consumes:
      - application/json
      description: 根据路径中的日期（2006-02-01T19:00:00+08:00）白班夜班值班人创建排班记录
      parameters:
      - description: 要创建的日期（示例：2023-01-01）
        in: path
        name: date
        required: true
        type: string
      - description: 排班信息
        in: body
        name: schedule
        required: true
        schema:
          $ref: '#/definitions/model.HardSchedule'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建指定日期的排班信息
      tags:
      - 排班管理
  /hard-sche/{date}:
    get:
      consumes:
      - application/json
      description: 根据年份和月份查询当月所有日期的排班信息
      parameters:
      - description: 要查询的日期（示例：2006-02-01T19:00:00+08:00）
        in: path
        name: date
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取指定年月的排班记录
      tags:
      - 排班管理
  /import/template/{model}:
    get:
      description: 下载指定模型的导入模板
      parameters:
      - description: 模型类型(region/az/device/resource)
        in: path
        name: model
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: CSV模板文件
          schema:
            type: file
      summary: 获取导入模板
      tags:
      - CMDB-数据导入
  /inbound-tickets/new:
    post:
      consumes:
      - application/json
      description: 创建新购入库工单，并初始化相关信息和启动工作流
      parameters:
      - description: 新购入库请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/controller.NewInbondReq'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功，返回工单 ID 和工单编号
          schema:
            additionalProperties: true
            type: object
        "400":
          description: 参数错误
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器内部错误
          schema:
            additionalProperties: true
            type: object
      summary: 创建新购入库工单
      tags:
      - 新购入库工单
  /inventory:
    get:
      description: 分页查询库存明细列表
      parameters:
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页数量
        in: query
        name: pageSize
        type: integer
      - description: 产品ID
        in: query
        name: productID
        type: integer
      - description: 仓库名称
        in: query
        name: warehouse
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 获取库存明细列表
      tags:
      - 库存管理
    post:
      consumes:
      - application/json
      description: 创建一个新的库存明细记录
      parameters:
      - description: 库存明细信息
        in: body
        name: inventory
        required: true
        schema:
          $ref: '#/definitions/inventory.InventoryDetail'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 创建库存明细
      tags:
      - 库存管理
  /inventory/{id}:
    delete:
      description: 根据ID删除库存明细记录
      parameters:
      - description: 库存明细ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 删除库存明细
      tags:
      - 库存管理
    get:
      description: 根据ID获取库存明细记录详情
      parameters:
      - description: 库存明细ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/inventory.InventoryDetail'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 获取库存明细详情
      tags:
      - 库存管理
    put:
      consumes:
      - application/json
      description: 根据ID更新库存明细信息
      parameters:
      - description: 库存明细ID
        in: path
        name: id
        required: true
        type: integer
      - description: 库存明细信息
        in: body
        name: inventory
        required: true
        schema:
          $ref: '#/definitions/inventory.InventoryDetail'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 更新库存明细
      tags:
      - 库存管理
  /inventory/{id}/adjust:
    post:
      consumes:
      - application/json
      description: 增加或减少库存数量
      parameters:
      - description: 库存明细ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 调整库存数量
      tags:
      - 库存管理
  /inventory/{id}/allocate:
    post:
      consumes:
      - application/json
      description: 分配库存给特定用途
      parameters:
      - description: 库存明细ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 分配库存
      tags:
      - 库存管理
  /inventory/{id}/release:
    post:
      consumes:
      - application/json
      description: 释放之前分配的库存
      parameters:
      - description: 库存明细ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 释放已分配库存
      tags:
      - 库存管理
  /inventory/expiring-warranty:
    get:
      description: 获取在指定天数内即将过保的库存项目
      parameters:
      - description: 天数
        in: query
        name: days
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 获取即将过保的库存项目
      tags:
      - 库存管理
  /inventory/history/{detailID}:
    get:
      description: 获取指定库存明细的变更历史记录
      parameters:
      - description: 库存明细ID
        in: path
        name: detailID
        required: true
        type: integer
      - description: 开始时间
        in: query
        name: startTime
        type: string
      - description: 结束时间
        in: query
        name: endTime
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 获取库存变更历史
      tags:
      - 库存管理
  /inventory/low-stock:
    get:
      description: 获取库存数量低于阈值的产品列表
      parameters:
      - description: 库存阈值
        in: query
        name: threshold
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 获取低库存产品列表
      tags:
      - 库存管理
  /inventory/summary/{productID}:
    get:
      description: 获取指定产品的库存汇总信息
      parameters:
      - description: 产品ID
        in: path
        name: productID
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 获取产品库存汇总
      tags:
      - 库存管理
  /inventory/warehouse/{warehouseID}:
    get:
      description: 根据仓库ID查询库存明细列表
      parameters:
      - description: 仓库ID
        in: path
        name: warehouseID
        required: true
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页数量
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.Response'
      summary: 查询仓库内的库存明细
      tags:
      - 库存管理
  /purchase/orders:
    get:
      consumes:
      - application/json
      description: 支持分页和筛选条件（订单编号、供应商名称、订单日期范围）
      parameters:
      - description: 采购订单编号（模糊查询）
        in: query
        name: po_number
        type: string
      - description: 供应商名称（模糊查询）
        in: query
        name: supplier_name
        type: string
      - description: 订单日期起始（格式：YYYY-MM-DD）
        in: query
        name: order_date_start
        type: string
      - description: 订单日期结束（格式：YYYY-MM-DD）
        in: query
        name: order_date_end
        type: string
      - description: 页码（从1开始）
        in: query
        name: page
        required: true
        type: integer
      - description: 每页数量（最大100）
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 查询采购订单列表
      tags:
      - 采购管理
    post:
      consumes:
      - application/json
      description: 创建新的采购订单及详情
      parameters:
      - description: 采购订单信息
        in: body
        name: order
        required: true
        schema:
          $ref: '#/definitions/service.CreatePurchaseOrderDTO'
      produces:
      - application/json
      responses:
        "201":
          description: 操作成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 创建采购订单
      tags:
      - 采购管理
  /purchase/orders/{id}:
    get:
      consumes:
      - application/json
      description: 通过订单ID查询采购订单详情
      parameters:
      - description: 采购订单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: 采购订单不存在
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 获取采购订单详情
      tags:
      - 采购管理
  /purchase/orders/po/{poNumber}:
    get:
      consumes:
      - application/json
      description: 通过采购订单编号（PO Number）查询订单详情
      parameters:
      - description: 采购订单编号
        in: path
        name: poNumber
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "404":
          description: 采购订单不存在
          schema:
            $ref: '#/definitions/response.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 通过PO编号查询采购订单
      tags:
      - 采购管理
  /soft-sche:
    post:
      consumes:
      - application/json
      description: 根据路径中的日期（YYYY-MM-DD）值班人（主值班人/副值班人）创建当天的排班记录
      parameters:
      - description: 要创建的日期（示例：2023-01-01）
        in: path
        name: date
        required: true
        type: string
      - description: 排班信息
        in: body
        name: schedule
        required: true
        schema:
          $ref: '#/definitions/model.SoftSchedule'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建指定日期的排班信息
      tags:
      - 排班管理
  /soft-sche/{date}:
    get:
      consumes:
      - application/json
      description: 根据年份和月份查询当月所有日期的排班信息(主/副值班人)
      parameters:
      - description: 要查询的日期（示例：2023-01-01）
        in: path
        name: date
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取指定年月的排班记录
      tags:
      - 排班管理
    put:
      consumes:
      - application/json
      description: 根据路径中的日期（YYYY-MM-DD）更新当天的排班记录（主值班人/副值班人信息）
      parameters:
      - description: 要更新的日期（示例：2023-01-01）
        in: path
        name: date
        required: true
        type: string
      - description: 排班信息
        in: body
        name: schedule
        required: true
        schema:
          $ref: '#/definitions/model.SoftSchedule'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新指定日期的排班信息
      tags:
      - 排班管理
  /soft-sche/date/{date}:
    get:
      consumes:
      - application/json
      description: 根据日期（格式：YYYY-MM-DD）查询当天的主值班人和副值班人姓名
      parameters:
      - description: 要查询的日期（示例：2023-01-01）
        in: path
        name: date
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回当天值班人信息（主/副值班人姓名）
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.SoftSchedule'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取指定日期的值班人信息
      tags:
      - 排班管理
  /system/menu:
    post:
      consumes:
      - application/json
      description: 创建菜单
      parameters:
      - description: 菜单信息
        in: body
        name: menu
        required: true
        schema:
          $ref: '#/definitions/model.Menu'
      produces:
      - application/json
      responses:
        "200":
          description: 创建菜单成功
          schema:
            type: string
        "400":
          description: 请求参数错误
          schema:
            type: string
        "500":
          description: 创建菜单失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 创建菜单
      tags:
      - 系统管理
    put:
      consumes:
      - application/json
      description: 更新菜单
      parameters:
      - description: 菜单信息
        in: body
        name: menu
        required: true
        schema:
          $ref: '#/definitions/model.Menu'
      produces:
      - application/json
      responses:
        "200":
          description: 更新菜单成功
          schema:
            type: string
        "400":
          description: 请求参数错误
          schema:
            type: string
        "500":
          description: 更新菜单失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 更新菜单
      tags:
      - 系统管理
  /system/menu/{id}:
    delete:
      consumes:
      - application/json
      description: 删除菜单
      parameters:
      - description: 菜单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除菜单成功
          schema:
            type: string
        "400":
          description: 请求参数错误
          schema:
            type: string
        "500":
          description: 删除菜单失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 删除菜单
      tags:
      - 系统管理
    get:
      consumes:
      - application/json
      description: 根据ID获取菜单
      parameters:
      - description: 菜单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取菜单成功
          schema:
            $ref: '#/definitions/model.MenuResponse'
        "400":
          description: 请求参数错误
          schema:
            type: string
        "500":
          description: 获取菜单失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 根据ID获取菜单
      tags:
      - 系统管理
  /system/menu/all:
    get:
      consumes:
      - application/json
      description: 根据当前用户的角色权限获取菜单
      produces:
      - application/json
      responses:
        "200":
          description: 获取菜单成功
          schema:
            items:
              $ref: '#/definitions/model.MenuResponse'
            type: array
        "401":
          description: 未授权
          schema:
            type: string
        "500":
          description: 获取菜单失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 获取当前用户菜单
      tags:
      - 系统管理
  /system/menu/dashboard:
    get:
      consumes:
      - application/json
      description: 获取仪表盘菜单
      produces:
      - application/json
      responses:
        "200":
          description: 获取仪表盘菜单成功
          schema:
            items:
              $ref: '#/definitions/model.MenuResponse'
            type: array
        "500":
          description: 获取仪表盘菜单失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 获取仪表盘菜单
      tags:
      - 系统管理
  /system/menu/list:
    get:
      consumes:
      - application/json
      description: 获取带有id、type、status等字段的菜单管理列表
      produces:
      - application/json
      responses:
        "200":
          description: 获取菜单列表成功
          schema:
            items:
              $ref: '#/definitions/model.MenuListResponse'
            type: array
        "500":
          description: 获取菜单列表失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 获取菜单管理列表
      tags:
      - 系统管理
  /system/role:
    post:
      consumes:
      - application/json
      description: 创建角色
      parameters:
      - description: 角色信息
        in: body
        name: role
        required: true
        schema:
          $ref: '#/definitions/model.RoleCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建角色成功
          schema:
            type: string
        "400":
          description: 请求参数错误
          schema:
            type: string
        "500":
          description: 创建角色失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 创建角色
      tags:
      - 系统管理
    put:
      consumes:
      - application/json
      description: 更新角色
      parameters:
      - description: 角色信息
        in: body
        name: role
        required: true
        schema:
          $ref: '#/definitions/model.RoleUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新角色成功
          schema:
            type: string
        "400":
          description: 请求参数错误
          schema:
            type: string
        "500":
          description: 更新角色失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 更新角色
      tags:
      - 系统管理
  /system/role/{id}:
    delete:
      consumes:
      - application/json
      description: 删除角色
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 删除角色成功
          schema:
            type: string
        "400":
          description: 请求参数错误
          schema:
            type: string
        "500":
          description: 删除角色失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 删除角色
      tags:
      - 系统管理
    get:
      consumes:
      - application/json
      description: 根据ID获取角色
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取角色成功
          schema:
            $ref: '#/definitions/model.RoleResponse'
        "400":
          description: 请求参数错误
          schema:
            type: string
        "500":
          description: 获取角色失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 根据ID获取角色
      tags:
      - 系统管理
  /system/role/list:
    get:
      consumes:
      - application/json
      description: 获取角色列表
      parameters:
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页数量
        in: query
        name: size
        type: integer
      - description: 角色名称
        in: query
        name: name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取角色列表成功
          schema:
            $ref: '#/definitions/model.RoleListResponse'
        "500":
          description: 获取角色列表失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 获取角色列表
      tags:
      - 系统管理
  /system/role/menus:
    get:
      consumes:
      - application/json
      description: 根据用户角色ID获取有权限的菜单
      parameters:
      - description: 角色ID
        in: query
        name: roleId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户菜单成功
          schema:
            items:
              $ref: '#/definitions/model.MenuResponse'
            type: array
        "400":
          description: 请求参数错误
          schema:
            type: string
        "500":
          description: 获取用户菜单失败
          schema:
            type: string
      security:
      - Bearer: []
      summary: 获取用户菜单
      tags:
      - 系统管理
  /ticket/fault-tickets:
    get:
      consumes:
      - application/json
      description: 分页获取报障单列表，支持筛选
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: string
      - collectionFormat: csv
        description: 多状态筛选
        in: query
        items:
          type: string
        name: status[]
        type: array
      - description: 优先级筛选
        in: query
        name: priority
        type: string
      - description: 故障标题
        in: query
        name: title
        type: string
      - description: 租户IP
        in: query
        name: resource_identifier
        type: string
      - description: 故障类型
        in: query
        name: faultType
        type: string
      - description: 具体故障类型
        in: query
        name: fault_detail_type
        type: string
      - description: 报障人
        in: query
        name: reporterName
        type: string
      - description: 接单人
        in: query
        name: assignedTo
        type: string
      - description: 创建时间范围，格式：开始时间,结束时间，如：2023-01-01 00:00:00,2023-01-02 00:00:00
        in: query
        name: creationTimeRange
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取报障单列表
      tags:
      - 工单管理-报障单
    post:
      consumes:
      - application/json
      description: 创建新的报障单，只需提供服务器SN
      parameters:
      - description: 报障单信息 (只需要deviceSN字段)
        in: body
        name: fault_ticket
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建报障单
      tags:
      - 工单管理-报障单
  /ticket/fault-tickets/{id}:
    get:
      consumes:
      - application/json
      description: 根据ID获取报障单详情
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.FaultTicket'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取报障单详情
      tags:
      - 工单管理-报障单
    put:
      consumes:
      - application/json
      description: 更新报障单信息
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 报障单信息
        in: body
        name: fault_ticket
        required: true
        schema:
          $ref: '#/definitions/model.FaultTicket'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新报障单
      tags:
      - 工单管理-报障单
  /ticket/fault-tickets/{id}/assign:
    put:
      consumes:
      - application/json
      description: 将报障单分配给工程师
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 工程师信息
        in: body
        name: engineer
        required: true
        schema:
          $ref: '#/definitions/model.FaultTicketAssignment'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 分配报障单
      tags:
      - 工单管理-报障单
  /ticket/fault-tickets/{id}/close:
    put:
      consumes:
      - application/json
      description: 关闭报障单并添加总结
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 总结信息 (包含summary字段)
        in: body
        name: summary
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 关闭报障单
      tags:
      - 工单管理-报障单
  /ticket/fault-tickets/{id}/fields:
    put:
      consumes:
      - application/json
      description: 更新报障单特定字段，避免全字段更新可能引起的验证错误
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 要更新的字段
        in: body
        name: fields
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新报障单指定字段
      tags:
      - 工单管理-报障单
  /ticket/fault-tickets/{id}/history:
    get:
      consumes:
      - application/json
      description: 获取报障单状态变更历史记录
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取报障单状态历史
      tags:
      - 工单管理-报障单
  /ticket/fault-tickets/{id}/transition:
    put:
      consumes:
      - application/json
      description: 统一的工单状态转换接口，支持所有工作流阶段
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 转换信息
        in: body
        name: transition
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 状态转换与触发工作流
      tags:
      - 工单管理-报障单
  /ticket/fault-tickets/device/{sn}/fault-count:
    get:
      consumes:
      - application/json
      description: 根据设备SN获取本月有效故障次数（排除重复故障和误报）
      parameters:
      - description: 设备序列号
        in: path
        name: sn
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取设备本月故障次数
      tags:
      - 工单管理-报障单
  /ticket/inbound-tickets/{id}:
    get:
      consumes:
      - application/json
      description: 根据工单ID获取入库工单详情
      parameters:
      - description: 入库工单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取入库工单详情
      tags:
      - 工单管理-入库单
  /ticket/inbound-tickets/{inboundNo}/transition:
    put:
      consumes:
      - application/json
      description: 统一的工单状态转换接口，支持所有工作流阶段
      parameters:
      - description: 入库单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 转换信息
        in: body
        name: transition
        required: true
        schema:
          $ref: '#/definitions/model.InboundTrigger'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 状态转换与触发工作流
      tags:
      - 工单管理-入库单
  /ticket/inbound-tickets/new/{id}/transition:
    put:
      consumes:
      - application/json
      description: 统一的工单状态转换接口，支持所有工作流阶段
      parameters:
      - description: 入库单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 转换信息
        in: body
        name: transition
        required: true
        schema:
          $ref: '#/definitions/model.InboundTrigger'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 状态转换与触发工作流
      tags:
      - 工单管理-入库单
  /ticket/inbound-tickets/part:
    get:
      consumes:
      - application/json
      description: 获取入库工单列表，支持分页和条件查询
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: page_size
        type: integer
      - description: 工单状态
        in: query
        name: status
        type: string
      - description: 工单类别
        in: query
        name: order_category
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取入库工单列表
      tags:
      - 工单管理-入库单
    post:
      consumes:
      - application/json
      description: 创建入库单
      parameters:
      - description: 入库工单信息
        in: body
        name: ticket
        required: true
        schema:
          $ref: '#/definitions/model.PartInboundTicket'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建入库单
      tags:
      - 工单管理-入库单
  /ticket/inbound-tickets/part/{id}/transition:
    put:
      consumes:
      - application/json
      description: 统一的工单状态转换接口，支持所有工作流阶段
      parameters:
      - description: 入库单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 转换信息
        in: body
        name: transition
        required: true
        schema:
          $ref: '#/definitions/model.InboundTrigger'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 状态转换与触发工作流
      tags:
      - 工单管理-入库单
  /ticket/inbound-tickets/part/history/{InboundNo}:
    get:
      consumes:
      - application/json
      description: 获取指定入库单号的所有历史记录
      parameters:
      - description: 入库单号
        in: path
        name: InboundNo
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取入库工单历史记录
      tags:
      - 工单管理-入库单
  /ticket/inbound-tickets/partStart:
    post:
      consumes:
      - application/json
      description: 启动维修入库工作流，用于测试目的
      parameters:
      - description: 入库工单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 启动维修入库工作流
      tags:
      - 工单管理-入库单
  /ticket/repair-tickets:
    get:
      consumes:
      - application/json
      description: 分页获取维修单列表，支持筛选
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: string
      - description: 工程师ID筛选
        in: query
        name: engineer_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取维修单列表
      tags:
      - 工单管理-维修单
    post:
      consumes:
      - application/json
      description: 创建新的维修单
      parameters:
      - description: 维修单信息
        in: body
        name: repair_ticket
        required: true
        schema:
          $ref: '#/definitions/model.RepairTicketCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建维修单
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}:
    get:
      consumes:
      - application/json
      description: 根据ID获取维修单详情
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.RepairTicket'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取维修单详情
      tags:
      - 工单管理-维修单
    put:
      consumes:
      - application/json
      description: 更新维修单信息
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 维修单信息
        in: body
        name: repair_ticket
        required: true
        schema:
          $ref: '#/definitions/model.RepairTicket'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新维修单
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/arrive:
    put:
      consumes:
      - application/json
      description: 记录工程师到达现场时间
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 工程师到达现场
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/assign:
    put:
      consumes:
      - application/json
      description: 将维修单分配给工程师
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 工程师信息
        in: body
        name: engineer
        required: true
        schema:
          $ref: '#/definitions/model.RepairTicketAssignment'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 分配维修单
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/complete:
    put:
      consumes:
      - application/json
      description: 记录维修完成
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 维修结果
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/service.RepairResult'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 完成维修
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/hardware-replace/complete:
    put:
      consumes:
      - application/json
      description: 记录硬件更换完成
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 备件信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.HardwareReplaceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 完成硬件更换
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/hardware-replace/start:
    put:
      consumes:
      - application/json
      description: 记录开始更换硬件时间
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 开始更换硬件
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/history:
    get:
      consumes:
      - application/json
      description: 获取维修单的状态变更历史记录
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/model.RepairTicketStatusHistory'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取维修单历史记录
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/spare-machine-request:
    post:
      consumes:
      - application/json
      description: 为维修工单申请备机
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 备机模板信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.SpareMachineRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 申请备机
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/spare-request:
    post:
      consumes:
      - application/json
      description: 为维修工单申请备件
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 备件信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/model.SpareRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 申请备件
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/start:
    put:
      consumes:
      - application/json
      description: 开始执行维修工作
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 开始维修
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/status:
    put:
      consumes:
      - application/json
      description: 更新维修单状态
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 状态信息
        in: body
        name: status
        required: true
        schema:
          $ref: '#/definitions/model.RepairTicketStatusUpdate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 更新维修单状态
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/{id}/transition:
    put:
      consumes:
      - application/json
      description: 统一的维修工单状态转换接口，支持所有工作流阶段
      parameters:
      - description: 维修单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 转换信息
        in: body
        name: transition
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 状态转换与触发工作流
      tags:
      - 工单管理-维修单
  /ticket/repair-tickets/available:
    get:
      consumes:
      - application/json
      description: 获取状态为"waiting_accept"的维修单列表，供工程师主动接单
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/model.RepairTicket'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取可接单的维修单列表
      tags:
      - 工单管理-维修单
  /ticket/workflow/cold-migrations:
    get:
      consumes:
      - application/json
      description: 获取冷迁移记录列表，支持分页和筛选
      parameters:
      - description: 页码, 默认1
        in: query
        name: page
        type: integer
      - description: 每页数量, 默认20
        in: query
        name: page_size
        type: integer
      - description: 故障设备SN
        in: query
        name: fault_device_sn
        type: string
      - description: 备机设备SN
        in: query
        name: backup_device_sn
        type: string
      - description: 状态(success/failed)
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/response.PageResult'
                  - properties:
                      list:
                        items:
                          $ref: '#/definitions/model.ColdMigration'
                        type: array
                    type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取冷迁移列表
      tags:
      - 工单管理-工作流
  /ticket/workflow/tickets/{id}/accept:
    put:
      consumes:
      - application/json
      description: 接受报障单，将状态改为排查中，并设置接单人为当前操作用户
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 接单
      tags:
      - 工单管理-工作流
  /ticket/workflow/tickets/{id}/cold-migration:
    get:
      consumes:
      - application/json
      description: 获取报障单关联的冷迁移记录
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.ColdMigration'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取冷迁移信息
      tags:
      - 工单管理-工作流
  /ticket/workflow/tickets/{id}/customer-approval:
    get:
      consumes:
      - application/json
      description: 获取报障单关联的客户审批记录
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.CustomerApproval'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取客户审批
      tags:
      - 工单管理-工作流
    post:
      consumes:
      - application/json
      description: 创建客户审批记录，审批维修方案
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 客户审批信息
        in: body
        name: customer_approval
        required: true
        schema:
          $ref: '#/definitions/model.CustomerApproval'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建客户审批
      tags:
      - 工单管理-工作流
  /ticket/workflow/tickets/{id}/repair-selection:
    get:
      consumes:
      - application/json
      description: 获取报障单关联的维修选择记录
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.RepairSelection'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取维修选择
      tags:
      - 工单管理-工作流
    post:
      consumes:
      - application/json
      description: 创建维修选择记录，选择维修方式(重启/硬件维修/软件维修/冷迁移)
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 维修选择信息
        in: body
        name: repair_selection
        required: true
        schema:
          $ref: '#/definitions/model.RepairSelection'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建维修选择
      tags:
      - 工单管理-工作流
  /ticket/workflow/tickets/{id}/verification:
    get:
      consumes:
      - application/json
      description: 获取报障单关联的验证记录
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  $ref: '#/definitions/model.Verification'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取验证
      tags:
      - 工单管理-工作流
    post:
      consumes:
      - application/json
      description: 创建验证记录，验证维修结果
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 验证信息
        in: body
        name: verification
        required: true
        schema:
          $ref: '#/definitions/model.Verification'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 创建验证
      tags:
      - 工单管理-工作流
  /ticket/workflow/trigger/{id}:
    post:
      consumes:
      - application/json
      description: 当工作流在等待手动触发状态时，通过此API手动触发工作流继续执行
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 包含stage字段，指定触发的阶段
        in: body
        name: body
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.ResponseStruct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 手动触发工作流下一步
      tags:
      - 工单管理-工作流
  /ticket/workflow/waiting-stages/{id}:
    get:
      consumes:
      - application/json
      description: 获取工单当前等待手动触发的工作流阶段列表
      parameters:
      - description: 报障单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.ResponseStruct'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/response.ResponseStruct'
      summary: 获取工单当前等待的工作流阶段
      tags:
      - 工单管理-工作流
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
