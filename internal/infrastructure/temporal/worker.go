package temporal

import (
	"backend/configs"
	"backend/internal/infrastructure/cache"
	"backend/internal/infrastructure/database"
	zapLog "backend/internal/infrastructure/logger"
	"log"
	"os"
	"os/signal"
	"syscall"

	"go.temporal.io/sdk/client"
	temporalWorker "go.temporal.io/sdk/worker"
	"go.uber.org/zap"
)

// Worker 包含所有worker相关的组件
type Worker struct {
	logger         *zap.Logger
	config         *configs.Config
	temporalClient client.Client
	workers        []temporalWorker.Worker
	repositories   *repositories
	redisClient    *cache.RedisClient
}

// NewWorker 创建一个新的Worker实例
func NewWorker() (*Worker, error) {
	// 加载配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		return nil, err
	}

	// 初始化日志
	zapLogger, err := zapLog.NewLoggerFromConfig(&cfg.Logger)
	if err != nil {
		return nil, err
	}

	// 初始化数据库
	db := database.InitDB(cfg)

	// 初始化Redis客户端
	redisClient := cache.NewRedisClient(cfg.Redis)

	// 初始化Temporal客户端
	temporalClient, err := initTemporalClient(cfg, zapLogger)
	if err != nil {
		return nil, err
	}

	// 初始化服务和存储库
	w := &Worker{
		logger:         zapLogger,
		config:         cfg,
		temporalClient: temporalClient,
		redisClient:    redisClient,
	}

	// 初始化各种仓库和服务
	if err := w.initRepositoriesAndServices(db); err != nil {
		return nil, err
	}

	// 初始化各种Worker
	if err := w.initWorkers(); err != nil {
		return nil, err
	}

	return w, nil
}

// Start 启动所有Worker
func (w *Worker) Start() error {
	w.logger.Info("启动Temporal workers...")

	// 启动所有Worker
	for i, worker := range w.workers {
		workerIndex := i
		workerInstance := worker // 创建一个副本以避免闭包问题
		go func() {
			if err := workerInstance.Run(temporalWorker.InterruptCh()); err != nil {
				w.logger.Fatal("启动Worker失败", zap.Int("workerIndex", workerIndex), zap.Error(err))
			}
		}()
	}

	w.logger.Info("Temporal workers启动成功")

	// 等待中断信号
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh

	w.logger.Info("关闭Temporal workers...")
	return nil
}

// Close 关闭Worker及其相关资源
func (w *Worker) Close() error {
	if err := w.logger.Sync(); err != nil {
		log.Printf("同步zap logger失败: %v", err)
	}

	w.temporalClient.Close()
	return nil
}
