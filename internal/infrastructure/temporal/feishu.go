package temporal

import (
	"backend/configs"
	"backend/internal/common/utils/notifier"
	"fmt"
	"go.uber.org/zap"
)

// SetInboundBot 设置入库相关飞书机器人
func SetInboundBot(cfg *configs.FeishuConfig, logger *zap.Logger) map[string]notifier.FeishuBotConfig {
	// 入库相关通知
	var (
		inboundWebhookURL, inboundSecret, inboundFeishuDetailUrlTemplate string
		securityGuardWebhookURL, securityGuardSecret                     string
	)

	if cfg == nil || cfg.InboundWebhookURL == "" || cfg.InboundSecret == "" || cfg.InboundTicketDetailUrlTemplate == "" {
		fmt.Println("【ERROR】初始化入库飞书通知失败，缺少入库机器人配置")
		logger.Warn("入库飞书配置不存在")
		return nil
	}

	inboundWebhookURL = cfg.InboundWebhookURL
	inboundSecret = cfg.InboundSecret
	inboundFeishuDetailUrlTemplate = cfg.InboundTicketDetailUrlTemplate

	securityGuardWebhookURL = cfg.SecurityGuardWebhookURL
	securityGuardSecret = cfg.SecurityGuardSecret
	// 入库相关机器人
	inboundBots := map[string]notifier.FeishuBotConfig{
		"QY_inbound":  {Name: "入库通知", WebhookURL: inboundWebhookURL, Secret: inboundSecret, DetailURLTemplate: inboundFeishuDetailUrlTemplate},
		"QY_security": {Name: "保安群通知", WebhookURL: securityGuardWebhookURL, Secret: securityGuardSecret},
	}
	return inboundBots
}
