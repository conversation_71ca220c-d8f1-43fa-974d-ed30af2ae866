package temporal

import (
	"backend/configs"
	zapLog "backend/internal/infrastructure/logger"
	"os"

	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

// initTemporalClient 初始化Temporal客户端
func initTemporalClient(cfg *configs.Config, logger *zap.Logger) (client.Client, error) {
	temporalAddr := "127.0.0.1:7233" // 默认值

	// 优先从环境变量获取
	if envAddr := os.Getenv("TEMPORAL_ADDRESS"); envAddr != "" {
		temporalAddr = envAddr
		logger.Info("从环境变量获取Temporal地址", zap.String("address", temporalAddr))
	} else if cfg.Temporal != nil && cfg.Temporal.Address != "" {
		// 如果环境变量未设置，则从配置文件获取
		temporalAddr = cfg.Temporal.Address
		logger.Info("从配置文件获取Temporal地址", zap.String("address", temporalAddr))
	} else {
		logger.Info("使用默认Temporal地址", zap.String("address", temporalAddr))
	}

	namespace := "default"
	// 优先从环境变量获取命名空间
	if envNs := os.Getenv("TEMPORAL_NAMESPACE"); envNs != "" {
		namespace = envNs
		logger.Info("从环境变量获取Temporal命名空间", zap.String("namespace", namespace))
	} else if cfg.Temporal != nil && cfg.Temporal.Namespace != "" {
		namespace = cfg.Temporal.Namespace
		logger.Info("从配置文件获取Temporal命名空间", zap.String("namespace", namespace))
	} else {
		logger.Info("使用默认Temporal命名空间", zap.String("namespace", namespace))
	}

	logger.Info("连接Temporal服务器", zap.String("address", temporalAddr), zap.String("namespace", namespace))

	// 尝试使用127.0.0.1而不是localhost
	if temporalAddr == "localhost:7233" {
		logger.Info("尝试使用127.0.0.1替代localhost", zap.String("new_address", "127.0.0.1:7233"))
		temporalAddr = "127.0.0.1:7233"
	}

	// 创建Temporal客户端
	c, err := client.Dial(client.Options{
		HostPort:  temporalAddr,
		Namespace: namespace,
		Logger:    zapLog.NewTemporalLogger(logger),
	})

	if err != nil {
		logger.Fatal("无法创建Temporal客户端",
			zap.Error(err),
			zap.String("address", temporalAddr),
			zap.String("详细信息", "请确保Temporal服务正在运行，并检查地址和端口是否正确。服务器是否可达？防火墙是否阻止连接？"),
		)
		return nil, err
	}

	return c, nil
}
