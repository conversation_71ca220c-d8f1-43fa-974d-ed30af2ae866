package temporal

import (
	"backend/internal/common/utils/notifier"
	"backend/internal/modules/cmdb/repository/asset"
	"backend/internal/modules/cmdb/repository/component"
	cmdbRepo "backend/internal/modules/cmdb/repository/inbound"
	"backend/internal/modules/cmdb/repository/inventory"
	"backend/internal/modules/cmdb/repository/outbound"
	"backend/internal/modules/cmdb/repository/outbound/mysql"
	productRepo "backend/internal/modules/cmdb/repository/product"
	cmdbAsset "backend/internal/modules/cmdb/service/asset"
	component2 "backend/internal/modules/cmdb/service/component"
	"backend/internal/modules/cmdb/service/inbound"
	cmdbInventory "backend/internal/modules/cmdb/service/inventory"
	cmdbOutbound "backend/internal/modules/cmdb/service/outbound"
	productService "backend/internal/modules/cmdb/service/product"
	cmdbActivities "backend/internal/modules/cmdb/workflow/activities"
	fileSvc "backend/internal/modules/file/service"
	acceptanceRepo "backend/internal/modules/hardware_maintenance/repository/acceptance"
	racking2 "backend/internal/modules/hardware_maintenance/repository/racking"
	acceptanceServ "backend/internal/modules/hardware_maintenance/service/acceptance"
	"backend/internal/modules/hardware_maintenance/service/racking"
	hardwareMaintanceActivity "backend/internal/modules/hardware_maintenance/workflow/activities"
	importSvc "backend/internal/modules/import/service"
	newPurchaseRepo "backend/internal/modules/purchase/repository"
	newPurchaseService "backend/internal/modules/purchase/service"
	arrivalActivity "backend/internal/modules/purchase/workflow/activity/arrival"
	shipmentActivity "backend/internal/modules/purchase/workflow/activity/shipment"
	purchaseRepository "backend/internal/modules/purchase_old/repository"
	purchaseService "backend/internal/modules/purchase_old/service"
	scheduleRepo "backend/internal/modules/schedule/repository"
	scheduleService "backend/internal/modules/schedule/service"
	serviceReposity "backend/internal/modules/server/repository"
	serviceService "backend/internal/modules/server/service"
	softwareRepo "backend/internal/modules/software_maintenance/repository"
	softwareService "backend/internal/modules/software_maintenance/service"
	softwareMaintanceActivity "backend/internal/modules/software_maintenance/workflow/activities"
	"backend/internal/modules/ticket/repository"
	mysqlRepo "backend/internal/modules/ticket/repository/mysql"
	"backend/internal/modules/ticket/service"
	"backend/internal/modules/ticket/workflow/activities"
	userRepository "backend/internal/modules/user/repository"
	userService "backend/internal/modules/user/service"
	"go.temporal.io/sdk/client"
	"gorm.io/gorm"
	"time"
)

// repositories 存储所有的仓库和服务实例
type repositories struct {
	// 票据相关
	EntryTicketRepo      repository.EntryTicketRepository
	FaultTicketRepo      repository.FaultTicketRepository
	RepairTicketRepo     repository.RepairTicketRepository
	RepairSelectionRepo  repository.RepairSelectionRepository
	OutboundApprovalRepo outbound.OutboundApprovalRepository
	CustomerApprovalRepo repository.CustomerApprovalRepository
	EntryApprovalRepo    repository.EntryApprovalRepository
	VerificationRepo     repository.VerificationRepository
	EntryPersonRepo      repository.EntryPersonRepository
	OutboundTicketRepo   outbound.OutboundTicketRepository
	InboundTicketRepo    repository.InboundTicketRepository

	// CMDB相关
	PurchaseRepo                 purchaseRepository.PurchaseRepository
	InboundRepo                  cmdbRepo.Repository
	InventoryRepo                inventory.InventoryRepository
	ServiceRepo                  serviceReposity.ServerRepository
	AssetSpareRepo               asset.SpareRepository
	ServerComponentRepo          component.ServerComponentRepository
	ProductRepository            productRepo.ProductRepository
	DeviceRepo                   asset.DeviceRepository
	ResourceRepo                 asset.ResourceRepository
	StatusChangeLogRepo          asset.StatusChangeRepository
	ComponentStatusChangeLogRepo component.ComponentChangeLogRepository

	// 软件排班相关
	SoftScheduleRepo scheduleRepo.SoftScheduleRepository

	// 硬件运维相关
	AcceptanceOrderRepo         acceptanceRepo.AcceptanceOrderRepository
	AcceptanceItemRepo          acceptanceRepo.AcceptanceItemRepository
	AcceptedComponentRepo       acceptanceRepo.AcceptedComponentRepository
	ComponentModelPNMappingRepo acceptanceRepo.ComponentModelPNMappingRepository
	rackingTicketRepo           racking2.AssetRackingTicketRepository

	// 软件运维相关
	LaunchTicketRepo  softwareRepo.LaunchRepository
	OfflineTicketRepo softwareRepo.OfflineRepository

	// 服务实例
	UserService           service.UserService
	RepairTicketService   service.RepairTicketService
	FaultTicketService    service.FaultTicketService
	OutboundTicketService cmdbOutbound.OutboundTicketService
	EntryTicketService    service.EntryTicketService
	EntryPersonService    service.EntryPersonService
	PurchaseService       purchaseService.PurchaseService
	ServiceSvc            serviceService.ServerService
	InboundService        inbound.InboundService
	InventoryService      cmdbInventory.InventoryService
	AssetSpareService     cmdbAsset.SpareService
	FileService           fileSvc.FileService
	ImportService         importSvc.ImportService
	SoftScheduleService   scheduleService.SoftScheduleService
	AcceptanceService     acceptanceServ.AcceptanceService
	ComponentService      component2.ServerComponentService
	ProductService        productService.ProductService
	DeviceService         cmdbAsset.DeviceService
	DeviceSearchService   cmdbAsset.DeviceSearchService
	RackingService        racking.RackingService
	InboundTicketService  service.InboundTicketService
	LaunchService         softwareService.LaunchService
	OfflineService        softwareService.OfflineService

	// Workflow活动实例
	PartInboundActivities       activities.PartiInboundActivity
	NewInboundActivities        activities.NewInboundActivity
	RepairInboundActivities     activities.RepairInboundActivity
	DismantledInboundActivities activities.DismantleInboundActivity
	DeviceInboundActivities     activities.DeviceInboundActivity
	PartOutboundActivities      cmdbActivities.OutboundPartActivities
	DeviceOutboundActivities    cmdbActivities.OutboundDeviceActivities
	AcceptanceOrderActivities   *hardwareMaintanceActivity.AcceptanceOrderActivity
	RackingTicketActivities     *hardwareMaintanceActivity.RackingTicketActivity
	LaunchTicketActivities      softwareMaintanceActivity.LaunchActivity
	OfflineTicketActivities     softwareMaintanceActivity.OfflineActivity
	ArrivalActivities           *arrivalActivity.ArrivalActivities
	ShipmentActivities          *shipmentActivity.ShipmentActivities
}

// initRepositoriesAndServices 初始化仓库和服务
func (w *Worker) initRepositoriesAndServices(db *gorm.DB) error {
	var repos repositories

	// 初始化CMDB相关仓库
	repos.PurchaseRepo = purchaseRepository.NewPurchaseRepository(db)
	repos.InboundRepo = cmdbRepo.NewInboundRepository(db)
	repos.InventoryRepo = inventory.NewInventoryRepository(db)
	repos.ServiceRepo = serviceReposity.NewServerRepository(db)
	repos.AssetSpareRepo = asset.NewSpareRepository(db)
	repos.ServerComponentRepo = component.NewServerComponentRepository(db)
	repos.ProductRepository = productRepo.NewProductRepository(db)
	repos.DeviceRepo = asset.NewDeviceRepository(db)
	repos.ResourceRepo = asset.NewResourceRepository(db)
	repos.StatusChangeLogRepo = asset.NewStatusChangeRepository(db)
	repos.ComponentStatusChangeLogRepo = component.NewComponentChangeLogRepository(db)

	// 初始化ticket相关仓库
	repos.EntryTicketRepo = repository.NewEntryTicketRepository(db)
	repos.FaultTicketRepo = repository.NewFaultTicketRepository(db)
	repos.RepairTicketRepo = repository.NewRepairTicketRepository(db)
	repos.RepairSelectionRepo = mysqlRepo.NewRepairSelectionRepository(db)
	repos.OutboundApprovalRepo = mysql.NewOutboundApprovalRepository(db)
	repos.CustomerApprovalRepo = mysqlRepo.NewCustomerApprovalRepository(db)
	repos.EntryApprovalRepo = mysqlRepo.NewEntryApprovalRepository(db)
	repos.VerificationRepo = mysqlRepo.NewVerificationRepository(db)
	repos.EntryPersonRepo = mysqlRepo.NewEntryPersonRepository(db)
	repos.InboundTicketRepo = repository.NewInboundTicketRepository(db, repos.InventoryRepo)
	repos.OutboundTicketRepo = outbound.NewOutboundTicketRepository(db)

	// 初始化软件排班相关仓库
	repos.SoftScheduleRepo = scheduleRepo.NewSoftScheRepository(db)

	// 初始化硬件运维相关仓库
	repos.AcceptanceOrderRepo = acceptanceRepo.NewAcceptanceOrderRepository(db)
	repos.AcceptanceItemRepo = acceptanceRepo.NewAcceptanceItemRepository(db)
	repos.AcceptedComponentRepo = acceptanceRepo.NewAcceptedComponentRepository(db)
	repos.ComponentModelPNMappingRepo = acceptanceRepo.NewComponentModelPNMappingRepository(db)
	repos.rackingTicketRepo = racking2.NewAssetRackingTicketRepository(db)

	// 初始化软件运维相关仓库
	repos.LaunchTicketRepo = softwareRepo.NewLaunchRepository(db)
	repos.OfflineTicketRepo = softwareRepo.NewOfflineRepository(db)

	// 初始化服务
	repos.UserService = service.NewUserService(nil) // 实际项目中应该传入真实的用户服务实现
	repos.PurchaseService = purchaseService.NewPurchaseService(repos.PurchaseRepo)
	repos.ServiceSvc = serviceService.NewServerService(repos.ServiceRepo)
	repos.InventoryService = cmdbInventory.NewInventoryService(repos.InventoryRepo, db)
	repos.InboundService = inbound.InitInboundService(repos.InboundRepo, repos.PurchaseService, repos.InventoryService, repos.InventoryRepo, repos.ServiceSvc)
	repos.FileService = fileSvc.NewFileService(db)
	repos.ImportService = importSvc.NewImportService(db)
	repos.SoftScheduleService = scheduleService.NewSoftScheService(repos.SoftScheduleRepo)
	repos.DeviceService = cmdbAsset.NewDeviceService(repos.DeviceRepo)
	repos.DeviceSearchService = cmdbAsset.NewDeviceSearchService(repos.DeviceRepo, repos.ResourceRepo, repos.AssetSpareRepo)
	repos.FileService = fileSvc.NewFileService(db)

	// 初始化维修单服务
	repos.RepairTicketService = service.NewRepairTicketService(
		repos.RepairTicketRepo,
		repos.FaultTicketRepo,
		nil,
		repos.UserService,
	)

	// 设置Temporal客户端
	if rs, ok := repos.RepairTicketService.(interface{ SetTemporalClient(client.Client) }); ok {
		rs.SetTemporalClient(w.temporalClient)
		w.logger.Info("成功设置维修单服务的Temporal客户端")
	} else {
		w.logger.Warn("无法设置维修单服务的Temporal客户端，子工作流可能无法自动启动")
	}

	// 初始化故障单服务
	repos.FaultTicketService = service.NewFaultTicketService(
		repos.FaultTicketRepo,
		repos.RepairSelectionRepo,
		repos.CustomerApprovalRepo,
		repos.VerificationRepo,
		w.temporalClient,
		w.logger,
		repos.RepairTicketService,
		repos.SoftScheduleService,
	)

	// 初始化入室单服务
	repos.EntryTicketService = service.NewEntryTicketService(
		repos.EntryTicketRepo,
		repos.EntryPersonRepo,
		repos.EntryApprovalRepo,
		w.temporalClient,
		w.logger,
	)

	// 初始化入室人员服务
	repos.EntryPersonService = service.NewEntryPersonService(repos.EntryPersonRepo, repos.EntryTicketRepo, w.logger)

	// 初始化库存和资产服务
	repos.InventoryService = cmdbInventory.NewInventoryService(repos.InventoryRepo, db)
	repos.AssetSpareService = cmdbAsset.NewSpareService(repos.AssetSpareRepo, repos.InventoryService, repos.DeviceService)

	repos.ComponentService = component2.NewServerComponentService(repos.ServerComponentRepo, repos.InventoryService, repos.AssetSpareService)
	repos.ProductService = productService.NewProductService(repos.ProductRepository)

	// 初始化出库单服务
	repos.OutboundTicketService = cmdbOutbound.NewOutboundTicketService(
		repos.OutboundTicketRepo,
		repos.OutboundApprovalRepo,
		repos.DeviceService,
		repos.ProductService,
		repos.InventoryService,
		repos.AssetSpareService,
		w.temporalClient,
		w.logger,
	)

	// 初始化验收工单服务
	repos.AcceptanceService = acceptanceServ.NewAcceptanceService(
		repos.AcceptanceOrderRepo,
		repos.AcceptanceItemRepo,
		repos.AcceptedComponentRepo,
		repos.ComponentModelPNMappingRepo,
		repos.ServerComponentRepo,
		repos.ComponentStatusChangeLogRepo,
		repos.DeviceRepo,
		repos.ResourceRepo,
		repos.StatusChangeLogRepo,
		w.logger,
		w.temporalClient)

	//  初始化上架工单服务
	repos.RackingService = racking.NewRackingService(repos.rackingTicketRepo, repos.ResourceRepo, repos.DeviceRepo, repos.StatusChangeLogRepo, w.logger, w.temporalClient)

	// 初始化软件工单服务
	repos.LaunchService = softwareService.NewLaunchService(repos.LaunchTicketRepo, w.temporalClient, w.logger)
	repos.OfflineService = softwareService.NewOfflineService(repos.OfflineTicketRepo, w.temporalClient, w.logger, db)

	// 初始化活动
	activities.InitEntryActivities(
		repos.EntryTicketRepo,
		repos.EntryTicketService,
		repos.EntryPersonRepo,
		repos.EntryPersonService,
		repos.EntryApprovalRepo,
	)

	// 初始化各种活动
	activities.InitActivities(
		repos.FaultTicketRepo,
		repos.FaultTicketService,
		repos.RepairTicketRepo,
		repos.RepairSelectionRepo,
		repos.CustomerApprovalRepo,
		repos.VerificationRepo,
		repos.RepairTicketService,
		repos.UserService,
		w.logger,
	)

	activities.InitRepairActivities(
		repos.RepairTicketRepo,
		repos.RepairTicketService,
		repos.UserService,
		w.logger,
		repos.FaultTicketRepo,
		repos.DeviceSearchService,
	)

	// 初始化飞书通知器（全局唯一初始化）
	w.logger.Info("开始初始化飞书通知器")
	activities.InitFeishuNotifier(
		w.config.Feishu.WebhookURL,
		w.config.Feishu.Secret,
		w.config.Feishu.TicketDetailUrlTemplate,
		w.config.Feishu.RepairTicketDetailUrlTemplate,
		w.config.Feishu.RepairProjectWebhooks,
		w.logger,
	)
	w.logger.Info("飞书通知器初始化完成")

	cmdbActivities.InitActivities(
		repos.OutboundTicketRepo,
		repos.OutboundTicketService,
		repos.OutboundApprovalRepo,
		repos.AssetSpareService,
		repos.InventoryService,
		w.logger,
	)

	// 初始化验收工单服务
	repos.AcceptanceOrderActivities = hardwareMaintanceActivity.InitAcceptanceOrderActivity(repos.AcceptanceService, repos.FileService, repos.ComponentService, repos.ProductService, repos.DeviceService, db)
	repos.RackingTicketActivities = hardwareMaintanceActivity.InitRackingTicketActivity(repos.RackingService, db)

	// 初始化软件工单活动
	repos.LaunchTicketActivities = softwareMaintanceActivity.NewLaunchActivities(db, repos.LaunchService, repos.LaunchTicketRepo, w.logger, repos.OfflineService)
	repos.OfflineTicketActivities = softwareMaintanceActivity.NewOfflineActivities(db, repos.OfflineTicketRepo, repos.OfflineService, w.logger)

	// 初始化到货管理活动
	arrivalRepo := newPurchaseRepo.NewArrivalRepository(db)
	contractRepo := newPurchaseRepo.NewPurchaseContractRepository(db)

	// 创建用户服务所需的依赖
	userRepo := userRepository.NewUserRepository(db)
	userSvc := userService.NewUserService(userRepo, w.config.JWT.SecretKey, time.Duration(w.config.JWT.Expires)*time.Minute, w.redisClient, w.logger)
	repos.ArrivalActivities = arrivalActivity.NewArrivalActivities(arrivalRepo, contractRepo, userSvc)

	// 初始化发货管理活动
	shipmentRepo := newPurchaseRepo.NewShipmentRepository(db)
	repos.ShipmentActivities = shipmentActivity.NewShipmentActivities(shipmentRepo, contractRepo, userSvc)

	// 项目模块
	projectRepo := newPurchaseRepo.NewProjectRepository(db)
	projectSvc := newPurchaseService.NewProjectService(projectRepo)

	// 供应商模块
	supplierRepo := newPurchaseRepo.NewSupplierRepository(db)
	supplierSvc := newPurchaseService.NewSupplierService(supplierRepo)

	// 公司模块
	companyRepo := newPurchaseRepo.NewCompanyRepository(db)
	companySvc := newPurchaseService.NewCompanyService(companyRepo)

	// 采购模块
	requestRepo := newPurchaseRepo.NewPurchaseRequestRepository(db)
	inquiryRepo := newPurchaseRepo.NewPurchaseInquiryRepository(db)
	requestSvc := newPurchaseService.NewPurchaseRequestService(requestRepo, w.temporalClient, userSvc, projectSvc)
	inquirySvc := newPurchaseService.NewPurchaseInquiryService(inquiryRepo, requestRepo, w.temporalClient, userSvc, projectSvc, supplierSvc)
	contractSvc := newPurchaseService.NewPurchaseContractService(contractRepo, w.temporalClient, userSvc, supplierSvc, projectSvc, companySvc, repos.ProductService, inquirySvc)

	// 初始化配件入库单服务
	repos.InboundTicketService = service.InitInboundTicketService(repos.InboundTicketRepo, repos.InboundService, repos.PurchaseService, repos.InventoryService, repos.ProductService, repos.AssetSpareService, repos.DeviceService, requestSvc, inquirySvc, contractSvc, w.temporalClient, w.logger)

	// 初始化入库通知
	w.logger.Info("成功设置飞书机器人")
	feishuBots := SetInboundBot(w.config.Feishu, w.logger)
	inboundNotifier := notifier.InitInboundNotifier(feishuBots, w.logger)

	// 初始化入库活动
	repos.PartInboundActivities = activities.InitPartInboundActivities(db, repos.InboundTicketService, repos.InboundRepo, repos.InventoryService, repos.FileService, inboundNotifier)

	repos.NewInboundActivities = activities.NewInboundActivities(
		db,
		repos.InboundRepo,
		repos.InboundService,
		repos.InboundTicketRepo,
		repos.FileService,
		repos.ImportService,
		inboundNotifier,
	)

	repos.RepairInboundActivities = activities.NewRepairInboundActivities(
		db,
		repos.InboundRepo,
		repos.InboundService,
		repos.InboundTicketRepo,
		repos.InventoryService,
		repos.FileService,
		inboundNotifier,
	)

	repos.DismantledInboundActivities = activities.NewdismantledInboundActivities(
		db,
		repos.InboundRepo,
		repos.InboundService,
		repos.InboundTicketRepo,
		repos.FileService,
		repos.ImportService,
		inboundNotifier,
	)
	repos.DeviceInboundActivities = activities.InitDeviceInboundActivities(db, repos.InboundRepo, repos.InboundTicketService, repos.InboundTicketRepo, repos.FileService, inboundNotifier)

	repos.PartOutboundActivities = cmdbActivities.InitPartOutboundActivities(db, repos.OutboundTicketService, repos.AssetSpareService, repos.InventoryService, repos.FileService, inboundNotifier)
	repos.DeviceOutboundActivities = cmdbActivities.InitDeviceOutboundActivities(db, repos.OutboundTicketService, repos.FileService, repos.InventoryService, inboundNotifier)

	// 注意：飞书通知器已在ticket模块中初始化，这里不需要重复初始化

	// 保存仓库和服务实例
	w.repositories = &repos
	return nil
}
