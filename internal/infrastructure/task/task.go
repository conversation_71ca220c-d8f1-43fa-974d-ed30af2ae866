package task

import (
	"context"
	"log"
	"time"

	assetRepo "backend/internal/modules/cmdb/repository/asset"
	inventoryRepo "backend/internal/modules/cmdb/repository/inventory"
	assetSvc "backend/internal/modules/cmdb/service/asset"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"

	"github.com/robfig/cron/v3"
	"gorm.io/gorm"
)

// TaskScheduler cron任务调度器
type TaskScheduler struct {
	cron      *cron.Cron
	isRunning bool
}

// NewTaskScheduler 创建任务调度器
func NewTaskScheduler() *TaskScheduler {
	// 创建一个定时任务管理器（使用东八区时间）
	c := cron.New(cron.WithLocation(time.FixedZone("CST", 8*60*60)))

	return &TaskScheduler{
		cron:      c,
		isRunning: false,
	}
}

// AddFunc 添加定时任务
func (s *TaskScheduler) AddFunc(spec string, cmd func()) error {
	_, err := s.cron.AddFunc(spec, cmd)
	return err
}

// Start 启动任务调度器
func (s *TaskScheduler) Start() {
	if s.isRunning {
		log.Printf("任务调度器已经在运行中，忽略此次启动请求")
		return
	}

	log.Printf("启动任务调度器")
	s.cron.Start()
	s.isRunning = true
}

// Stop 停止任务调度器
func (s *TaskScheduler) Stop() {
	if !s.isRunning {
		log.Printf("任务调度器已经停止，忽略此次停止请求")
		return
	}

	s.cron.Stop()
	s.isRunning = false
	log.Printf("任务调度器已停止")
}

// SetupInventorySync 设置库存同步定时任务
func SetupInventorySync(scheduler *TaskScheduler, spareService assetSvc.SpareService) {
	// 定义同步任务函数
	syncTask := func() {
		ctx := context.Background()
		//log.Printf("开始执行库存同步...")
		err := spareService.SyncInventoryWithSpareStatus(ctx)
		if err != nil {
			log.Printf("库存同步失败: %v", err)
			return
		}
		log.Printf("库存同步完成")
	}

	// 每3分钟执行一次库存同步
	err := scheduler.AddFunc("*/3 * * * *", syncTask)
	if err != nil {
		log.Printf("添加库存同步定时任务失败: %v", err)
	} else {
		log.Printf("库存同步定时任务已添加，将每3分钟执行一次")
	}

	// 启动时立即执行一次同步
	go syncTask()
}

// SetupTaskSchedulers 设置任务调度器
func SetupTaskSchedulers(db *gorm.DB, scheduler *TaskScheduler) {
	// 初始化各种服务
	// 备件仓库
	spareRepo := assetRepo.NewSpareRepository(db)

	// 库存仓库
	inventoryRepo := inventoryRepo.NewInventoryRepository(db)

	// 库存服务
	inventoryService := inventorySvc.NewInventoryService(inventoryRepo, db)

	// 资产设备仓库
	deviceRepo := assetRepo.NewDeviceRepository(db)
	// 资产设备服务
	deviceService := assetSvc.NewDeviceService(deviceRepo)

	// 备件服务
	spareService := assetSvc.NewSpareService(spareRepo, inventoryService,deviceService)

	// 设置库存同步任务
	SetupInventorySync(scheduler, spareService)
}
