package app

import (
	"backend/configs"
	"backend/internal/infrastructure/cache"
	"backend/internal/infrastructure/database"
	zapLog "backend/internal/infrastructure/logger"
	"database/sql"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// App 应用程序结构体
type App struct {
	DB         *gorm.DB
	Logger     *zap.Logger
	Config     *configs.Config
	RedisCache *cache.RedisClient
	sqlDB      *sql.DB // 用于关闭数据库连接
}

// NewApp 创建应用程序实例
func NewApp(config *configs.Config) (*App, error) {
	// 初始化日志
	logger, err := zapLog.NewLoggerFromConfig(&config.Logger)
	if err != nil {
		return nil, err
	}

	// 初始化数据库
	db := database.InitDB(config)

	// 获取sql.DB实例用于后续关闭
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// 初始化Redis客户端
	redisClient := cache.NewRedisClient(config.Redis)

	// 创建应用程序实例
	app := &App{
		DB:         db,
		Logger:     logger,
		Config:     config,
		RedisCache: redisClient,
		sqlDB:      sqlDB,
	}

	return app, nil
}

// Close 关闭应用程序资源
func (a *App) Close() error {
	// 关闭日志
	if err := a.Logger.Sync(); err != nil {
		return err
	}

	// 关闭Redis连接
	if a.RedisCache != nil {
		if err := a.RedisCache.Close(); err != nil {
			return err
		}
	}

	// 关闭数据库连接
	if a.sqlDB != nil {
		if err := a.sqlDB.Close(); err != nil {
			return err
		}
	}

	return nil
}
