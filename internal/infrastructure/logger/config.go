package zapLog

import (
	"os"
	"path/filepath"
	"strings"
	"time"

	"backend/configs"

	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// 自定义时间格式编码器
type customTimeEncoder struct {
	timeFormat string
}

func (t *customTimeEncoder) EncodeTime(ts time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(ts.Format(t.timeFormat))
}

// OptimizedLoggerConfig 返回一个优化的日志配置
func OptimizedLoggerConfig(config *configs.LogConfig) (*zap.Logger, error) {
	// 创建日志目录
	if err := ensureLogDirectory(config.OutputPaths); err != nil {
		return nil, err
	}
	if err := ensureLogDirectory(config.ErrorPaths); err != nil {
		return nil, err
	}

	// 解析日志级别
	var zapLevel zapcore.Level
	switch strings.ToLower(config.Level) {
	case "debug":
		zapLevel = zapcore.DebugLevel
	case "info":
		zapLevel = zapcore.InfoLevel
	case "warn":
		zapLevel = zapcore.WarnLevel
	case "error":
		zapLevel = zapcore.ErrorLevel
	default:
		zapLevel = zapcore.InfoLevel
	}

	// 配置基础编码器
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeDuration: zapcore.MillisDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 使用自定义时间编码器
	timeEncoder := &customTimeEncoder{timeFormat: "2006-01-02T15:04:05.000-0700"}
	encoderConfig.EncodeTime = timeEncoder.EncodeTime

	// 控制台格式
	consoleEncoderConfig := encoderConfig
	consoleEncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder // 大写无颜色

	// JSON格式
	jsonEncoderConfig := encoderConfig
	jsonEncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 创建编码器
	consoleEncoder := zapcore.NewConsoleEncoder(consoleEncoderConfig)
	fileEncoder := zapcore.NewJSONEncoder(jsonEncoderConfig)

	// 准备输出配置
	var cores []zapcore.Core

	// 控制台输出
	if config.Console {
		cores = append(cores, zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), zapLevel))
	}

	// 文件输出
	for _, path := range config.OutputPaths {
		if path != "stdout" && path != "stderr" {
			// 配置日志切割
			lumberjackLogger := &lumberjack.Logger{
				Filename:   path,
				MaxSize:    config.MaxSize,
				MaxBackups: config.MaxBackups,
				MaxAge:     config.MaxAge,
				Compress:   config.Compress,
			}
			cores = append(cores, zapcore.NewCore(fileEncoder, zapcore.AddSync(lumberjackLogger), zapLevel))
		}
	}

	// 错误日志输出
	for _, path := range config.ErrorPaths {
		if path == "stderr" {
			cores = append(cores, zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stderr), zapcore.ErrorLevel))
		} else if path != "stdout" {
			lumberjackLogger := &lumberjack.Logger{
				Filename:   path,
				MaxSize:    config.MaxSize,
				MaxBackups: config.MaxBackups,
				MaxAge:     config.MaxAge,
				Compress:   config.Compress,
			}
			cores = append(cores, zapcore.NewCore(fileEncoder, zapcore.AddSync(lumberjackLogger), zapcore.ErrorLevel))
		}
	}

	// 合并cores
	core := zapcore.NewTee(cores...)

	// 构建选项
	options := []zap.Option{
		zap.AddCaller(),
		zap.AddCallerSkip(1),
	}

	if config.Development {
		options = append(options, zap.Development())
	}

	// 返回日志器
	return zap.New(core, options...), nil
}

// 确保日志目录存在
func ensureLogDirectory(paths []string) error {
	for _, path := range paths {
		if path != "stdout" && path != "stderr" {
			dir := filepath.Dir(path)
			if _, err := os.Stat(dir); os.IsNotExist(err) {
				if err := os.MkdirAll(dir, 0750); err != nil {
					return err
				}
			}
		}
	}
	return nil
}
