package zapLog

import (
	"go.temporal.io/sdk/log"
	"go.uber.org/zap"
)

// temporalLogger 实现Temporal的log.Logger接口
type temporalLogger struct {
	logger *zap.Logger
}

// NewTemporalLogger 创建一个新的Temporal日志适配器
func NewTemporalLogger(logger *zap.Logger) log.Logger {
	return &temporalLogger{
		logger: logger.Named("temporal"),
	}
}

// Debug logs message at debug level
func (l *temporalLogger) Debug(msg string, keyvals ...interface{}) {
	l.logger.Sugar().Debugw(msg, keyvals...)
}

// Info logs message at info level
func (l *temporalLogger) Info(msg string, keyvals ...interface{}) {
	l.logger.Sugar().Infow(msg, keyvals...)
}

// Warn logs message at warn level
func (l *temporalLogger) Warn(msg string, keyvals ...interface{}) {
	l.logger.Sugar().Warnw(msg, keyvals...)
}

// Error logs message at error level
func (l *temporalLogger) Error(msg string, keyvals ...interface{}) {
	l.logger.Sugar().Errorw(msg, keyvals...)
}

// With returns a logger with keyvals added to it
func (l *temporalLogger) With(keyvals ...interface{}) log.Logger {
	return &temporalLogger{
		logger: l.logger.With(keyvalsToFields(keyvals...)...),
	}
}

// 将keyvals转换为zap.Field切片
func keyvalsToFields(keyvals ...interface{}) []zap.Field {
	if len(keyvals)%2 != 0 {
		return []zap.Field{zap.Any("ERROR", "Odd number of key-value pairs")}
	}

	fields := make([]zap.Field, 0, len(keyvals)/2)
	for i := 0; i < len(keyvals); i += 2 {
		key, ok := keyvals[i].(string)
		if !ok {
			key = "INVALID_KEY"
		}
		fields = append(fields, zap.Any(key, keyvals[i+1]))
	}
	return fields
}
