package export

import (
	"backend/internal/middleware"
	"backend/internal/modules/cmdb/repository/asset"
	inboundRepository "backend/internal/modules/cmdb/repository/inbound"
	inventoryRepository "backend/internal/modules/cmdb/repository/inventory"
	outboundRepository "backend/internal/modules/cmdb/repository/outbound"
	"backend/internal/modules/cmdb/repository/product"
	"backend/internal/modules/export/controller"
	"backend/internal/modules/export/repository"
	"backend/internal/modules/export/service"
	ticketRepository "backend/internal/modules/ticket/repository"
	"path/filepath"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RegisterExportModule 注册导出模块
func RegisterExportModule(router *gin.RouterGroup, db *gorm.DB) {
	// 初始化仓库
	exportRepo := repository.NewExportRepository(db)

	// 设置导出文件存储路径
	storagePath := filepath.Join("storage", "exports")
	productRepo := product.NewProductRepository(db)
	inboundRepo := inboundRepository.NewInboundRepository(db)
	warehouseRepo := asset.NewWarehouseRepository(db)
	outboundRope := outboundRepository.NewOutboundTicketRepository(db)
	inventoryRepo := inventoryRepository.NewInventoryRepository(db)
	inboundTicketRepo := ticketRepository.NewInboundTicketRepository(db, inventoryRepo)

	exportSvc := service.NewExportService(exportRepo, storagePath, productRepo, inboundTicketRepo, inboundRepo, warehouseRepo, outboundRope)

	// 初始化控制器
	exportCtrl := controller.NewExportController(exportSvc)

	// 注册路由
	apiGroup := router.Group("/")
	apiGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件
	exportCtrl.RegisterRoutes(apiGroup)
}

// Module 导出模块
type Module struct {
	db *gorm.DB

	// 服务
	exportService service.ExportService

	// 控制器
	exportController *controller.ExportController
}

// NewModule 创建导出模块
func NewModule(db *gorm.DB) *Module {
	return &Module{
		db: db,
	}
}

// Initialize 初始化模块
func (m *Module) Initialize() error {
	// 初始化仓库
	exportRepo := repository.NewExportRepository(m.db)
	productRepo := product.NewProductRepository(m.db)
	inboundRepo := inboundRepository.NewInboundRepository(m.db)
	warehouseRepo := asset.NewWarehouseRepository(m.db)
	outboundRepo := outboundRepository.NewOutboundTicketRepository(m.db)
	inventoryRepo := inventoryRepository.NewInventoryRepository(m.db)
	inboundTicketRepo := ticketRepository.NewInboundTicketRepository(m.db,inventoryRepo)

	// 设置导出文件存储路径
	storagePath := filepath.Join("storage", "exports")

	// 初始化服务和控制器
	m.exportService = service.NewExportService(exportRepo, storagePath, productRepo, inboundTicketRepo, inboundRepo, warehouseRepo, outboundRepo)
	m.exportController = controller.NewExportController(m.exportService)

	return nil
}

// RegisterRoutes 注册路由
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	// 使用全局认证中间件保护路由
	exportGroup := router.Group("/")
	exportGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件

	m.exportController.RegisterRoutes(exportGroup)
}
