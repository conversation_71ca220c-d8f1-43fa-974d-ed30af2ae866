package model

// 需要用到的工作表
const (
	PurchaseInboundSheet     = "采购入库表"
	NewPartInfoSheet         = "配件入库信息表"
	NewPartSheet             = "配件入库信息表"
	NewDeviceSheet           = "设备入库信息表"
	WarehouseSheet           = "仓库详情表"
	ProductSheet             = "规格详情表"
	ReturnRepairPartSheet    = "返修入库信息表"
	AssetSpare               = "配件详情表"
	DismantlePartdSheet      = "拆机配件入库信息表"
	PartStatusSheet          = "配件状态表"
	ReplacePartSheet         = "配件改配出库信息表"
	AllocatePartSheet        = "配件调拨出库信息表"
	ReturnRepairOutPartSheet = "配件返修出库信息表"
	RepairOutPartSheet       = "配件维修出库信息表"
	SellPartSheet            = "配件售卖出库信息表"
	RackDeviceSheet          = "设备上架信息表"
	AllocateDeviceSheet      = "设备调拨信息表"
)

// 模板名称
const (
	PruchaseInboundTemplate     = "purchase_inbound_template"       // 采购入库模板
	NewPartTemplate             = "new_part_template"               // 新购配件入库模板
	NewDeviceTemplate           = "new_device_template"             // 新购设备入库模板
	DismantledPartTemplate      = "dismantled_part_template"        // 拆机入库模板
	ReturnRepairPartTemplate    = "return_repair_part_template"     // 返修入库模板
	ReplacePartTemplate         = "replace_part_template"           // 配件改配出库模板
	AllocatePartTemplate        = "allocate_part_template"          // 配件调拨出库模板
	ReturnRepairOutPartTemplate = "return_repair_out_part_template" // 配件返修出库模板
	RepairOutPartTemplate       = "repair_out_part_template"        // 配件维修出库模板
	SellPartTemplate            = "sell_part_template"              // 配件售卖出库模板
	RackDeviceTemplate          = "rack_device_template"            // 上架出库模板
	AllocateDeviceTemplate      = "allocate_device_template"        // 设备调拨出库模板
)

// 模板格式
var (
	PurchaseInboundHeaders    = []string{"PN号码", "品牌", "数量", "PN-品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID"}
	ProductHeaders            = []string{"PN号码", "品牌", "PN-品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID"}
	NewPartHeader             = []string{"序号", "SN", "PN号码", "品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID", "详情ID"}
	DismantledPartHeader      = []string{"序号", "配件SN", "主设备SN", "配件状态", "PN号码", "品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID", "配件状态映射", "详情ID"}
	ReturnRepairPartHeader    = []string{"序号", "原SN", "维修类型", "换新SN", "配件状态", "PN号码", "品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID", "维修类型映射", "配件状态映射", "详情ID"}
	ReplacePartHeader         = []string{"配件SN", "服务器SN", "PN号码", "品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID", "对应ID"}
	AllocatePartHeader        = []string{"配件SN", "PN号码", "品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID", "对应ID"}
	ReturnRepairOutPartHeader = []string{"配件SN", "PN号码", "品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID", "对应ID"}

	// 设备出库模板
	NewDeviceHeader      = []string{"序号", "SN", "PN号码", "品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID", "详情ID"}
	RackDeviceHeader     = []string{"序号", "SN", "PN号码", "品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID", "详情ID"}
	AllocateDeviceHeader = []string{"序号", "SN", "PN号码", "品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID", "详情ID"}
	DeviceInboundHeader  = []string{"序号", "SN", "PN号码", "品牌", "物料类型", "产品类别", "规格详情", "型号", "规格ID", "详情ID"}

	// 其他
	PartStatusHeader = []string{"状态映射", "配件状态"}
)
