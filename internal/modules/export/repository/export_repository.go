package repository

import (
	"context"
	"strings"

	"gorm.io/gorm"
)

// FieldMapping 表示字段映射关系
type FieldMapping map[string]string

// TableMappings 存储各表的字段映射关系
var TableMappings = map[string]FieldMapping{
	"fault_tickets": {
		// 前端字段名: 数据库字段名
		"id":                    "id",
		"title":                 "title",
		"ticketNo":              "ticket_no",
		"deviceSN":              "device_sn",
		"resource_identifier":   "resource_identifier",
		"faultDescription":      "fault_description",
		"status":                "status",
		"priority":              "priority",
		"faultType":             "fault_type",
		"fault_detail_type":     "fault_detail_type",
		"repairMethod":          "repair_method",
		"source":                "source",
		"componentSN":           "component_sn",
		"componentType":         "component_type",
		"reporterName":          "reporter_name",
		"assignedTo":            "assigned_to",
		"repairTicketID":        "repair_ticket_id",
		"is_frequent_fault":     "is_frequent_fault",
		"count_in_sla":          "count_in_sla",
		"creationTime":          "creation_time",
		"acknowledgeTime":       "acknowledge_time",
		"assignmentTime":        "assignment_time",
		"customerApprovalTime":  "customer_approval_time",
		"actualFixTime":         "actual_fix_time",
		"verificationStartTime": "verification_start_time",
		"verificationEndTime":   "verification_end_time",
		"closeTime":             "close_time",
	},
	// 可以添加其他表的映射...
}

// ExportRepository 导出仓库接口
type ExportRepository interface {
	// 获取指定ID的数据
	GetData(ctx context.Context, tableName string, ids []int64, condition string) ([]map[string]interface{}, error)
	// 获取所有数据
	GetAllData(ctx context.Context, tableName string, condition string) ([]map[string]interface{}, error)
}

// exportRepository 导出仓库实现
type exportRepository struct {
	db *gorm.DB
}

// NewExportRepository 创建导出仓库
func NewExportRepository(db *gorm.DB) ExportRepository {
	return &exportRepository{db: db}
}

// mapFields 将前端字段映射到数据库字段
// 目前只在GetData和GetAllData中使用了mapResultFields，但未使用此函数
// 添加//nolint:unused注释忽略未使用警告
//
//nolint:unused
func (r *exportRepository) mapFields(tableName string, frontendFields []string) []string {
	dbFields := make([]string, len(frontendFields))

	// 获取表的字段映射，如果不存在则使用原始字段名
	mapping, ok := TableMappings[tableName]

	for i, field := range frontendFields {
		if ok {
			// 如果有映射关系，使用映射后的字段名
			if dbField, exists := mapping[field]; exists {
				dbFields[i] = dbField
			} else {
				// 如果没有映射关系，尝试将驼峰命名转换为下划线命名
				dbFields[i] = camelToSnake(field)
			}
		} else {
			// 如果没有该表的映射关系，尝试将驼峰命名转换为下划线命名
			dbFields[i] = camelToSnake(field)
		}
	}

	return dbFields
}

// camelToSnake 将驼峰命名转换为下划线命名
// 由mapFields函数调用，添加//nolint:unused注释忽略未使用警告
//
//nolint:unused
func camelToSnake(s string) string {
	var result strings.Builder
	for i, r := range s {
		if i > 0 && 'A' <= r && r <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

// mapResultFields 将数据库字段映射回前端字段
func (r *exportRepository) mapResultFields(tableName string, data []map[string]interface{}) []map[string]interface{} {
	mapping, ok := TableMappings[tableName]
	if !ok {
		return data
	}

	// 反向映射(数据库字段名 -> 前端字段名)
	reverseMapping := make(map[string]string)
	for frontend, db := range mapping {
		reverseMapping[db] = frontend
	}

	mappedData := make([]map[string]interface{}, len(data))
	for i, row := range data {
		mappedRow := make(map[string]interface{})
		for dbField, value := range row {
			if frontendField, exists := reverseMapping[dbField]; exists {
				mappedRow[frontendField] = value
			} else {
				// 如果没有映射，保持原样
				mappedRow[dbField] = value
			}
		}
		mappedData[i] = mappedRow
	}

	return mappedData
}

// GetData 获取指定ID的数据
func (r *exportRepository) GetData(ctx context.Context, tableName string, ids []int64, condition string) ([]map[string]interface{}, error) {
	// 使用动态表名
	query := r.db.WithContext(ctx).Table(tableName).Where("id IN ?", ids)

	// 添加额外的查询条件
	if condition != "" {
		query = query.Where(condition)
	}

	var results []map[string]interface{}
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}

	// 将数据库字段映射回前端字段
	return r.mapResultFields(tableName, results), nil
}

// GetAllData 获取所有数据
func (r *exportRepository) GetAllData(ctx context.Context, tableName string, condition string) ([]map[string]interface{}, error) {
	// 使用动态表名
	query := r.db.WithContext(ctx).Table(tableName)

	// 添加额外的查询条件
	if condition != "" {
		query = query.Where(condition)
	}

	var results []map[string]interface{}
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}

	// 将数据库字段映射回前端字段
	return r.mapResultFields(tableName, results), nil
}
