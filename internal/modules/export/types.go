package export

import (
	"time"
)

// Field 导出字段定义
type Field struct {
	Field string `json:"field"`
	Title string `json:"title"`
}

// ExportRequest 导出请求
type ExportRequest struct {
	Mode      string  `json:"mode"`      // 导出模式：all/selected
	IDs       []int64 `json:"ids"`       // 选定的ID列表
	Fields    []Field `json:"fields"`    // 导出字段
	Filename  string  `json:"filename"`  // 文件名
	SheetName string  `json:"sheetName"` // 工作表名称
	IsHeader  bool    `json:"isHeader"`  // 是否包含表头
}

// ExportResponse 导出响应
type ExportResponse struct {
	FileURL string    `json:"fileUrl"` // 文件下载URL
	Expired time.Time `json:"expired"` // 过期时间
}
