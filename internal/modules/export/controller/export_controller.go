package controller

import (
	"backend/internal/modules/export/model"
	"backend/internal/modules/export/service"
	"backend/response"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// ExportController 导出控制器
type ExportController struct {
	service service.ExportService
}

// NewExportController 创建导出控制器
func NewExportController(service service.ExportService) *ExportController {
	return &ExportController{service: service}
}

// RegisterRoutes 注册路由
func (c *ExportController) RegisterRoutes(router *gin.RouterGroup) {
	exportRouter := router.Group("")
	{
		exportRouter.POST("/export/export", c.Export)
		exportRouter.GET("/download/:filename", c.DownloadFile)
	}
	// 入库路由
	inboundExportGroup := exportRouter.Group("/export")
	{
		inboundExportGroup.GET("/template", c.ExportInboundTemplate)                // 导出模板
		inboundExportGroup.GET("/template/:InboundNo", c.ExportInboundTemplateByNo) // 根据工单号导出相应填写模板
	}
}

// Export 处理导出请求
// @Summary 导出数据
// @Description 导出数据到Excel文件
// @Tags 导出
// @Accept json
// @Produce json
// @Param request body model.ExportRequest true "导出请求参数"
// @Success 200 {object} response.ResponseStruct{data=model.ExportResponse}
// @Failure 400 {object} response.ResponseStruct
// @Router /export [post]
func (c *ExportController) Export(ctx *gin.Context) {
	var req model.ExportRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求参数")
		return
	}

	// 验证必填字段
	if req.TableName == "" {
		response.Fail(ctx, http.StatusBadRequest, "表名不能为空")
		return
	}

	if len(req.Fields) == 0 {
		response.Fail(ctx, http.StatusBadRequest, "导出字段不能为空")
		return
	}

	resp, err := c.service.Export(ctx, &req)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "导出失败: "+err.Error())
		return
	}

	response.Success(ctx, resp, "导出成功")
}

// DownloadFile 下载导出的文件
// @Summary 下载导出文件
// @Description 下载已导出的Excel文件
// @Tags 导出
// @Produce octet-stream
// @Param filename path string true "文件名"
// @Success 200 {file} binary "文件内容"
// @Failure 404 {object} response.ResponseStruct "文件不存在"
// @Router /export/download/{filename} [get]
func (c *ExportController) DownloadFile(ctx *gin.Context) {
	filename := ctx.Param("filename")
	if filename == "" {
		response.Fail(ctx, http.StatusBadRequest, "文件名不能为空")
		return
	}

	// 构建文件路径
	filePath := filepath.Join("storage", "exports", filename)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		response.Fail(ctx, http.StatusNotFound, "文件不存在")
		return
	}

	// 设置响应头，让浏览器将响应作为文件下载处理
	ctx.Header("Content-Description", "File Transfer")
	ctx.Header("Content-Disposition", "attachment; filename="+filename)
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Header("Cache-Control", "no-cache")

	// 添加defer函数，在文件下载完成后删除文件
	defer func() {
		// 延迟200毫秒确保文件内容完全发送
		time.Sleep(200 * time.Millisecond)
		if err := os.Remove(filePath); err != nil {
			// 仅记录错误，不影响下载
			fmt.Printf("文件删除失败: %v\n", err)
		} else {
			fmt.Printf("已删除文件: %s\n", filename)
		}
	}()

	// 提供文件下载
	ctx.File(filePath)
}

// ExportInboundTemplate 导出模板统一接口
func (c *ExportController) ExportInboundTemplate(ctx *gin.Context) {
	var outboundID uint
	TemplateType := ctx.Query("template_type")
	InboundNo := ctx.Query("inbound_no")
	OutboundID := ctx.Query("outbound_id")
	if OutboundID != "" {
		outboundIDUint64, err := strconv.ParseUint(OutboundID, 10, 64)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "")
			return
		}
		outboundID = uint(outboundIDUint64)
	}

	if TemplateType == "" {
		response.Fail(ctx, http.StatusBadRequest, "请选择模板类型")
		return
	}

	// 获取文件模板
	fileName, err := c.service.ExportInboundTemplate(ctx, TemplateType, InboundNo, outboundID)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	filePath := filepath.Join("storage", "exports", fileName)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		response.Fail(ctx, http.StatusNotFound, "文件不存在")
		return
	}

	response.Success(ctx, gin.H{
		"fileName": fileName,
		"filePath": filePath,
	}, "获取文件成功")
}

func (c *ExportController) ExportInboundTemplateByNo(ctx *gin.Context) {
	InboundNo := ctx.Param("InboundNo")
	if InboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "InboundNo为空")
		return
	}
}
