package service

import (
	"backend/internal/modules/software_maintenance/model"
	launch3 "backend/internal/modules/software_maintenance/repository"
	"backend/internal/modules/software_maintenance/workflow"

	common1 "backend/internal/modules/software_maintenance/common"
	"context"
	"fmt"
	"time"

	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

type LaunchService interface {
	ListLaunchTickets(ctx context.Context, page, pageSize int, ticketNo, status, title string) ([]*model.SoftwareLaunchTicket, int64, error)
	CreateLaunchTicket(ctx context.Context, launchTicket *model.SoftwareLaunchTicket) error
	GetLaunchTicketByID(ctx context.Context, id uint) (*model.SoftwareLaunchTicket, error)
	GetLaunchTicketByNo(ctx context.Context, ticketNo string) (*model.SoftwareLaunchTicket, error)
	InitLaunchTicketAndHistory(ctx context.Context, launchTicket *model.SoftwareLaunchTicket, launchHistory *model.LaunchHistory) (*common1.LaunchWorkflowInput, error)
	StartLaunchWorkflow(ctx context.Context, input *common1.LaunchWorkflowInput) error
	GetLaunchTicketStatusHistory(ctx context.Context, ticketID uint) ([]*model.LaunchHistory, error)
	TriggerWorkflowStage(ctx context.Context, ticketID uint, LaunchTicketNo string, triggers model.LaunchTrigger) error
}

type launchService struct {
	repo           launch3.LaunchRepository
	temporalClient client.Client
	logger         *zap.Logger
}

func NewLaunchService(repo launch3.LaunchRepository, temporalClient client.Client, logger *zap.Logger) LaunchService {
	return &launchService{
		repo:           repo,
		temporalClient: temporalClient,
		logger:         logger,
	}
}

// CreateLaunchTicket 创建上线工单
func (s *launchService) CreateLaunchTicket(ctx context.Context, launchTicket *model.SoftwareLaunchTicket) error {
	// 创建历史记录
	history := &model.LaunchHistory{
		SoftwareLaunchID: launchTicket.LaunchTicketNo,
		NewStatus:        launchTicket.Status,
		OperatorID:       launchTicket.ApplicantID,
		OperatorName:     launchTicket.ApplicantName,
		OperationTime:    time.Now(),
		Stage:            launchTicket.Stage,
	}

	// 初始化工单和历史记录
	workflowInput, err := s.InitLaunchTicketAndHistory(ctx, launchTicket, history)
	if err != nil {
		return err
	}

	// 启动工作流
	if err := s.StartLaunchWorkflow(ctx, workflowInput); err != nil {
		return err
	}

	return nil
}

// InitLaunchTicketAndHistory 初始化工单和历史记录
func (s *launchService) InitLaunchTicketAndHistory(ctx context.Context, launchTicket *model.SoftwareLaunchTicket, launchHistory *model.LaunchHistory) (*common1.LaunchWorkflowInput, error) {
	// 在数据库中初始化工单和历史记录
	if err := s.repo.InitLaunchTicket(ctx, launchTicket, launchHistory); err != nil {
		return nil, fmt.Errorf("初始化软件上线工单失败: %w", err)
	}

	// 创建工作流输入
	workflowInput := &common1.LaunchWorkflowInput{
		LaunchTicketID:      launchTicket.ID,
		LaunchNo:            launchTicket.LaunchTicketNo,
		RequireVerification: true, // 默认需要验证
	}

	return workflowInput, nil
}

// StartLaunchWorkflow 启动软件上线工作流
func (s *launchService) StartLaunchWorkflow(ctx context.Context, input *common1.LaunchWorkflowInput) error {
	// 设置工作流选项
	options := client.StartWorkflowOptions{
		ID:        fmt.Sprintf("Software_Launch_ticket_%d", input.LaunchTicketID),
		TaskQueue: common1.LaunchTaskQueue, // 定义一个专门的任务队列
	}

	// 启动工作流
	_, err := s.temporalClient.ExecuteWorkflow(ctx, options, workflow.SoftwareLaunchWorkflow, *input)
	if err != nil {
		s.logger.Error("启动软件上线工作流失败",
			zap.Error(err),
			zap.Uint("ticketID", input.LaunchTicketID))
		return fmt.Errorf("启动软件上线工作流失败: %w", err)
	}

	s.logger.Info("成功启动软件上线工作流",
		zap.String("workflowID", options.ID))

	return nil
}

// ListLaunchTickets 分页获取上线工单列表
func (s *launchService) ListLaunchTickets(ctx context.Context, page, pageSize int, ticketNo, status, title string) ([]*model.SoftwareLaunchTicket, int64, error) {
	return s.repo.List(ctx, page, pageSize, ticketNo, status, title)
}

// GetLaunchTicketByID 根据ID获取上线工单
func (s *launchService) GetLaunchTicketByID(ctx context.Context, id uint) (*model.SoftwareLaunchTicket, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *launchService) GetLaunchTicketStatusHistory(ctx context.Context, id uint) ([]*model.LaunchHistory, error) {
	// 获取历史记录
	histories, err := s.repo.GetLaunchStatusHistory(ctx, id)
	if err != nil {
		s.logger.Error("获取工单历史记录失败",
			zap.Error(err))
		return nil, fmt.Errorf("获取工单历史记录失败: %w", err)
	}
	s.logger.Info("成功获取入库工单历史记录",
		zap.Int("historyCount", len(histories)))

	return histories, nil
}

func (s *launchService) GetLaunchTicketByNo(ctx context.Context, ticketNo string) (*model.SoftwareLaunchTicket, error) {
	return s.repo.GetByNo(ctx, ticketNo)
}

func (s *launchService) TriggerWorkflowStage(ctx context.Context, ticketID uint, LaunchTicketNo string, triggers model.LaunchTrigger) error {
	// 验证工作流阶段参数
	if triggers.Status == "" {
		return fmt.Errorf("工作流状态不能为空")
	}
	// 构造工作流ID
	workflowID := fmt.Sprintf("Software_Launch_ticket_%d", ticketID)

	// 构造工作流控制信号
	signal := common1.LaunchSignal{
		Status:          triggers.Status,
		OperatorID:      triggers.OperatorID,
		OperatorName:    triggers.OperatorName,
		RequiredHandler: triggers.RequiredHandler,
		Comments:        triggers.Comments,
		Data:            triggers.Data,
	}

	s.logger.Info("准备更新软件上线工作流状态",
		zap.String("workflowID", workflowID),
		zap.String("stage", triggers.Stage),
		zap.String("status", triggers.Status),
		zap.String("operatorName", triggers.OperatorName),
		zap.Uint("ticketID", ticketID))

	// 更新工作流状态
	updateOptions := client.UpdateWorkflowOptions{
		WorkflowID:   workflowID,
		UpdateName:   common1.SoftwareLaunchUpdateSignal,
		WaitForStage: client.WorkflowUpdateStageCompleted,
		Args:         []interface{}{signal},
	}

	updateHandle, err := s.temporalClient.UpdateWorkflow(ctx, updateOptions)
	if err != nil {
		s.logger.Error("更新软件上线工作流状态失败",
			zap.Error(err),
			zap.String("workflowID", workflowID),
			zap.String("stage", triggers.Stage),
			zap.String("status", triggers.Status),
			zap.String("operatorName", triggers.OperatorName),
			zap.Uint("ticketID", ticketID))
		return fmt.Errorf("更新软件上线工作流状态失败: %w", err)
	}

	err = updateHandle.Get(ctx, nil)
	if err != nil {
		s.logger.Error("获取更新结果失败", zap.Error(err))
		return err
	}

	s.logger.Info("成功更新软件上线工作流",
		zap.String("workflowID", workflowID),
		zap.String("stage", triggers.Stage),
		zap.String("status", triggers.Status),
		zap.Uint("ticketID", ticketID))

	return nil
}
