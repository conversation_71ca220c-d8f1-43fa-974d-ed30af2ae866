package service

import (
	"backend/internal/modules/cmdb/model/asset"
	common1 "backend/internal/modules/software_maintenance/common"
	"backend/internal/modules/software_maintenance/model"
	launch3 "backend/internal/modules/software_maintenance/repository"
	"backend/internal/modules/software_maintenance/workflow"
	"context"
	"fmt"
	"time"

	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type OfflineService interface {
	ListOfflineTickets(ctx context.Context, page, pageSize int, ticketNo, status, title string) ([]*model.SoftwareOfflineTicket, int64, error)
	CreateOfflineTicket(ctx context.Context, offlineTicket *model.SoftwareOfflineTicket) error
	StartOfflineWorkflow(ctx context.Context, input *common1.OfflineWorkflowInput) error
	GetOfflineTicketByID(ctx context.Context, id uint) (*model.SoftwareOfflineTicket, error)
	InitOfflineTicketAndHistory(ctx context.Context, offlineTicket *model.SoftwareOfflineTicket, offlineHistory *model.OfflineHistory) (*common1.OfflineWorkflowInput, error)
	TriggerWorkflowStage(ctx context.Context, ticketID uint, triggers model.OfflineTrigger) error
	GetOfflineTicketStatusHistory(ctx context.Context, ticketID uint) ([]*model.OfflineHistory, error)
	RecordAssetStatusChange(ctx context.Context, sn string, oldAssetStatus, newAssetStatus, oldBizStatus, newBizStatus, workflow, oldHardwareStatus, newHardwareStatus string, ticketNO, source, title string, operatorID uint, operatorName string) error
}

// OfflineService 实现OfflineService接口
type offlineService struct {
	repo           launch3.OfflineRepository
	temporalClient client.Client
	logger         *zap.Logger
	db             *gorm.DB
}

func NewOfflineService(repo launch3.OfflineRepository, temporalClient client.Client, logger *zap.Logger, db *gorm.DB) OfflineService {
	return &offlineService{
		repo:           repo,
		temporalClient: temporalClient,
		logger:         logger,
		db:             db,
	}
}

// ListOfflineTickets 分页获取下线工单列表
func (s *offlineService) ListOfflineTickets(ctx context.Context, page, pageSize int, ticketNo, status, title string) ([]*model.SoftwareOfflineTicket, int64, error) {
	return s.repo.List(ctx, page, pageSize, ticketNo, status, title)
}

func (s *offlineService) CreateOfflineTicket(ctx context.Context, offlineTicket *model.SoftwareOfflineTicket) error {
	// 创建历史记录
	history := &model.OfflineHistory{
		SoftwareOfflineID: offlineTicket.ID,
		OfflineTicketNo:   offlineTicket.OfflineTicketNo,
		NewStatus:         offlineTicket.Status,
		OperatorID:        offlineTicket.ApplicantID,
		OperatorName:      offlineTicket.ApplicantName,
		OperationTime:     time.Now(),
		Stage:             offlineTicket.Stage,
	}
	// 初始化工单和历史记录
	workflowInput, err := s.InitOfflineTicketAndHistory(ctx, offlineTicket, history)
	if err != nil {
		return err
	}

	// 启动工作流
	if err := s.StartOfflineWorkflow(ctx, workflowInput); err != nil {
		return err
	}

	return nil

}

// InitOfflineTicketAndHistory 初始化工单和历史记录
func (s *offlineService) InitOfflineTicketAndHistory(ctx context.Context, offlineTicket *model.SoftwareOfflineTicket, offlineHistory *model.OfflineHistory) (*common1.OfflineWorkflowInput, error) {
	// 在数据库中初始化工单和历史记录
	if err := s.repo.InitOfflineTicket(ctx, offlineTicket, offlineHistory); err != nil {
		return nil, fmt.Errorf("初始化软件上线工单失败: %w", err)
	}

	// 创建工作流输入
	workflowInput := &common1.OfflineWorkflowInput{
		OfflineTicketID:     offlineTicket.ID,
		OfflineNo:           offlineTicket.OfflineTicketNo,
		RequireVerification: true, // 默认需要验证
	}

	return workflowInput, nil
}

// StartOfflineWorkflow  启动软件下线工作流
func (s *offlineService) StartOfflineWorkflow(ctx context.Context, input *common1.OfflineWorkflowInput) error {
	// 设置工作流选项
	options := client.StartWorkflowOptions{
		ID:        fmt.Sprintf("Software_Offline_ticket_%d", input.OfflineTicketID),
		TaskQueue: common1.OfflineTaskQueue, // 定义一个专门的任务队列
	}

	// 启动工作流
	_, err := s.temporalClient.ExecuteWorkflow(ctx, options, workflow.SoftwareOfflineWorkflow, *input)
	if err != nil {
		s.logger.Error("启动软件下线工作流失败",
			zap.Error(err),
			zap.Uint("ticketID", input.OfflineTicketID))
		return fmt.Errorf("启动软件下线工作流失败: %w", err)
	}

	s.logger.Info("成功启动软件下线工作流",
		zap.String("workflowID", options.ID))

	return nil
}

// GetOfflineTicketByID 根据ID获取上线工单
func (s *offlineService) GetOfflineTicketByID(ctx context.Context, id uint) (*model.SoftwareOfflineTicket, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *offlineService) GetOfflineTicketStatusHistory(ctx context.Context, id uint) ([]*model.OfflineHistory, error) {
	// 获取历史记录
	histories, err := s.repo.GetOfflineStatusHistory(ctx, id)
	if err != nil {
		s.logger.Error("获取工单历史记录失败",
			zap.Error(err))
		return nil, fmt.Errorf("获取工单历史记录失败: %w", err)
	}
	s.logger.Info("成功获取下线工单历史记录",
		zap.Int("historyCount", len(histories)))

	return histories, nil
}

// TriggerWorkflowStage 触发工作流阶段
func (s *offlineService) TriggerWorkflowStage(ctx context.Context, ticketID uint, triggers model.OfflineTrigger) error {
	// 验证工作流阶段参数
	if triggers.Status == "" {
		return fmt.Errorf("工作流状态不能为空")
	}
	// 构造工作流ID
	workflowID := fmt.Sprintf("Software_Offline_ticket_%d", ticketID)

	// 构造工作流控制信号
	signal := common1.OfflineSignal{
		Status:          triggers.Status,
		OperatorID:      triggers.OperatorID,
		OperatorName:    triggers.OperatorName,
		RequiredHandler: triggers.RequiredHandler,
		Comments:        triggers.Comments,
	}

	s.logger.Info("准备更新软件下线工作流状态",
		zap.String("workflowID", workflowID),
		zap.String("stage", triggers.Stage),
		zap.String("status", triggers.Status),
		zap.String("operatorName", triggers.OperatorName),
		zap.Uint("ticketID", ticketID))

	// 更新工作流状态
	updateOptions := client.UpdateWorkflowOptions{
		WorkflowID:   workflowID,
		UpdateName:   common1.SoftwareOfflineUpdateSignal,
		WaitForStage: client.WorkflowUpdateStageCompleted,
		Args:         []interface{}{signal},
	}

	updateHandle, err := s.temporalClient.UpdateWorkflow(ctx, updateOptions)
	if err != nil {
		s.logger.Error("更新软件下线工作流状态失败",
			zap.Error(err),
			zap.String("workflowID", workflowID),
			zap.String("stage", triggers.Stage),
			zap.String("status", triggers.Status),
			zap.String("operatorName", triggers.OperatorName),
			zap.Uint("ticketID", ticketID))
		return fmt.Errorf("更新软件下线工作流状态失败: %w", err)
	}

	err = updateHandle.Get(ctx, nil)
	if err != nil {
		s.logger.Error("获取更新结果失败", zap.Error(err))
		return err
	}

	s.logger.Info("成功更新软件下线工作流",
		zap.String("workflowID", workflowID),
		zap.String("stage", triggers.Stage),
		zap.String("status", triggers.Status),
		zap.Uint("ticketID", ticketID))

	return nil
}

// RecordAssetStatusChange 记录资产状态变更
func (s *offlineService) RecordAssetStatusChange(ctx context.Context, sn string, oldAssetStatus, newAssetStatus, oldBizStatus, newBizStatus, workflow, oldHardwareStatus, newHardwareStatus string, ticketNO, source, title string, operatorID uint, operatorName string) error {
	// 参数校验
	if sn == "" {
		return fmt.Errorf("SN不能为空")
	}

	if oldAssetStatus == "" || newAssetStatus == "" {
		return fmt.Errorf("资产状态不能为空")
	}

	// 查询资产ID
	var assetID uint
	err := s.db.WithContext(ctx).Table("asset_devices").
		Where("sn = ?", sn).
		Select("id").
		Scan(&assetID).Error

	if err != nil {
		s.logger.Error("查询资产ID失败", zap.Error(err), zap.String("sn", sn))
		return fmt.Errorf("查询资产ID失败: %w", err)
	}

	if assetID == 0 {
		return fmt.Errorf("未找到SN为 %s 的资产", sn)
	}
	// 根据旧状态确定Source字段的值

	// 创建状态变更记录
	statusChangeLog := &asset.StatusChangeLog{
		AssetID:           assetID,
		OldAssetStatus:    oldAssetStatus,
		NewAssetStatus:    newAssetStatus,
		OldBizStatus:      oldBizStatus,
		NewBizStatus:      newBizStatus,
		WorkflowID:        workflow,
		ChangeReason:      title,
		OldHardwareStatus: oldHardwareStatus,
		NewHardwareStatus: newHardwareStatus,
		Source:            source,
		TicketNo:          ticketNO,
		OperatorID:        operatorID,
		OperatorName:      operatorName,
	}

	// 写入数据库
	err = s.db.WithContext(ctx).Create(statusChangeLog).Error
	if err != nil {
		s.logger.Error("创建资产状态变更记录失败",
			zap.Error(err),
			zap.String("sn", sn),
			zap.String("oldAssetStatus", oldAssetStatus),
			zap.String("newAssetStatus", newAssetStatus))
		return fmt.Errorf("创建资产状态变更记录失败: %w", err)
	}

	s.logger.Info("成功记录资产状态变更",
		zap.String("sn", sn),
		zap.String("oldAssetStatus", oldAssetStatus),
		zap.String("newAssetStatus", newAssetStatus),
		zap.Uint("assetID", assetID))

	return nil
}
