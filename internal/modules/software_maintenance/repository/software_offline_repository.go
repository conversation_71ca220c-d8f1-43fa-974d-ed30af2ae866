package repository

import (
	common1 "backend/internal/modules/software_maintenance/common"
	"backend/internal/modules/software_maintenance/model"
	"context"
	"fmt"

	"gorm.io/gorm"
)

type OfflineRepository interface {
	List(ctx context.Context, page, pageSize int, ticketNo, status, title string) ([]*model.SoftwareOfflineTicket, int64, error)
	GetByID(ctx context.Context, id uint) (*model.SoftwareOfflineTicket, error)
	InitOfflineTicket(ctx context.Context, ticket *model.SoftwareOfflineTicket, history *model.OfflineHistory) error
	GetOfflineStatusHistory(ctx context.Context, id uint) ([]*model.OfflineHistory, error)
	GetDevicesByOfflineTicketNo(ctx context.Context, offlineNo string) ([]model.OfflineDevice, error)
}

type offlineRepository struct {
	db *gorm.DB
}

func NewOfflineRepository(db *gorm.DB) OfflineRepository {
	return &offlineRepository{db: db}
}

// List 分页获取软件下线工单列表
func (r *offlineRepository) List(ctx context.Context, page, pageSize int, ticketNo, status, title string) ([]*model.SoftwareOfflineTicket, int64, error) {
	var tickets []*model.SoftwareOfflineTicket
	var total int64
	// 快速检查：如果工单编号存在，先快速检查是否有完全匹配的记录
	if ticketNo != "" {
		var count int64
		// 快速检查是否有完全匹配的工单编号
		quickCheck := r.db.WithContext(ctx).Model(&model.SoftwareOfflineTicket{}).
			Where("offline_ticket_no = ?", ticketNo).
			Count(&count)

		if quickCheck.Error != nil {
			return nil, 0, fmt.Errorf("快速检查工单编号失败: %w", quickCheck.Error)
		}

		// 如果找到完全匹配的工单编号，直接查询该工单
		if count > 0 {
			var exactTicket []*model.SoftwareOfflineTicket
			if err := r.db.WithContext(ctx).Model(&model.SoftwareOfflineTicket{}).
				Where("offline_ticket_no = ?", ticketNo).
				Find(&exactTicket).Error; err != nil {
				return nil, 0, fmt.Errorf("查询精确匹配工单失败: %w", err)
			}
			return exactTicket, count, nil
		}
	}

	query := r.db.WithContext(ctx).Model(&model.SoftwareOfflineTicket{})

	// 应用筛选条件
	if ticketNo != "" {
		query = query.Where("offline_ticket_no LIKE ?", "%"+ticketNo+"%")
	}
	// 添加标题筛选条件
	if title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}

	if status != "" {
		// 验证状态是否有效
		validStatuses := []string{
			common1.OfflineStatusPending,
			common1.OfflineStatusDataCleared,
			common1.OfflineStatusWaitingDown,
			common1.OfflineStatusMachineDown,
			common1.OfflineStatusCompleted}
		isValid := false
		for _, s := range validStatuses {
			if status == s {
				isValid = true
				break
			}
		}

		if isValid {
			query = query.Where("status = ?", status)
		} else {
			// 如果状态无效，记录警告但不应用筛选
			fmt.Printf("警告: 无效的状态值 '%s'，忽略此筛选条件\n", status)
		}
	}

	// 计算总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("计算总数失败: %w", err)
	}

	// 如果总数为0，直接返回空结果
	if total == 0 {
		return tickets, 0, nil
	}
	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&tickets).Error
	if err != nil {
		return nil, 0, fmt.Errorf("查询工单列表失败: %w", err)
	}

	return tickets, total, nil
}

func (r *offlineRepository) InitOfflineTicket(ctx context.Context, ticket *model.SoftwareOfflineTicket, history *model.OfflineHistory) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先清空设备关联和检查项关联，，避免自动创建
		devices := ticket.Devices
		checkItems := ticket.CheckItems
		ticket.Devices = nil
		ticket.CheckItems = nil
		// 创建工单
		ticket.ID = 0
		if err := tx.Create(ticket).Error; err != nil {
			return fmt.Errorf("创建软件上线工单失败: %v", err)
		}
		// 创建历史记录
		history.ID = 0
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("创建软件上线工单历史记录失败: %v", err)
		}
		// 手动创建设备记录
		if len(devices) > 0 {
			for i := range devices {
				devices[i].ID = 0
				devices[i].OfflineTicketNo = ticket.OfflineTicketNo
			}
			// 创建设备记录
			if err := tx.Omit("id").CreateInBatches(devices, 100).Error; err != nil {
				return fmt.Errorf("创建设备记录失败: %v", err)
			}
		}
		// 手动创建检查项记录
		if len(checkItems) > 0 {
			for i := range checkItems {
				checkItems[i].ID = 0
				checkItems[i].OfflineTicketNo = ticket.OfflineTicketNo
			}
			// 创建检查项记录
			if err := tx.Omit("id").CreateInBatches(checkItems, 100).Error; err != nil {
				return fmt.Errorf("创建检查项记录失败: %v", err)
			}
		}
		return nil
	})
}

// GetByID 根据ID获取下线工单
func (r *offlineRepository) GetByID(ctx context.Context, id uint) (*model.SoftwareOfflineTicket, error) {
	var ticket model.SoftwareOfflineTicket
	err := r.db.WithContext(ctx).Preload("Devices").Preload("CheckItems").First(&ticket, id).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

// GetOfflineStatusHistory 获取下线状态历史
func (r *offlineRepository) GetOfflineStatusHistory(ctx context.Context, ticketID uint) ([]*model.OfflineHistory, error) {
	// 先获取工单信息，以获取工单号
	var ticket model.SoftwareOfflineTicket
	if err := r.db.WithContext(ctx).First(&ticket, ticketID).Error; err != nil {
		return nil, fmt.Errorf("获取工单信息失败: %w", err)
	}

	// 使用工单号查询历史记录
	var histories []*model.OfflineHistory
	err := r.db.WithContext(ctx).
		Where("offline_ticket_no = ?", ticket.OfflineTicketNo).
		Order("operation_time DESC").
		Find(&histories).Error
	if err != nil {
		return nil, err
	}
	return histories, nil
}

// GetDevicesByOfflineTicketNo 根据工单号获取关联设备列表
func (r *offlineRepository) GetDevicesByOfflineTicketNo(ctx context.Context, offlineNo string) ([]model.OfflineDevice, error) {
	var devices []model.OfflineDevice
	err := r.db.WithContext(ctx).
		Where("offline_ticket_no = ?", offlineNo).
		Find(&devices).Error
	if err != nil {
		return nil, fmt.Errorf("获取工单关联设备失败: %w", err)
	}
	return devices, nil
}
