package model

import (
	"time"

	"gorm.io/gorm"
)

// SoftwareOfflineTicket 软件下线工单模型
type SoftwareOfflineTicket struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	// 工单基本信息
	OfflineTicketNo string `json:"offline_ticket_no" gorm:"unique;column:offline_ticket_no;type:varchar(50);not null;comment:工单号;index"`
	Title           string `json:"title" gorm:"type:varchar(200);comment:下线标题"`
	Status          string `json:"status" gorm:"type:varchar(50);default:offline_pending;comment:工单状态"`
	Stage           string `json:"stage" gorm:"type:varchar(50);comment:目前处在的阶段"`
	PreviousStatus  string `json:"previous_status" gorm:"type:varchar(50);comment:之前的工单状态"`
	TryCount        uint   `json:"try_count" gorm:"comment: 尝试次数"`
	OfflineReason   string `json:"offline_reason" gorm:"type:varchar(200);comment:下线原因"`
	// 申请人信息
	ApplicantID   uint   `json:"applicant_id" gorm:"column:applicant_id;comment:申请人ID"`
	ApplicantName string `json:"applicant_name" gorm:"column:applicant_name;type:varchar(50);comment:申请人姓名"`
	// 处理人信息
	HandlerID uint   `json:"handler_id" gorm:"column:handler_id;comment:处理人ID"`
	Handler   string `json:"handler" gorm:"column:handler;type:varchar(50);comment:处理人姓名"`
	// 时间信息
	PlannedCompletionTime time.Time `json:"planned_completion_time" gorm:"column:planned_completion_time;comment:预计完成日期"`
	// 关联设备表和检查项表
	Devices      []OfflineDevice    `json:"devices" gorm:"foreignKey:OfflineTicketNo;references:OfflineTicketNo"` // 一对多关系
	CheckItems   []OfflineCheckItem `json:"check_items" gorm:"foreignKey:OfflineTicketNo;references:OfflineTicketNo"`
	DevicesCount uint               `json:"devices_count" gorm:"column:devices_count;comment:下线设备数量"`
	// 其他信息
	Notes string `json:"notes" gorm:"type:text;comment:备注"`
}

// TableName 指定表名
func (SoftwareOfflineTicket) TableName() string {
	return "software_offline_tickets"
}

// OfflineDevice  下线设备信息表
type OfflineDevice struct {
	ID        uint           `json:"id" gorm:"primarykey;autoIncrement"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	// 外键关联
	OfflineTicketNo string `json:"offline_ticket_no" gorm:"index;type:varchar(50);not null;comment:关联的工单号"`
	// 设备基本信息
	SN       string `json:"sn" gorm:"type:varchar(100);comment:序列号"`
	Vendor   string `json:"vendor" gorm:"type:varchar(100);comment:厂商"`
	Model    string `json:"model" gorm:"type:varchar(100);comment:型号"`
	Hostname string `json:"hostname" gorm:"type:varchar(100);comment:主机名"`
	VPCIP    string `json:"vpc_ip" gorm:"column:vpc_ip;type:varchar(50);comment:VPC IP"`
	BMCIP    string `json:"bmc_ip" gorm:"column:bmc_ip;type:varchar(50);comment:BMC IP"`
	Notes    string `json:"notes" gorm:"type:text;comment:备注"`
}

// TableName 指定表名
func (OfflineDevice) TableName() string {
	return "software_offline_devices"
}

// OfflineCheckItem 下线检查项表
type OfflineCheckItem struct {
	ID              uint           `json:"id" gorm:"primarykey;autoIncrement"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`
	OfflineTicketNo string         `json:"offline_ticket_no" gorm:"type:varchar(50);index;comment:关联的下线工单号"`
	Item1Content    string         `json:"item1_content" gorm:"type:varchar(255);comment:检查项1内容描述"`
	Item1Checked    bool           `json:"item1_checked" gorm:"default:false;comment:检查项1是否已勾选"`
	Item2Content    string         `json:"item2_content" gorm:"type:varchar(255);comment:检查项2内容描述"`
	Item2Checked    bool           `json:"item2_checked" gorm:"default:false;comment:检查项2是否已勾选"`
	Notes           string         `json:"notes" gorm:"type:text;comment:备注"`
	// 其他字段...
}

func (OfflineCheckItem) TableName() string {
	return "software_offline_check_items"
}

// OfflineHistory  软件下线工单状态历史
type OfflineHistory struct {
	ID                uint           `json:"id" gorm:"primarykey"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`
	SoftwareOfflineID uint           `json:"software_offline_id" gorm:"comment:软件下线工单ID"`
	OfflineTicketNo   string         `json:"offline_ticket_no" gorm:"comment:软件下线工单编号"`
	PreviousStatus    string         `json:"previous_status" gorm:"type:varchar(50);comment:先前状态"`
	NewStatus         string         `json:"new_status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID        uint           `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName      string         `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	OperationTime     time.Time      `json:"operation_time" gorm:"comment:操作时间"`
	Comment           string         `json:"comment" gorm:"comment:操作备注"`
	Stage             string         `json:"stage" gorm:"type:varchar(50);comment:阶段"`
}

// TableName 指定表名
func (OfflineHistory) TableName() string {
	return "software_offline_status_histories"
}

// OfflineTrigger 软件下线触发器
type OfflineTrigger struct {
	Stage           string                 `json:"stage"`
	Status          string                 `json:"status" binding:"required"`
	OperatorID      uint                   `json:"operator"`
	OperatorName    string                 `json:"operator_name"`
	Comments        string                 `json:"comments"`
	RequiredHandler bool                   `json:"required_handler"`
	Data            map[string]interface{} `json:"data"`
}
