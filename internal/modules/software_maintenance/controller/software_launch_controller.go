package controller

import (
	common1 "backend/internal/modules/software_maintenance/common"
	"backend/internal/modules/software_maintenance/model"
	launch2 "backend/internal/modules/software_maintenance/service"
	"backend/internal/modules/ticket/common"
	"backend/response"
	cryptorand "crypto/rand"
	"encoding/binary"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
)

type LaunchController struct {
	service launch2.LaunchService
	logger  *zap.Logger
}

func NewLaunchController(service launch2.LaunchService) *LaunchController {
	return &LaunchController{service: service}
}

// RegisterRoutes 注册软件上线相关路由
// @Summary 注册软件上线相关路由
// @Description 注册软件上线工单相关的所有API路由
// @Tags 软件上线
func (c *LaunchController) RegisterRoutes(r *gin.RouterGroup) {
	launchRouter := r.Group("/launch")
	{
		launchRouter.GET("", c.ListLaunchTickets)                          // 获取列表
		launchRouter.POST("", c.CreateLaunchTicket)                        // 创建工单,触发工作流
		launchRouter.GET("/:id", c.GetLaunchTicketDetail)                  // 获取详情
		launchRouter.GET("/:id/history", c.GetLaunchTicketStatusHistory)   // 获取状态历史
		launchRouter.PUT("/part/:id/transition", c.TransitionLaunchTicket) //状态转换
	}
}

// CreateLaunchTicketDTO 软件上线工单创建请求DTO
type CreateLaunchTicketDTO struct {
	Title                 string      `json:"title" binding:"required"`
	PlannedCompletionTime string      `json:"planned_completion_time" binding:"required"`
	Devices               []Device    `json:"devices" binding:"required"`
	CheckItems            []CheckItem `json:"check_items" `
	Notes                 string      `json:"notes"`
}

// Device 设备信息
type Device struct {
	SN       string `json:"sn"`
	Vendor   string `json:"vendor"`
	Model    string `json:"model"`
	Hostname string `json:"hostname"`
	VPCIP    string `json:"vpc_ip"`
	BMCIP    string `json:"bmc_ip"`
}

// CheckItem 检查项信息
type CheckItem struct {
	Item1Content string `json:"item1_content"`
	Item1Checked bool   `json:"item1_checked"`
	Item2Content string `json:"item2_content"`
	Item2Checked bool   `json:"item2_checked"`
	Notes        string `json:"notes"`
}

// CreateLaunchTicket 创建软件上线工单
// @Summary 创建软件上线工单
// @Description 创建新的软件上线工单
// @Tags 软件上线
// @Accept json
// @Produce json
// @Param ticket body CreateLaunchTicketDTO true "工单信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /api/v1/software_maintenance/launch [post]
func (c *LaunchController) CreateLaunchTicket(ctx *gin.Context) {
	var DTO CreateLaunchTicketDTO
	if err := ctx.ShouldBindJSON(&DTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	fmt.Printf("用户ID: %v, 用户名: %v, 错误: %v\n", userID, userName, err)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
		return
	}

	// 验证IP地址格式
	ipPattern := `^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$`
	ipRegex := regexp.MustCompile(ipPattern)

	for i, device := range DTO.Devices {
		// 验证BMC IP (如果提供)
		if device.BMCIP != "" && !ipRegex.MatchString(device.BMCIP) {
			response.Fail(ctx, http.StatusBadRequest, fmt.Sprintf("设备 %d BMC IP 格式不正确: %s", i+1, device.BMCIP))
			return
		}

		// 验证VPC IP (如果提供)
		if device.VPCIP != "" && !ipRegex.MatchString(device.VPCIP) {
			response.Fail(ctx, http.StatusBadRequest, fmt.Sprintf("设备 %d VPC IP 格式不正确: %s", i+1, device.VPCIP))
			return
		}
	}

	// 生成工单号
	ticketNo := c.generateLaunchTicketNo()
	// 将DTO中的设备转换为模型中的LaunchDevice
	var devices []model.LaunchDevice
	for _, d := range DTO.Devices {
		devices = append(devices, model.LaunchDevice{
			LaunchTicketNo: ticketNo,
			SN:             d.SN,
			Vendor:         d.Vendor,
			Model:          d.Model,
			Hostname:       d.Hostname,
			VPCIP:          d.VPCIP,
			BMCIP:          d.BMCIP,
		})
	}
	var checkItems []model.LaunchItem
	for _, item := range DTO.CheckItems {
		checkItems = append(checkItems, model.LaunchItem{
			LaunchTicketNo: ticketNo,
			Item1Content:   item.Item1Content,
			Item1Checked:   item.Item1Checked,
			Item2Content:   item.Item2Content,
			Item2Checked:   item.Item2Checked,
			Notes:          item.Notes,
		})
	}
	plannedCompletionTime, err := time.Parse("2006-01-02 15:04:05", DTO.PlannedCompletionTime)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "日期格式错误: "+err.Error())
		return
	}

	// 创建工单对象
	LaunchTicket := &model.SoftwareLaunchTicket{
		LaunchTicketNo:        ticketNo,
		Title:                 DTO.Title,
		Status:                common1.StatusWaitingBeforeInstallation, // 初始状态为
		Stage:                 common1.StageBeforeInstallation,         // 初始阶段为
		ApplicantID:           userID,
		ApplicantName:         userName,
		PlannedCompletionTime: plannedCompletionTime,
		Devices:               devices, // 使用转换后的设备列表
		CheckItems:            checkItems,
		DevicesCount:          uint(len(DTO.Devices)),
		Notes:                 DTO.Notes,
		//DeliveryResult:        "success",
	}

	// 调用服务层创建工单
	if err := c.service.CreateLaunchTicket(ctx, LaunchTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建上线工单失败: "+err.Error())
		return
	}
	response.Success(ctx, nil, "创建上线工单成功")
}

// ListLaunchTickets 获取上线工单列表
// @Summary 获取上线工单列表
// @Description 分页获取上线工单列表，支持筛选
// @Tags 软件上线
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param ticket_no query string false "工单编号"
// @Param status query string false "工单状态"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /api/v1/software_maintenance/launch [get]
func (c *LaunchController) ListLaunchTickets(ctx *gin.Context) {
	var req struct {
		Page     int    `form:"page" binding:"omitempty,min=1"`
		PageSize int    `form:"pageSize" binding:"omitempty,min=1,max=100"`
		TicketNo string `form:"ticket_no"`
		Status   string `form:"status"`
		Title    string `form:"title"`
	}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	tickets, total, err := c.service.ListLaunchTickets(ctx, req.Page, req.PageSize, req.TicketNo, req.Status, req.Title)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取工单列表失败: "+err.Error())
		return
	}
	// 构建响应数据，确保包含所需字段
	var responseList []map[string]interface{}
	for _, ticket := range tickets {
		// 构建响应项
		item := map[string]interface{}{
			"id":                      ticket.ID,
			"launch_ticket_no":        ticket.LaunchTicketNo,
			"title":                   ticket.Title,
			"devices_count":           ticket.DevicesCount,
			"planned_completion_time": ticket.PlannedCompletionTime,
			"status":                  ticket.Status,
			"handler_name":            ticket.Handler,
			"applicant_name":          ticket.ApplicantName,
			"created_at":              ticket.CreatedAt,
			"devices":                 ticket.Devices,
			"check_items":             ticket.CheckItems,
		}
		responseList = append(responseList, item)
	}

	response.Success(ctx, gin.H{
		"list":  responseList,
		"total": total,
	}, "获取工单列表成功")
}

// GetLaunchTicketDetail 获取上线工单详情
// @Summary 获取上线工单详情
// @Description 获取上线工单详情信息
// @Tags 软件上线
// @Accept json
// @Produce json
// @Param id path int true "工单ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /api/v1/software_maintenance/launch/{id} [get]
func (c *LaunchController) GetLaunchTicketDetail(ctx *gin.Context) {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}
	if id <= 0 {
		response.Fail(ctx, http.StatusBadRequest, "ID必须是正数")
		return
	}

	ticket, err := c.service.GetLaunchTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取工单详情失败: "+err.Error())
		return
	}

	response.Success(ctx, ticket, "获取工单详情成功")
}

func (c *LaunchController) GetLaunchTicketStatusHistory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	history, err := c.service.GetLaunchTicketStatusHistory(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取状态历史失败: "+err.Error())
		return
	}

	response.Success(ctx, history, "获取状态历史成功")
}

// generateLaunchTicketNo 生成上线工单号
func (c *LaunchController) generateLaunchTicketNo() string {
	// 使用时间戳和随机数生成工单号
	timestamp := time.Now().Format("20060102150405")

	// 使用crypto/rand代替math/rand
	randomBytes := make([]byte, 4)
	var random int
	if _, err := cryptorand.Read(randomBytes); err != nil {
		// 如果加密随机数生成失败，使用时间纳秒作为备选
		random = int(time.Now().UnixNano() % 1000)
		c.logger.Warn("使用加密随机数生成失败，使用时间纳秒替代", zap.Error(err))
	} else {
		// 使用加密随机数，取模确保在0-999范围内
		random = int(binary.BigEndian.Uint32(randomBytes) % 1000)
	}

	return fmt.Sprintf("SLT%s%03d", timestamp, random)
}

// TransitionLaunchTicket 转换上线工单状态
// @Summary 转换上线工单状态
// @Description 根据提供的阶段和状态，转换上线工单的工作流状态
// @Tags 软件上线
// @Accept json
// @Produce json
// @Param id path int true "工单ID" minimum(1)
// @Param request body TransitionRequestDTO true "状态转换请求"
// @Success 200 {object} response.ResponseStruct{data=interface{}} "状态转换成功"
// @Failure 400 {object} response.ResponseStruct{data=interface{}} "参数错误或无效的状态转换"
// @Router /api/v1/software_maintenance/launch/part/{id}/transition [put]
func (c *LaunchController) TransitionLaunchTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}
	var transitionRequest struct {
		Stage    string                 `json:"stage" binding:"required"`  // 工作流阶段
		Status   string                 `json:"status" binding:"required"` // 状态
		Comments string                 `json:"comments"`                  // 备注
		Data     map[string]interface{} `json:"data"`                      // 阶段相关数据
	}
	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取当前工单
	ticket, err := c.service.GetLaunchTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取维修入库单失败: "+err.Error())
		return
	}
	// 验证状态转换是否合法
	if !common1.IsValidLaunchStatusTransition(ticket.Status, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", ticket.Status, transitionRequest.Status))
		return
	}

	isValid, message := common1.ValidateLaunchStageTransition(ticket.Status, transitionRequest.Stage)
	if !isValid {
		response.Fail(ctx, http.StatusBadRequest, "当前状态下不能触发此阶段: "+message)
		return
	}
	triggers := model.LaunchTrigger{
		Stage:        transitionRequest.Stage,
		Status:       transitionRequest.Status,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     transitionRequest.Comments,
		Data:         transitionRequest.Data,
	}
	// 触发工作流，确保工作流可以正常工作
	err = c.service.TriggerWorkflowStage(ctx, uint(id), ticket.LaunchTicketNo, triggers)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "状态转换成功")
}
