package controller

import (
	common1 "backend/internal/modules/software_maintenance/common"
	"backend/internal/modules/software_maintenance/model"
	"backend/internal/modules/software_maintenance/service"
	"backend/internal/modules/ticket/common"
	"backend/response"
	cryptorand "crypto/rand"
	"encoding/binary"
	"strconv"
	"time"

	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OfflineController struct {
	service service.OfflineService
	logger  *zap.Logger
}

func NewOfflineController(service service.OfflineService) *OfflineController {
	return &OfflineController{service: service}
}

// RegisterRoutes 注册软件下线相关路由
// @Summary 注册软件下线相关路由
// @Description 注册软件下线工单相关的所有API路由
// @Tags 软件下线
func (c *OfflineController) RegisterRoutes(r *gin.RouterGroup) {
	offlineRouter := r.Group("/offline")
	{
		offlineRouter.GET("", c.ListOfflineTickets)                          // 获取列表
		offlineRouter.POST("", c.CreateOfflineTicket)                        // 创建工单,触发工作流
		offlineRouter.GET("/:id", c.GetOfflineTicketDetail)                  // 获取详情
		offlineRouter.GET("/:id/history", c.GetOfflineTicketStatusHistory)   // 获取状态历史
		offlineRouter.PUT("/part/:id/transition", c.TransitionOfflineTicket) //状态转换
	}
}

// ListOfflineTickets 获取下线工单列表
// @Summary 获取下线工单列表
// @Description 分页获取下线工单列表，支持筛选
// @Tags 软件下线
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1" default(1) minimum(1)
// @Param pageSize query int false "每页数量，默认10" default(10) minimum(1) maximum(100)
// @Param ticket_no query string false "工单编号，支持模糊查询"
// @Param status query string false "工单状态，精确匹配"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /api/v1/software_maintenance/offline [get]
func (c *OfflineController) ListOfflineTickets(ctx *gin.Context) {
	var req struct {
		Page     int    `form:"page" binding:"omitempty,min=1"`
		PageSize int    `form:"pageSize" binding:"omitempty,min=1,max=100"`
		TicketNo string `form:"ticket_no"`
		Status   string `form:"status"`
		Title    string `form:"title"`
	}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	tickets, total, err := c.service.ListOfflineTickets(ctx, req.Page, req.PageSize, req.TicketNo, req.Status, req.Title)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取下线工单列表失败: "+err.Error())
		return
	}
	// 构建响应数据，确保包含所需字段
	var responseList []map[string]interface{}
	for _, ticket := range tickets {
		// 构建响应项
		item := map[string]interface{}{
			"id":                      ticket.ID,
			"offline_ticket_no":       ticket.OfflineTicketNo,
			"title":                   ticket.Title,
			"devices_count":           ticket.DevicesCount,
			"planned_completion_time": ticket.PlannedCompletionTime,
			"status":                  ticket.Status,
			"handler_name":            ticket.Handler,
			"applicant_name":          ticket.ApplicantName,
			"created_at":              ticket.CreatedAt,
			"devices":                 ticket.Devices,
			"check_items":             ticket.CheckItems,
		}
		responseList = append(responseList, item)
	}

	response.Success(ctx, gin.H{
		"list":  responseList,
		"total": total,
	}, "获取下线工单列表成功")
}

// CreateLaunchTicketCOM 软件下线工单创建请求
type CreateLaunchTicketCOM struct {
	Title                 string      `json:"title" binding:"required"`
	PlannedCompletionTime string      `json:"planned_completion_time" binding:"required"`
	OfflineReason         string      `json:"offline_reason" binding:"required"`
	Devices               []Device    `json:"devices" binding:"required"`
	CheckItems            []CheckItem `json:"check_items" `
	Notes                 string      `json:"notes"`
}

// CreateOfflineTicket 创建软件下线工单
// @Summary 创建软件下线工单
// @Description 创建新的软件下线工单并启动工作流
// @Tags 软件下线
// @Accept json
// @Produce json
// @Param ticket body CreateLaunchTicketCOM true "下线工单信息"
// @Success 200 {object} response.ResponseStruct{} "创建成功"
// @Failure 400 {object} response.ResponseStruct{} "参数错误或用户信息获取失败"
// @Failure 500 {object} response.ResponseStruct{} "创建工单失败"
// @Router /api/v1/software_maintenance/offline [post]
func (c *OfflineController) CreateOfflineTicket(ctx *gin.Context) {
	var COM CreateLaunchTicketCOM
	if err := ctx.ShouldBindJSON(&COM); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	fmt.Printf("用户ID: %v, 用户名: %v, 错误: %v\n", userID, userName, err)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
		return
	}

	// 生成工单号
	ticketNo := c.generateOfflineTicketNo()
	fmt.Printf("用户ticketNo: %v, ", ticketNo)
	// 将COM中的设备转换为模型中的LaunchDevice
	var devices []model.OfflineDevice
	for _, d := range COM.Devices {
		devices = append(devices, model.OfflineDevice{
			OfflineTicketNo: ticketNo,
			SN:              d.SN,
			Vendor:          d.Vendor,
			Model:           d.Model,
			Hostname:        d.Hostname,
			VPCIP:           d.VPCIP,
			BMCIP:           d.BMCIP,
		})
	}
	var checkItems []model.OfflineCheckItem
	for _, item := range COM.CheckItems {
		checkItems = append(checkItems, model.OfflineCheckItem{
			OfflineTicketNo: ticketNo,
			Item1Content:    item.Item1Content,
			Item1Checked:    item.Item1Checked,
			Item2Content:    item.Item2Content,
			Item2Checked:    item.Item2Checked,
			Notes:           item.Notes,
		})
	}
	plannedCompletionTime, err := time.Parse("2006-01-02 15:04:05", COM.PlannedCompletionTime)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "日期格式错误: "+err.Error())
		return
	}

	// 创建工单对象
	OfflineTicket := &model.SoftwareOfflineTicket{
		OfflineTicketNo:       ticketNo,
		Title:                 COM.Title,
		Status:                common1.OfflineStatusPending,     // 初始状态等待数据清理
		Stage:                 common1.OfflineStageDataCleaning, // 初始阶段数据清理阶段
		ApplicantID:           userID,
		ApplicantName:         userName,
		PlannedCompletionTime: plannedCompletionTime,
		OfflineReason:         COM.OfflineReason,
		Devices:               devices, // 使用转换后的设备列表
		CheckItems:            checkItems,
		DevicesCount:          uint(len(COM.Devices)),
		Notes:                 COM.Notes,
	}

	// 调用服务层创建工单
	if err := c.service.CreateOfflineTicket(ctx, OfflineTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建上线工单失败: "+err.Error())
		return
	}
	response.Success(ctx, nil, "创建上线工单成功")
}

// GetOfflineTicketDetail 获取下线工单详情
// @Summary 获取下线工单详情
// @Description 根据ID获取软件下线工单的详细信息
// @Tags 软件下线
// @Accept json
// @Produce json
// @Param id path int true "工单ID" minimum(1)
// @Success 200 {object} response.ResponseStruct{data=model.SoftwareOfflineTicket} "获取成功"
// @Failure 400 {object} response.ResponseStruct{} "无效的ID"
// @Failure 500 {object} response.ResponseStruct{} "获取工单详情失败"
// @Router /api/v1/software_maintenance/offline/{id} [get]
func (c *OfflineController) GetOfflineTicketDetail(ctx *gin.Context) {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}
	if id <= 0 {
		response.Fail(ctx, http.StatusBadRequest, "ID必须是正数")
		return
	}

	ticket, err := c.service.GetOfflineTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取工单详情失败: "+err.Error())
		return
	}

	response.Success(ctx, ticket, "获取工单详情成功")
}

// generateOfflineTicketNo 生成下线工单号
func (c *OfflineController) generateOfflineTicketNo() string {
	// 使用时间戳和随机数生成工单号
	timestamp := time.Now().Format("20060102150405")

	// 使用crypto/rand代替math/rand
	randomBytes := make([]byte, 4)
	var random int
	if _, err := cryptorand.Read(randomBytes); err != nil {
		// 如果加密随机数生成失败，使用时间纳秒作为备选
		random = int(time.Now().UnixNano() % 1000)
		c.logger.Warn("使用加密随机数生成失败，使用时间纳秒替代", zap.Error(err))
	} else {
		// 使用加密随机数，取模确保在0-999范围内
		random = int(binary.BigEndian.Uint32(randomBytes) % 1000)
	}

	return fmt.Sprintf("SOT%s%03d", timestamp, random)
}

// GetOfflineTicketStatusHistory 获取下线工单状态历史
// @Summary 获取下线工单状态历史
// @Description 根据工单ID获取状态变更历史记录
// @Tags 软件下线
// @Accept json
// @Produce json
// @Param id path int true "工单ID" minimum(1)
// @Success 200 {object} response.ResponseStruct{data=[]model.OfflineHistory} "获取成功"
// @Failure 400 {object} response.ResponseStruct{} "无效的ID"
// @Failure 500 {object} response.ResponseStruct{} "获取状态历史失败"
// @Router /api/v1/software_maintenance/offline/{id}/history [get]
func (c *OfflineController) GetOfflineTicketStatusHistory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	history, err := c.service.GetOfflineTicketStatusHistory(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取状态历史失败: "+err.Error())
		return
	}

	response.Success(ctx, history, "获取状态历史成功")
}

// TransitionOfflineTicket 转换下线工单状态
// @Summary 转换下线工单状态
// @Description 触发工单状态转换，执行相应的工作流阶段
// @Tags 软件下线
// @Accept json
// @Produce json
// @Param id path int true "工单ID" minimum(1)
// @Param transition body object true "状态转换信息"
// @Param transition.stage body string true "工作流阶段"
// @Param transition.status body string true "目标状态"
// @Param transition.comments body string false "备注信息"
// @Param transition.data body object false "阶段相关数据"
// @Success 200 {object} response.ResponseStruct{} "状态转换成功"
// @Failure 400 {object} response.ResponseStruct{} "参数错误或状态转换无效"
// @Failure 401 {object} response.ResponseStruct{} "未获取到用户信息"
// @Failure 500 {object} response.ResponseStruct{} "状态转换失败"
// @Router /api/v1/software_maintenance/offline/part/{id}/transition [put]
func (c *OfflineController) TransitionOfflineTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}
	var transitionRequest struct {
		Stage    string                 `json:"stage" binding:"required"`  // 工作流阶段
		Status   string                 `json:"status" binding:"required"` // 状态
		Comments string                 `json:"comments"`                  // 备注
		Data     map[string]interface{} `json:"data"`                      // 阶段相关数据
	}
	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取当前工单
	ticket, err := c.service.GetOfflineTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取维修入库单失败: "+err.Error())
		return
	}
	// 验证状态转换是否合法
	if !common1.IsValidOfflineStatusTransition(ticket.Status, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", ticket.Status, transitionRequest.Status))
		return
	}

	isValid, message := common1.ValidateOfflineStageTransition(ticket.Status, transitionRequest.Stage)
	if !isValid {
		response.Fail(ctx, http.StatusBadRequest, "当前状态下不能触发此阶段: "+message)
		return
	}
	triggers := model.OfflineTrigger{
		Stage:        transitionRequest.Stage,
		Status:       transitionRequest.Status,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     transitionRequest.Comments,
		Data:         transitionRequest.Data,
	}
	// 触发工作流，确保工作流可以正常工作
	err = c.service.TriggerWorkflowStage(ctx, uint(id), triggers)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "状态转换成功")
}
