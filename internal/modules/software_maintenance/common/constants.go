package common

// 任务队列常量
const LaunchTaskQueue = "SOFTWARE_LAUNCH_TICKET_TASK_QUEUE"
const OfflineTaskQueue = "SOFTWARE_OFFLINE_TICKET_TASK_QUEUE"

// 工作流ID前缀常量
const PrefixLaunchTicketWorkflowID = "launch_ticket_"
const PrefixOfflineTicketWorkflowID = "offline_ticket_"
const (
	SOFTLAUNCHSOURCE  = "online"
	SOFTOFFLINESOURCE = "offline"
)

// 资产状态常量
const (
	// 资产状态
	AssetStatusOnRacking = "on_racking" // 已上架
	AssetStatusInUse     = "in_use"     // 使用中

	// 硬件状态
	HardwareStatusNormal = "normal" // 正常

	// 业务状态
	BizStatusActive      = "active"      // 运行中(已上线且已交付客户)
	BizStatusMaintaining = "maintaining" // 维护中(满足上线条件或作为备机)

	// 资产类型
	AssetTypeServer = "server" // 服务器
)

// 软件运维工单常量
const (
	StatusWaitingBeforeInstallation     = "waiting_before_installation"     //等待装机前
	StatusBeforeInstallationCompleted   = "before_installation_completed"   //装机前完成
	StatusWaitingBeforeExpansion        = "waiting_before_expansion"        //等待扩容前
	StatusBeforeExpansionCompleted      = "before_expansion_completed"      //扩容前完成
	StatusWaitingAfterExpansion         = "waiting_after_expansion"         //等待扩容后
	StatusAfterExpansionCompleted       = "after_expansion_completed"       //扩容后完成
	StatusWaitingDeliveryConfirmation   = "waiting_delivery_confirmation"   //等待交付确认
	StatusDeliveryConfirmationCompleted = "delivery_confirmation_completed" //交付确认完成
	StatusCompleted                     = "launch_completed"                //上线完成

	OfflineStatusPending     = "offline_pending" // 下线工作流初始阶段,等待数据清理
	OfflineStatusDataCleared = "data_cleared"    // 数据清理完成
	OfflineStatusWaitingDown = "waiting_down"    // 等待物理机下线
	OfflineStatusMachineDown = "machine_down"    // 物理机下线完成
	OfflineStatusCompleted   = "completed"       // 下线完成
)

// 工作流阶段常量
const (
	StageBeforeInstallation = "before_installation" // 装机前阶段
	StageBeforeExpansion    = "before_expansion"    // 扩容前阶段
	StageAfterExpansion     = "after_expansion"     // 扩容后阶段
	StageWaitingDelivery    = "waiting_delivery"    // 等待交付阶段
	StageCompleted          = "completed"           // 完成

	OfflineStageDataCleaning = "data_cleaning"    // 数据清理阶段
	OfflineStageMachineDown  = "machine_down"     // 物理机下线阶段
	OfflineStageComplete     = "complete_offline" // 完成下线

)

const (
	SoftwareLaunchUpdateSignal  = "software_launch_update_signal"  // 软件上线工单更新信号
	SoftwareOfflineUpdateSignal = "software_offline_update_signal" // 软件下线工单更新信号

)
