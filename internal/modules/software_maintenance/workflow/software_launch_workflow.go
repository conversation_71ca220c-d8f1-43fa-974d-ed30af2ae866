package workflow

import (
	common1 "backend/internal/modules/software_maintenance/common"
	"backend/internal/modules/ticket/common"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

type launchWorkflowState struct {
	LaunchTicketID      uint
	LaunchNo            string
	CurrentStatus       string
	CurrentStage        string
	RequireVerification bool
	WorkflowRetryCount  int
}

// SoftwareLaunchWorkflow 软件上线工作流
func SoftwareLaunchWorkflow(ctx workflow.Context, input common1.LaunchWorkflowInput) error {
	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: common.ActivityStartToCloseTimeout,
		HeartbeatTimeout:    common.ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        common.InitialInterval,
			BackoffCoefficient:     common.BackoffCoefficient,
			MaximumInterval:        common.MaximumInterval,
			MaximumAttempts:        common.MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)
	// 创建工作流日志
	logger := workflow.GetLogger(ctx)
	logger.Info("初始化SoftwareLaunchWorkflow工作流日志成功")
	logger.Info("输入信息为", "SoftwareLaunchInput", input)

	launchState := launchWorkflowState{
		LaunchTicketID:      input.LaunchTicketID,
		LaunchNo:            input.LaunchNo,
		CurrentStatus:       common1.StatusWaitingBeforeInstallation,
		CurrentStage:        common1.StageBeforeInstallation,
		RequireVerification: input.RequireVerification,
		WorkflowRetryCount:  0,
	}
	var isWorkflowComplete bool
	completeChan := make(chan struct{})
	// 注册 SetUpdateHandler接收更新信号
	err := workflow.SetUpdateHandler(ctx, common1.SoftwareLaunchUpdateSignal, func(ctx workflow.Context, signal common1.LaunchSignal) error {
		logger.Info("收到UpdateHandler信号",
			"SoftwareOnlineTicketID", input.LaunchTicketID,
			"status", signal.Status,
			"operator", signal.OperatorName)
		ctx = workflow.WithActivityOptions(ctx, ao)
		// 处理信号
		switch signal.Status {
		case common1.StatusBeforeInstallationCompleted:
			// 装机前完成
			// 更新ticket和history
			updateInput := common1.UpdateLaunchTicketInput{
				LaunchTicketID: input.LaunchTicketID,
				LaunchNo:       input.LaunchNo,
				OperatorID:     signal.OperatorID,
				OperatorName:   signal.OperatorName,
				Comments:       signal.Comments,
				CurrentStage:   common1.StageBeforeInstallation,
				CurrentStatus:  common1.StatusBeforeInstallationCompleted,
				NextStage:      common1.StageBeforeExpansion,
				NextStatus:     common1.StatusWaitingBeforeExpansion,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateLaunchTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 更新launchList状态
			err = workflow.ExecuteActivity(ctx, "UpdateLaunchListStage", input.LaunchNo, common1.StatusWaitingBeforeExpansion).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 更新内部状态  记录日志
			launchState.CurrentStatus = common1.StatusBeforeInstallationCompleted
			launchState.CurrentStage = common1.StageBeforeExpansion
		case common1.StatusBeforeExpansionCompleted:
			// 扩容前完成
			// 更新ticket和history
			updateInput := common1.UpdateLaunchTicketInput{
				LaunchTicketID: input.LaunchTicketID,
				LaunchNo:       input.LaunchNo,
				OperatorID:     signal.OperatorID,
				OperatorName:   signal.OperatorName,
				Comments:       signal.Comments,
				CurrentStage:   common1.StageBeforeExpansion,
				CurrentStatus:  common1.StatusBeforeExpansionCompleted,
				NextStage:      common1.StageAfterExpansion,
				NextStatus:     common1.StatusWaitingAfterExpansion,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateLaunchTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 更新launchList状态
			err = workflow.ExecuteActivity(ctx, "UpdateLaunchListStage", input.LaunchNo, common1.StatusWaitingAfterExpansion).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 更新内部状态
			launchState.CurrentStatus = common1.StatusWaitingAfterExpansion
			launchState.CurrentStage = common1.StageAfterExpansion

		case common1.StatusAfterExpansionCompleted:
			// 扩容后完成
			// 更新ticket和history
			updateInput := common1.UpdateLaunchTicketInput{
				LaunchTicketID: input.LaunchTicketID,
				LaunchNo:       input.LaunchNo,
				OperatorID:     signal.OperatorID,
				OperatorName:   signal.OperatorName,
				Comments:       signal.Comments,
				CurrentStage:   common1.StageAfterExpansion,
				CurrentStatus:  common1.StatusAfterExpansionCompleted,
				NextStage:      common1.StageWaitingDelivery,
				NextStatus:     common1.StatusWaitingDeliveryConfirmation,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateLaunchTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 更新launchList状态
			err = workflow.ExecuteActivity(ctx, "UpdateLaunchListStage", input.LaunchNo, common1.StatusWaitingDeliveryConfirmation).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 更新内部状态
			launchState.CurrentStatus = common1.StatusWaitingDeliveryConfirmation
			launchState.CurrentStage = common1.StageWaitingDelivery

		case common1.StatusDeliveryConfirmationCompleted:
			// 确认完成
			// 首先更新设备交付状态，从signal.Data中获取设备状态数据
			deviceStatuses := make(map[string]string)
			if deviceData, ok := signal.Data["delivery_status"].(map[string]interface{}); ok {
				for sn, status := range deviceData {
					if statusStr, ok := status.(string); ok {
						deviceStatuses[sn] = statusStr
					}
				}
			}
			if len(deviceStatuses) > 0 {
				// 更新设备交付状态
				err := workflow.ExecuteActivity(ctx, "UpdateDeviceDeliveryStatus", input.LaunchNo, deviceStatuses).Get(ctx, nil)
				if err != nil {
					logger.Error("更新设备交付状态失败：", zap.Error(err))
					// 继续执行，不中断流程
				}
			}
			// 更新CMDB状态从闲置中改为使用中,而且是小device里面,如果状态是失败，那么CMDB状态不改
			if err := workflow.ExecuteActivity(ctx, "UpdateCMDBLaunchStatus", input.LaunchTicketID, input.LaunchNo, "使用中", signal.OperatorID, signal.OperatorName).Get(ctx, nil); err != nil {
				logger.Error("更新CMDB状态失败：", zap.Error(err))
			}

			// 更新ticket和history
			updateInput := common1.UpdateLaunchTicketInput{
				LaunchTicketID: input.LaunchTicketID,
				LaunchNo:       input.LaunchNo,
				OperatorID:     signal.OperatorID,
				OperatorName:   signal.OperatorName,
				Comments:       signal.Comments,
				CurrentStage:   common1.StageWaitingDelivery,
				CurrentStatus:  common1.StatusDeliveryConfirmationCompleted,
				NextStage:      common1.StageCompleted,
				NextStatus:     common1.StatusCompleted,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateLaunchTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 更新launchList状态
			err = workflow.ExecuteActivity(ctx, "UpdateLaunchListStage", input.LaunchNo, common1.StatusCompleted).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		}
		return nil
	})
	if err != nil {
		logger.Error("SetUpdateHandler failed.", "error:", err, zap.Error(err))
	}

	// 使用 workflow.Await 等待条件满足
	err = workflow.Await(ctx, func() bool {
		select {
		case <-completeChan:
			return true
		default:
			return isWorkflowComplete
		}
	})

	if err != nil {
		logger.Error("Workflow await failed.", "error:", err, zap.Error(err))
		return err
	}
	return nil
}
