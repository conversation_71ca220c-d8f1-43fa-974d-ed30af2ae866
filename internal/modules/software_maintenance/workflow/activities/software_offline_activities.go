package activities

import (
	"backend/internal/common/constants"
	"backend/internal/modules/cmdb/model/asset"
	common "backend/internal/modules/software_maintenance/common"
	"backend/internal/modules/software_maintenance/model"
	offlineRepo "backend/internal/modules/software_maintenance/repository"
	offlineSvc "backend/internal/modules/software_maintenance/service"
	"context"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"time"
)

type OfflineActivity interface {
	UpdateTicketAndHistory(ctx context.Context, input common.UpdateOfflineTicketInput) error
	UpdateOfflineTicketStage(ctx context.Context, offlineNo string, stage string) error
	UpdateCMDBOfflineStatus(ctx context.Context, offlineTicketID uint, offlineNo string, newStatus string, operatorID uint, operatorName string) error
}

type offlineActivities struct {
	db          *gorm.DB
	offlineSvc  offlineSvc.OfflineService
	offlineRepo offlineRepo.OfflineRepository
	logger      *zap.Logger
}

func NewOfflineActivities(db *gorm.DB, offlineRepo offlineRepo.OfflineRepository, offlineSvc offlineSvc.OfflineService, logger *zap.Logger) OfflineActivity {
	return &offlineActivities{
		db:          db,
		offlineSvc:  offlineSvc,
		offlineRepo: offlineRepo,
		logger:      logger,
	}
}

// UpdateTicketAndHistory 更新下线工单信息和工单历史
func (o *offlineActivities) UpdateTicketAndHistory(ctx context.Context, input common.UpdateOfflineTicketInput) error {
	// 获取下线工单信息
	o.logger.Info("更新上线工单信息：", zap.Any("UpdateInput", input))
	offlineTicket, err := o.offlineRepo.GetByID(ctx, input.OfflineTicketID)
	if err != nil {
		return fmt.Errorf("获取下线工单信息失败: %v", err)
	}

	// 更新工单状态
	offlineTicket.PreviousStatus = offlineTicket.Status
	offlineTicket.Status = input.NextStatus
	offlineTicket.UpdatedAt = time.Now()
	offlineTicket.Stage = input.NextStage

	offlineTicket.Handler = input.OperatorName
	offlineTicket.HandlerID = input.OperatorID

	// 创建历史记录
	history := &model.OfflineHistory{
		OfflineTicketNo: offlineTicket.OfflineTicketNo,
		PreviousStatus:  input.CurrentStatus,
		NewStatus:       input.NextStatus,
		OperatorID:      input.OperatorID,
		OperatorName:    input.OperatorName,
		Comment:         input.Comments,
		Stage:           input.NextStage,
		OperationTime:   time.Now(),
	}
	err = o.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新工单
		if err := tx.Save(offlineTicket).Error; err != nil {
			return fmt.Errorf("更新软件下线工单状态失败，事务回滚: %v", err)
		}

		// 创建历史记录
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("创建软件下线工单历史记录失败，事务回滚: %v", err)
		}
		return nil
	})

	o.logger.Info("更新工单及历史记录成功", zap.Any("TicketHistory", history), zap.Any("Ticket", offlineTicket))
	if err != nil {
		return err
	}
	return nil
}

// UpdateOfflineTicketStage  更新下线工单阶段
func (o *offlineActivities) UpdateOfflineTicketStage(ctx context.Context, offlineNo string, stage string) error {
	o.logger.Info("开始更新工单列表阶段", zap.String("工单号", offlineNo), zap.String("阶段", stage))
	update := map[string]interface{}{
		"stage": stage,
	}
	err := o.db.WithContext(ctx).Model(&model.SoftwareOfflineTicket{}).Where("offline_ticket_no = ?", offlineNo).Updates(update).Error
	if err != nil {
		return fmt.Errorf("更新OfflineList的stage字段失败：: %w", err)
	}
	return nil
}

// UpdateCMDBOfflineStatus 更新CMDB资产状态、CMDB业务状态、状态变更表
func (o *offlineActivities) UpdateCMDBOfflineStatus(ctx context.Context, offlineTicketID uint, offlineNo string, newStatus string, operatorID uint, operatorName string) error {
	o.logger.Info("开始更新CMDB状态", zap.String("工单号", offlineNo), zap.String("新状态", newStatus))
	// 获取工单关联的所有设备
	devices, err := o.offlineRepo.GetDevicesByOfflineTicketNo(ctx, offlineNo)
	if err != nil {
		o.logger.Error("获取工单设备失败", zap.Error(err))
		return fmt.Errorf("获取工单设备失败: %w", err)
	}

	// 遍历设备，更新交付状态为成功的设备在CMDB中的状态
	for _, device := range devices {
		o.logger.Info("更新CMDB设备状态",
			zap.String("SN", device.SN),
			zap.String("新状态", newStatus))

		// 创建当前迭代的设备副本
		currentDevice := device
		err := o.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			// 获取设备当前状态，用于记录状态变更前的状态
			var currentAssetStatus, currentBizStatus string
			assetStatusResult := tx.Table("asset_devices").
				Where("sn = ?", currentDevice.SN).
				Select("asset_status").
				Scan(&currentAssetStatus)
			if assetStatusResult.Error != nil {
				return fmt.Errorf("获取当前资产状态失败: %w", assetStatusResult.Error)
			}

			bizStatusResult := tx.Table("resources").
				Where("sn = ?", currentDevice.SN).
				Select("biz_status").
				Scan(&currentBizStatus)
			if bizStatusResult.Error != nil {
				return fmt.Errorf("获取当前业务状态失败: %w", bizStatusResult.Error)
			}
			// 更新资产状态，并抹除主机名
			assetResult := tx.Table("asset_devices").
				Where("sn = ?", currentDevice.SN).
				Updates(map[string]interface{}{
					"asset_status": constants.AssetStatusOnRack, // 已上架中状态

				})
			// 更新业务状态，并抹除VPC IP和BMC IP
			resourceResult := tx.Table("resources").
				Where("sn = ?", currentDevice.SN).
				Updates(map[string]interface{}{
					"biz_status": asset.BizStatusMaintaining, // 业务状态更新为维护中
					"hostname":   "",                         // 抹除主机名
					"vpc_ip":     "",                         // 抹除VPC IP
					"bmc_ip":     "",                         // 抹除BMC IP
				})
			if assetResult.Error != nil {
				return fmt.Errorf("更新CMDB资产状态失败: %w", assetResult.Error)
			}
			if resourceResult.Error != nil {
				return fmt.Errorf("更新CMDB业务状态失败: %w", resourceResult.Error)
			}
			if assetResult.RowsAffected == 0 {
				o.logger.Warn("未找到对应的资产记录",
					zap.String("SN", currentDevice.SN))
			}
			if resourceResult.RowsAffected == 0 {
				o.logger.Warn("未找到对应的资源记录",
					zap.String("SN", currentDevice.SN))
			}
			return nil
		})

		if err != nil {
			o.logger.Error("更新CMDB资产状态和业务状态失败",
				zap.String("SN", device.SN),
				zap.Error(err))
			continue
		}
		// 记录状态变更
		var (
			workFlowID = fmt.Sprintf("%s%d", common.PrefixOfflineTicketWorkflowID, offlineTicketID)
		)
		// 获取下线工单
		ticket, err := o.offlineRepo.GetByID(ctx, offlineTicketID)
		if err != nil {
			o.logger.Error("获取工单失败", zap.Error(err))
			return fmt.Errorf("获取工单失败: %w", err)
		}
		// 调用service层方法记录状态变更
		err = o.offlineSvc.RecordAssetStatusChange(
			ctx,
			device.SN,
			constants.AssetStatusInUse,     // 旧资产状态为"使用中"
			constants.AssetStatusOnRack,    // 新资产状态为"上架中"
			constants.BizStatusActive,      // 旧业务状态为"运行中"
			constants.BizStatusMaintaining, // 新业务状态为"维护中"
			workFlowID,
			constants.HardwareStatusNormal,
			constants.HardwareStatusNormal,
			ticket.OfflineTicketNo,
			common.SOFTOFFLINESOURCE,
			ticket.OfflineReason,
			operatorID, // 使用传入的操作人ID
			operatorName,
		)

		if err != nil {
			o.logger.Error("记录资产状态变更失败",
				zap.String("SN", device.SN),
				zap.Error(err))

		}
	}
	o.logger.Info("完成CMDB资产状态和业务状态和记录状态变更", zap.String("工单号", offlineNo))
	return nil

}
