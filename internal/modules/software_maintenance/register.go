package software_maintenance

import (
	"backend/internal/middleware"
	control "backend/internal/modules/software_maintenance/controller"
	repo "backend/internal/modules/software_maintenance/repository"
	"backend/internal/modules/software_maintenance/service"
	"github.com/gin-gonic/gin"
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Module 软件模块结构体
type Module struct {
	db             *gorm.DB
	logger         *zap.Logger
	temporalClient client.Client
	// 上线
	launchService    service.LaunchService
	launchController *control.LaunchController
	//launchActivities activities.LaunchActivity
	// 下线
	offlineService    service.OfflineService
	offlineController *control.OfflineController
	//offlineActivities activities.OfflineActivity
}

// NewModule 创建软件上线模块
func NewModule(db *gorm.DB, logger *zap.Logger, temporalClient client.Client) *Module {
	return &Module{
		db:             db,
		logger:         logger,
		temporalClient: temporalClient,
	}
}

// Initialize 初始化模块
func (m *Module) Initialize() error {
	// 初始化仓库
	launchRepo := repo.NewLaunchRepository(m.db)
	offlineRepo := repo.NewOfflineRepository(m.db)
	// 初始化服务
	m.launchService = service.NewLaunchService(launchRepo, m.temporalClient, m.logger)
	m.offlineService = service.NewOfflineService(offlineRepo, m.temporalClient, m.logger, m.db)
	// 初始化控制器
	m.launchController = control.NewLaunchController(m.launchService)
	m.offlineController = control.NewOfflineController(m.offlineService)
	return nil
}

// RegisterRoutes 注册路由
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	apiGroup := router.Group("/software_maintenance")
	apiGroup.Use(middleware.AuthMiddleware())

	m.launchController.RegisterRoutes(apiGroup)
	m.offlineController.RegisterRoutes(apiGroup)
}

// RegisterSoftwareMaintenance 注册软件运维模块入口
func RegisterSoftwareMaintenance(router *gin.RouterGroup, db *gorm.DB, logger *zap.Logger, temporalClient client.Client) {
	module := NewModule(db, logger, temporalClient)
	if err := module.Initialize(); err != nil {
		logger.Error("初始化软件上线模块失败", zap.Error(err))
		return
	}

	module.RegisterRoutes(router)
}
