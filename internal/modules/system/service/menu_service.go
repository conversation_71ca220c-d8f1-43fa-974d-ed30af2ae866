package service

import (
	"backend/internal/infrastructure/cache"
	"backend/internal/modules/system/model"
	"backend/internal/modules/system/repository"
	userService "backend/internal/modules/user/service"
	"context"
	"fmt"
	"strings"
	"time"
)

// IMenuService 菜单服务接口
type IMenuService interface {
	GetAllMenus(ctx context.Context) ([]model.MenuResponse, error)
	GetMenuByID(ctx context.Context, id uint) (*model.MenuResponse, error)
	CreateMenu(ctx context.Context, menu *model.Menu) error
	UpdateMenu(ctx context.Context, menu *model.Menu) error
	DeleteMenu(ctx context.Context, id uint) error
	GetDashboardMenus(ctx context.Context) ([]model.MenuResponse, error)
	ListMenus(ctx context.Context) ([]model.MenuListResponse, error)
	// 新增方法：根据角色列表获取用户菜单
	GetUserMenusByRoles(ctx context.Context, roles []string, roleService IRoleService) ([]model.MenuResponse, error)
	// 新增方法：过滤按钮类型菜单
	FilterButtonMenus(menus []model.MenuResponse) []model.MenuResponse
	// 新增方法：判断是否为按钮类型菜单
	IsButtonMenu(menu model.MenuResponse) bool
	// 新增方法：清除菜单缓存
	ClearMenuCache(ctx context.Context) error
	// 设置用户服务
	SetUserService(userSvc userService.IUserService)
}

// MenuService 菜单服务实现
type MenuService struct {
	menuRepo    repository.IMenuRepository
	redisCache  *cache.RedisClient
	userService userService.IUserService
}

// NewMenuService 创建菜单服务实例
func NewMenuService(repo repository.IMenuRepository, redisCache *cache.RedisClient) IMenuService {
	return &MenuService{
		menuRepo:   repo,
		redisCache: redisCache,
	}
}

// SetUserService 设置用户服务
func (s *MenuService) SetUserService(userSvc userService.IUserService) {
	s.userService = userSvc
}

// GetAllMenus 获取所有菜单
func (s *MenuService) GetAllMenus(ctx context.Context) ([]model.MenuResponse, error) {
	// 尝试从Redis缓存获取
	var cachedMenus []model.MenuResponse
	cacheKey := "menu:all"

	if s.redisCache != nil {
		// 尝试从缓存获取
		err := s.redisCache.Get(ctx, cacheKey, &cachedMenus)
		if err == nil && len(cachedMenus) > 0 {
			return cachedMenus, nil
		}
	}

	// 缓存未命中，从数据库获取
	menus, err := s.menuRepo.GetAllMenus(ctx)
	if err != nil {
		return nil, err
	}

	// 构建菜单树结构
	menuTree, err := s.buildMenuTree(ctx, menus, 0)
	if err != nil {
		return nil, err
	}

	// 存入Redis缓存
	if s.redisCache != nil {
		if err := s.redisCache.Set(ctx, cacheKey, menuTree, time.Hour*24); err != nil {
			// 记录缓存错误但不影响正常流程
			fmt.Printf("Failed to cache menu tree: %v\n", err)
		}
	}

	return menuTree, nil
}

// GetMenuByID 根据ID获取菜单
func (s *MenuService) GetMenuByID(ctx context.Context, id uint) (*model.MenuResponse, error) {
	// 尝试从Redis缓存获取
	var cachedMenu model.MenuResponse
	cacheKey := fmt.Sprintf("menu:id:%d", id)

	if s.redisCache != nil {
		// 尝试从缓存获取
		err := s.redisCache.Get(ctx, cacheKey, &cachedMenu)
		if err == nil {
			return &cachedMenu, nil
		}
	}

	menu, err := s.menuRepo.GetMenuByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取所有菜单，然后在内存中构建树结构
	allMenus, err := s.menuRepo.GetAllMenus(ctx)
	if err != nil {
		return nil, err
	}

	response := s.convertMenuToResponse(*menu)

	// 查找此菜单的子菜单
	childResponses, err := s.buildMenuTree(ctx, allMenus, menu.ID)
	if err != nil {
		return nil, err
	}
	if len(childResponses) > 0 {
		response.Children = childResponses
	}

	// 存入Redis缓存
	if s.redisCache != nil {
		if err := s.redisCache.Set(ctx, cacheKey, response, time.Hour*24); err != nil {
			// 记录缓存错误但不影响正常流程
			fmt.Printf("Failed to cache menu response: %v\n", err)
		}
	}

	return &response, nil
}

// CreateMenu 创建菜单
func (s *MenuService) CreateMenu(ctx context.Context, menu *model.Menu) error {
	err := s.menuRepo.CreateMenu(ctx, menu)
	if err != nil {
		return err
	}

	// 如果是按钮类型菜单，且有权限码，则同步到super角色
	if menu.Type == "button" && menu.AuthCode != "" {
		// 获取数据库连接
		db := s.menuRepo.GetDB()

		// 使用角色仓库添加权限
		roleRepo := repository.NewRoleRepository(db)

		// 更新super角色权限，添加新按钮的ID
		err = roleRepo.AddPermissionToRole(ctx, "super", menu.ID)
		if err != nil {
			// 记录错误但不中断流程
			fmt.Printf("Failed to add menu permission to super role: %v\n", err)
		}

		// 设置角色权限更新标志
		if s.redisCache != nil {
			updateFlagKey := fmt.Sprintf("role:permission:updated:%s", "super")
			updateTimeKey := fmt.Sprintf("role:permission:update_time:%s", "super")
			if err := s.redisCache.Set(ctx, updateFlagKey, true, time.Hour*24*7); err != nil {
				fmt.Printf("Failed to set permission update flag: %v\n", err)
			}
			if err := s.redisCache.Set(ctx, updateTimeKey, time.Now().Unix(), time.Hour*24*7); err != nil {
				fmt.Printf("Failed to set permission update time: %v\n", err)
			}

			// 清除所有用户的权限码缓存
			if s.userService != nil {
				if err := s.userService.ClearAllAuthCodesCache(ctx); err != nil {
					fmt.Printf("Failed to clear all auth codes cache: %v\n", err)
				}
			} else if s.redisCache != nil {
				// 如果userService未设置，直接使用redisCache清除
				if err := s.redisCache.DelByPattern(ctx, "auth:codes:user:*"); err != nil {
					fmt.Printf("Failed to delete auth codes pattern: %v\n", err)
				}
			}
		}
	}

	// 清除菜单缓存
	if err := s.ClearMenuCache(ctx); err != nil {
		fmt.Printf("Failed to clear menu cache: %v\n", err)
	}

	return nil
}

// UpdateMenu 更新菜单
func (s *MenuService) UpdateMenu(ctx context.Context, menu *model.Menu) error {
	// 获取原菜单信息，检查类型是否变更为按钮类型
	oldMenu, err := s.menuRepo.GetMenuByID(ctx, menu.ID)
	if err != nil {
		return err
	}

	// 更新菜单
	err = s.menuRepo.UpdateMenu(ctx, menu)
	if err != nil {
		return err
	}

	// 如果菜单类型改为按钮类型，或者是按钮类型且权限码有变更
	if (oldMenu.Type != "button" && menu.Type == "button" && menu.AuthCode != "") ||
		(oldMenu.Type == "button" && menu.Type == "button" && oldMenu.AuthCode != menu.AuthCode && menu.AuthCode != "") {
		// 获取数据库连接
		db := s.menuRepo.GetDB()

		// 使用角色仓库添加权限
		roleRepo := repository.NewRoleRepository(db)

		// 更新super角色权限，添加按钮的ID
		err = roleRepo.AddPermissionToRole(ctx, "super", menu.ID)
		if err != nil {
			// 记录错误但不中断流程
			fmt.Printf("Failed to add menu permission to super role: %v\n", err)
		}

		// 设置角色权限更新标志
		if s.redisCache != nil {
			updateFlagKey := fmt.Sprintf("role:permission:updated:%s", "super")
			updateTimeKey := fmt.Sprintf("role:permission:update_time:%s", "super")
			if err := s.redisCache.Set(ctx, updateFlagKey, true, time.Hour*24*7); err != nil {
				fmt.Printf("Failed to set permission update flag: %v\n", err)
			}
			if err := s.redisCache.Set(ctx, updateTimeKey, time.Now().Unix(), time.Hour*24*7); err != nil {
				fmt.Printf("Failed to set permission update time: %v\n", err)
			}

			// 清除所有用户的权限码缓存
			if s.userService != nil {
				if err := s.userService.ClearAllAuthCodesCache(ctx); err != nil {
					fmt.Printf("Failed to clear all auth codes cache: %v\n", err)
				}
			} else if s.redisCache != nil {
				// 如果userService未设置，直接使用redisCache清除
				if err := s.redisCache.DelByPattern(ctx, "auth:codes:user:*"); err != nil {
					fmt.Printf("Failed to delete auth codes pattern: %v\n", err)
				}
			}
		}
	}

	// 清除菜单缓存
	if err := s.ClearMenuCache(ctx); err != nil {
		fmt.Printf("Failed to clear menu cache: %v\n", err)
	}

	return nil
}

// DeleteMenu 删除菜单
func (s *MenuService) DeleteMenu(ctx context.Context, id uint) error {
	// 删除前先获取菜单信息
	menu, err := s.menuRepo.GetMenuByID(ctx, id)
	if err != nil {
		return err
	}

	// 保存菜单是否为按钮类型
	isButton := menu.Type == "button"

	// 删除菜单
	err = s.menuRepo.DeleteMenu(ctx, id)
	if err != nil {
		return err
	}

	// 如果是按钮类型菜单，设置super角色权限更新标志
	if isButton && s.redisCache != nil {
		updateFlagKey := fmt.Sprintf("role:permission:updated:%s", "super")
		updateTimeKey := fmt.Sprintf("role:permission:update_time:%s", "super")
		if err := s.redisCache.Set(ctx, updateFlagKey, true, time.Hour*24*7); err != nil {
			fmt.Printf("Failed to set permission update flag: %v\n", err)
		}
		if err := s.redisCache.Set(ctx, updateTimeKey, time.Now().Unix(), time.Hour*24*7); err != nil {
			fmt.Printf("Failed to set permission update time: %v\n", err)
		}

		// 清除所有用户的权限码缓存
		if s.userService != nil {
			if err := s.userService.ClearAllAuthCodesCache(ctx); err != nil {
				fmt.Printf("Failed to clear all auth codes cache: %v\n", err)
			}
		} else if s.redisCache != nil {
			// 如果userService未设置，直接使用redisCache清除
			if err := s.redisCache.DelByPattern(ctx, "auth:codes:user:*"); err != nil {
				fmt.Printf("Failed to delete auth codes pattern: %v\n", err)
			}
		}
	}

	// 清除菜单缓存
	if err := s.ClearMenuCache(ctx); err != nil {
		fmt.Printf("Failed to clear menu cache: %v\n", err)
	}

	return nil
}

// ClearMenuCache 清除菜单缓存
func (s *MenuService) ClearMenuCache(ctx context.Context) error {
	if s.redisCache == nil {
		return nil
	}

	// 使用模式匹配清除所有菜单相关缓存
	patterns := []string{
		"menu:all",       // 所有菜单缓存
		"menu:id:*",      // 所有单个菜单缓存
		"menu:dashboard", // 仪表盘菜单缓存
		"menu:roles:*",   // 所有角色菜单缓存
		"menu:role:*",    // 单个角色菜单缓存
	}

	for _, pattern := range patterns {
		// 对于确定的键直接删除
		if !strings.Contains(pattern, "*") {
			if err := s.redisCache.Del(ctx, pattern); err != nil {
				fmt.Printf("Failed to delete cache key %s: %v\n", pattern, err)
			}
		} else {
			// 对于模式匹配使用DelByPattern
			if err := s.redisCache.DelByPattern(ctx, pattern); err != nil {
				fmt.Printf("Failed to delete cache pattern %s: %v\n", pattern, err)
			}
		}
	}

	// 同时清除角色服务中的缓存
	// 通知相关系统组件菜单已更新

	return nil
}

// GetDashboardMenus 获取仪表盘菜单
func (s *MenuService) GetDashboardMenus(ctx context.Context) ([]model.MenuResponse, error) {
	// 尝试从Redis缓存获取
	var cachedMenus []model.MenuResponse
	cacheKey := "menu:dashboard"

	if s.redisCache != nil {
		// 尝试从缓存获取
		err := s.redisCache.Get(ctx, cacheKey, &cachedMenus)
		if err == nil && len(cachedMenus) > 0 {
			return cachedMenus, nil
		}
	}

	// 这里实现仪表盘菜单的获取逻辑
	// 可以从数据库获取，也可以直接返回预定义的菜单结构

	// 创建仪表盘菜单
	dashboardMenus := []model.MenuResponse{
		{
			Name:     "Dashboard",
			Path:     "/",
			Redirect: "/analytics",
			Meta: model.MenuMeta{
				Order: -1,
				Title: "page.dashboard.title",
			},
			Children: []model.MenuResponse{
				{
					Name:      "Analytics",
					Path:      "/analytics",
					Component: "/dashboard/analytics/index",
					Meta: model.MenuMeta{
						AffixTab: true,
						Title:    "page.dashboard.analytics",
					},
				},
				{
					Name:      "Workspace",
					Path:      "/workspace",
					Component: "/dashboard/workspace/index",
					Meta: model.MenuMeta{
						Title: "page.dashboard.workspace",
					},
				},
			},
		},
		{
			Name:      "Test",
			Path:      "/test",
			Component: "/test/index",
			Meta: model.MenuMeta{
				Title:         "page.test",
				NoBasicLayout: true,
			},
		},
	}

	// 存入Redis缓存
	if s.redisCache != nil {
		if err := s.redisCache.Set(ctx, cacheKey, dashboardMenus, time.Hour*24); err != nil {
			// 记录缓存错误但不影响正常流程
			fmt.Printf("Failed to cache dashboard menus: %v\n", err)
		}
	}

	return dashboardMenus, nil
}

// ListMenus 获取菜单管理列表
func (s *MenuService) ListMenus(ctx context.Context) ([]model.MenuListResponse, error) {
	// 对于管理页面的菜单列表，我们不使用缓存，始终返回最新数据
	menus, err := s.menuRepo.GetAllMenusWithoutStatusFilter(ctx)
	if err != nil {
		return nil, err
	}

	// 构建菜单树结构
	menuList, err := s.buildMenuListTree(ctx, menus, 0)
	if err != nil {
		return nil, err
	}

	// 对结果进行排序
	return s.sortMenuListWithChildren(menuList), nil
}

// 辅助函数：构建菜单树结构
func (s *MenuService) buildMenuTree(ctx context.Context, menus []model.Menu, parentID uint) ([]model.MenuResponse, error) {
	var result []model.MenuResponse

	for _, menu := range menus {
		if menu.ParentID == parentID {
			menuResp := s.convertMenuToResponse(menu)

			// 递归获取子菜单
			children, err := s.menuRepo.GetChildMenus(ctx, menu.ID)
			if err != nil {
				return nil, err
			}

			if len(children) > 0 {
				childResponses, err := s.buildMenuTree(ctx, children, menu.ID)
				if err != nil {
					return nil, err
				}

				// 按meta.order排序子菜单
				// 分为两组：有order和没有order的
				var withOrder []model.MenuResponse
				var withoutOrder []model.MenuResponse

				for _, child := range childResponses {
					if child.Meta.Order == 0 {
						withoutOrder = append(withoutOrder, child)
					} else {
						withOrder = append(withOrder, child)
					}
				}

				// 对有order的菜单按order从小到大排序
				for i := 0; i < len(withOrder); i++ {
					for j := i + 1; j < len(withOrder); j++ {
						if withOrder[i].Meta.Order > withOrder[j].Meta.Order {
							withOrder[i], withOrder[j] = withOrder[j], withOrder[i]
						}
					}
				}

				// 先放没有order的，再放有order的
				menuResp.Children = append(withoutOrder, withOrder...)
			}

			result = append(result, menuResp)
		}
	}

	return result, nil
}

// 辅助函数：将数据库菜单模型转换为响应结构
func (s *MenuService) convertMenuToResponse(menu model.Menu) model.MenuResponse {
	return model.MenuResponse{
		Name:      menu.Name,
		Path:      menu.Path,
		Component: menu.Component,
		Redirect:  menu.Redirect,
		Meta:      menu.Meta,
	}
}

// 辅助函数：构建菜单列表树结构
func (s *MenuService) buildMenuListTree(ctx context.Context, menus []model.Menu, parentID uint) ([]model.MenuListResponse, error) {
	var result []model.MenuListResponse

	for _, menu := range menus {
		if menu.ParentID == parentID {
			menuResp := s.convertMenuToListResponse(menu)

			// 查找当前菜单的所有子菜单
			var childMenus []model.Menu
			for _, childMenu := range menus {
				if childMenu.ParentID == menu.ID {
					childMenus = append(childMenus, childMenu)
				}
			}

			// 处理子菜单
			if len(childMenus) > 0 {
				childResponses, err := s.buildMenuListTree(ctx, menus, menu.ID)
				if err != nil {
					return nil, err
				}
				menuResp.Children = childResponses
			}

			result = append(result, menuResp)
		}
	}

	return result, nil
}

// 辅助函数：将数据库菜单模型转换为列表响应结构
func (s *MenuService) convertMenuToListResponse(menu model.Menu) model.MenuListResponse {
	// 使用数据库中存储的菜单类型
	menuType := menu.Type
	// 如果类型为空，则根据条件判断
	if menuType == "" {
		if menu.ParentID == 0 {
			menuType = "catalog"
		} else if menu.Component == "" {
			menuType = "catalog"
		} else if menu.Path == "" && menu.Component == "" {
			menuType = "button"
		} else {
			menuType = "menu"
		}
	}

	// 确保获取正确的AuthCode，不要硬编码
	authCode := menu.AuthCode

	return model.MenuListResponse{
		ID:        menu.ID,
		ParentID:  menu.ParentID,
		Name:      menu.Name,
		Path:      menu.Path,
		Component: menu.Component,
		Status:    menu.Status, // 使用数据库中存储的状态
		Type:      menuType,
		Meta:      menu.Meta,
		AuthCode:  authCode, // 使用数据库中的值
		Order:     menu.Order,
	}
}

// 新增方法: 根据角色列表获取用户菜单
func (s *MenuService) GetUserMenusByRoles(ctx context.Context, roles []string, roleService IRoleService) ([]model.MenuResponse, error) {
	// 缓存键格式: menu:roles:[role1,role2,...]
	cacheKey := fmt.Sprintf("menu:roles:%v", roles)

	if s.redisCache != nil {
		// 尝试从缓存获取
		var cachedMenus []model.MenuResponse
		err := s.redisCache.Get(ctx, cacheKey, &cachedMenus)
		if err == nil && len(cachedMenus) > 0 {
			return cachedMenus, nil
		}
	}

	// 检查是否是超级管理员
	isSuperAdmin := false
	for _, role := range roles {
		if role == "super" {
			isSuperAdmin = true
			break
		}
	}

	var menus []model.MenuResponse
	var err error

	// 超级管理员返回所有菜单（不包括按钮类型）
	if isSuperAdmin {
		menus, err = s.GetAllMenus(ctx)
		if err != nil {
			return nil, err
		}
		// 过滤掉按钮类型的菜单
		menus = s.FilterButtonMenus(menus)

		// 缓存结果
		if s.redisCache != nil {
			if err := s.redisCache.Set(ctx, cacheKey, menus, time.Hour*24); err != nil {
				// 记录缓存错误但不影响正常流程
				fmt.Printf("Failed to cache user menus: %v\n", err)
			}
		}

		return menus, nil
	}

	// 非超级管理员，根据角色获取有权限的菜单
	if len(roles) == 0 {
		return nil, nil // 返回空表示没有权限
	}

	// 处理多角色情况
	var allMenus []model.MenuResponse
	menuMap := make(map[string]model.MenuResponse) // 用于去重

	for _, roleID := range roles {
		roleMenus, err := roleService.GetUserMenusByRoleID(ctx, roleID)
		if err != nil {
			continue // 某个角色出错继续处理其他角色
		}

		// 将菜单添加到map中去重
		for _, menu := range roleMenus {
			// 使用菜单路径作为唯一标识
			menuMap[menu.Path] = menu
		}
	}

	// 将map转换回切片
	for _, menu := range menuMap {
		allMenus = append(allMenus, menu)
	}

	// 过滤掉按钮类型的菜单
	result := s.FilterButtonMenus(allMenus)

	// 对菜单进行排序，包括每个菜单的子菜单
	result = s.sortMenusWithChildren(result)

	// 缓存结果
	if s.redisCache != nil {
		if err := s.redisCache.Set(ctx, cacheKey, result, time.Hour*24); err != nil {
			// 记录缓存错误但不影响正常流程
			fmt.Printf("Failed to cache user menus: %v\n", err)
		}
	}

	return result, nil
}

// 新增辅助函数：递归排序菜单及其子菜单
func (s *MenuService) sortMenusWithChildren(menus []model.MenuResponse) []model.MenuResponse {
	// 先对当前层级菜单排序
	// 分为两组：有order和没有order的
	var withOrder []model.MenuResponse
	var withoutOrder []model.MenuResponse

	for _, menu := range menus {
		// 如果菜单有子菜单，先递归排序子菜单
		if len(menu.Children) > 0 {
			menu.Children = s.sortMenusWithChildren(menu.Children)
		}

		if menu.Meta.Order == 0 {
			withoutOrder = append(withoutOrder, menu)
		} else {
			withOrder = append(withOrder, menu)
		}
	}

	// 对有order的菜单按order从小到大排序
	for i := 0; i < len(withOrder); i++ {
		for j := i + 1; j < len(withOrder); j++ {
			if withOrder[i].Meta.Order > withOrder[j].Meta.Order {
				withOrder[i], withOrder[j] = withOrder[j], withOrder[i]
			}
		}
	}

	// 先放没有order的，再放有order的
	return append(withoutOrder, withOrder...)
}

// FilterButtonMenus 过滤掉按钮类型的菜单
func (s *MenuService) FilterButtonMenus(menus []model.MenuResponse) []model.MenuResponse {
	var result []model.MenuResponse

	for _, menu := range menus {
		// 检查菜单名称和路径，跳过可能的按钮类型菜单
		// 按钮类型的菜单通常没有组件路径或者有特殊的命名规则
		if !s.IsButtonMenu(menu) {
			// 如果有子菜单，递归过滤子菜单
			if len(menu.Children) > 0 {
				menu.Children = s.FilterButtonMenus(menu.Children)
			}
			result = append(result, menu)
		}
	}

	return result
}

// IsButtonMenu 判断是否为按钮类型的菜单
func (s *MenuService) IsButtonMenu(menu model.MenuResponse) bool {
	// 按钮类型菜单的特征：
	// 1. 通常没有组件路径
	// 2. 可能有特殊的命名规则，如以Create、Edit、Delete等结尾
	// 3. 可能没有子菜单

	// 检查名称是否包含按钮特征
	buttonKeywords := []string{"Create", "Edit", "Delete", "Update", "Remove", "Add", "Import", "Export", "Download", "Upload"}
	for _, keyword := range buttonKeywords {
		if len(menu.Name) >= len(keyword) && menu.Name[len(menu.Name)-len(keyword):] == keyword {
			return true
		}
	}

	// 如果没有组件路径且路径为空或很短，可能是按钮
	if menu.Component == "" && (menu.Path == "" || len(menu.Path) < 3) {
		return true
	}

	return false
}

// 新增辅助函数：递归排序菜单列表及其子菜单
func (s *MenuService) sortMenuListWithChildren(menus []model.MenuListResponse) []model.MenuListResponse {
	// 先对当前层级菜单排序
	// 分为两组：有order和没有order的
	var withOrder []model.MenuListResponse
	var withoutOrder []model.MenuListResponse

	for _, menu := range menus {
		// 如果菜单有子菜单，先递归排序子菜单
		if len(menu.Children) > 0 {
			menu.Children = s.sortMenuListWithChildren(menu.Children)
		}

		if menu.Meta.Order == 0 {
			withoutOrder = append(withoutOrder, menu)
		} else {
			withOrder = append(withOrder, menu)
		}
	}

	// 对有order的菜单按order从小到大排序
	for i := 0; i < len(withOrder); i++ {
		for j := i + 1; j < len(withOrder); j++ {
			if withOrder[i].Meta.Order > withOrder[j].Meta.Order {
				withOrder[i], withOrder[j] = withOrder[j], withOrder[i]
			}
		}
	}

	// 先放没有order的，再放有order的
	return append(withoutOrder, withOrder...)
}
