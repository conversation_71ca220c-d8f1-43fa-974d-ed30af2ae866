package controller

import (
	"backend/internal/modules/system/model"
	sysservice "backend/internal/modules/system/service"
	"backend/response"
	"context"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 定义自定义键类型，避免使用内置字符串作为context的键
type contextKey string

// 定义常量作为context的键
const (
	menuUpdateIDKey contextKey = "menuUpdateID"
)

// MenuController 菜单控制器
type MenuController struct {
	menuService sysservice.IMenuService
	roleService sysservice.IRoleService
}

// NewMenuController 创建菜单控制器
func NewMenuController(ms sysservice.IMenuService, rs sysservice.IRoleService) *MenuController {
	return &MenuController{
		menuService: ms,
		roleService: rs,
	}
}

// GetAllMenus godoc
// @Summary 获取当前用户菜单
// @Description 根据当前用户的角色权限获取菜单
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} []model.MenuResponse "获取菜单成功"
// @Failure 401 {string} string "未授权"
// @Failure 500 {string} string "获取菜单失败"
// @Router /system/menu/all [get]
func (ctrl *MenuController) GetAllMenus(c *gin.Context) {
	// 从上下文获取用户ID和角色
	_, exists := c.Get("userID")
	if !exists {
		response.Response(c, http.StatusUnauthorized, nil, "用户未登录")
		return
	}

	rolesVal, exists := c.Get("userRoles")
	if !exists {
		response.Response(c, http.StatusInternalServerError, nil, "获取用户角色失败")
		return
	}

	// 获取角色列表，支持多种可能的类型
	var roles []string

	// 尝试不同的类型转换
	if rolesArray, ok := rolesVal.([]string); ok {
		// 标准字符串数组
		roles = rolesArray
	} else if stringArray, ok := rolesVal.(model.StringArray); ok {
		// model.StringArray类型
		roles = []string(stringArray)
	} else if anyArray, ok := rolesVal.([]interface{}); ok {
		// 处理可能的[]interface{}类型
		for _, v := range anyArray {
			if s, ok := v.(string); ok {
				roles = append(roles, s)
			}
		}
	} else {
		// 如果是单一字符串，转为数组
		if role, ok := rolesVal.(string); ok {
			roles = []string{role}
		} else {
			response.Response(c, http.StatusInternalServerError, nil, "用户角色格式错误")
			return
		}
	}

	// 使用service层方法处理菜单获取逻辑
	menus, err := ctrl.menuService.GetUserMenusByRoles(c.Request.Context(), roles, ctrl.roleService)
	if err != nil {
		if err := c.Error(err); err != nil {
			// 记录错误但继续执行
			log.Printf("Failed to add error to context: %v", err)
		}
		response.Response(c, http.StatusInternalServerError, nil, "获取菜单失败: "+err.Error())
		return
	}

	// 如果用户没有角色或没有任何菜单权限
	if menus == nil && len(roles) == 0 {
		response.Response(c, http.StatusForbidden, nil, "用户没有分配角色")
		return
	}

	response.Success(c, menus, "获取菜单成功")
}

// GetDashboardMenus godoc
// @Summary 获取仪表盘菜单
// @Description 获取仪表盘菜单
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} []model.MenuResponse "获取仪表盘菜单成功"
// @Failure 500 {string} string "获取仪表盘菜单失败"
// @Router /system/menu/dashboard [get]
func (ctrl *MenuController) GetDashboardMenus(c *gin.Context) {
	menus, err := ctrl.menuService.GetDashboardMenus(c.Request.Context())
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "获取仪表盘菜单失败")
		return
	}
	response.Success(c, menus, "获取仪表盘菜单成功")
}

// GetMenuByID godoc
// @Summary 根据ID获取菜单
// @Description 根据ID获取菜单
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "菜单ID"
// @Success 200 {object} model.MenuResponse "获取菜单成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 500 {string} string "获取菜单失败"
// @Router /system/menu/{id} [get]
func (ctrl *MenuController) GetMenuByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Response(c, http.StatusBadRequest, nil, "请求参数错误")
		return
	}

	menu, err := ctrl.menuService.GetMenuByID(c.Request.Context(), uint(id))
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "获取菜单失败")
		return
	}
	response.Success(c, menu, "获取菜单成功")
}

// CreateMenu godoc
// @Summary 创建菜单
// @Description 创建菜单
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param menu body model.Menu true "菜单信息"
// @Success 200 {string} string "创建菜单成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 500 {string} string "创建菜单失败"
// @Router /system/menu [post]
func (ctrl *MenuController) CreateMenu(c *gin.Context) {
	var menu model.Menu
	if err := c.ShouldBindJSON(&menu); err != nil {
		response.Response(c, http.StatusBadRequest, nil, "请求参数错误")
		return
	}

	if err := ctrl.menuService.CreateMenu(c.Request.Context(), &menu); err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "创建菜单失败")
		return
	}
	response.Success(c, nil, "创建菜单成功")
}

// UpdateMenu godoc
// @Summary 更新菜单
// @Description 更新菜单
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param menu body model.Menu true "菜单信息"
// @Success 200 {string} string "更新菜单成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 500 {string} string "更新菜单失败"
// @Router /system/menu [put]
func (ctrl *MenuController) UpdateMenu(c *gin.Context) {
	var menu model.Menu
	if err := c.ShouldBindJSON(&menu); err != nil {
		response.Response(c, http.StatusBadRequest, nil, "请求参数错误")
		return
	}

	// 记录更新前的菜单ID，便于诊断，使用自定义类型作为键
	c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), menuUpdateIDKey, menu.ID))

	// 调用服务层更新菜单
	if err := ctrl.menuService.UpdateMenu(c.Request.Context(), &menu); err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "更新菜单失败")
		return
	}

	// 添加日志，记录菜单更新成功
	logger, exists := c.Get("logger")
	if exists {
		if log, ok := logger.(*zap.Logger); ok {
			log.Info("菜单更新成功",
				zap.Uint("menuID", menu.ID),
				zap.String("menuName", menu.Name),
				zap.Bool("menuStatus", menu.Status))
		}
	}

	response.Success(c, nil, "更新菜单成功")
}

// DeleteMenu godoc
// @Summary 删除菜单
// @Description 删除菜单
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "菜单ID"
// @Success 200 {string} string "删除菜单成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 500 {string} string "删除菜单失败"
// @Router /system/menu/{id} [delete]
func (ctrl *MenuController) DeleteMenu(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Response(c, http.StatusBadRequest, nil, "请求参数错误")
		return
	}

	if err := ctrl.menuService.DeleteMenu(c.Request.Context(), uint(id)); err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "删除菜单失败")
		return
	}
	response.Success(c, nil, "删除菜单成功")
}

// ListMenus godoc
// @Summary 获取菜单管理列表
// @Description 获取带有id、type、status等字段的菜单管理列表
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} []model.MenuListResponse "获取菜单列表成功"
// @Failure 500 {string} string "获取菜单列表失败"
// @Router /system/menu/list [get]
func (ctrl *MenuController) ListMenus(c *gin.Context) {
	menus, err := ctrl.menuService.ListMenus(c.Request.Context())
	if err != nil {
		if err := c.Error(err); err != nil {
			// 记录错误但继续执行
			log.Printf("Failed to add error to context: %v", err)
		}
		response.Response(c, http.StatusInternalServerError, nil, "获取菜单列表失败: "+err.Error())
		return
	}
	response.Success(c, menus, "获取菜单列表成功")
}
