package model

import (
	"time"

	"gorm.io/gorm"
)

// Role 角色模型
type Role struct {
	ID         uint           `gorm:"primaryKey"`
	Name       string         `json:"name" gorm:"type:varchar(50);not null"`
	Status     int            `json:"status" gorm:"type:tinyint;default:1"` // 0-禁用 1-启用
	CreateTime time.Time      `json:"createTime" gorm:"autoCreateTime"`
	UpdateTime time.Time      `json:"updateTime" gorm:"autoUpdateTime"`
	Remark     string         `json:"remark" gorm:"type:varchar(255)"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
}

// RolePermission 角色权限关联模型
type RolePermission struct {
	ID           uint           `gorm:"primaryKey;autoIncrement"`
	RoleID       string         `json:"roleId" gorm:"type:varchar(36);not null;index"`
	PermissionID uint           `json:"permissionId" gorm:"type:int;not null"`
	CreateTime   time.Time      `json:"createTime" gorm:"autoCreateTime"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// RoleResponse 角色响应结构
type RoleResponse struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Status      int    `json:"status"`
	CreateTime  string `json:"createTime"`
	Permissions []uint `json:"permissions"`
	Remark      string `json:"remark"`
}

// RoleListResponse 角色列表响应结构
type RoleListResponse struct {
	Items []RoleResponse `json:"items"`
	Total int64          `json:"total"`
}

// RoleCreateRequest 创建角色请求结构
type RoleCreateRequest struct {
	Name        string `json:"name" binding:"required"`
	Status      int    `json:"status"`
	Permissions []uint `json:"permissions"`
	Remark      string `json:"remark"`
}

// RoleUpdateRequest 更新角色请求结构
type RoleUpdateRequest struct {
	ID          string `json:"id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Status      int    `json:"status"`
	Permissions []uint `json:"permissions"`
	Remark      string `json:"remark"`
}
