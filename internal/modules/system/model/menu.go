package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"gorm.io/gorm"
)

// Menu 菜单模型
type Menu struct {
	gorm.Model
	Name      string   `gorm:"type:varchar(50);not null" json:"name"`
	Path      string   `gorm:"type:varchar(255);not null" json:"path"`
	Component string   `gorm:"type:varchar(255)" json:"component,omitempty"`
	Redirect  string   `gorm:"type:varchar(255)" json:"redirect,omitempty"`
	Meta      MenuMeta `gorm:"embedded;embeddedPrefix:meta_" json:"meta"`
	ParentID  uint     `gorm:"default:0" json:"pid,omitempty"`                               // 父菜单ID，0表示顶级菜单
	Order     int      `gorm:"default:0" json:"order,omitempty"`                             // 排序字段
	Status    bool     `gorm:"default:true" json:"status"`                                   // 菜单状态：
	Type      string   `gorm:"type:varchar(20);default:'menu'" json:"type"`                  // 菜单类型：catalog-目录，menu-菜单，button-按钮,external-外链,iframe-内嵌页面
	AuthCode  string   `gorm:"column:auth_code;type:varchar(100)" json:"authCode,omitempty"` // 权限标识
}

// MenuMeta 菜单元数据
type MenuMeta struct {
	Title                    string `json:"title" gorm:"type:varchar(100);not null"`
	Icon                     string `json:"icon,omitempty" gorm:"type:varchar(100)"`
	ActiveIcon               string `json:"activeIcon,omitempty" gorm:"type:varchar(100)"`
	ActivePath               string `json:"activePath,omitempty" gorm:"type:varchar(255)"`
	AffixTab                 bool   `json:"affixTab,omitempty" gorm:"default:false"`
	AffixTabOrder            int    `json:"affixTabOrder,omitempty" gorm:"default:0"`
	Badge                    string `json:"badge,omitempty" gorm:"type:varchar(50)"`
	BadgeType                string `json:"badgeType,omitempty" gorm:"type:varchar(20)"`
	BadgeVariants            string `json:"badgeVariants,omitempty" gorm:"type:varchar(20)"`
	HideChildrenInMenu       bool   `json:"hideChildrenInMenu,omitempty" gorm:"default:false"`
	HideInBreadcrumb         bool   `json:"hideInBreadcrumb,omitempty" gorm:"default:false"`
	HideInMenu               bool   `json:"hideInMenu,omitempty" gorm:"default:false"`
	HideInTab                bool   `json:"hideInTab,omitempty" gorm:"default:false"`
	IframeSrc                string `json:"iframeSrc,omitempty" gorm:"type:varchar(255)"`
	IgnoreAccess             bool   `json:"ignoreAccess,omitempty" gorm:"default:false"`
	KeepAlive                bool   `json:"keepAlive,omitempty" gorm:"default:false"`
	Link                     string `json:"link,omitempty" gorm:"type:varchar(255)"`
	MenuVisibleWithForbidden bool   `json:"menuVisibleWithForbidden,omitempty" gorm:"default:false"`
	NoBasicLayout            bool   `json:"noBasicLayout,omitempty" gorm:"default:false"`
	OpenInNewWindow          bool   `json:"openInNewWindow,omitempty" gorm:"default:false"`
	Order                    int    `json:"order,omitempty" gorm:"default:0"`
}

// MenuResponse 菜单响应结构
type MenuResponse struct {
	Name      string         `json:"name"`
	Path      string         `json:"path"`
	Component string         `json:"component,omitempty"`
	Redirect  string         `json:"redirect,omitempty"`
	Meta      MenuMeta       `json:"meta"`
	Children  []MenuResponse `json:"children,omitempty"`
}

// MenuListResponse 菜单列表响应结构
type MenuListResponse struct {
	ID        uint               `json:"id"`
	ParentID  uint               `json:"pid,omitempty"`
	Name      string             `json:"name"`
	Path      string             `json:"path"`
	Component string             `json:"component,omitempty"`
	Status    bool               `json:"status"`
	Type      string             `json:"type"`
	AuthCode  string             `json:"authCode,omitempty"`
	Order     int                `json:"order,omitempty"`
	Meta      MenuMeta           `json:"meta"`
	Children  []MenuListResponse `json:"children,omitempty"`
}

// StringArray 字符串数组类型
type StringArray []string

// Scan 实现 sql.Scanner 接口
func (sa *StringArray) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("failed to unmarshal StringArray value")
	}

	err := json.Unmarshal(bytes, &sa)
	return err
}

// Value 实现 driver.Valuer 接口
func (sa StringArray) Value() (driver.Value, error) {
	if len(sa) == 0 {
		return nil, nil
	}
	return json.Marshal(sa)
}
