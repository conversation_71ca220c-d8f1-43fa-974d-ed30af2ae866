package repository

import (
	"backend/internal/modules/system/model"
	"context"

	"gorm.io/gorm"
)

// IRoleRepository 角色仓库接口
type IRoleRepository interface {
	ListRoles(ctx context.Context, page, size int, name string) ([]model.Role, int64, error)
	GetRoleByID(ctx context.Context, id string) (*model.Role, error)
	CreateRole(ctx context.Context, role *model.Role) error
	UpdateRole(ctx context.Context, role *model.Role) error
	DeleteRole(ctx context.Context, id string) error
	GetRolePermissionIDs(ctx context.Context, roleID string) ([]uint, error)
	UpdateRolePermissions(ctx context.Context, roleID string, permissions []uint) error
	GetAllRolePermissions(ctx context.Context) ([]model.RolePermission, error)
	GetRolePermissionRecords(ctx context.Context, roleID string) ([]model.RolePermission, error)
	AddPermissionToRole(ctx context.Context, roleID string, permissionID uint) error
}

// RoleRepository 角色仓库实现
type RoleRepository struct {
	db *gorm.DB
}

// NewRoleRepository 创建角色仓库实例
func NewRoleRepository(db *gorm.DB) IRoleRepository {
	return &RoleRepository{
		db: db,
	}
}

// ListRoles 获取角色列表
func (r *RoleRepository) ListRoles(ctx context.Context, page, size int, name string) ([]model.Role, int64, error) {
	var roles []model.Role
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Role{})

	// 添加查询条件
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && size > 0 {
		offset := (page - 1) * size
		query = query.Offset(offset).Limit(size)
	}

	// 执行查询
	if err := query.Order("create_time DESC").Find(&roles).Error; err != nil {
		return nil, 0, err
	}

	return roles, total, nil
}

// GetRoleByID 根据ID获取角色
func (r *RoleRepository) GetRoleByID(ctx context.Context, id string) (*model.Role, error) {
	var role model.Role
	if err := r.db.WithContext(ctx).First(&role, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &role, nil
}

// CreateRole 创建角色
func (r *RoleRepository) CreateRole(ctx context.Context, role *model.Role) error {
	return r.db.WithContext(ctx).Create(role).Error
}

// UpdateRole 更新角色
func (r *RoleRepository) UpdateRole(ctx context.Context, role *model.Role) error {
	return r.db.WithContext(ctx).Save(role).Error
}

// DeleteRole 删除角色
func (r *RoleRepository) DeleteRole(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&model.Role{}, "id = ?", id).Error
}

// GetRolePermissionIDs 获取角色权限ID列表
func (r *RoleRepository) GetRolePermissionIDs(ctx context.Context, roleID string) ([]uint, error) {
	var permissions []model.RolePermission
	// 默认只查询未软删除的记录，使用Find方法
	if err := r.db.WithContext(ctx).Where("role_id = ?", roleID).Find(&permissions).Error; err != nil {
		return nil, err
	}

	var permissionIDs []uint
	for _, p := range permissions {
		permissionIDs = append(permissionIDs, p.PermissionID)
	}

	return permissionIDs, nil
}

// 辅助函数：计算需要执行的权限操作
func calculatePermissionChanges(allPermissions []model.RolePermission, newPermissionIDs []uint, roleID string) (toSoftDelete, toRestore []uint, toAdd []model.RolePermission) {
	// 将所有权限转换为map，方便查找（包括已软删除的）
	existingPermMap := make(map[uint]model.RolePermission)
	for _, p := range allPermissions {
		existingPermMap[p.PermissionID] = p
	}

	// 将新权限转换为map，方便查找
	newPermMap := make(map[uint]bool)
	for _, p := range newPermissionIDs {
		newPermMap[p] = true
	}

	// 1. 找出需要软删除的权限（在新权限中不存在且未被删除的）
	for _, p := range allPermissions {
		if _, exists := newPermMap[p.PermissionID]; !exists && p.DeletedAt.Time.IsZero() {
			toSoftDelete = append(toSoftDelete, p.PermissionID)
		}
	}

	// 2. 找出需要恢复的权限（在新权限中存在且已被删除的）
	for permID := range newPermMap {
		if p, exists := existingPermMap[permID]; exists && !p.DeletedAt.Time.IsZero() {
			toRestore = append(toRestore, permID)
		}
	}

	// 3. 找出需要新增的权限（在所有权限中不存在的）
	for permID := range newPermMap {
		if _, exists := existingPermMap[permID]; !exists {
			toAdd = append(toAdd, model.RolePermission{
				RoleID:       roleID,
				PermissionID: permID,
			})
		}
	}

	return toSoftDelete, toRestore, toAdd
}

// UpdateRolePermissions 更新角色权限
func (r *RoleRepository) UpdateRolePermissions(ctx context.Context, roleID string, permissions []uint) error {
	// 开启事务
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取所有权限记录（包括已软删除的）
		var allPermissions []model.RolePermission
		if err := tx.Unscoped().Where("role_id = ?", roleID).Find(&allPermissions).Error; err != nil {
			return err
		}

		// 计算需要执行的各种操作
		toSoftDelete, toRestore, toAdd := calculatePermissionChanges(allPermissions, permissions, roleID)

		// 执行软删除操作
		if len(toSoftDelete) > 0 {
			if err := tx.Where("role_id = ? AND permission_id IN ?", roleID, toSoftDelete).Delete(&model.RolePermission{}).Error; err != nil {
				return err
			}
		}

		// 执行恢复操作
		if len(toRestore) > 0 {
			// 清除deleted_at并更新update_time
			if err := tx.Unscoped().Model(&model.RolePermission{}).
				Where("role_id = ? AND permission_id IN ?", roleID, toRestore).
				Updates(map[string]interface{}{
					"deleted_at": nil,
				}).Error; err != nil {
				return err
			}
		}

		// 执行新增操作
		if len(toAdd) > 0 {
			if err := tx.Create(&toAdd).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// GetAllRolePermissions 获取所有角色权限
func (r *RoleRepository) GetAllRolePermissions(ctx context.Context) ([]model.RolePermission, error) {
	var rolePermissions []model.RolePermission
	if err := r.db.WithContext(ctx).Find(&rolePermissions).Error; err != nil {
		return nil, err
	}
	return rolePermissions, nil
}

// GetRolePermissionRecords 获取指定角色的权限记录
func (r *RoleRepository) GetRolePermissionRecords(ctx context.Context, roleID string) ([]model.RolePermission, error) {
	var rolePermissions []model.RolePermission
	if err := r.db.WithContext(ctx).Where("role_id = ?", roleID).Find(&rolePermissions).Error; err != nil {
		return nil, err
	}
	return rolePermissions, nil
}

// AddPermissionToRole 向角色添加权限
func (r *RoleRepository) AddPermissionToRole(ctx context.Context, roleID string, permissionID uint) error {
	// 先检查是否已存在该权限（包括已软删除的）
	var existingPermission model.RolePermission
	result := r.db.WithContext(ctx).Unscoped().Where("role_id = ? AND permission_id = ?", roleID, permissionID).First(&existingPermission)

	if result.Error == nil {
		// 权限记录已存在
		if !existingPermission.DeletedAt.Time.IsZero() {
			// 如果已被软删除，则恢复
			return r.db.WithContext(ctx).Unscoped().Model(&model.RolePermission{}).
				Where("role_id = ? AND permission_id = ?", roleID, permissionID).
				Updates(map[string]interface{}{
					"deleted_at": nil,
				}).Error
		}
		// 权限已存在且未被删除，无需操作
		return nil
	}

	// 权限不存在，添加新权限
	newPermission := model.RolePermission{
		RoleID:       roleID,
		PermissionID: permissionID,
	}
	return r.db.WithContext(ctx).Create(&newPermission).Error
}
