package service

import (
	exportModel "backend/internal/modules/export/model"
	"backend/internal/modules/purchase_old/model"
	"backend/internal/modules/purchase_old/repository"
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/xuri/excelize/v2"

	"gorm.io/gorm"
)

// purchaseServiceImpl 采购服务实现
type purchaseServiceImpl struct {
	repo repository.PurchaseRepository
}

// NewPurchaseService 创建采购服务
func NewPurchaseService(repo repository.PurchaseRepository) PurchaseService {
	return &purchaseServiceImpl{repo: repo}
}

// CreatePurchaseOrder 通过用户手动创建采购合同
func (s *purchaseServiceImpl) CreatePurchaseOrderByInput(ctx context.Context, dto CreatePurchaseOrderDTO) (*model.PurchaseOrder, error) {
	// 验证数据
	if dto.PurchaseOrderNo == "" || dto.SupplierName == "" || len(dto.Details) == 0 {
		return nil, ErrInvalidPurchaseData
	}

	// 检查采购订单号是否已存在
	//existingOrder, err := s.repo.GetByPurchaseOrderNo(ctx, dto.PurchaseOrderNo)
	//if err == nil && existingOrder != nil {
	//	return nil, ErrDuplicatePONumber
	//} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
	//	// 如果是其他错误则返回
	//	return nil, err
	//}

	// 创建采购订单对象
	purchaseOrder := &model.PurchaseOrder{
		PurchaseTitle:   dto.PurchaseTitle,
		PurchaseOrderNo: dto.PurchaseOrderNo,
		SupplierName:    dto.SupplierName,
		OrderDate:       dto.OrderDate,
		PurchaseType:    dto.PurchaseType,
		// TotalAmount 会在 repository 层计算
	}

	// 转换详情项
	details := make([]model.PurchaseDetails, len(dto.Details))
	for i, detailDTO := range dto.Details {
		details[i] = model.PurchaseDetails{
			ProductID:  detailDTO.ProductID,
			Amount:     detailDTO.Amount,
			Price:      detailDTO.Price,
			TemplateID: detailDTO.TemplateID,
			//PN:              detailDTO.PN,
			//MaterialType:    detailDTO.MaterialType,
			//Brand:           detailDTO.Brand,
			//ProductModel:    detailDTO.ProductModel,
			//ProductCategory: detailDTO.ProductCategory,
		}
	}

	// 调用仓库层创建采购订单和详情（事务操作）
	if err := s.repo.CreateWithDetails(ctx, purchaseOrder, details); err != nil {
		return nil, err
	}

	// 返回创建成功的订单（包含详情）
	return s.repo.GetByID(ctx, purchaseOrder.ID)
}

// CreatePurchaseOrderByFile 通过文件的形式创建采购单
func (s *purchaseServiceImpl) CreatePurchaseOrderByFile(ctx context.Context, form model.ImportForm) (*model.PurchaseOrder, error) {
	// 验证数据
	if form.PurchaseOrderNo == "" {
		return nil, fmt.Errorf("采购订单编号不能为空")
	}

	if form.SupplierName == "" {
		return nil, fmt.Errorf("供应商名称不能为空")
	}

	if form.File == nil {
		return nil, fmt.Errorf("文件不能为空")
	}

	// 创建采购订单对象
	purchaseOrder := &model.PurchaseOrder{
		PurchaseTitle:   form.PurchaseTitle,
		PurchaseOrderNo: form.PurchaseOrderNo,
		SupplierName:    form.SupplierName,
		OrderDate:       form.OrderDate,
		// TotalAmount 会在 repository 层计算
	}

	// 读取文件
	// 保存上传的文件
	src, err := form.File.Open()
	if err != nil {
		return nil, fmt.Errorf("打开上传文件失败: %w", err)
	}
	defer func(src multipart.File) {
		err := src.Close()
		if err != nil {
			fmt.Printf("关闭文件失败: %s\n", err)
		}
	}(src)

	// 构建文件路径
	timestamp := time.Now().UnixNano()
	filename := fmt.Sprintf("%s_%d.%s", form.PurchaseOrderNo, timestamp, filepath.Ext(form.File.Filename))
	filePath := filepath.Join("storage", "imports", filename)

	// 创建目录结构（如果不存在）
	err = os.MkdirAll(filepath.Dir(filePath), 0750)
	if err != nil {
		return nil, fmt.Errorf("创建目录失败: %w", err)
	}

	// #nosec G304 -- 文件路径由程序内部生成，不是直接来自用户输入
	dst, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer func(dst *os.File) {
		err := dst.Close()
		if err != nil {
			fmt.Printf("关闭文件失败: %s\n", err)
		}
	}(dst)

	// 将文件保存到本地
	if _, err = io.Copy(dst, src); err != nil {
		return nil, fmt.Errorf("保存文件失败: %w", err)
	}

	// 读取文件
	details, err := s.ReadPurchasePartInboundExcel(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取Excel文档失败: %w", err)
	}
	// 调用仓库层创建采购订单和详情（事务操作）
	if err := s.repo.CreateWithDetails(ctx, purchaseOrder, details); err != nil {
		return nil, err
	}

	// 返回创建成功的订单（包含详情）
	return s.repo.GetByID(ctx, purchaseOrder.ID)
}

// GetPurchaseOrderByID 根据ID获取采购订单
func (s *purchaseServiceImpl) GetPurchaseOrderByID(ctx context.Context, id uint) (*model.PurchaseOrder, error) {
	order, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPurchaseOrderNotFound
		}
		return nil, err
	}

	return order, nil
}

// GetPurchaseOrderByPurchaseOrderNo 根据PO编号获取采购订单
func (s *purchaseServiceImpl) GetPurchaseOrderByPurchaseOrderNo(ctx context.Context, poNumber string) (*model.PurchaseOrder, error) {
	order, err := s.repo.GetByPurchaseOrderNo(ctx, poNumber)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPurchaseOrderNotFound
		}
		return nil, fmt.Errorf("没有找到相应的采购入库单：%w", err)
	}

	return order, nil
}

// ListPurchaseOrders 获取采购订单列表
func (s *purchaseServiceImpl) ListPurchaseOrders(ctx context.Context, query PurchaseOrderListQuery) (*PurchaseOrderListResult, error) {
	filter := repository.PurchaseFilter{
		PONumber:       query.PurchaseOrderNo,
		SupplierName:   query.SupplierName,
		OrderDateStart: query.OrderDateStart,
		OrderDateEnd:   query.OrderDateEnd,
	}

	pagination := repository.PaginationOptions{
		Page:     query.Page,
		PageSize: query.PageSize,
	}

	orders, total, err := s.repo.List(ctx, filter, pagination)
	if err != nil {
		return nil, err
	}

	return &PurchaseOrderListResult{
		Total: total,
		List:  orders,
	}, nil
}

func (s *purchaseServiceImpl) StartNewInboundWorkflow(ctx context.Context) error {
	//TODO implement me
	panic("implement me")
}

// ReadPurchasePartInboundExcel 读取采购入库Excel文件
func (s *purchaseServiceImpl) ReadPurchasePartInboundExcel(filePath string) ([]model.PurchaseDetails, error) {
	// 打开Excel文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("关闭Excel文件失败: %v\n", err)
		}
	}()

	// 获取所有行
	rows, err := f.GetRows(exportModel.PurchaseInboundSheet)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %w", err)
	}
	fmt.Println(len(rows))
	// 检查是否有数据
	if len(rows) < 2 { // 至少要有表头和一行数据
		return nil, fmt.Errorf("Excel文件中没有数据")
	}

	// 存储结果
	var results []model.PurchaseDetails

	// 从第二行开始读取数据（跳过表头）
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		// 检查行是否有足够的列
		if len(row) < 9 { // 需要至少9列数据
			continue // 跳过数据不完整的行
		}

		// 检查PN号码是否为空
		if row[0] == "" {
			continue // 如果PN为空，跳过该行
		}

		productID, err := strconv.ParseUint(row[8], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("规格ID转换格式失败: %w", err)
		}
		amount, err := strconv.ParseFloat(row[2], 64)
		if err != nil {
			return nil, fmt.Errorf("数量转换格式失败: %w", err)
		}

		// 创建记录
		item := model.PurchaseDetails{
			ProductID: uint(productID),
			Amount:    uint(amount),
		}

		// 添加到结果集
		results = append(results, item)
	}

	return results, nil
}
