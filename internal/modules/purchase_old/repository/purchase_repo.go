package repository

import (
	"backend/internal/modules/purchase_old/model"
	"context"
	"fmt"

	"gorm.io/gorm"
)

// PurchaseFilter 订单查询过滤条件
type PurchaseFilter struct {
	PONumber       string
	SupplierName   string
	OrderDateStart string
	OrderDateEnd   string
}

// PaginationOptions 分页选项
type PaginationOptions struct {
	Page     int
	PageSize int
}

// PurchaseRepository 采购订单仓库接口
type PurchaseRepository interface {
	// GetByID 根据ID获取采购订单
	GetByID(ctx context.Context, id uint) (*model.PurchaseOrder, error)

	// GetByPurchaseOrderNo 根据PO编号获取采购订单
	GetByPurchaseOrderNo(ctx context.Context, poNumber string) (*model.PurchaseOrder, error)

	// List 获取采购订单列表
	List(ctx context.Context, filter PurchaseFilter, pagination PaginationOptions) ([]*model.PurchaseOrder, int64, error)

	// CreateWithDetails 创建采购订单和详情（事务操作）
	CreateWithDetails(ctx context.Context, order *model.PurchaseOrder, details []model.PurchaseDetails) error

	// 获取事务对象
	GetDB() *gorm.DB
}

// purchaseRepository 采购订单仓库实现
type purchaseRepository struct {
	db *gorm.DB
}

// NewPurchaseRepository 创建采购订单仓库
func NewPurchaseRepository(db *gorm.DB) PurchaseRepository {
	return &purchaseRepository{db: db}
}

// GetByID 根据ID获取采购订单
func (r *purchaseRepository) GetByID(ctx context.Context, id uint) (*model.PurchaseOrder, error) {
	var order model.PurchaseOrder
	if err := r.db.WithContext(ctx).Preload("NewInbound").Preload("PurchaseDetails").Preload("PurchaseDetails.Product").First(&order, id).Error; err != nil {
		return nil, err
	}
	return &order, nil
}

// GetByPONumber 根据PO编号获取采购订单
func (r *purchaseRepository) GetByPurchaseOrderNo(ctx context.Context, poNumber string) (*model.PurchaseOrder, error) {
	var order model.PurchaseOrder
	if err := r.db.WithContext(ctx).Where("purchase_order_no = ?", poNumber).Preload("PurchaseDetails").First(&order).Error; err != nil {
		return nil, err
	}
	return &order, nil
}

// List 获取采购订单列表
func (r *purchaseRepository) List(ctx context.Context, filter PurchaseFilter, pagination PaginationOptions) ([]*model.PurchaseOrder, int64, error) {
	var orders []*model.PurchaseOrder
	var total int64

	query := r.db.WithContext(ctx).Model(&model.PurchaseOrder{})

	// 应用过滤条件
	if filter.PONumber != "" {
		query = query.Where("purchase_order_no LIKE ?", "%"+filter.PONumber+"%")
	}
	if filter.SupplierName != "" {
		query = query.Where("supplier_name LIKE ?", "%"+filter.SupplierName+"%")
	}
	if filter.OrderDateStart != "" {
		query = query.Where("order_date >= ?", filter.OrderDateStart)
	}
	if filter.OrderDateEnd != "" {
		query = query.Where("order_date <= ?", filter.OrderDateEnd)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用分页
	if pagination.Page > 0 && pagination.PageSize > 0 {
		offset := (pagination.Page - 1) * pagination.PageSize
		query = query.Offset(offset).Limit(pagination.PageSize)
	}

	// 排序
	query = query.Order("created_at DESC")

	// 执行查询并预加载关联数据
	if err := query.Preload("PurchaseDetails").Find(&orders).Error; err != nil {
		return nil, 0, err
	}

	return orders, total, nil
}

// CreateWithDetails 创建采购订单和详情（事务操作）
func (r *purchaseRepository) CreateWithDetails(ctx context.Context, order *model.PurchaseOrder, details []model.PurchaseDetails) error {

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 创建订单
		if err := tx.Create(order).Error; err != nil {
			return fmt.Errorf("创建采购订单失败：%w", err)
		}

		// 2. 关联详情到订单
		for i := range details {
			details[i].PurchaseOrderID = order.ID
		}

		// 3. 创建订单详情
		if len(details) > 0 {
			if err := tx.Create(&details).Error; err != nil {
				return fmt.Errorf("创建订单详情失败：%w", err)
			}
		}

		// 4. 计算总金额
		var totalAmount float64
		for _, detail := range details {
			totalAmount += float64(detail.Amount) * detail.Price
		}

		// 5. 更新订单总金额
		order.TotalAmount = totalAmount
		if err := tx.Model(order).Update("total_amount", totalAmount).Error; err != nil {
			return fmt.Errorf("更新订单总金额失败：%w", err)
		}

		return nil
	})
}

// GetDB 获取数据库连接
func (r *purchaseRepository) GetDB() *gorm.DB {
	return r.db
}
