package service

import (
	"backend/internal/modules/file/model"
	"errors"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/disintegration/imaging"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// 定义文件存储目录
const (
	// BaseUploadDir 基础上传目录
	BaseUploadDir = "storage/uploads"
	// ImageDir 图片存储目录
	ImageDir = BaseUploadDir + "/images"
	// DocDir 文档存储目录
	DocDir = BaseUploadDir + "/documents"
	// PDFDir PDF文件存储目录
	PDFDir = BaseUploadDir + "/pdf"
	// WordDir Word文档存储目录
	WordDir = BaseUploadDir + "/word"
	// ExcelDir Excel表格存储目录
	ExcelDir = BaseUploadDir + "/excel"
	// PPTDir PowerPoint演示文稿存储目录
	PPTDir = BaseUploadDir + "/ppt"
	// TextDir 文本文件存储目录
	TextDir = BaseUploadDir + "/text"
	// ZipDir 压缩文件存储目录
	ZipDir = BaseUploadDir + "/zip"
	// CSVDir CSV文件存储目录
	CSVDir = BaseUploadDir + "/csv"
	// AudioDir 音频文件存储目录
	AudioDir = BaseUploadDir + "/audio"
	// VideoDir 视频文件存储目录
	VideoDir = BaseUploadDir + "/video"
	// OtherDir 其他文件存储目录
	OtherDir = BaseUploadDir + "/others"
	// ThumbnailDir 缩略图存储目录
	ThumbnailDir = BaseUploadDir + "/thumbnails"
	// WorkflowDir 流程附件
	WorkflowDir = BaseUploadDir + "/workflows"
	// MaxFileSize 最大文件大小 (50MB)
	MaxFileSize = 50 * 1024 * 1024
	// ThumbnailSize 缩略图大小
	ThumbnailSize = 200
)

// FileService 文件服务接口
type FileService interface {
	// UploadFile 上传单个文件
	UploadFile(ctx *gin.Context, file *multipart.FileHeader, moduleType string, moduleID uint, description string) (*model.FileResponse, error)

	// UploadFiles 批量上传文件
	UploadFiles(ctx *gin.Context, files []*multipart.FileHeader, moduleType string, moduleID uint, description string) (*model.FileBatchResponse, error)

	// GetFileByID 通过ID获取文件信息
	GetFileByID(fileID uint) (*model.File, error)

	// GetFileByFilename 通过文件名获取文件信息
	GetFileByFilename(filename string, file *model.File) error

	// GetFilesByModule 获取指定模块的所有文件
	GetFilesByModule(moduleType string, moduleID uint) ([]model.File, error)

	// DeleteFile 删除文件
	DeleteFile(fileID uint, userID uint) error
}

// FileServiceImpl 文件服务实现
type FileServiceImpl struct {
	db *gorm.DB
}

// NewFileService 创建文件服务实例
func NewFileService(db *gorm.DB) FileService {
	// 确保上传目录存在
	ensureDirectories()
	return &FileServiceImpl{db: db}
}

// ensureDirectories 确保所有必要的目录存在
func ensureDirectories() {
	dirs := []string{
		ImageDir, DocDir, PDFDir, WordDir, ExcelDir, PPTDir,
		TextDir, ZipDir, CSVDir, AudioDir, VideoDir,
		OtherDir, ThumbnailDir, WorkflowDir,
	}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0750); err != nil {
			fmt.Printf("创建目录失败: %s, 错误: %v\n", dir, err)
		}
	}
}

// UploadFile 上传单个文件
func (s *FileServiceImpl) UploadFile(ctx *gin.Context, fileHeader *multipart.FileHeader, moduleType string, moduleID uint, description string) (*model.FileResponse, error) {
	// 检查文件大小
	if fileHeader.Size > MaxFileSize {
		return nil, errors.New("文件大小超过限制")
	}

	// 打开上传的文件
	file, err := fileHeader.Open()
	if err != nil {
		return nil, err
	}
	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			fmt.Println(err.Error())
		}
	}(file)

	// 读取文件前512字节判断文件类型
	buffer := make([]byte, 512)
	_, err = file.Read(buffer)
	if err != nil {
		return nil, err
	}

	// 复位文件指针
	_, err = file.Seek(0, io.SeekStart)
	if err != nil {
		return nil, err
	}

	// 生成存储文件名和路径
	originalFileName := fileHeader.Filename
	fileExt := filepath.Ext(originalFileName)
	storedFileName := uuid.New().String() + fileExt

	// 判断MIME类型
	mimeType := http.DetectContentType(buffer)
	fileType := getFileType(mimeType, fileExt)

	// 确定存储目录
	var baseDir string
	switch fileType {
	case model.FileTypeImage:
		baseDir = ImageDir
	case model.FileTypePDF:
		baseDir = PDFDir
	case model.FileTypeWord:
		baseDir = WordDir
	case model.FileTypeExcel:
		baseDir = ExcelDir
	case model.FileTypePPT:
		baseDir = PPTDir
	case model.FileTypeText:
		baseDir = TextDir
	case model.FileTypeZip, model.FileTypeRAR, model.FileType7z:
		baseDir = ZipDir
	case model.FileTypeCSV:
		baseDir = CSVDir
	case model.FileTypeAudio:
		baseDir = AudioDir
	case model.FileTypeVideo:
		baseDir = VideoDir
	case model.FileTypeDoc: // 兼容旧版本
		baseDir = DocDir
	default:
		baseDir = OtherDir
	}

	// 按模块类型创建子目录
	var moduleTypeDir string
	if moduleType != "" {
		// 规范化模块类型名称，避免特殊字符
		safeModuleType := strings.ReplaceAll(moduleType, "/", "_")
		safeModuleType = strings.ReplaceAll(safeModuleType, "\\", "_")
		safeModuleType = strings.ReplaceAll(safeModuleType, "..", "_")

		moduleTypeDir = filepath.Join(baseDir, safeModuleType)
		// 确保模块类型目录存在
		if err := os.MkdirAll(moduleTypeDir, 0750); err != nil {
			fmt.Printf("创建模块目录失败: %s, 错误: %v\n", moduleTypeDir, err)
			moduleTypeDir = baseDir // 如果创建失败，回退到基础目录
		}
	} else {
		moduleTypeDir = baseDir
	}

	storagePath := filepath.Join(moduleTypeDir, storedFileName)

	// 获取当前用户ID
	userID, exists := ctx.Get("userID")
	if !exists {
		userID = uint(0) // 默认为0，表示系统上传
	}

	// 安全地进行类型断言
	userIDValue, ok := userID.(uint)
	if !ok {
		// 如果类型断言失败，使用默认值0
		userIDValue = 0
	}

	// 生成访问URL
	baseURL := getBaseURL(ctx)
	url := fmt.Sprintf("%s/api/v1/file/view/%s", baseURL, storedFileName)

	// 创建目标文件 - 移动到此处，先保存文件
	// #nosec G304 -- 文件路径已经过验证
	out, err := os.Create(storagePath)
	if err != nil {
		return nil, err
	}
	defer func(out *os.File) {
		err := out.Close()
		if err != nil {
			fmt.Printf("<UNK>: %s, <UNK>: %v\n", storagePath, err)
		}
	}(out)

	// 复制文件内容
	_, err = io.Copy(out, file)
	if err != nil {
		return nil, err
	}

	// 生成缩略图(仅图片类型) - 移到文件保存之后
	var thumbnailURL string
	if fileType == model.FileTypeImage {
		thumbnailPath := filepath.Join(ThumbnailDir, "thumb_"+storedFileName)
		if err := generateThumbnail(storagePath, thumbnailPath, ThumbnailSize); err == nil {
			thumbnailURL = fmt.Sprintf("%s/api/v1/file/thumbnail/%s", baseURL, "thumb_"+storedFileName)
		} else {
			// 如果缩略图生成失败，记录错误但不中断流程
			fmt.Printf("生成缩略图失败: %v\n", err)
		}
	}

	// 创建文件记录
	fileRecord := model.File{
		FileName:     originalFileName,
		StoragePath:  storagePath,
		FileType:     fileType,
		FileSize:     fileHeader.Size,
		MimeType:     mimeType,
		URL:          url,
		UploadedBy:   userIDValue,
		ModuleType:   moduleType,
		ModuleID:     moduleID,
		Description:  description,
		ThumbnailURL: thumbnailURL,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 保存到数据库
	if err := s.db.Create(&fileRecord).Error; err != nil {
		// 删除已上传的文件
		err := os.Remove(storagePath)
		if err != nil {
			fmt.Printf("<UNK>: %s, <UNK>: %v\n", storagePath, err)
		}
		if thumbnailURL != "" {
			err := os.Remove(filepath.Join(ThumbnailDir, "thumb_"+storedFileName))
			if err != nil {
				fmt.Printf("<UNK>: %s, <UNK>: %v\n", storagePath, err)
			}
		}
		return nil, err
	}

	// 返回响应
	return &model.FileResponse{
		ID:           fileRecord.ID,
		URL:          url,
		FileName:     originalFileName,
		FileSize:     fileHeader.Size,
		FileType:     fileType,
		ThumbnailURL: thumbnailURL,
	}, nil
}

// generateThumbnail 生成缩略图
func generateThumbnail(srcPath, dstPath string, size int) error {
	// 打开源图片
	// #nosec G304 -- 文件路径已经过验证
	srcFile, err := os.Open(srcPath)
	if err != nil {
		return fmt.Errorf("打开源图片失败: %w", err)
	}
	defer func(srcFile *os.File) {
		err := srcFile.Close()
		if err != nil {
			fmt.Printf("<UNK>: %s, <UNK>: %v\n", srcPath, err)
		}
	}(srcFile)

	// 解码图片
	img, format, err := image.Decode(srcFile)
	if err != nil {
		return fmt.Errorf("解码图片失败: %w", err)
	}

	// 调整图片大小，使用Lanczos算法保持图片质量
	// 计算缩略图尺寸，保持原图比例
	width := img.Bounds().Dx()
	height := img.Bounds().Dy()

	var thumbnail image.Image
	if width > height {
		thumbnail = imaging.Resize(img, size, 0, imaging.Lanczos)
	} else {
		thumbnail = imaging.Resize(img, 0, size, imaging.Lanczos)
	}

	// 创建目标文件
	// #nosec G304 -- 文件路径已经过验证
	dstFile, err := os.Create(dstPath)
	if err != nil {
		return fmt.Errorf("创建缩略图文件失败: %w", err)
	}
	defer func(dstFile *os.File) {
		err := dstFile.Close()
		if err != nil {
			fmt.Printf("<UNK>: %s, <UNK>: %v\n", dstPath, err)
		}
	}(dstFile)

	// 根据原图格式进行编码
	switch format {
	case "jpeg":
		err = jpeg.Encode(dstFile, thumbnail, &jpeg.Options{Quality: 85})
	case "png":
		err = png.Encode(dstFile, thumbnail)
	default:
		// 默认使用JPEG格式
		err = jpeg.Encode(dstFile, thumbnail, &jpeg.Options{Quality: 85})
	}

	if err != nil {
		return fmt.Errorf("保存缩略图失败: %w", err)
	}

	return nil
}

// UploadFiles 批量上传文件
func (s *FileServiceImpl) UploadFiles(ctx *gin.Context, files []*multipart.FileHeader, moduleType string, moduleID uint, description string) (*model.FileBatchResponse, error) {
	response := &model.FileBatchResponse{
		Files:  make([]model.FileResponse, 0),
		Errors: make([]map[string]string, 0),
	}

	for _, file := range files {
		fileResp, err := s.UploadFile(ctx, file, moduleType, moduleID, description)
		if err != nil {
			response.ErrorCount++
			response.Errors = append(response.Errors, map[string]string{
				"file_name": file.Filename,
				"error":     err.Error(),
			})
		} else {
			response.SuccessCount++
			response.Files = append(response.Files, *fileResp)
		}
	}

	return response, nil
}

// GetFileByID 通过ID获取文件信息
func (s *FileServiceImpl) GetFileByID(fileID uint) (*model.File, error) {
	var file model.File
	err := s.db.First(&file, fileID).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

// GetFileByFilename 通过文件名获取文件信息
func (s *FileServiceImpl) GetFileByFilename(filename string, file *model.File) error {
	// 从文件路径中提取文件名
	baseName := filepath.Base(filename)

	// 在数据库中查找匹配的记录
	// 使用LIKE查询匹配文件路径末尾的文件名
	return s.db.Where("storage_path LIKE ?", "%/"+baseName).First(file).Error
}

// GetFilesByModule 获取指定模块的所有文件
func (s *FileServiceImpl) GetFilesByModule(moduleType string, moduleID uint) ([]model.File, error) {
	var files []model.File
	err := s.db.Where("module_type = ? AND module_id = ?", moduleType, moduleID).Find(&files).Error
	return files, err
}

// DeleteFile 删除文件
func (s *FileServiceImpl) DeleteFile(fileID uint, userID uint) error {
	var file model.File

	// 查找文件记录
	if err := s.db.First(&file, fileID).Error; err != nil {
		return err
	}

	// 检查权限（只允许上传者或管理员删除）
	if file.UploadedBy != userID {
		// 这里可以加入管理员检查的逻辑
		return errors.New("无权删除该文件")
	}

	// 软删除文件记录
	if err := s.db.Delete(&file).Error; err != nil {
		return err
	}

	// 物理删除文件（如果需要的话）
	// os.Remove(file.StoragePath)
	// if file.ThumbnailURL != "" {
	//    os.Remove(filepath.Join(ThumbnailDir, filepath.Base(file.ThumbnailURL)))
	// }

	return nil
}

// getFileType 根据MIME类型确定文件类型
func getFileType(mimeType, fileExt string) model.FileType {
	// 转换文件扩展名为小写，去掉前导点
	lowercaseExt := strings.ToLower(fileExt)
	if strings.TrimPrefix(lowercaseExt, ".") != lowercaseExt {
		lowercaseExt = lowercaseExt[1:]
	}

	// 图片类型检测
	if strings.HasPrefix(mimeType, "image/") {
		return model.FileTypeImage
	}

	// PDF 文件
	if strings.HasPrefix(mimeType, "application/pdf") || lowercaseExt == "pdf" {
		return model.FileTypePDF
	}

	// Word 文档
	if strings.HasPrefix(mimeType, "application/msword") ||
		strings.HasPrefix(mimeType, "application/vnd.openxmlformats-officedocument.wordprocessingml") ||
		lowercaseExt == "doc" || lowercaseExt == "docx" || lowercaseExt == "rtf" {
		return model.FileTypeWord
	}

	// Excel 表格
	if strings.HasPrefix(mimeType, "application/vnd.ms-excel") ||
		strings.HasPrefix(mimeType, "application/vnd.openxmlformats-officedocument.spreadsheetml") ||
		lowercaseExt == "xls" || lowercaseExt == "xlsx" || lowercaseExt == "xlsm" {
		return model.FileTypeExcel
	}

	// PowerPoint 演示文稿
	if strings.HasPrefix(mimeType, "application/vnd.ms-powerpoint") ||
		strings.HasPrefix(mimeType, "application/vnd.openxmlformats-officedocument.presentationml") ||
		lowercaseExt == "ppt" || lowercaseExt == "pptx" {
		return model.FileTypePPT
	}

	// 文本文件
	if strings.HasPrefix(mimeType, "text/plain") ||
		lowercaseExt == "txt" || lowercaseExt == "log" {
		return model.FileTypeText
	}

	// 压缩文件
	if strings.HasPrefix(mimeType, "application/zip") || lowercaseExt == "zip" {
		return model.FileTypeZip
	}
	if strings.HasPrefix(mimeType, "application/x-rar-compressed") || lowercaseExt == "rar" {
		return model.FileTypeRAR
	}
	if strings.HasPrefix(mimeType, "application/x-7z-compressed") || lowercaseExt == "7z" {
		return model.FileType7z
	}

	// CSV 文件
	if strings.Contains(mimeType, "csv") || lowercaseExt == "csv" {
		return model.FileTypeCSV
	}

	// JSON 文件
	if strings.Contains(mimeType, "json") || lowercaseExt == "json" {
		return model.FileTypeJSON
	}

	// XML 文件
	if strings.Contains(mimeType, "xml") || lowercaseExt == "xml" {
		return model.FileTypeXML
	}

	// Markdown 文件
	if strings.Contains(mimeType, "markdown") || lowercaseExt == "md" || lowercaseExt == "markdown" {
		return model.FileTypeMarkdown
	}

	// 音频文件
	if strings.HasPrefix(mimeType, "audio/") ||
		lowercaseExt == "mp3" || lowercaseExt == "wav" || lowercaseExt == "ogg" || lowercaseExt == "flac" {
		return model.FileTypeAudio
	}

	// 视频文件
	if strings.HasPrefix(mimeType, "video/") ||
		lowercaseExt == "mp4" || lowercaseExt == "avi" || lowercaseExt == "mov" || lowercaseExt == "mkv" {
		return model.FileTypeVideo
	}

	// 对于旧版本兼容，保持文档类型检测
	if strings.HasPrefix(mimeType, "application/msword") ||
		strings.HasPrefix(mimeType, "application/vnd.openxmlformats-officedocument.wordprocessingml") ||
		strings.HasPrefix(mimeType, "application/vnd.ms-excel") ||
		strings.HasPrefix(mimeType, "application/vnd.openxmlformats-officedocument.spreadsheetml") ||
		strings.HasPrefix(mimeType, "application/pdf") {
		return model.FileTypeDoc
	}

	// 其他类型
	return model.FileTypeOther
}

// getBaseURL 获取基础URL
func getBaseURL(ctx *gin.Context) string {
	scheme := "http"
	if ctx.Request.TLS != nil {
		scheme = "https"
	}
	return fmt.Sprintf("%s://%s", scheme, ctx.Request.Host)
}
