package model

import (
	"time"

	"gorm.io/gorm"
)

// FileType 文件类型枚举
type FileType string

const (
	FileTypeImage    FileType = "image"    // 图片
	FileTypeDoc      FileType = "doc"      // 文档
	FileTypePDF      FileType = "pdf"      // PDF文件
	FileTypeWord     FileType = "word"     // Word文档
	FileTypeExcel    FileType = "excel"    // Excel表格
	FileTypePPT      FileType = "ppt"      // PowerPoint演示文稿
	FileTypeText     FileType = "text"     // 纯文本文件
	FileTypeZip      FileType = "zip"      // 压缩文件
	FileTypeRAR      FileType = "rar"      // RAR压缩文件
	FileType7z       FileType = "7z"       // 7z压缩文件
	FileTypeCSV      FileType = "csv"      // CSV文件
	FileTypeJSON     FileType = "json"     // JSON文件
	FileTypeXML      FileType = "xml"      // XML文件
	FileTypeMarkdown FileType = "markdown" // Markdown文件
	FileTypeAudio    FileType = "audio"    // 音频文件
	FileTypeVideo    FileType = "video"    // 视频文件
	FileTypeOther    FileType = "other"    // 其他
)

// File 上传文件模型
type File struct {
	ID           uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt    time.Time      `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
	FileName     string         `gorm:"size:255;not null" json:"file_name"`    // 原始文件名
	StoragePath  string         `gorm:"size:500;not null" json:"storage_path"` // 存储路径
	FileType     FileType       `gorm:"size:50;not null" json:"file_type"`     // 文件类型
	FileSize     int64          `gorm:"not null" json:"file_size"`             // 文件大小(字节)
	MimeType     string         `gorm:"size:100" json:"mime_type"`             // MIME类型
	URL          string         `gorm:"size:500" json:"url"`                   // 访问URL
	UploadedBy   uint           `gorm:"not null" json:"uploaded_by"`           // 上传用户ID
	ModuleType   string         `gorm:"size:100" json:"module_type"`           // 关联模块类型
	ModuleID     uint           `gorm:"index" json:"module_id"`                // 关联模块ID
	Description  string         `gorm:"size:500" json:"description"`           // 文件描述
	Tags         string         `gorm:"size:500" json:"tags"`                  // 文件标签
	ThumbnailURL string         `gorm:"size:500" json:"thumbnail_url"`         // 缩略图URL(图片类型)

}

// FileResponse 文件上传响应
type FileResponse struct {
	ID           uint     `json:"id"`                      // 文件ID
	URL          string   `json:"url"`                     // 访问URL
	FileName     string   `json:"file_name"`               // 文件名
	FileSize     int64    `json:"file_size"`               // 文件大小
	FileType     FileType `json:"file_type"`               // 文件类型
	ThumbnailURL string   `json:"thumbnail_url,omitempty"` // 缩略图URL
	UploadedBy   uint     `json:"uploaded_by"`             // 上传用户ID
}

// FileBatchResponse 批量上传响应
type FileBatchResponse struct {
	SuccessCount int                 `json:"success_count"` // 成功上传数量
	ErrorCount   int                 `json:"error_count"`   // 失败数量
	Files        []FileResponse      `json:"files"`         // 成功上传的文件
	Errors       []map[string]string `json:"errors"`        // 失败的文件及原因
}
