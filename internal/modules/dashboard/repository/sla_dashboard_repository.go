package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// DashboardRepository 看板仓库接口
type DashboardRepository interface {
	// 获取报障单时间分布统计
	GetTicketTimeStats(ctx context.Context, startDate, endDate time.Time, groupBy string, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取故障类型分布
	GetFaultTypeStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取故障来源分布
	GetSourceStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取具体故障类型分布
	GetFaultDetailTypeStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取报障单状态分布
	GetStatusStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取处理时长统计
	GetDurationStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取SLA达标情况
	GetSLAStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 计算计入SLA且已完成报障单的业务影响总时长
	GetBusinessImpactTotalTime(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取GPU卡故障比
	GetGPUFaultRatio(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取故障单详情列表
	GetFaultTicketDetails(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取故障总台数（根据不重复的device_sn统计）
	GetTotalFaultDevices(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取分配人员响应和修复时长统计
	GetEngineerResponseAndFixTimeStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取故障设备厂商分布统计
	GetFaultDeviceBrandDistribution(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取每天故障处理总时长趋势统计
	GetDailyBusinessImpactTrend(ctx context.Context, startDate, endDate time.Time, groupBy string, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取每天硬件故障次数趋势统计
	GetHardwareFaultTrend(ctx context.Context, startDate, endDate time.Time, groupBy string, project string, countInSLA bool) (map[string]interface{}, error)
	// 获取资源变更统计
	GetResourceChangesStats(ctx context.Context, startDate, endDate time.Time, project string) (map[string]interface{}, error)
}

// dashboardRepository 看板仓库实现
type dashboardRepository struct {
	db *gorm.DB
}

// NewDashboardRepository 创建看板仓库
func NewDashboardRepository(db *gorm.DB) DashboardRepository {
	return &dashboardRepository{db: db}
}

// GetTicketTimeStats 获取报障单时间分布统计
func (r *dashboardRepository) GetTicketTimeStats(ctx context.Context, startDate, endDate time.Time, groupBy string, project string, countInSLA bool) (map[string]interface{}, error) {
	var timeFormat string
	var interval string

	// 根据分组方式确定时间格式和SQL查询
	switch groupBy {
	case "day":
		timeFormat = "2006-01-02"
		interval = "day"
	case "week":
		timeFormat = "2006-W%V" // ISO周格式
		interval = "week"
	case "month":
		timeFormat = "2006-01"
		interval = "month"
	default:
		timeFormat = "2006-01-02"
		interval = "day"
	}

	type Result struct {
		Date  string `json:"date"`
		Count int    `json:"count"`
	}

	var results []Result
	var total int

	// 生成日期序列（避免没有数据的日期不显示）
	dates := []string{}
	counts := []int{}

	// 构建查询SQL
	query := fmt.Sprintf(`
		SELECT
			DATE_FORMAT(ft.creation_time, '%s') as date,
			COUNT(*) as count
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`, getSQLDateFormat(interval))

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 添加分组和排序
	query += `
			GROUP BY
				date
			ORDER BY
				date ASC
		`

	// 执行查询
	rows, err := r.db.WithContext(ctx).Raw(query, args...).Rows()
	if err != nil {
		return nil, err
	}
	defer func() {
		if err := rows.Close(); err != nil {
			fmt.Println("关闭数据库行失败:", err)
		}
	}()

	// 结果映射
	for rows.Next() {
		var result Result
		if err := rows.Scan(&result.Date, &result.Count); err != nil {
			return nil, err
		}
		results = append(results, result)
		total += result.Count
	}

	// 检查遍历过程中是否有错误
	if err := rows.Err(); err != nil {
		return nil, err
	}

	// 填充日期和对应的计数
	dateMap := make(map[string]int)
	for _, result := range results {
		dateMap[result.Date] = result.Count
	}

	// 生成连续的日期范围
	currentDate := startDate
	for currentDate.Before(endDate) || currentDate.Equal(endDate) {
		var dateStr string

		switch interval {
		case "day":
			dateStr = currentDate.Format(timeFormat)
			currentDate = currentDate.AddDate(0, 0, 1)
		case "week":
			year, week := currentDate.ISOWeek()
			dateStr = fmt.Sprintf("%d-W%02d", year, week)
			currentDate = currentDate.AddDate(0, 0, 7)
		case "month":
			dateStr = currentDate.Format(timeFormat)
			currentDate = time.Date(currentDate.Year(), currentDate.Month()+1, 1, 0, 0, 0, 0, currentDate.Location())
		}

		dates = append(dates, dateStr)

		if count, ok := dateMap[dateStr]; ok {
			counts = append(counts, count)
		} else {
			counts = append(counts, 0)
		}
	}

	return map[string]interface{}{
		"dates":  dates,
		"counts": counts,
		"total":  total,
	}, nil
}

// GetFaultTypeStats 获取故障类型分布
func (r *dashboardRepository) GetFaultTypeStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	type Result struct {
		Type  string `json:"type"`
		Count int    `json:"count"`
	}

	var results []Result
	var total int

	// 构建查询SQL
	query := `
		SELECT
			ft.fault_type as type,
			COUNT(*) as count
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 添加分组和排序
	query += `
		GROUP BY
			ft.fault_type
		ORDER BY
			count DESC
	`

	// 执行查询
	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 准备返回数据
	types := []string{}
	data := []map[string]interface{}{}

	for _, result := range results {
		types = append(types, getTypeName(result.Type))
		data = append(data, map[string]interface{}{
			"name":  getTypeName(result.Type),
			"value": result.Count,
		})
		total += result.Count
	}

	return map[string]interface{}{
		"types": types,
		"data":  data,
		"total": total,
	}, nil
}

// GetSourceStats 获取故障来源分布
func (r *dashboardRepository) GetSourceStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	type Result struct {
		Source string `json:"source"`
		Count  int    `json:"count"`
	}

	var results []Result
	var total int

	// 构建查询SQL
	query := `
		SELECT
			CASE
				WHEN ft.source = 'customer' THEN 'customer'
				ELSE 'internal'
			END as source,
			COUNT(*) as count
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 添加分组和排序
	query += `
		GROUP BY
			source
		ORDER BY
			count DESC
	`

	// 执行查询
	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 准备返回数据
	sources := []string{}
	data := []map[string]interface{}{}

	for _, result := range results {
		sourceName := getSourceName(result.Source)
		sources = append(sources, sourceName)
		data = append(data, map[string]interface{}{
			"name":  sourceName,
			"value": result.Count,
		})
		total += result.Count
	}

	return map[string]interface{}{
		"sources": sources,
		"data":    data,
		"total":   total,
	}, nil
}

// GetFaultDetailTypeStats 获取具体故障类型分布
func (r *dashboardRepository) GetFaultDetailTypeStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	type Result struct {
		Type  string `json:"type"`
		Count int    `json:"count"`
	}

	var results []Result
	var total int

	// 构建查询SQL
	query := `
		SELECT
			ft.fault_detail_type as type,
			COUNT(*) as count
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 添加分组和排序
	query += `
		GROUP BY
			ft.fault_detail_type
		ORDER BY
			count DESC
		LIMIT 20
	`

	// 执行查询
	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 准备返回数据
	types := []string{}
	data := []map[string]interface{}{}

	for _, result := range results {
		types = append(types, result.Type)
		data = append(data, map[string]interface{}{
			"name":  result.Type,
			"value": result.Count,
		})
		total += result.Count
	}

	return map[string]interface{}{
		"types": types,
		"data":  data,
		"total": total,
	}, nil
}

// GetStatusStats 获取报障单状态分布
func (r *dashboardRepository) GetStatusStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	type Result struct {
		Status string `json:"status"`
		Count  int    `json:"count"`
	}

	var results []Result
	var total int

	// 构建查询SQL
	query := `
		SELECT
			ft.status,
			COUNT(*) as count
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 添加分组和排序
	query += `
		GROUP BY
			ft.status
		ORDER BY
			count DESC
	`

	// 执行查询
	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 准备返回数据
	statuses := []string{}
	data := []map[string]interface{}{}

	for _, result := range results {
		statuses = append(statuses, getStatusName(result.Status))
		data = append(data, map[string]interface{}{
			"name":  getStatusName(result.Status),
			"value": result.Count,
		})
		total += result.Count
	}

	return map[string]interface{}{
		"statuses": statuses,
		"data":     data,
		"total":    total,
	}, nil
}

// GetDurationStats 获取处理时长统计
func (r *dashboardRepository) GetDurationStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	// 预定义的阶段
	//phases := []string{"响应时长", "硬件维修时长", "软件处理时长", "总停机时间", "业务影响时长"}
	phases := []string{"响应时长", "硬件平均响应时长", "硬件维修时长", "软件处理时长", "业务影响时长"}

	// 构建查询SQL
	query := `
		SELECT
-- 			AVG(ft.diagnosis_duration) as avg_diagnosis,
-- 			MAX(ft.diagnosis_duration) as max_diagnosis, //诊断时长
-- 			MIN(ft.diagnosis_duration) as min_diagnosis,
			AVG(ft.response_duration) as avg_response,
			MAX(ft.response_duration) as max_response,
			MIN(ft.response_duration) as min_response,
			AVG(CASE WHEN ft.repair_ticket_id <> 0 THEN ft.hardware_repair_duration ELSE NULL END) as avg_hardware,
			MAX(CASE WHEN ft.repair_ticket_id <> 0  THEN ft.hardware_repair_duration ELSE NULL END) as max_hardware,
			MIN(CASE WHEN ft.repair_ticket_id <> 0  THEN ft.hardware_repair_duration ELSE NULL END) as min_hardware,
			AVG(ft.software_fix_duration) as avg_software,
			MAX(ft.software_fix_duration) as max_software,
			MIN(ft.software_fix_duration) as min_software,
-- 			AVG(ft.total_downtime) as avg_downtime, //停机总时长
-- 			MAX(ft.total_downtime) as max_downtime,
-- 			MIN(ft.total_downtime) as min_downtime,
			AVG(ft.business_impact_time) as avg_impact,
			MAX(ft.business_impact_time) as max_impact,
			MIN(ft.business_impact_time) as min_impact
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 执行查询
	var result struct {
		//AvgDiagnosis sql.NullFloat64 `json:"avg_diagnosis"`
		//MaxDiagnosis sql.NullInt64   `json:"max_diagnosis"`
		//MinDiagnosis sql.NullInt64   `json:"min_diagnosis"` //诊断时长
		AvgResponse sql.NullFloat64 `json:"avg_response"`
		MaxResponse sql.NullInt64   `json:"max_response"`
		MinResponse sql.NullInt64   `json:"min_response"`
		AvgHardware sql.NullFloat64 `json:"avg_hardware"`
		MaxHardware sql.NullInt64   `json:"max_hardware"`
		MinHardware sql.NullInt64   `json:"min_hardware"`
		AvgSoftware sql.NullFloat64 `json:"avg_software"`
		MaxSoftware sql.NullInt64   `json:"max_software"`
		MinSoftware sql.NullInt64   `json:"min_software"`
		//AvgDowntime sql.NullFloat64 `json:"avg_downtime"`
		//MaxDowntime sql.NullInt64   `json:"max_downtime"`
		//MinDowntime sql.NullInt64   `json:"min_downtime"` //停机总时长
		AvgImpact sql.NullFloat64 `json:"avg_impact"`
		MaxImpact sql.NullInt64   `json:"max_impact"`
		MinImpact sql.NullInt64   `json:"min_impact"`
	}

	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&result).Error; err != nil {
		return nil, err
	}

	// 查询硬件平均响应时长（从waiting_accept到assigned的时间）
	hwResponseQuery := `
		SELECT
			AVG(TIMESTAMPDIFF(MINUTE, t1.operation_time, t2.operation_time)) as avg_hw_response,
			MAX(TIMESTAMPDIFF(MINUTE, t1.operation_time, t2.operation_time)) as max_hw_response,
			MIN(TIMESTAMPDIFF(MINUTE, t1.operation_time, t2.operation_time)) as min_hw_response,
			COUNT(DISTINCT t1.repair_ticket_id) as total_tickets
		FROM
			repair_ticket_status_histories t1
		JOIN
			repair_ticket_status_histories t2 ON t1.repair_ticket_id = t2.repair_ticket_id
		JOIN
			repair_tickets rt ON t1.repair_ticket_id = rt.id
		LEFT JOIN
			fault_tickets ft ON rt.fault_ticket_id = ft.id
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			t1.new_status = 'waiting_accept' AND
			t2.previous_status = 'waiting_accept' AND
			t2.new_status = 'assigned' AND
			t1.operation_time < t2.operation_time AND
			rt.created_at BETWEEN ? AND ? AND
			rt.deleted_at IS NULL
	`

	// 添加过滤条件
	hwResponseArgs := []interface{}{startDate, endDate}

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		hwResponseQuery += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	if project != "" {
		hwResponseQuery += " AND r.project = ?"
		hwResponseArgs = append(hwResponseArgs, project)
	}

	var hwResponseResult struct {
		AvgHwResponse sql.NullFloat64 `json:"avg_hw_response"`
		MaxHwResponse sql.NullInt64   `json:"max_hw_response"`
		MinHwResponse sql.NullInt64   `json:"min_hw_response"`
		TotalTickets  sql.NullInt64   `json:"total_tickets"`
	}

	if err := r.db.WithContext(ctx).Raw(hwResponseQuery, hwResponseArgs...).Scan(&hwResponseResult).Error; err != nil {
		return nil, err
	}

	// 准备返回数据
	avgTimes := []float64{
		//getFloat64FromNullable(result.AvgDiagnosis),
		getFloat64FromNullable(result.AvgResponse),
		getFloat64FromNullable(hwResponseResult.AvgHwResponse),
		getFloat64FromNullable(result.AvgHardware),
		getFloat64FromNullable(result.AvgSoftware),
		//getFloat64FromNullable(result.AvgDowntime),//停机总时长
		getFloat64FromNullable(result.AvgImpact),
	}

	maxTimes := []int64{
		//getInt64FromNullable(result.MaxDiagnosis),
		getInt64FromNullable(result.MaxResponse),
		getInt64FromNullable(hwResponseResult.MaxHwResponse),
		getInt64FromNullable(result.MaxHardware),
		getInt64FromNullable(result.MaxSoftware),
		//getInt64FromNullable(result.MaxDowntime),
		getInt64FromNullable(result.MaxImpact),
	}

	minTimes := []int64{
		//getInt64FromNullable(result.MinDiagnosis),
		getInt64FromNullable(result.MinResponse),
		getInt64FromNullable(hwResponseResult.MinHwResponse),
		getInt64FromNullable(result.MinHardware),
		getInt64FromNullable(result.MinSoftware),
		//getInt64FromNullable(result.MinDowntime),
		getInt64FromNullable(result.MinImpact),
	}

	return map[string]interface{}{
		"phases":              phases,
		"avgTimes":            avgTimes,
		"maxTimes":            maxTimes,
		"minTimes":            minTimes,
		"hw_response_tickets": getInt64FromNullable(hwResponseResult.TotalTickets),
	}, nil
}

// GetSLAStats 获取SLA达标情况
func (r *dashboardRepository) GetSLAStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	// 构建查询SQL
	query := `
		SELECT
			ft.sla_status as status,
			COUNT(*) as count
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 添加分组
	query += `
		GROUP BY
			ft.sla_status
	`

	type Result struct {
		Status string `json:"status"`
		Count  int    `json:"count"`
	}

	var results []Result

	// 执行查询
	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 准备返回数据
	var compliance, violated, exempted, total int

	for _, result := range results {
		switch result.Status {
		case "in_compliance":
			compliance = result.Count
		case "violated":
			violated = result.Count
		case "exempted":
			exempted = result.Count
		}
		total += result.Count
	}

	// 计算达标率
	var complianceRate float64 = 0
	if total > 0 {
		complianceRate = float64(compliance) / float64(total) * 100
	}

	return map[string]interface{}{
		"compliance":     compliance,
		"violated":       violated,
		"exempted":       exempted,
		"total":          total,
		"complianceRate": complianceRate,
	}, nil
}

// GetBusinessImpactTotalTime 计算计入SLA且已完成报障单的业务影响总时长
func (r *dashboardRepository) GetBusinessImpactTotalTime(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	// 构建查询SQL
	query := `
		SELECT
			COUNT(*) as total_tickets,
			SUM(ft.business_impact_time) as total_impact_time,
			AVG(ft.business_impact_time) as avg_impact_time,
			MAX(ft.business_impact_time) as max_impact_time,
			MIN(ft.business_impact_time) as min_impact_time
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 执行查询
	var result struct {
		TotalTickets    int             `json:"total_tickets"`
		TotalImpactTime sql.NullInt64   `json:"total_impact_time"`
		AvgImpactTime   sql.NullFloat64 `json:"avg_impact_time"`
		MaxImpactTime   sql.NullInt64   `json:"max_impact_time"`
		MinImpactTime   sql.NullInt64   `json:"min_impact_time"`
	}

	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&result).Error; err != nil {
		return nil, err
	}

	// 按照故障类型分组统计业务影响时间
	typeQuery := `
		SELECT
			ft.fault_type as type,
			SUM(ft.business_impact_time) as impact_time
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		typeQuery += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	typeArgs := []interface{}{startDate, endDate}
	if project != "" {
		typeQuery += " AND r.project = ?"
		typeArgs = append(typeArgs, project)
	}

	// 添加分组和排序
	typeQuery += `
		GROUP BY
			ft.fault_type
		ORDER BY
			impact_time DESC
	`

	type TypeImpactResult struct {
		Type       string `json:"type"`
		ImpactTime int64  `json:"impact_time"`
	}

	var typeResults []TypeImpactResult
	if err := r.db.WithContext(ctx).Raw(typeQuery, typeArgs...).Scan(&typeResults).Error; err != nil {
		return nil, err
	}

	// 准备类型统计数据
	typeData := make([]map[string]interface{}, 0, len(typeResults))
	for _, tr := range typeResults {
		typeData = append(typeData, map[string]interface{}{
			"type": getTypeName(tr.Type),
			"time": tr.ImpactTime,
		})
	}

	// 准备返回数据
	return map[string]interface{}{
		"total_tickets":     result.TotalTickets,
		"total_impact_time": getInt64FromNullable(result.TotalImpactTime),
		"avg_impact_time":   getFloat64FromNullable(result.AvgImpactTime),
		"max_impact_time":   getInt64FromNullable(result.MaxImpactTime),
		"min_impact_time":   getInt64FromNullable(result.MinImpactTime),
		"type_statistics":   typeData,
	}, nil
}

// GetGPUFaultRatio 获取GPU卡故障比
func (r *dashboardRepository) GetGPUFaultRatio(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	if project == "" {
		return nil, fmt.Errorf("项目不能为空")
	}

	// 1. 查询该项目下的GPU故障工单和插槽位置信息
	type GPUFaultResult struct {
		TicketID     uint   `json:"ticket_id"`
		SlotPosition string `json:"slot_position"`
	}
	var gpuFaults []GPUFaultResult

	gpuFaultQuery := `
		SELECT
			ft.id as ticket_id,
			ft.slot_position as slot_position
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
			AND ft.fault_detail_type LIKE 'GPU故障%'
			AND r.project = ?
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		gpuFaultQuery += " AND ft.count_in_sla = true"
	}

	if err := r.db.WithContext(ctx).Raw(gpuFaultQuery, startDate, endDate, project).Scan(&gpuFaults).Error; err != nil {
		return nil, err
	}

	// 2. 计算实际故障GPU卡数量
	var gpuCardCount int64
	for _, fault := range gpuFaults {
		if fault.SlotPosition == "" || fault.SlotPosition == "N/A" {
			// 如果插槽位置为空或N/A，记为1张卡
			gpuCardCount++
		} else {
			// 替换中文逗号为英文逗号
			normalizedPosition := strings.ReplaceAll(fault.SlotPosition, "，", ",")
			// 统计逗号分隔的插槽位置数量
			positions := strings.Split(normalizedPosition, ",")
			gpuCardCount += int64(len(positions))
		}
	}

	// 3. 查询该项目下的GPU服务器数量
	var gpuServerCount int64
	gpuServerQuery := `
		SELECT
			COUNT(*) as count
		FROM
			resources r
		LEFT JOIN
			asset_devices ad ON r.asset_id = ad.id
		WHERE
			r.deleted_at IS NULL
			AND r.project = ?
			AND ad.asset_type = 'gpu_server'
			AND ad.asset_status != 'scrapped'
	`

	if err := r.db.WithContext(ctx).Raw(gpuServerQuery, project).Scan(&gpuServerCount).Error; err != nil {
		return nil, err
	}

	// 4. 计算GPU卡故障比（每台GPU服务器有8张GPU卡）
	totalGPUCards := gpuServerCount * 8
	var faultRatio float64 = 0
	if totalGPUCards > 0 {
		faultRatio = float64(gpuCardCount) / float64(totalGPUCards)
	}

	return map[string]interface{}{
		"project":             project,
		"gpu_fault_count":     gpuCardCount,
		"gpu_server_count":    gpuServerCount,
		"total_gpu_cards":     totalGPUCards,
		"gpu_fault_ratio":     faultRatio,
		"fault_ratio_percent": faultRatio * 100,
		"time_range": map[string]string{
			"start": startDate.Format("2006-01-02"),
			"end":   endDate.Format("2006-01-02"),
		},
	}, nil
}

// GetFaultTicketDetails 获取故障单详情列表
func (r *dashboardRepository) GetFaultTicketDetails(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	// 定义返回结果结构
	type TicketDetail struct {
		TicketNo           string `json:"ticket_no"`
		ResourceIdentifier string `json:"resource_identifier"`
		FaultDetailType    string `json:"fault_detail_type"`
		BusinessImpactTime int    `json:"business_impact_time"`
		RepairMethod       string `json:"repair_method"`
		IsColdMigration    bool   `json:"is_cold_migration"`
		CreationTime       string `json:"creation_time"`
	}

	var tickets []TicketDetail
	var total int64

	// 构建查询SQL
	query := `
		SELECT
			ft.ticket_no,
			ft.resource_identifier,
			COALESCE(ft.fault_detail_type, '') as fault_detail_type,
			COALESCE(ft.business_impact_time, 0) as business_impact_time,
			COALESCE(ft.repair_method, '') as repair_method,
			CASE WHEN ft.repair_method = 'cold_migration' THEN true ELSE false END as is_cold_migration,
			ft.creation_time
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
			AND r.project = ?
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 添加排序
	query += `
		ORDER BY
			ft.creation_time DESC
	`

	// 准备查询参数
	args := []interface{}{startDate, endDate, project}

	// 执行查询
	rows, err := r.db.WithContext(ctx).Raw(query, args...).Rows()
	if err != nil {
		return nil, err
	}
	defer func() {
		if err := rows.Close(); err != nil {
			fmt.Println("关闭数据库行失败:", err)
		}
	}()

	// 使用sql.NullString和sql.NullInt64处理NULL值
	type ScanResult struct {
		TicketNo           sql.NullString
		ResourceIdentifier sql.NullString
		FaultDetailType    sql.NullString
		BusinessImpactTime sql.NullInt64
		RepairMethod       sql.NullString
		IsColdMigration    bool
		CreationTime       time.Time
	}

	// 遍历结果
	for rows.Next() {
		var scanResult ScanResult

		if err := rows.Scan(
			&scanResult.TicketNo,
			&scanResult.ResourceIdentifier,
			&scanResult.FaultDetailType,
			&scanResult.BusinessImpactTime,
			&scanResult.RepairMethod,
			&scanResult.IsColdMigration,
			&scanResult.CreationTime,
		); err != nil {
			return nil, err
		}

		// 将NullString转换为普通字符串，处理NULL值
		ticket := TicketDetail{
			TicketNo:           getStringFromNullable(scanResult.TicketNo),
			ResourceIdentifier: getStringFromNullable(scanResult.ResourceIdentifier),
			FaultDetailType:    getStringFromNullable(scanResult.FaultDetailType),
			BusinessImpactTime: int(getInt64FromNullable(scanResult.BusinessImpactTime)),
			RepairMethod:       getStringFromNullable(scanResult.RepairMethod),
			IsColdMigration:    scanResult.IsColdMigration,
			CreationTime:       scanResult.CreationTime.Format("2006-01-02 15:04:05"),
		}

		tickets = append(tickets, ticket)
	}

	// 检查遍历过程中是否有错误
	if err := rows.Err(); err != nil {
		return nil, err
	}

	// 获取总数
	countQuery := `
		SELECT
			COUNT(*)
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
			AND r.project = ?
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		countQuery += " AND ft.count_in_sla = true"
	}

	if err := r.db.WithContext(ctx).Raw(countQuery, args...).Count(&total).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"tickets": tickets,
		"total":   total,
	}, nil
}

// GetTotalFaultDevices 获取故障总台数（根据不重复的device_sn统计）
func (r *dashboardRepository) GetTotalFaultDevices(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	// 构建查询SQL
	query := `
		SELECT
			COUNT(DISTINCT ft.device_sn) as total_devices,
			COUNT(*) as total_tickets
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 定义结果结构
	var result struct {
		TotalDevices int64 `json:"total_devices"`
		TotalTickets int64 `json:"total_tickets"`
	}

	// 执行查询
	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&result).Error; err != nil {
		return nil, err
	}

	// 统计故障类型分布
	typeQuery := `
		SELECT
			ft.fault_type as type,
			COUNT(DISTINCT ft.device_sn) as devices_count
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		typeQuery += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	typeArgs := []interface{}{startDate, endDate}
	if project != "" {
		typeQuery += " AND r.project = ?"
		typeArgs = append(typeArgs, project)
	}

	// 添加分组和排序
	typeQuery += `
		GROUP BY
			ft.fault_type
		ORDER BY
			devices_count DESC
	`

	type TypeDevicesResult struct {
		Type         string `json:"type"`
		DevicesCount int64  `json:"devices_count"`
	}

	var typeResults []TypeDevicesResult
	if err := r.db.WithContext(ctx).Raw(typeQuery, typeArgs...).Scan(&typeResults).Error; err != nil {
		return nil, err
	}

	// 准备类型统计数据
	typeData := make([]map[string]interface{}, 0, len(typeResults))
	types := make([]string, 0, len(typeResults))
	for _, tr := range typeResults {
		typeName := getTypeName(tr.Type)
		typeData = append(typeData, map[string]interface{}{
			"name":  typeName,
			"value": tr.DevicesCount,
		})
		types = append(types, typeName)
	}

	return map[string]interface{}{
		"total_devices": result.TotalDevices,
		"total_tickets": result.TotalTickets,
		"type_stats": map[string]interface{}{
			"types": types,
			"data":  typeData,
		},
		"time_range": map[string]string{
			"start": startDate.Format("2006-01-02"),
			"end":   endDate.Format("2006-01-02"),
		},
	}, nil
}

// GetEngineerResponseAndFixTimeStats 获取分配人员响应和修复时长统计
func (r *dashboardRepository) GetEngineerResponseAndFixTimeStats(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	// 软件修复分配人统计查询
	softwareStatsQuery := r.db.Table("fault_tickets ft").
		Select(`
			ft.assigned_to as engineer_name,
			COUNT(DISTINCT ft.id) as ticket_count,
			AVG(ft.response_duration) as avg_response_time,
			AVG(ft.software_fix_duration) as avg_fix_time,
			'software' as ticket_type
		`).
		Joins("LEFT JOIN resources r ON ft.device_sn = r.sn").
		Where("ft.creation_time BETWEEN ? AND ?", startDate, endDate).
		Where("ft.assigned_to IS NOT NULL AND ft.assigned_to != ''").
		Where("ft.status = 'completed'")
	// 如果指定了项目，添加项目过滤条件
	if project != "" {
		softwareStatsQuery = softwareStatsQuery.Where("r.project = ?", project)
	}

	// 是否只统计计入SLA的工单
	if countInSLA {
		softwareStatsQuery = softwareStatsQuery.Where("ft.count_in_sla = ?", true)
	}

	// 硬件修复工程师统计查询
	hwQuery := `
		SELECT
			rt.assigned_engineer_name as engineer_name,
			COUNT(DISTINCT rt.id) as ticket_count,
			AVG(TIMESTAMPDIFF(MINUTE, t1.operation_time, t2.operation_time)) as avg_response_time,
			AVG(CASE WHEN ft.repair_ticket_id <> 0 THEN ft.hardware_repair_duration ELSE NULL END) as avg_fix_time,
			'hardware' as ticket_type
		FROM
			repair_tickets rt
		JOIN
			repair_ticket_status_histories t1 ON rt.id = t1.repair_ticket_id
		JOIN
			repair_ticket_status_histories t2 ON rt.id = t2.repair_ticket_id
		JOIN
			fault_tickets ft ON rt.fault_ticket_id = ft.id
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			t1.new_status = 'waiting_accept' AND
			t2.previous_status = 'waiting_accept' AND
			t2.new_status = 'assigned' AND
			t1.operation_time < t2.operation_time AND
			rt.created_at BETWEEN ? AND ? AND
			rt.deleted_at IS NULL AND
			ft.status = 'completed' AND
			rt.assigned_engineer_name IS NOT NULL AND
			rt.assigned_engineer_name != ''
	`

	// 添加过滤条件
	hwQueryArgs := []interface{}{startDate, endDate}

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		hwQuery += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	if project != "" {
		hwQuery += " AND r.project = ?"
		hwQueryArgs = append(hwQueryArgs, project)
	}

	// 按工程师分组
	hwQuery += " GROUP BY rt.assigned_engineer_name"

	// 软件统计结果
	type EngineerStats struct {
		EngineerName    string  `json:"engineer_name"`
		TicketCount     int     `json:"ticket_count"`
		AvgResponseTime float64 `json:"avg_response_time"`
		AvgFixTime      float64 `json:"avg_fix_time"`
		TicketType      string  `json:"ticket_type"`
	}

	var softwareStats []EngineerStats
	var hardwareStats []EngineerStats

	// 执行软件统计查询
	if err := softwareStatsQuery.Group("ft.assigned_to").Find(&softwareStats).Error; err != nil {
		return nil, err
	}

	// 执行硬件统计查询
	if err := r.db.WithContext(ctx).Raw(hwQuery, hwQueryArgs...).Scan(&hardwareStats).Error; err != nil {
		return nil, err
	}

	// 合并结果
	var allStats []map[string]interface{}
	for _, stat := range softwareStats {
		allStats = append(allStats, map[string]interface{}{
			"engineer_name":     stat.EngineerName,
			"ticket_count":      stat.TicketCount,
			"avg_response_time": stat.AvgResponseTime,
			"avg_fix_time":      stat.AvgFixTime,
			"ticket_type":       stat.TicketType,
		})
	}

	for _, stat := range hardwareStats {
		allStats = append(allStats, map[string]interface{}{
			"engineer_name":     stat.EngineerName,
			"ticket_count":      stat.TicketCount,
			"avg_response_time": stat.AvgResponseTime,
			"avg_fix_time":      stat.AvgFixTime,
			"ticket_type":       stat.TicketType,
		})
	}

	// 构建工程师列表
	engineerMap := make(map[string]bool)
	for _, stat := range softwareStats {
		engineerMap[stat.EngineerName] = true
	}
	for _, stat := range hardwareStats {
		engineerMap[stat.EngineerName] = true
	}

	engineersList := make([]string, 0, len(engineerMap))
	for engineer := range engineerMap {
		engineersList = append(engineersList, engineer)
	}

	return map[string]interface{}{
		"engineers": engineersList,
		"stats":     allStats,
	}, nil
}

// GetFaultDeviceBrandDistribution 获取故障设备厂商分布统计
func (r *dashboardRepository) GetFaultDeviceBrandDistribution(ctx context.Context, startDate, endDate time.Time, project string, countInSLA bool) (map[string]interface{}, error) {
	// 查询故障设备厂商分布
	query := `
		SELECT
			d.brand as brand,
			COUNT(DISTINCT ft.device_sn) as device_count
		FROM
			fault_tickets ft
		JOIN
			asset_devices d ON ft.device_sn = d.sn
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 按厂商分组
	query += `
		GROUP BY
			d.brand
		ORDER BY
			device_count DESC
	`

	// 统计结果
	type BrandResult struct {
		Brand       string `json:"brand"`
		DeviceCount int    `json:"device_count"`
	}

	var results []BrandResult
	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 计算总数
	var totalDevices int
	for _, result := range results {
		totalDevices += result.DeviceCount
	}

	// 准备图表数据
	brands := make([]string, 0, len(results))
	data := make([]map[string]interface{}, 0, len(results))

	for _, result := range results {
		// 如果厂商为空，显示为"未知"
		brandName := result.Brand
		if brandName == "" {
			brandName = "未知"
		}

		brands = append(brands, brandName)
		data = append(data, map[string]interface{}{
			"name":  brandName,
			"value": result.DeviceCount,
		})
	}

	return map[string]interface{}{
		"total_devices": totalDevices,
		"brands":        brands,
		"data":          data,
		"time_range": map[string]string{
			"start": startDate.Format("2006-01-02"),
			"end":   endDate.Format("2006-01-02"),
		},
	}, nil
}

// GetDailyBusinessImpactTrend 获取每天业务影响时长趋势统计
func (r *dashboardRepository) GetDailyBusinessImpactTrend(ctx context.Context, startDate, endDate time.Time, groupBy string, project string, countInSLA bool) (map[string]interface{}, error) {
	var timeFormat string
	var interval string

	// 根据分组方式确定时间格式和SQL查询
	switch groupBy {
	case "day":
		timeFormat = "2006-01-02"
		interval = "day"
	case "week":
		timeFormat = "2006-W%V" // ISO周格式
		interval = "week"
	case "month":
		timeFormat = "2006-01"
		interval = "month"
	default:
		timeFormat = "2006-01-02"
		interval = "day"
	}

	// 构建查询SQL
	query := `
		SELECT
			DATE_FORMAT(ft.creation_time, ?) as date,
			SUM(ft.business_impact_time) as total_impact_time,
			COUNT(DISTINCT ft.id) as ticket_count
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{getSQLDateFormat(interval), startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 按日期分组
	query += `
		GROUP BY
			date
		ORDER BY
			date ASC
	`

	// 统计结果
	type DailyImpact struct {
		Date            string `json:"date"`
		TotalImpactTime int64  `json:"total_impact_time"`
		TicketCount     int    `json:"ticket_count"`
	}

	var results []DailyImpact
	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 生成日期序列（避免没有数据的日期不显示）
	dateMap := make(map[string]DailyImpact)

	for _, result := range results {
		dateMap[result.Date] = result
	}

	// 填充所有日期
	var allDates []string
	var allImpactTimes []int64
	var allTicketCounts []int
	var totalImpactTime int64
	var totalTickets int

	// 根据不同的聚合粒度生成日期
	currentDate := startDate
	for !currentDate.After(endDate) {
		var dateStr string

		switch interval {
		case "day":
			dateStr = currentDate.Format(timeFormat)
			currentDate = currentDate.AddDate(0, 0, 1)
		case "week":
			year, week := currentDate.ISOWeek()
			dateStr = fmt.Sprintf("%d-W%02d", year, week)
			currentDate = currentDate.AddDate(0, 0, 7)
		case "month":
			dateStr = currentDate.Format(timeFormat)
			currentDate = time.Date(currentDate.Year(), currentDate.Month()+1, 1, 0, 0, 0, 0, currentDate.Location())
		}

		allDates = append(allDates, dateStr)

		if impact, exists := dateMap[dateStr]; exists {
			allImpactTimes = append(allImpactTimes, impact.TotalImpactTime)
			allTicketCounts = append(allTicketCounts, impact.TicketCount)
			totalImpactTime += impact.TotalImpactTime
			totalTickets += impact.TicketCount
		} else {
			allImpactTimes = append(allImpactTimes, 0)
			allTicketCounts = append(allTicketCounts, 0)
		}
	}

	return map[string]interface{}{
		"dates":             allDates,
		"impact_times":      allImpactTimes,
		"ticket_counts":     allTicketCounts,
		"total_impact_time": totalImpactTime,
		"total_tickets":     totalTickets,
		"time_range": map[string]string{
			"start": startDate.Format("2006-01-02"),
			"end":   endDate.Format("2006-01-02"),
		},
	}, nil
}

// GetHardwareFaultTrend 获取每天硬件故障次数趋势统计
func (r *dashboardRepository) GetHardwareFaultTrend(ctx context.Context, startDate, endDate time.Time, groupBy string, project string, countInSLA bool) (map[string]interface{}, error) {
	var timeFormat string
	var interval string

	// 根据分组方式确定时间格式和SQL查询
	switch groupBy {
	case "day":
		timeFormat = "2006-01-02"
		interval = "day"
	case "week":
		timeFormat = "2006-W%V" // ISO周格式
		interval = "week"
	case "month":
		timeFormat = "2006-01"
		interval = "month"
	default:
		timeFormat = "2006-01-02"
		interval = "day"
	}

	// 构建查询SQL
	query := `
		SELECT
			DATE_FORMAT(ft.creation_time, ?) as date,
			COUNT(DISTINCT ft.id) as ticket_count
		FROM
			fault_tickets ft
		LEFT JOIN
			resources r ON ft.device_sn = r.sn
		WHERE
			ft.creation_time BETWEEN ? AND ?
			AND ft.deleted_at IS NULL
			AND ft.fault_type = 'hardware'
			AND ft.status = 'completed'
	`

	// 如果需要过滤计入SLA的工单
	if countInSLA {
		query += " AND ft.count_in_sla = true"
	}

	// 如果指定了项目，添加项目过滤条件
	args := []interface{}{getSQLDateFormat(interval), startDate, endDate}
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	// 按日期分组
	query += `
		GROUP BY
			date
		ORDER BY
			date ASC
	`

	// 统计结果
	type DailyFault struct {
		Date        string `json:"date"`
		TicketCount int    `json:"ticket_count"`
	}

	var results []DailyFault
	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 生成日期序列（避免没有数据的日期不显示）
	dateMap := make(map[string]DailyFault)

	for _, result := range results {
		dateMap[result.Date] = result
	}

	// 填充所有日期
	var allDates []string
	var allTicketCounts []int
	var totalTickets int

	// 根据不同的聚合粒度生成日期
	currentDate := startDate
	for !currentDate.After(endDate) {
		var dateStr string

		switch interval {
		case "day":
			dateStr = currentDate.Format(timeFormat)
			currentDate = currentDate.AddDate(0, 0, 1)
		case "week":
			year, week := currentDate.ISOWeek()
			dateStr = fmt.Sprintf("%d-W%02d", year, week)
			currentDate = currentDate.AddDate(0, 0, 7)
		case "month":
			dateStr = currentDate.Format(timeFormat)
			currentDate = time.Date(currentDate.Year(), currentDate.Month()+1, 1, 0, 0, 0, 0, currentDate.Location())
		}

		allDates = append(allDates, dateStr)

		if fault, exists := dateMap[dateStr]; exists {
			allTicketCounts = append(allTicketCounts, fault.TicketCount)
			totalTickets += fault.TicketCount
		} else {
			allTicketCounts = append(allTicketCounts, 0)
		}
	}

	return map[string]interface{}{
		"dates":         allDates,
		"ticket_counts": allTicketCounts,
		"total_tickets": totalTickets,
		"time_range": map[string]string{
			"start": startDate.Format("2006-01-02"),
			"end":   endDate.Format("2006-01-02"),
		},
	}, nil
}

// 辅助函数: 根据不同数据库返回不同的日期格式化字符串
func getSQLDateFormat(interval string) string {
	switch interval {
	case "day":
		return "%Y-%m-%d"
	case "week":
		return "%x-W%v" // ISO周格式
	case "month":
		return "%Y-%m"
	default:
		return "%Y-%m-%d"
	}
}

// 辅助函数: 获取故障类型的中文名称
func getTypeName(typeCode string) string {
	typeNames := map[string]string{
		"hardware":  "硬件故障",
		"software":  "软件故障",
		"network":   "网络故障",
		"system":    "系统故障",
		"operation": "操作故障",
		"other":     "其他",
	}

	if name, ok := typeNames[typeCode]; ok {
		return name
	}
	return typeCode
}

// 辅助函数: 获取状态的中文名称
func getStatusName(statusCode string) string {
	statusNames := map[string]string{
		"waiting_accept":          "等待受理",
		"investigating":           "故障定位中",
		"waiting_approval":        "等待审批",
		"approved_waiting_action": "维修前置处理",
		"repairing":               "维修中",
		"restarting":              "重启中",
		"migrating":               "迁移中",
		"software_fixing":         "软件修复中",
		"waiting_verification":    "等待验证",
		"summarizing":             "总结中",
		"completed":               "已完成",
		"cancelled":               "已取消",
	}

	if name, ok := statusNames[statusCode]; ok {
		return name
	}
	return statusCode
}

// 辅助函数: 获取故障来源的中文名称
func getSourceName(sourceCode string) string {
	sourceNames := map[string]string{
		"customer": "客户报障",
		"internal": "主动发现",
	}

	if name, ok := sourceNames[sourceCode]; ok {
		return name
	}
	return sourceCode
}

// 辅助函数: 从SQL.NullFloat64安全获取值
func getFloat64FromNullable(v sql.NullFloat64) float64 {
	if v.Valid {
		return v.Float64
	}
	return 0
}

// 辅助函数: 从SQL.NullInt64安全获取值
func getInt64FromNullable(v sql.NullInt64) int64 {
	if v.Valid {
		return v.Int64
	}
	return 0
}

// 辅助函数: 从SQL.NullString安全获取值
func getStringFromNullable(v sql.NullString) string {
	if v.Valid {
		return v.String
	}
	return ""
}

// GetResourceChangesStats 获取资源变更统计
func (r *dashboardRepository) GetResourceChangesStats(ctx context.Context, startDate, endDate time.Time, project string) (map[string]interface{}, error) {
	// 获取新增资源统计（软件上线工单）
	addedStats, err := r.getAddedResourceStats(ctx, startDate, endDate, project)
	if err != nil {
		return nil, fmt.Errorf("获取新增资源统计失败: %w", err)
	}

	// 获取下线资源统计（软件下线工单）
	removedStats, err := r.getRemovedResourceStats(ctx, startDate, endDate, project)
	if err != nil {
		return nil, fmt.Errorf("获取下线资源统计失败: %w", err)
	}

	return map[string]interface{}{
		"added":   addedStats,
		"removed": removedStats,
		"timeRange": map[string]string{
			"start": startDate.Format("2006-01-02"),
			"end":   endDate.Format("2006-01-02"),
		},
	}, nil
}

// getAddedResourceStats 获取新增资源统计（软件上线工单）
func (r *dashboardRepository) getAddedResourceStats(ctx context.Context, startDate, endDate time.Time, project string) (map[string]interface{}, error) {
	// 查询软件上线工单中的设备SN
	query := `
		SELECT DISTINCT
			sld.sn as device_sn,
			r.cluster
		FROM software_launch_tickets slt
		LEFT JOIN software_launch_devices sld ON slt.launch_ticket_no = sld.launch_ticket_no
		LEFT JOIN resources r ON sld.sn = r.sn
		WHERE slt.status = 'launch_completed'
		AND slt.created_at BETWEEN ? AND ?
		AND slt.deleted_at IS NULL
		AND sld.sn IS NOT NULL
		AND sld.sn != ''
	`

	args := []interface{}{startDate, endDate}

	// 如果指定了项目，添加项目过滤条件
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	var results []struct {
		DeviceSN string         `json:"device_sn"`
		Cluster  sql.NullString `json:"cluster"`
	}

	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	return r.calculateResourceTypeStats(results), nil
}

// getRemovedResourceStats 获取下线资源统计（软件下线工单）
func (r *dashboardRepository) getRemovedResourceStats(ctx context.Context, startDate, endDate time.Time, project string) (map[string]interface{}, error) {
	// 查询软件下线工单中的设备SN
	query := `
		SELECT DISTINCT
			sod.sn as device_sn,
			r.cluster
		FROM software_offline_tickets sot
		LEFT JOIN software_offline_devices sod ON sot.offline_ticket_no = sod.offline_ticket_no
		LEFT JOIN resources r ON sod.sn = r.sn
		WHERE sot.status = 'completed'
		AND sot.created_at BETWEEN ? AND ?
		AND sot.deleted_at IS NULL
		AND sod.sn IS NOT NULL
		AND sod.sn != ''
	`

	args := []interface{}{startDate, endDate}

	// 如果指定了项目，添加项目过滤条件
	if project != "" {
		query += " AND r.project = ?"
		args = append(args, project)
	}

	var results []struct {
		DeviceSN string         `json:"device_sn"`
		Cluster  sql.NullString `json:"cluster"`
	}

	if err := r.db.WithContext(ctx).Raw(query, args...).Scan(&results).Error; err != nil {
		return nil, err
	}

	return r.calculateResourceTypeStats(results), nil
}

// calculateResourceTypeStats 计算资源类型统计
func (r *dashboardRepository) calculateResourceTypeStats(results []struct {
	DeviceSN string         `json:"device_sn"`
	Cluster  sql.NullString `json:"cluster"`
}) map[string]interface{} {
	// 使用map统计每个cluster的数量
	clusterCounts := make(map[string]int)
	total := 0

	for _, result := range results {
		if result.DeviceSN == "" {
			continue
		}

		cluster := getStringFromNullable(result.Cluster)
		// 如果cluster为空，使用"未知"作为默认值
		if cluster == "" {
			cluster = "未知"
		}

		clusterCounts[cluster]++
		total++
	}

	// 构建返回数据
	types := make([]map[string]interface{}, 0, len(clusterCounts))

	for clusterName, count := range clusterCounts {
		percent := 0
		if total > 0 {
			percent = int(float64(count) / float64(total) * 100)
		}

		types = append(types, map[string]interface{}{
			"name":    clusterName,
			"count":   count,
			"percent": percent,
		})
	}

	return map[string]interface{}{
		"total": total,
		"types": types,
		"trend": 0, // TODO: 实现趋势计算
	}
}
