package service

import (
	"backend/internal/modules/dashboard/repository"
	"context"
	"time"
)

// ResourceStatsService 资源统计服务接口
type ResourceStatsService interface {
	// 获取服务器总数量及各状态数量
	GetServerStats(ctx context.Context) (map[string]interface{}, error)
	// 获取各集群类型的数量
	GetClusterStats(ctx context.Context) (map[string]interface{}, error)
	// 按项目获取服务器数量（支持时间范围参数）
	GetServerStatsByProject(ctx context.Context, project string, startDate, endDate *time.Time) (map[string]interface{}, error)
	// 按项目获取集群数量（支持时间范围参数）
	GetClusterStatsByProject(ctx context.Context, project string, startDate, endDate *time.Time) (map[string]interface{}, error)
	// 获取设备品牌分布
	GetBrandDistribution(ctx context.Context, project string) (map[string]interface{}, error)
	// 获取服务器机房分布
	GetRoomDistribution(ctx context.Context, project string) (map[string]interface{}, error)
	// 获取GPU卡型号分布
	GetGpuModelDistribution(ctx context.Context, project string) (map[string]interface{}, error)
}

// resourceStatsService 资源统计服务实现
type resourceStatsService struct {
	repo repository.ResourceStatsRepository
}

// NewResourceStatsService 创建资源统计服务
func NewResourceStatsService(repo repository.ResourceStatsRepository) ResourceStatsService {
	return &resourceStatsService{repo: repo}
}

// GetServerStats 获取服务器总数量及各状态数量
func (s *resourceStatsService) GetServerStats(ctx context.Context) (map[string]interface{}, error) {
	return s.repo.GetServerStats(ctx)
}

// GetClusterStats 获取各集群类型的数量
func (s *resourceStatsService) GetClusterStats(ctx context.Context) (map[string]interface{}, error) {
	return s.repo.GetClusterStats(ctx)
}

// GetServerStatsByProject 按项目获取服务器数量（支持时间范围参数）
func (s *resourceStatsService) GetServerStatsByProject(ctx context.Context, project string, startDate, endDate *time.Time) (map[string]interface{}, error) {
	return s.repo.GetServerStatsByProject(ctx, project, startDate, endDate)
}

// GetClusterStatsByProject 按项目获取集群数量（支持时间范围参数）
func (s *resourceStatsService) GetClusterStatsByProject(ctx context.Context, project string, startDate, endDate *time.Time) (map[string]interface{}, error) {
	return s.repo.GetClusterStatsByProject(ctx, project, startDate, endDate)
}

// GetBrandDistribution 获取设备品牌分布
func (s *resourceStatsService) GetBrandDistribution(ctx context.Context, project string) (map[string]interface{}, error) {
	return s.repo.GetBrandDistribution(ctx, project)
}

// GetRoomDistribution 获取服务器机房分布
func (s *resourceStatsService) GetRoomDistribution(ctx context.Context, project string) (map[string]interface{}, error) {
	return s.repo.GetRoomDistribution(ctx, project)
}

// GetGpuModelDistribution 获取GPU卡型号分布
func (s *resourceStatsService) GetGpuModelDistribution(ctx context.Context, project string) (map[string]interface{}, error) {
	return s.repo.GetGpuModelDistribution(ctx, project)
}
