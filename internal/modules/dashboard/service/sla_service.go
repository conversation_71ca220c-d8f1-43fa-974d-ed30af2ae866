package service

import (
	"backend/internal/modules/dashboard/repository"
	"context"
	"fmt"
	"math"
	"sort"
	"time"
)

// SLAService SLA服务接口
type SLAService interface {
	// 计算SLA
	CalculateSLA(ctx context.Context, year, month int, algorithm string) (map[string]interface{}, error)
	// 计算特定项目的SLA
	CalculateProjectSLA(ctx context.Context, year, month int, algorithm string, project string) (map[string]interface{}, error)
	// 计算每天的SLA数据
	CalculateDailySLA(ctx context.Context, year, month int, project string) (map[string]interface{}, error)
}

// slaService SLA服务实现
type slaService struct {
	repo repository.SLARepository
}

// NewSLAService 创建SLA服务
func NewSLAService(repo repository.SLARepository) SLAService {
	return &slaService{repo: repo}
}

// CalculateSLA 计算SLA
func (s *slaService) CalculateSLA(ctx context.Context, year, month int, algorithm string) (map[string]interface{}, error) {
	// 调用项目计算SLA方法，但不指定项目（计算所有项目的综合SLA）
	return s.CalculateProjectSLA(ctx, year, month, algorithm, "")
}

// CalculateProjectSLA 计算特定项目的SLA
func (s *slaService) CalculateProjectSLA(ctx context.Context, year, month int, algorithm string, project string) (map[string]interface{}, error) {
	// 获取指定月份的总分钟数
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

	// 计算月份总分钟数
	totalMinutes := int64(endDate.Sub(startDate).Minutes()) + 1

	// 获取指定月份的GPU服务器数量（优先使用月度统计数据）
	gpuServerCount, err := s.repo.GetProjectGPUServerCountByMonth(ctx, project, year, month)
	if err != nil {
		return nil, fmt.Errorf("获取GPU服务器数量失败: %w", err)
	}

	// 检查是否使用了月度统计数据
	hasMonthlyStats, _ := s.repo.HasMonthlyStats(ctx, year, month)
	dataSource := "real_time"
	if hasMonthlyStats {
		dataSource = "monthly_stats"
	}

	// 如果项目没有GPU服务器，返回100%的SLA
	if gpuServerCount == 0 {
		return map[string]interface{}{
			"year":                 year,
			"month":                month,
			"project":              project,
			"algorithm":            algorithm,
			"total_minutes":        totalMinutes,
			"total_impact_minutes": 0,
			"excluded_minutes":     0,
			"excluded_count":       0,
			"gpu_server_count":     0,
			"sla_percentage":       100.0,
			"sla_tickets_count":    0,
			"period": map[string]string{
				"start": startDate.Format("2006-01-02 15:04:05"),
				"end":   endDate.Format("2006-01-02 15:04:05"),
			},
		}, nil
	}

	// 获取影响SLA的工单及其影响时长
	tickets, err := s.repo.GetSLAImpactTickets(ctx, startDate, endDate, project)
	if err != nil {
		return nil, fmt.Errorf("获取SLA影响工单失败: %w", err)
	}

	// 计算总的SLA影响时长
	var totalImpactMinutes int64 = 0
	for _, ticket := range tickets {
		totalImpactMinutes += ticket.ImpactMinutes
	}

	var slaPercentage float64
	var excludedImpactMinutes int64 = 0
	var excludeCount int

	switch algorithm {
	case "100":
		// 100%算法:
		// 分子：项目GPU服务器数量 * 指定月份总分钟数 - 指定月份内影响SLA工单的"影响SLA时长"之和
		// 分母：项目GPU服务器数量 * 指定月份总分钟数
		numerator := int64(gpuServerCount)*totalMinutes - totalImpactMinutes
		denominator := int64(gpuServerCount) * totalMinutes
		slaPercentage = float64(numerator) / float64(denominator) * 100

	case "90":
		// 90%算法:
		// 按影响时长排序工单
		sortedTickets := make([]repository.SLATicket, len(tickets))
		copy(sortedTickets, tickets)
		sort.Slice(sortedTickets, func(i, j int) bool {
			return sortedTickets[i].ImpactMinutes > sortedTickets[j].ImpactMinutes
		})

		// 计算需要排除的服务器数量（GPU服务器总数的10%）
		excludeCount = int(math.Ceil(float64(gpuServerCount) * 0.1))

		// 从影响时间最长的工单开始排除，直到排除的工单数量达到excludeCount
		excludedImpactMinutes = 0
		for i := 0; i < len(sortedTickets) && i < excludeCount; i++ {
			excludedImpactMinutes += sortedTickets[i].ImpactMinutes
		}

		numerator := int64(gpuServerCount)*totalMinutes - (totalImpactMinutes - excludedImpactMinutes)
		denominator := int64(gpuServerCount) * totalMinutes
		slaPercentage = float64(numerator) / float64(denominator) * 100

	case "95":
		// 95%算法:
		// 按影响时长排序工单
		sortedTickets := make([]repository.SLATicket, len(tickets))
		copy(sortedTickets, tickets)
		sort.Slice(sortedTickets, func(i, j int) bool {
			return sortedTickets[i].ImpactMinutes > sortedTickets[j].ImpactMinutes
		})

		// 计算需要排除的服务器数量（GPU服务器总数的5%）
		excludeCount = int(math.Ceil(float64(gpuServerCount) * 0.05))

		// 从影响时间最长的工单开始排除，直到排除的工单数量达到excludeCount
		excludedImpactMinutes = 0
		for i := 0; i < len(sortedTickets) && i < excludeCount; i++ {
			excludedImpactMinutes += sortedTickets[i].ImpactMinutes
		}

		numerator := int64(gpuServerCount)*totalMinutes - (totalImpactMinutes - excludedImpactMinutes)
		denominator := int64(gpuServerCount) * totalMinutes
		slaPercentage = float64(numerator) / float64(denominator) * 100

	default:
		return nil, fmt.Errorf("不支持的算法类型: %s", algorithm)
	}

	// 保留两位小数
	slaPercentage = math.Round(slaPercentage*10000) / 10000

	// 封装结果
	result := map[string]interface{}{
		"year":                 year,
		"month":                month,
		"project":              project,
		"algorithm":            algorithm,
		"total_minutes":        totalMinutes,
		"total_impact_minutes": totalImpactMinutes,
		"excluded_minutes":     excludedImpactMinutes,
		"excluded_count":       excludeCount,
		"gpu_server_count":     gpuServerCount,
		"sla_percentage":       slaPercentage,
		"sla_tickets_count":    len(tickets),
		"data_source":          dataSource,
		"period": map[string]string{
			"start": startDate.Format("2006-01-02 15:04:05"),
			"end":   endDate.Format("2006-01-02 15:04:05"),
		},
	}

	return result, nil
}

// CalculateDailySLA 计算每天的SLA数据
func (s *slaService) CalculateDailySLA(ctx context.Context, year, month int, project string) (map[string]interface{}, error) {
	// 获取指定月份的开始和结束日期
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

	// 获取指定月份的GPU服务器数量（优先使用月度统计数据）
	gpuServerCount, err := s.repo.GetProjectGPUServerCountByMonth(ctx, project, year, month)
	if err != nil {
		return nil, fmt.Errorf("获取GPU服务器数量失败: %w", err)
	}

	// 检查是否使用了月度统计数据
	hasMonthlyStats, _ := s.repo.HasMonthlyStats(ctx, year, month)
	dataSource := "real_time"
	if hasMonthlyStats {
		dataSource = "monthly_stats"
	}

	// 如果项目没有GPU服务器，返回空数据
	if gpuServerCount == 0 {
		return map[string]interface{}{
			"year":             year,
			"month":            month,
			"project":          project,
			"gpu_server_count": 0,
			"data_source":      dataSource,
			"daily_data":       map[string]interface{}{},
		}, nil
	}

	// 获取已完成且关闭时间在指定时间范围内的影响SLA的工单
	tickets, err := s.repo.GetSLAImpactTickets(ctx, startDate, endDate, project)
	if err != nil {
		return nil, fmt.Errorf("获取SLA影响工单失败: %w", err)
	}

	// 按日期对工单进行分组
	ticketsByDay := make(map[int][]repository.SLATicket)
	daysInMonth := endDate.Day()

	// 初始化每天的工单列表
	for day := 1; day <= daysInMonth; day++ {
		ticketsByDay[day] = []repository.SLATicket{}
	}

	// 按照关闭日期对工单进行分组
	for _, ticket := range tickets {
		closeDay := 1 // 默认为1号
		if ticket.CloseTime != nil {
			closeDay = ticket.CloseTime.Day()
		}
		ticketsByDay[closeDay] = append(ticketsByDay[closeDay], ticket)
	}

	// 计算当月总分钟数
	totalMinutesInMonth := int64(endDate.Sub(startDate).Minutes()) + 1

	// 计算每天的工单影响总时间（分钟）- 每天新增的影响时间
	dailyNewImpactMinutes := make([]int64, daysInMonth+1) // 索引0不使用，从1开始

	for day := 1; day <= daysInMonth; day++ {
		var impact int64 = 0
		for _, ticket := range ticketsByDay[day] {
			impact += ticket.ImpactMinutes
		}
		dailyNewImpactMinutes[day] = impact
	}

	type AlgorithmData struct {
		SLAPercentage   float64
		ExcludedMinutes int64
		ExcludedCount   int
	}

	dailyData := make(map[string]map[string]AlgorithmData)

	// 准备图表数据
	dates := make([]string, 0, daysInMonth)
	sla100Data := make([]float64, 0, daysInMonth)
	sla90Data := make([]float64, 0, daysInMonth)
	sla95Data := make([]float64, 0, daysInMonth)

	// 累积的工单列表，用于90%和95%算法计算
	var accumulatedTickets []repository.SLATicket

	// 累计的业务影响时间
	var accumulatedImpactMinutes int64 = 0

	// 计算每天的累积影响和SLA
	for day := 1; day <= daysInMonth; day++ {
		dayStr := fmt.Sprintf("%d-%02d-%02d", year, month, day)
		dates = append(dates, dayStr)

		// 添加当天的工单到累积工单列表
		accumulatedTickets = append(accumulatedTickets, ticketsByDay[day]...)

		// 累加当天的新增影响分钟数
		accumulatedImpactMinutes += dailyNewImpactMinutes[day]

		// 计算100%算法的SLA
		// SLA = (服务器数量 * 当月总分钟数 - 累积影响分钟数) / (服务器数量 * 当月总分钟数) * 100%
		denominator := int64(gpuServerCount) * totalMinutesInMonth
		sla100 := float64(denominator-accumulatedImpactMinutes) / float64(denominator) * 100

		// 保证不超过100%
		if sla100 > 100 {
			sla100 = 100
		} else if sla100 < 0 {
			sla100 = 0
		}

		// 计算90%算法的SLA - 排除10%最严重的影响
		excludedCount90 := int(math.Ceil(float64(gpuServerCount) * 0.1))
		var excludedMinutes90 int64 = 0

		// 如果有累积工单，按影响时长排序
		if len(accumulatedTickets) > 0 {
			sortedTickets90 := make([]repository.SLATicket, len(accumulatedTickets))
			copy(sortedTickets90, accumulatedTickets)
			sort.Slice(sortedTickets90, func(i, j int) bool {
				return sortedTickets90[i].ImpactMinutes > sortedTickets90[j].ImpactMinutes
			})

			// 从影响时间最长的工单开始排除，直到排除的工单数量达到excludeCount
			for i := 0; i < len(sortedTickets90) && i < excludedCount90; i++ {
				excludedMinutes90 += sortedTickets90[i].ImpactMinutes
			}
		}

		sla90 := float64(denominator-(accumulatedImpactMinutes-excludedMinutes90)) / float64(denominator) * 100
		if sla90 > 100 {
			sla90 = 100
		} else if sla90 < 0 {
			sla90 = 0
		}

		// 计算95%算法的SLA - 排除5%最严重的影响
		excludedCount95 := int(math.Ceil(float64(gpuServerCount) * 0.05))
		var excludedMinutes95 int64 = 0

		// 如果有累积工单，按影响时长排序
		if len(accumulatedTickets) > 0 {
			sortedTickets95 := make([]repository.SLATicket, len(accumulatedTickets))
			copy(sortedTickets95, accumulatedTickets)
			sort.Slice(sortedTickets95, func(i, j int) bool {
				return sortedTickets95[i].ImpactMinutes > sortedTickets95[j].ImpactMinutes
			})

			// 从影响时间最长的工单开始排除，直到排除的工单数量达到excludeCount
			for i := 0; i < len(sortedTickets95) && i < excludedCount95; i++ {
				excludedMinutes95 += sortedTickets95[i].ImpactMinutes
			}
		}

		sla95 := float64(denominator-(accumulatedImpactMinutes-excludedMinutes95)) / float64(denominator) * 100
		if sla95 > 100 {
			sla95 = 100
		} else if sla95 < 0 {
			sla95 = 0
		}

		// 保留4位小数
		sla100 = math.Round(sla100*10000) / 10000
		sla90 = math.Round(sla90*10000) / 10000
		sla95 = math.Round(sla95*10000) / 10000

		sla100Data = append(sla100Data, sla100)
		sla90Data = append(sla90Data, sla90)
		sla95Data = append(sla95Data, sla95)

		// 存储每天的SLA数据
		dailyData[dayStr] = map[string]AlgorithmData{
			"100": {
				SLAPercentage:   sla100,
				ExcludedMinutes: 0,
				ExcludedCount:   0,
			},
			"90": {
				SLAPercentage:   sla90,
				ExcludedMinutes: excludedMinutes90,
				ExcludedCount:   excludedCount90,
			},
			"95": {
				SLAPercentage:   sla95,
				ExcludedMinutes: excludedMinutes95,
				ExcludedCount:   excludedCount95,
			},
		}
	}

	// 组装结果
	result := map[string]interface{}{
		"year":             year,
		"month":            month,
		"project":          project,
		"gpu_server_count": gpuServerCount,
		"data_source":      dataSource,
		"daily_data":       dailyData,
		"period": map[string]string{
			"start": startDate.Format("2006-01-02 15:04:05"),
			"end":   endDate.Format("2006-01-02 15:04:05"),
		},
		"chart_data": map[string]interface{}{
			"dates":   dates,
			"sla_100": sla100Data,
			"sla_90":  sla90Data,
			"sla_95":  sla95Data,
		},
	}

	return result, nil
}
