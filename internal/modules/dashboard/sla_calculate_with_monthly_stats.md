# SLA计算接口支持月度统计数据

## 功能概述

SLA计算接口现在完全支持使用月度统计数据。当查询特定年月的SLA时，系统会自动检查是否有该年月的月度统计数据，如果有则使用月度统计的GPU服务器数量进行计算，确保历史SLA计算的准确性。

## 接口说明

### 接口地址
```
GET /api/v1/dashboard/sla/calculate
```

### 请求参数
- `year`: 年份（必填）
- `month`: 月份（必填，1-12）
- `project`: 项目名称（必填）
- `algorithm`: 算法类型（必填，如"100"）

## 使用示例

### 1. 查询2025年1月的SLA（使用月度统计数据）

**请求**：
```bash
curl "http://localhost:8080/api/v1/dashboard/sla/calculate?year=2025&month=1&project=cloud17&algorithm=100"
```

**响应示例**：
```json
{
  "code": 200,
  "message": "SLA计算成功",
  "data": {
    "year": 2025,
    "month": 1,
    "project": "cloud17",
    "algorithm": "100",
    "total_minutes": 44640,
    "total_impact_minutes": 1200,
    "excluded_minutes": 0,
    "excluded_count": 0,
    "gpu_server_count": 773,
    "sla_percentage": 99.65,
    "sla_tickets_count": 8,
    "data_source": "monthly_stats",
    "period": {
      "start": "2025-01-01 00:00:00",
      "end": "2025-01-31 23:59:59"
    }
  }
}
```

**关键信息**：
- `gpu_server_count: 773` - 使用月度统计表中的数据
- `data_source: "monthly_stats"` - 标识使用了月度统计数据

### 2. 查询2025年2月的SLA（使用月度统计数据）

**请求**：
```bash
curl "http://localhost:8080/api/v1/dashboard/sla/calculate?year=2025&month=2&project=cloud17&algorithm=100"
```

**响应示例**：
```json
{
  "code": 200,
  "message": "SLA计算成功",
  "data": {
    "year": 2025,
    "month": 2,
    "project": "cloud17",
    "algorithm": "100",
    "total_minutes": 40320,
    "total_impact_minutes": 800,
    "excluded_minutes": 0,
    "excluded_count": 0,
    "gpu_server_count": 778,
    "sla_percentage": 99.74,
    "sla_tickets_count": 5,
    "data_source": "monthly_stats",
    "period": {
      "start": "2025-02-01 00:00:00",
      "end": "2025-02-28 23:59:59"
    }
  }
}
```

**关键信息**：
- `gpu_server_count: 778` - 使用月度统计表中的数据
- `data_source: "monthly_stats"` - 标识使用了月度统计数据

### 3. 查询2025年3月的SLA（使用月度统计数据）

**请求**：
```bash
curl "http://localhost:8080/api/v1/dashboard/sla/calculate?year=2025&month=3&project=cloud17&algorithm=100"
```

**响应示例**：
```json
{
  "code": 200,
  "message": "SLA计算成功",
  "data": {
    "year": 2025,
    "month": 3,
    "project": "cloud17",
    "algorithm": "100",
    "total_minutes": 44640,
    "total_impact_minutes": 1500,
    "excluded_minutes": 0,
    "excluded_count": 0,
    "gpu_server_count": 1022,
    "sla_percentage": 99.67,
    "sla_tickets_count": 10,
    "data_source": "monthly_stats",
    "period": {
      "start": "2025-03-01 00:00:00",
      "end": "2025-03-31 23:59:59"
    }
  }
}
```

**关键信息**：
- `gpu_server_count: 1022` - 使用月度统计表中的数据
- `data_source: "monthly_stats"` - 标识使用了月度统计数据

### 4. 查询2025年4月的SLA（使用实时数据）

**请求**：
```bash
curl "http://localhost:8080/api/v1/dashboard/sla/calculate?year=2025&month=4&project=cloud17&algorithm=100"
```

**响应示例**：
```json
{
  "code": 200,
  "message": "SLA计算成功",
  "data": {
    "year": 2025,
    "month": 4,
    "project": "cloud17",
    "algorithm": "100",
    "total_minutes": 43200,
    "total_impact_minutes": 600,
    "excluded_minutes": 0,
    "excluded_count": 0,
    "gpu_server_count": 1050,
    "sla_percentage": 99.86,
    "sla_tickets_count": 3,
    "data_source": "real_time",
    "period": {
      "start": "2025-04-01 00:00:00",
      "end": "2025-04-30 23:59:59"
    }
  }
}
```

**关键信息**：
- `gpu_server_count: 1050` - 使用当前实时查询的数据
- `data_source: "real_time"` - 标识使用了实时数据

## 数据源判断逻辑

### 1. 检查月度统计数据

系统会首先检查指定年月是否有月度统计数据：

```sql
SELECT COUNT(*) FROM gpu_server_monthly_stats
WHERE year = 2025 AND month = 1;
```

### 2. 数据源选择

- **有月度统计数据** → 使用月度统计表中的GPU服务器数量
- **无月度统计数据** → 使用实时查询的GPU服务器数量

### 3. 响应标识

- `data_source: "monthly_stats"` - 使用了月度统计数据
- `data_source: "real_time"` - 使用了实时数据

## SLA计算公式

### 100%算法
```
SLA = (GPU服务器数量 × 当月总分钟数 - 累积影响分钟数) / (GPU服务器数量 × 当月总分钟数) × 100%
```

### 示例计算（2025年1月）
- GPU服务器数量：773台（来自月度统计）
- 当月总分钟数：44640分钟（31天 × 24小时 × 60分钟）
- 累积影响分钟数：1200分钟
- SLA = (773 × 44640 - 1200) / (773 × 44640) × 100% = 99.65%

## 月度统计数据表

### 表结构
```sql
CREATE TABLE `gpu_server_monthly_stats` (
  `year` int(11) NOT NULL COMMENT '年份',
  `month` int(11) NOT NULL COMMENT '月份',
  `project` varchar(100) NOT NULL COMMENT '项目名称',
  `total_gpu_servers` int(11) NOT NULL DEFAULT 0 COMMENT '总GPU服务器数量',
  `cluster_name` varchar(100) NOT NULL COMMENT '集群名称',
  `cluster_gpu_count` int(11) NOT NULL DEFAULT 0 COMMENT '集群GPU服务器数量'
);
```

### 示例数据
```sql
-- 2025年各月份cloud17项目的GPU服务器数量
INSERT INTO `gpu_server_monthly_stats` VALUES
(2025, 1, 'cloud17', 773, 'GPU_Cluster_G05_RoCE_EMR', 395),
(2025, 1, 'cloud17', 773, 'GPU_Cluster_G05_RoCE_SPR', 378),
(2025, 2, 'cloud17', 778, 'GPU_Cluster_G05_RoCE_EMR', 400),
(2025, 2, 'cloud17', 778, 'GPU_Cluster_G05_RoCE_SPR', 378),
(2025, 3, 'cloud17', 1022, 'GPU_Cluster_G05_RoCE_EMR', 644),
(2025, 3, 'cloud17', 1022, 'GPU_Cluster_G05_RoCE_SPR', 378);
```

## 前端集成

### 1. JavaScript调用示例

```javascript
// 获取指定年月的SLA数据
async function getSLA(project, year, month, algorithm = '100') {
  const params = new URLSearchParams({
    project,
    year: year.toString(),
    month: month.toString(),
    algorithm
  });

  const response = await fetch(`/api/v1/dashboard/sla/calculate?${params}`);
  const result = await response.json();

  if (result.code === 200) {
    const data = result.data;
    console.log(`${year}年${month}月SLA: ${data.sla_percentage}%`);
    console.log(`GPU服务器数量: ${data.gpu_server_count}台`);
    console.log(`数据来源: ${data.data_source === 'monthly_stats' ? '月度统计' : '实时数据'}`);

    return data;
  } else {
    throw new Error(result.message);
  }
}

// 使用示例
getSLA('cloud17', 2025, 1).then(data => {
  // 处理SLA数据
});
```

### 2. 数据源检测

```javascript
// 检测数据来源并显示相应提示
function displaySLAData(slaData) {
  const isHistoricalData = slaData.data_source === 'monthly_stats';

  if (isHistoricalData) {
    console.log(`📊 使用${slaData.year}年${slaData.month}月的历史统计数据`);
    console.log(`🖥️ 当月GPU服务器数量: ${slaData.gpu_server_count}台`);
  } else {
    console.log(`⚡ 使用实时数据计算`);
    console.log(`🖥️ 当前GPU服务器数量: ${slaData.gpu_server_count}台`);
  }

  console.log(`📈 SLA: ${slaData.sla_percentage}%`);
}
```
# 基于现有时间参数的SLA月度统计方案

## 功能概述

通过解析现有的 `start_date` 和 `end_date` 参数，当检测到查询的是完整月份时，自动使用月度统计数据。这样无需修改前端代码，保持API的向后兼容性。

## 工作原理

### 1. 时间范围检测逻辑

系统会自动检测以下条件来判断是否为完整月份查询：

1. **同年同月**：开始日期和结束日期在同一年同一月
2. **月初开始**：开始日期是当月的第1天
3. **月末结束**：结束日期是当月的最后一天

### 2. 数据源选择逻辑

```
检测到完整月份查询
    ↓
检查月度统计表是否有该年月的数据
    ↓
有数据 → 使用月度统计数据
    ↓
无数据 → 使用原有的实时查询逻辑
```

## 使用示例

### 1. 集群统计接口

**您的原始请求**：
```
http://localhost:8080/api/v1/dashboard/resources/cluster-stats-by-project?time_range=custom&group_by=day&project=cloud17&start_date=2025-01-01&end_date=2025-01-31
```

**系统处理逻辑**：
1. 解析 `start_date=2025-01-01` 和 `end_date=2025-01-31`
2. 检测到这是2025年1月的完整月份查询
3. 查询月度统计表是否有2025年1月的数据
4. 如果有，使用月度统计数据；如果没有，使用实时数据

**响应示例**（使用月度统计数据）：
```json
{
  "code": 200,
  "message": "获取项目集群统计信息成功",
  "data": {
    "project_cluster_stats": {
      "cloud17": [
        {
          "cluster": "GPU_Cluster_G05_RoCE_EMR",
          "count": 395
        },
        {
          "cluster": "GPU_Cluster_G05_RoCE_SPR",
          "count": 378
        }
      ]
    },
    "data_source": "monthly_stats",
    "year": 2025,
    "month": 1
  }
}
```

### 2. 服务器统计接口

**请求示例**：
```bash
# 2025年1月完整月份（使用月度统计数据）
curl "http://localhost:8080/api/v1/dashboard/resources/server-stats-by-project?project=cloud17&start_date=2025-01-01&end_date=2025-01-31"

# 2025年2月完整月份（使用月度统计数据）
curl "http://localhost:8080/api/v1/dashboard/resources/server-stats-by-project?project=cloud17&start_date=2025-02-01&end_date=2025-02-28"

# 2025年3月完整月份（使用月度统计数据）
curl "http://localhost:8080/api/v1/dashboard/resources/server-stats-by-project?project=cloud17&start_date=2025-03-01&end_date=2025-03-31"

# 部分月份查询（使用实时数据）
curl "http://localhost:8080/api/v1/dashboard/resources/server-stats-by-project?project=cloud17&start_date=2025-01-15&end_date=2025-01-31"

# 跨月查询（使用实时数据）
curl "http://localhost:8080/api/v1/dashboard/resources/server-stats-by-project?project=cloud17&start_date=2025-01-15&end_date=2025-02-15"
```

### 3. SLA计算接口

SLA计算接口也会受益于这个改进，当查询特定月份时会使用正确的GPU服务器数量：

```bash
# 2025年1月SLA计算（使用773台GPU服务器）
curl "http://localhost:8080/api/v1/dashboard/sla/calculate?year=2025&month=1&project=cloud17&algorithm=100"

# 2025年2月SLA计算（使用778台GPU服务器）
curl "http://localhost:8080/api/v1/dashboard/sla/calculate?year=2025&month=2&project=cloud17&algorithm=100"

# 2025年3月SLA计算（使用1022台GPU服务器）
curl "http://localhost:8080/api/v1/dashboard/sla/calculate?year=2025&month=3&project=cloud17&algorithm=100"
```

## 时间范围检测示例

### 1. 会触发月度统计的查询

```bash
# ✅ 2025年1月完整月份
start_date=2025-01-01&end_date=2025-01-31

# ✅ 2025年2月完整月份（平年）
start_date=2025-02-01&end_date=2025-02-28

# ✅ 2025年3月完整月份
start_date=2025-03-01&end_date=2025-03-31

# ✅ 2024年2月完整月份（闰年）
start_date=2024-02-01&end_date=2024-02-29
```

### 2. 不会触发月度统计的查询

```bash
# ❌ 不是从月初开始
start_date=2025-01-02&end_date=2025-01-31

# ❌ 不是到月末结束
start_date=2025-01-01&end_date=2025-01-30

# ❌ 跨月查询
start_date=2025-01-15&end_date=2025-02-15

# ❌ 跨年查询
start_date=2024-12-01&end_date=2025-01-31
```

## 数据库表结构

```sql
CREATE TABLE `gpu_server_monthly_stats` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `year` int(11) NOT NULL COMMENT '年份',
  `month` int(11) NOT NULL COMMENT '月份',
  `project` varchar(100) NOT NULL COMMENT '项目名称',
  `total_gpu_servers` int(11) NOT NULL DEFAULT 0 COMMENT '总GPU服务器数量',
  `cluster_name` varchar(100) NOT NULL COMMENT '集群名称',
  `cluster_gpu_count` int(11) NOT NULL DEFAULT 0 COMMENT '集群GPU服务器数量',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_year_month_project_cluster` (`year`,`month`,`project`,`cluster_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='GPU服务器月度统计表';
```

## 示例数据

```sql
-- 插入2025年1月cloud17项目的数据
INSERT INTO `gpu_server_monthly_stats` (`year`, `month`, `project`, `total_gpu_servers`, `cluster_name`, `cluster_gpu_count`) VALUES
(2025, 1, 'cloud17', 773, 'GPU_Cluster_G05_RoCE_EMR', 395),
(2025, 1, 'cloud17', 773, 'GPU_Cluster_G05_RoCE_SPR', 378);

-- 插入2025年2月cloud17项目的数据
INSERT INTO `gpu_server_monthly_stats` (`year`, `month`, `project`, `total_gpu_servers`, `cluster_name`, `cluster_gpu_count`) VALUES
(2025, 2, 'cloud17', 778, 'GPU_Cluster_G05_RoCE_EMR', 400),
(2025, 2, 'cloud17', 778, 'GPU_Cluster_G05_RoCE_SPR', 378);

-- 插入2025年3月cloud17项目的数据
INSERT INTO `gpu_server_monthly_stats` (`year`, `month`, `project`, `total_gpu_servers`, `cluster_name`, `cluster_gpu_count`) VALUES
(2025, 3, 'cloud17', 1022, 'GPU_Cluster_G05_RoCE_EMR', 644),
(2025, 3, 'cloud17', 1022, 'GPU_Cluster_G05_RoCE_SPR', 378);
```

## 响应数据格式

### 1. 使用月度统计数据时

响应中会包含 `data_source: "monthly_stats"` 标识：

```json
{
  "code": 200,
  "message": "获取项目集群统计信息成功",
  "data": {
    "project_cluster_stats": {
      "cloud17": [
        {
          "cluster": "GPU_Cluster_G05_RoCE_EMR",
          "count": 395
        },
        {
          "cluster": "GPU_Cluster_G05_RoCE_SPR",
          "count": 378
        }
      ]
    },
    "data_source": "monthly_stats",
    "year": 2025,
    "month": 1
  }
}
```

### 2. 使用实时数据时

响应中不会包含 `data_source` 字段，或者值为其他：

```json
{
  "code": 200,
  "message": "获取项目集群统计信息成功",
  "data": {
    "project_cluster_stats": {
      "cloud17": [
        {
          "cluster": "GPU_Cluster_G05_RoCE_EMR",
          "count": 420
        },
        {
          "cluster": "GPU_Cluster_G05_RoCE_SPR",
          "count": 380
        }
      ]
    }
  }
}
```

## 前端兼容性

### 1. 无需修改现有代码

前端代码无需任何修改，继续使用现有的参数格式：

```javascript
// 现有的前端代码继续有效
const params = new URLSearchParams({
  time_range: 'custom',
  group_by: 'day',
  project: 'cloud17',
  start_date: '2025-01-01',
  end_date: '2025-01-31'
});

fetch(`/api/v1/dashboard/resources/cluster-stats-by-project?${params}`)
  .then(response => response.json())
  .then(data => {
    // 数据会自动使用月度统计（如果有的话）
    console.log('数据来源:', data.data.data_source || '实时数据');
  });
```

### 2. 可选的数据源检测

前端可以通过检测响应中的 `data_source` 字段来了解数据来源：

```javascript
fetch('/api/v1/dashboard/resources/cluster-stats-by-project?...')
  .then(response => response.json())
  .then(data => {
    if (data.data.data_source === 'monthly_stats') {
      console.log(`使用${data.data.year}年${data.data.month}月的月度统计数据`);
    } else {
      console.log('使用实时数据');
    }
  });
```
