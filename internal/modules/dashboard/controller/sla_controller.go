package controller

import (
	"backend/internal/modules/dashboard/service"
	"backend/response"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// SLAController SLA控制器
type SLAController struct {
	service service.SLAService
}

// NewSLAController 创建SLA控制器
func NewSLAController(service service.SLAService) *SLAController {
	return &SLAController{service: service}
}

// RegisterRoutes 注册路由
func (c *SLAController) RegisterRoutes(router *gin.RouterGroup) {
	slaRouter := router.Group("/sla")
	{
		// 计算SLA
		slaRouter.GET("/calculate", c.CalculateSLA)
		// 计算每日SLA
		slaRouter.GET("/daily", c.CalculateDailySLA)
	}
}

// CalculateSLA 计算SLA
// @Summary 计算SLA
// @Description 根据不同算法计算SLA
// @Tags 看板-SLA
// @Accept json
// @Produce json
// @Param year query int false "年份，默认当前年"
// @Param month query int false "月份，默认当前月"
// @Param algorithm query string false "算法类型(100/90/95)，默认100"
// @Param project query string false "项目名称，为空时计算所有项目"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/sla/calculate [get]
func (c *SLAController) CalculateSLA(ctx *gin.Context) {
	// 获取查询参数
	var year, month int
	var err error
	yearStr := ctx.DefaultQuery("year", "0")
	monthStr := ctx.DefaultQuery("month", "0")
	algorithm := ctx.DefaultQuery("algorithm", "100")
	project := ctx.Query("project")

	// 解析年月参数
	if yearStr != "0" {
		year, err = strconv.Atoi(yearStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "年份参数格式错误")
			return
		}
	} else {
		// 默认使用当前年份
		year = time.Now().Year()
	}

	if monthStr != "0" {
		month, err = strconv.Atoi(monthStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "月份参数格式错误")
			return
		}
	} else {
		// 默认使用当前月份
		month = int(time.Now().Month())
	}

	// 调用服务计算SLA
	result, err := c.service.CalculateProjectSLA(ctx, year, month, algorithm, project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "计算SLA失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "计算SLA成功")
}

// CalculateDailySLA 计算每日SLA数据
// @Summary 计算每日SLA数据
// @Description 计算指定月份内每天三种算法(100/90/95)的SLA数据，用于前端图表展示
// @Tags 看板-SLA
// @Accept json
// @Produce json
// @Param year query int false "年份，默认当前年"
// @Param month query int false "月份，默认当前月"
// @Param project query string false "项目名称，为空时计算所有项目"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/sla/daily [get]
func (c *SLAController) CalculateDailySLA(ctx *gin.Context) {
	// 获取查询参数
	var year, month int
	var err error
	yearStr := ctx.DefaultQuery("year", "0")
	monthStr := ctx.DefaultQuery("month", "0")
	project := ctx.Query("project")

	// 解析年月参数
	if yearStr != "0" {
		year, err = strconv.Atoi(yearStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "年份参数格式错误")
			return
		}
	} else {
		// 默认使用当前年份
		year = time.Now().Year()
	}

	if monthStr != "0" {
		month, err = strconv.Atoi(monthStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "月份参数格式错误")
			return
		}
	} else {
		// 默认使用当前月份
		month = int(time.Now().Month())
	}

	// 调用服务计算每日SLA数据
	result, err := c.service.CalculateDailySLA(ctx, year, month, project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "计算每日SLA数据失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "计算每日SLA数据成功")
}
