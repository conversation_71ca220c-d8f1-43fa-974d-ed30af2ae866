package common

type OrderStatus string

const (
	DefaultPage     = 1
	DefaultPageSize = 20

	Source = "硬件运维系统"
)

// 验收工单状态
const (
	OrderStatus_Pending     OrderStatus = "PENDING"     // 待审核
	OrderStatus_Approved    OrderStatus = "APPROVED"    // 已审核
	OrderStatus_Rejected    OrderStatus = "REJECTED"    // 已驳回
	OrderStatus_In_Progress OrderStatus = "IN_PROGRESS" // 线下验收数据进行中
	OrderStatus_Collected   OrderStatus = "COLLECTED"   // 线下结果已采集
	OrderStatus_Completed   OrderStatus = "COMPLETED"   // 验收完结
	OrderStatus_Canceled    OrderStatus = "CANCELLED"   // 已取消
)

// 活动类别，用于history记录
const (
	History_Activity_Create   = "Create"   // 创建工单
	History_Activity_Cancel   = "Cancel"   // 取消工单
	History_Activity_Approve  = "Approve"  // 审批通过
	History_Activity_Reject   = "Reject"   // 审批驳回
	History_Activity_Handle   = "Handle"   // 处理信息
	History_Activity_Collect  = "Collect"  // 收集信息
	History_Activity_Complete = "Complete" // 完结工单
)

// 用于生成工单ID
const (
	AcceptanceOrderNoPrefix = "ao"
	RackingTicketNoPrefix   = "rt"
)

// 上传文件相关
const (
	AcceptanceOrderFileModuleType    = "acceptance_orders"
	AcceptanceOrderUploadDescription = "Moss抓取硬件配置信息"
)

var OrderStatusTransferMap = map[OrderStatus][]OrderStatus{
	OrderStatus_Pending:     {OrderStatus_Approved, OrderStatus_Rejected, OrderStatus_Canceled},
	OrderStatus_Approved:    {OrderStatus_In_Progress, OrderStatus_Collected},
	OrderStatus_In_Progress: {OrderStatus_Collected},
	OrderStatus_Collected:   {OrderStatus_Completed},
}

var RackingTicketStatusTransferMap = map[OrderStatus][]OrderStatus{
	OrderStatus_Pending:     {OrderStatus_Approved, OrderStatus_Rejected},
	OrderStatus_Approved:    {OrderStatus_In_Progress},
	OrderStatus_In_Progress: {OrderStatus_Completed},
}

// 表示非法的sn
var (
	InvalidSNMap = map[string]bool{
		"not specified": true,
		"no specified":  true,
		"null":          true,
		"undefined":     true,
		"unknown":       true,
	}
	InvalidSNStrPrefix = "Not_Specified_"
)
