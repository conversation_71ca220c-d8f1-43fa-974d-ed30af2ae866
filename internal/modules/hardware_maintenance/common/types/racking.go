package types

import (
	"backend/internal/modules/hardware_maintenance/common"
	"time"
)

// RackingOrderFilter 上架工单filter
type RackingTicketFilter struct {
	IDs       []uint              `form:"ids[]"`                                      // 支持通过数组批量查，例如 ?ids[]=1&ids[]=2
	TicketNo  *string             `form:"ticketNo"`                                   // 精确查
	Title     *string             `form:"title"`                                      // 模糊查建议用 like
	Status    *common.OrderStatus `form:"status"`                                     // 枚举
	Applicant *string             `form:"Applicant"`                                  // 精确查
	StartTime *time.Time          `form:"startTime"`                                  // 创建时间范围起
	EndTime   *time.Time          `form:"endTime"`                                    // 创建时间范围止
	Page      int                 `form:"page" binding:"omitempty,min=1"`             // 分页页码
	PageSize  int                 `form:"pageSize" binding:"omitempty,min=1,max=100"` // 分页大小
}

type CreateRackingTicketRequest struct {
	Title           string     `json:"title"`           // 标题
	PlannedRackTime *time.Time `json:"plannedRackTime"` // 预计上架时间，若前端传字符串需注意时间格式解析
	Project         string     `json:"project"`         // 项目区域ID
	RackItems       []RackItem `json:"rackItems"`       // 上架内容，数组形式包含多行设备信息
	Remark          string     `json:"remark"`          // 备注
}

// RackItem 上架内容中的单行设备信息结构体
type RackItem struct {
	// 创建时填入信息
	AssetID uint   `json:"assetID"`
	Status  string `json:"status"` // 状态，对应前端的 已入库 等
	RoomID  uint   `json:"roomID"` // 包间id

	// 验证后补充信息
	CabinetID    uint   `json:"cabinetID"` // 机柜id
	VpcMAC       string `json:"vpcMAC"`
	BmcMAC       string `json:"bmcMAC"`
	RackPosition int    `json:"rackPosition"` // U位统一改成机架位
}

// TransitionRackingTicketRequest 变更状态
type TransitionRackingTicketRequest struct {
	Status    string     `json:"status"` // APPROVED, REJECTED ,COMPLETED
	RackItems []RackItem `json:"rackItems"`
	Comment   string     `json:"comment"`
}
