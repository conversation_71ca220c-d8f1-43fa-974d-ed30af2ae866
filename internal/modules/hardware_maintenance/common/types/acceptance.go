package types

import (
	"backend/internal/modules/hardware_maintenance/common"
	"time"
)

// CreateAcceptanceOrderRequest 创建验收审批单请求
type CreateAcceptanceOrderRequest struct {
	Title       string            `json:"title"`
	Description string            `json:"description"`
	Items       []*AcceptanceItem `json:"items"`
}

// CreateAcceptanceOrderInvalidResponse 创建验收审批单响应 创建成功，则返回无该内容
type CreateAcceptanceOrderInvalidResponse struct {
	InvalidSNs           []string `json:"invalid_sns"`
	InvalidTemplateNames []string `json:"invalid_template_names"`
	RepeatedSns          []string `json:"repeated_sns"`
}

// AcceptanceOrderFilter 验收工单条件filter
type AcceptanceOrderFilter struct {
	IDs       []uint              `form:"ids[]"`                                      // 支持通过数组批量查，例如 ?ids[]=1&ids[]=2
	OrderNo   *string             `form:"order_no"`                                   // 精确查
	Title     *string             `form:"title"`                                      // 模糊查建议用 like
	Status    *common.OrderStatus `form:"status"`                                     // 枚举
	Initiator *string             `form:"initiator"`                                  // 精确查
	Approver  *string             `form:"approver"`                                   // 精确查
	Handler   *string             `form:"handler"`                                    // 精确查
	StartTime *time.Time          `form:"start_time"`                                 // 创建时间范围起
	EndTime   *time.Time          `form:"end_time"`                                   // 创建时间范围止
	Page      int                 `form:"page" binding:"omitempty,min=1"`             // 分页页码
	PageSize  int                 `form:"pageSize" binding:"omitempty,min=1,max=100"` // 分页大小
}

// AcceptanceItem 验收项(设备级)
type AcceptanceItem struct {
	DeviceSN     string `json:"device_sn"`
	DeviceID     uint   `json:"device_id"`
	Vendor       string `json:"vendor"`
	Model        string `json:"model"`
	TemplateName string `json:"template_name"`
	IsPass       bool   `json:"is_pass"` // 1 表示验收通过，2表示验收未通过
	Reason       string `json:"reason"`
}

// AcceptanceItemComponent 验收项(组件级别)
type AcceptanceItemComponent struct {
	PN              string `json:"pn"`
	SN              string `json:"sn"`
	Vendor          string `json:"vendor"`
	Status          string `json:"status"`
	FirmwareVersion string `json:"firmware_version"`
}

// AuditAcceptanceOrderRequest 审核工单请求
type AuditAcceptanceOrderRequest struct {
	OrderID      uint   `json:"order_id"`
	IsApproved   bool   `json:"is_approved"` // 是否审批通过
	Comment      string `json:"comment"`     // 评论
	OperatorID   uint   `json:"-"`
	OperatorName string `json:"-"`
}

// CompleteAcceptanceOrderRequest 完结验收审批单请求
type CompleteAcceptanceOrderRequest struct {
	OrderID      string            `json:"order_id"`
	Comment      string            `json:"comment"`
	Items        []*AcceptanceItem `json:"items"`
	OperatorID   uint              `json:"-"`
	OperatorName string            `json:"-"`
}

// CompleteAcceptanceOrderResponse 完结验收审批回应
type CompleteAcceptanceOrderInValidResponse struct {
	InvalidDeviceSNs      []string            `json:"invalid_device_sns"`       // 检测出的设备结果SN不存在，但是通过的非法设备SNs
	NoExistingPNInfos     []*NoExistingPNInfo `json:"no_existing_pn_info"`      // 硬件检测结果中的不存在cmdb的PN
	DbExistingComponentSN []string            `json:"db_existing_component_sn"` // 通过的，但是设备SN已存在于cmdb中
}

type NoExistingPNInfo struct {
	PN            string `json:"pn"` // 为空，则表示这个组件的PN需要填入。不为空，则表示这个组件的PN的组件信息需要手动录入cmdb
	ComponentType string `json:"component_type"`
	Model         string `json:"model"`
	Description   string `json:"description"`
}

// CancelAcceptanceOrderRequest 取消工单请求
type CancelAcceptanceOrderRequest struct {
	OrderID    uint   `json:"order_id"`
	OperatorID uint   `json:"operator_id"`
	Comment    string `json:"comment"`
}

// AssignAcceptanceOrderRequest 分配验收工单请求
type AssignAcceptanceOrderRequest struct {
	OrderID    uint   `json:"order_id"`
	OperatorID uint   `json:"operator_id"`
	AssignedID uint   `json:"assignedID"`
	Comment    string `json:"comment"`
}

// UpdateInspectingComponentInfo 只允许部分字段更新

type UpdateInspectingComponentInfoReq struct {
	OrderID        uint                       `json:"order_id"`
	ItemID         uint                       `json:"item_id"`
	ComponentInfos []*InspectingComponentInfo `json:"component_infos"`
}

type InspectingComponentInfo struct {
	ItemId      uint   `json:"item_id"`
	PN          string `json:"pn"`
	SN          string `json:"sn"`
	Model       string `json:"model"`
	ComponentID uint   `json:"component_id"`
	Status      string `json:"status"`
	Description string `json:"description"`
}

// ComponentModelPNMappingFilter 设备型号到PN映射查询过滤器
type ComponentModelPNMappingFilter struct {
	IDs           []uint  `form:"ids[]"`                                      // 支持通过数组批量查询
	Model         *string `form:"model"`                                      // 型号模糊查询
	ComponentType *string `form:"component_type"`                             // 组件类型
	PN            *string `form:"pn"`                                         // PN号模糊查询
	Status        *string `form:"status"`                                     // 状态
	Page          int     `form:"page" binding:"omitempty,min=1"`             // 分页页码
	PageSize      int     `form:"pageSize" binding:"omitempty,min=1,max=100"` // 分页大小
}

// CreateComponentModelPNMappingRequest 创建设备型号到PN映射请求
type CreateComponentModelPNMappingRequest struct {
	Model         string `json:"model" binding:"required"`          // 设备型号
	ComponentType string `json:"component_type" binding:"required"` // 组件类型
	PN            string `json:"pn" binding:"required"`             // 部件号
	Description   string `json:"description"`                       // 描述
	Status        string `json:"status"`                            // 状态
}

// UpdateComponentModelPNMappingRequest 更新设备型号到PN映射请求
type UpdateComponentModelPNMappingRequest struct {
	Model         *string `json:"model"`          // 设备型号
	ComponentType *string `json:"component_type"` // 组件类型
	PN            *string `json:"pn"`             // 部件号
	Description   *string `json:"description"`    // 描述
	Status        *string `json:"status"`         // 状态
}

// ComponentModelPNMappingResponse 设备型号到PN映射响应
type ComponentModelPNMappingResponse struct {
	ID            uint   `json:"id"`
	Model         string `json:"model"`
	ComponentType string `json:"component_type"`
	PN            string `json:"pn"`
	Description   string `json:"description"`
	Status        string `json:"status"`
	CreatedAt     string `json:"created_at"`
	UpdatedAt     string `json:"updated_at"`
}

// FillComponentPNRequest //根据数据库中映射关系，修改列表
type FillComponentPNRequest struct {
	ItemIDs           []uint `json:"item_ids"`
	OverWriteExisting bool   `json:"over_write_existing"`
}
