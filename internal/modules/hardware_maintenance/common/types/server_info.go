package types

// 从Moss到出json解析的数据

type ServerInfoRaw struct {
	ID          string          `json:"_id"`
	Bios        BiosInfo        `json:"bios"`
	Bmc         BmcInfo         `json:"bmc"`
	CPU         CpuInfo         `json:"cpu"`
	Disk        DiskInfo        `json:"disk"`
	GPU         GpuInfo         `json:"gpu"`
	Memory      MemoryInfo      `json:"memory"`
	Motherboard MotherboardInfo `json:"motherboard"`
	Nic         <PERSON>c<PERSON>nfo         `json:"nic"`
	Other       OtherInfo       `json:"other"`
	Pcie        PcieInfo        `json:"pcie"`
}

type ServerInfo struct {
	ID          string
	Bios        BiosInfo
	Bmc         BmcInfo
	CPU         CpuInfo
	Disk        DiskInfo
	GPU         GpuInfo
	Memory      MemoryInfo
	Motherboard MotherboardInfo
	Nic         NicInfo
	Other       OtherInfo
	Pcie        PcieInfo
}

type BiosInfo struct {
	FirmwareVersion      string `json:"firmware_version"`
	HyperthreadingStatus string `json:"hyperthreading_status"`
	LlcPrefetchStatus    string `json:"llc_prefetch_status"`
	MonitorMwaitStatus   string `json:"monitor_mwait_status"`
	NumaStatus           string `json:"numa_status"`
	PstateStatus         string `json:"pstate_status"`
	PxeIPv4Status        string `json:"pxe_ipv4_status"`
	PxeIPv6Status        string `json:"pxe_ipv6_status"`
	SncNuma              string `json:"snc_numa"`
	SrIovStatus          string `json:"sr_iov_status"`
	TurboStatus          string `json:"turbo_status"`
	VmdStatus            string `json:"vmd_status"`
	VmxStatus            string `json:"vmx_status"`
}

type BmcInfo struct {
	FruDetail       FruDetailInfo `json:"fru_detail"`
	LanDetail       LanDetailInfo `json:"lan_detail"`
	FirmwareVersion string        `json:"firmware_version"`
	Users           []BmcUserInfo `json:"users"`
}

type FruDetailInfo struct {
	BoardMfg            string `json:"Board Mfg"`
	BoardProduct        string `json:"Board Product"`
	BoardSN             string `json:"Board SN"`
	BoardPN             string `json:"Board PN"`
	ProductPN           string `json:"Product PN"`
	ProductVersion      string `json:"Product Version"`
	ChassisType         string `json:"Chassis Type"`
	ChassisPN           string `json:"Chassis PN"`
	ProductManufacturer string `json:"Product Manufacturer"`
	ProductName         string `json:"Product Name"`
	ProductSN           string `json:"Product SN"`
	ProductAssetTag     string `json:"Product Asset Tag"`
	ChassisSN           string `json:"Chassis SN"`
	BoardMfgDate        string `json:"Board Mfg Date"`
}

type LanDetailInfo struct {
	MacAddress     string `json:"mac_address"`
	IPv4Address    string `json:"ipv4_address"`
	IPv4DhcpStatus string `json:"ipv4_dhcp_status"`
	IPv6DhcpStatus string `json:"ipv6_dhcp_status"`
}

type BmcUserInfo struct {
	Privilege string `json:"privilege"`
	UserID    string `json:"user_id"`
	Username  string `json:"username"`
}

type CpuInfo struct {
	Overview CpuOverview `json:"overview"`
	Detail   []CpuDetail `json:"detail"`
}

type CpuOverview struct {
	TotalCounts string   `json:"total_counts"`
	Types       []string `json:"types"`
}

type CpuDetail struct {
	ModelName string `json:"model_name"`
	SN        string `json:"SN"`
}

type DiskInfo struct {
	Overview DiskOverview `json:"overview"`
	Detail   []DiskDetail `json:"detail"`
}

type DiskOverview struct {
	TotalCounts string   `json:"total_counts"`
	Types       []string `json:"types"`
}

type DiskDetail struct {
	Type            string `json:"type"`
	Size            string `json:"size"`
	SN              string `json:"SN"`
	PN              string `json:"PN"`
	FirmwareVersion string `json:"firmware_version"`
	Name            string `json:"name"`
}

type GpuInfo struct {
	Overview GpuOverview `json:"overview"`
	Detail   []GpuDetail `json:"detail"`
}

type GpuOverview struct {
	TotalCounts string   `json:"total_counts"`
	Types       []string `json:"types"`
}

type GpuDetail struct {
	Name         string `json:"name"`
	VbiosVersion string `json:"vbios_version"`
	Serial       string `json:"serial"`
}

type MemoryInfo struct {
	Overview MemoryOverview `json:"overview"`
	Detail   []MemoryDetail `json:"detail"`
}

type MemoryOverview struct {
	TotalSize string   `json:"total_size"`
	Types     []string `json:"types"`
}

type MemoryDetail struct {
	PN           string `json:"PN"`
	Manufacturer string `json:"manufacturer"`
	Size         string `json:"size"`
	DdrType      string `json:"ddr_type"`
	Frequency    string `json:"frequency"`
	SN           string `json:"SN"`
}

type MotherboardInfo struct {
	Manufacturer string `json:"manufacturer"`
	SN           string `json:"SN"`
}

type NicInfo struct {
	Overview NicOverview `json:"overview"`
	VpcNic   []NicDetail `json:"vpc_nic"`
	RdmaNic  []NicDetail `json:"rdma_nic"`
}

type NicOverview struct {
	TotalCounts string   `json:"total_counts"`
	VpcTypes    []string `json:"vpc_types"`
	RdmaTypes   []string `json:"rdma_types"`
}

type NicDetail struct {
	DeviceName      string `json:"device_name"`
	MacAddress      string `json:"mac_address"`
	SN              string `json:"SN"`
	BusID           string `json:"bus_id"`
	Speed           string `json:"speed"`
	Model           string `json:"model"`
	FirmwareVersion string `json:"firmware_version"`
	LinkLayer       string `json:"link_layer"`
	PN              string `json:"PN"`
	Rev             string `json:"rev"`
}

type OtherInfo struct {
	Datetime string `json:"datetime"`
	VpcIP    string `json:"vpc_ip"`
	VpcMac   string `json:"vpc_mac"`
}

type PcieInfo struct {
	CpldVersion     string `json:"cpld_version"`
	FirmwareVersion string `json:"firmware_version"`
}
