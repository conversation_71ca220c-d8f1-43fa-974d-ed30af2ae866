package hardware_maintenance

import (
	"backend/internal/middleware"
	asset2 "backend/internal/modules/cmdb/repository/asset"
	"backend/internal/modules/cmdb/repository/component"
	product_Repo "backend/internal/modules/cmdb/repository/product"
	"backend/internal/modules/cmdb/service/asset"
	"backend/internal/modules/cmdb/service/product"
	fileService "backend/internal/modules/file/service"
	acceptance_ctl "backend/internal/modules/hardware_maintenance/controller/acceptance"
	"backend/internal/modules/hardware_maintenance/model/acceptance"
	"backend/internal/modules/hardware_maintenance/model/racking"

	"go.temporal.io/sdk/client"
	"go.uber.org/zap"

	racking_ctl "backend/internal/modules/hardware_maintenance/controller/racking"
	acceptance_repo "backend/internal/modules/hardware_maintenance/repository/acceptance"
	rack_repo "backend/internal/modules/hardware_maintenance/repository/racking"
	acceptance_serv "backend/internal/modules/hardware_maintenance/service/acceptance"
	racking_serv "backend/internal/modules/hardware_maintenance/service/racking"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Module 硬件运维模块
type Module struct {
	db     *gorm.DB
	logger *zap.Logger
	// 服务
	//acceptanceService acceptance_serv.AcceptanceService
	//rackingService    racking_serv.RackingService
	// 控制器
	acceptanceController *acceptance_ctl.AcceptanceController
	rackingController    *racking_ctl.RackingController

	temporalClient client.Client
}

// NewModule 创建硬件运维模块
func NewModule(db *gorm.DB, logger *zap.Logger, temporalClient client.Client) *Module {
	return &Module{
		db:             db,
		logger:         logger,
		temporalClient: temporalClient,
	}
}

// Initialize 初始化模块
func (m *Module) Initialize() error {
	// 初始化仓库
	orderRepo := acceptance_repo.NewAcceptanceOrderRepository(m.db)
	itemRepo := acceptance_repo.NewAcceptanceItemRepository(m.db)
	rackingRepo := rack_repo.NewAssetRackingTicketRepository(m.db)
	productRepo := product_Repo.NewProductRepository(m.db)
	deviceRepo := asset2.NewDeviceRepository(m.db)
	componentRepo := component.NewServerComponentRepository(m.db)
	resourceRepo := asset2.NewResourceRepository(m.db)
	statusChangeRepo := asset2.NewStatusChangeRepository(m.db)
	acceptedComponentRepo := acceptance_repo.NewAcceptedComponentRepository(m.db)
	deviceModelPNMappingRepo := acceptance_repo.NewComponentModelPNMappingRepository(m.db)
	componentChangeLogRepo := component.NewComponentChangeLogRepository(m.db)
	// 初始化服务
	acceptanceService := acceptance_serv.NewAcceptanceService(orderRepo, itemRepo, acceptedComponentRepo, deviceModelPNMappingRepo, componentRepo, componentChangeLogRepo, deviceRepo, resourceRepo, statusChangeRepo, m.logger, m.temporalClient)
	rackingService := racking_serv.NewRackingService(rackingRepo, resourceRepo, deviceRepo, statusChangeRepo, m.logger, m.temporalClient)
	productService := product.NewProductService(productRepo)
	fileSvc := fileService.NewFileService(m.db)
	deviceService := asset.NewDeviceService(deviceRepo)
	// 初始化控制器
	m.acceptanceController = acceptance_ctl.NewAcceptanceController(acceptanceService, fileSvc, productService, deviceService, m.logger)
	m.rackingController = racking_ctl.NewRackingController(rackingService, deviceService)

	return nil
}

// RegisterHardwareMaintenanceModule 注册硬件运维模块
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	apiGroup := router.Group("/hardware_maintenance")
	apiGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件

	m.acceptanceController.RegisterRoutes(apiGroup)
	m.rackingController.RegisterRoutes(apiGroup)
}

// AutoMigrate 自动迁移数据库
func (m *Module) AutoMigrate() error {
	// 现在重新创建表
	return m.db.AutoMigrate(
		&acceptance.AcceptanceOrder{},
		&acceptance.AcceptanceItem{},
		&acceptance.AcceptanceOrderHistory{},
		&acceptance.InspectingComponentInfo{},
		&acceptance.ComponentModelPNMapping{},
		&racking.AssetRackingTicket{},
		&racking.AssetRackingItem{},
		&racking.AssetRackingTicketHistory{},
	)
}
