package acceptance

import (
	"backend/internal/modules/cmdb/model/asset"
	productModel "backend/internal/modules/cmdb/model/product"
	"backend/internal/modules/cmdb/model/template"
	"backend/internal/modules/hardware_maintenance/model/acceptance"
	"context"

	"gorm.io/gorm"
)

type AcceptanceItemRepository interface {
	// 基本操作
	Create(ctx context.Context, item *acceptance.AcceptanceItem) error
	Update(ctx context.Context, item *acceptance.AcceptanceItem) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*acceptance.AcceptanceItem, error)
	GetByDeviceSN(ctx context.Context, sn string) ([]*acceptance.AcceptanceItem, error)
	ListByOrderID(ctx context.Context, orderID uint) ([]*acceptance.AcceptanceItem, error)
	// 批量操作
	BatchCreate(ctx context.Context, items []*acceptance.AcceptanceItem) error
	DeleteByOrderID(ctx context.Context, orderID uint) error

	// FilterExistingSNs 检查设备SN,返回不存在于数据库的sns
	FilterExistingSNs(ctx context.Context, sns []string) ([]string, error)

	// 检查套餐模板，返回不存在于数据库的sns
	FilterExistingTemplates(ctx context.Context, templates []string) ([]string, error)
	// DB 访问
	GetDB() *gorm.DB
	WithTx(tx *gorm.DB) AcceptanceItemRepository

	// 访问操作product表
	FilterExistingPNs(ctx context.Context, pns []string) ([]string, error)
}

type acceptanceItemRepo struct {
	db *gorm.DB
}

func NewAcceptanceItemRepository(db *gorm.DB) AcceptanceItemRepository {
	return &acceptanceItemRepo{db: db}
}

func (r *acceptanceItemRepo) Create(ctx context.Context, item *acceptance.AcceptanceItem) error {
	return r.db.WithContext(ctx).Create(item).Error
}

func (r *acceptanceItemRepo) Update(ctx context.Context, item *acceptance.AcceptanceItem) error {
	return r.db.WithContext(ctx).Save(item).Error
}

func (r *acceptanceItemRepo) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&acceptance.AcceptanceItem{}, id).Error
}

func (r *acceptanceItemRepo) GetByID(ctx context.Context, id uint) (*acceptance.AcceptanceItem, error) {
	var item acceptance.AcceptanceItem
	err := r.db.WithContext(ctx).First(&item, id).Error
	return &item, err
}

func (r *acceptanceItemRepo) GetByDeviceSN(ctx context.Context, sn string) ([]*acceptance.AcceptanceItem, error) {
	var items []*acceptance.AcceptanceItem
	err := r.db.WithContext(ctx).
		Where("device_sn = ?", sn).
		Find(&items).Error
	return items, err
}

func (r *acceptanceItemRepo) ListByOrderID(ctx context.Context, orderID uint) ([]*acceptance.AcceptanceItem, error) {
	var items []*acceptance.AcceptanceItem
	err := r.db.WithContext(ctx).
		Where("order_id = ?", orderID).
		Order("created_at ASC").
		Find(&items).Error
	return items, err
}

func (r *acceptanceItemRepo) BatchCreate(ctx context.Context, items []*acceptance.AcceptanceItem) error {
	return r.db.WithContext(ctx).Create(&items).Error
}

func (r *acceptanceItemRepo) DeleteByOrderID(ctx context.Context, orderID uint) error {
	return r.db.WithContext(ctx).
		Where("order_id = ?", orderID).
		Delete(&acceptance.AcceptanceItem{}).Error
}

func (r *acceptanceItemRepo) GetDB() *gorm.DB {
	return r.db
}

func (r *acceptanceItemRepo) WithTx(tx *gorm.DB) AcceptanceItemRepository {
	if tx == nil {
		return r
	}
	return &acceptanceItemRepo{tx}
}

// CheckDeviceSNs 检查设备SN
func (r *acceptanceItemRepo) FilterExistingSNs(ctx context.Context, sns []string) (existingSNs []string, err error) {
	err = r.db.Model(&asset.Device{}).
		Where("sn IN ?", sns).
		Pluck("sn", &existingSNs).Error
	return
}

// CheckDeviceSNs 检查套餐模板
func (r *acceptanceItemRepo) FilterExistingTemplates(ctx context.Context, templates []string) (existingTemplate []string, err error) {
	err = r.db.Model(&template.MachineTemplate{}).
		Where("template_name IN ?", templates).
		Pluck("template_name", &existingTemplate).Error
	return
}

func (r *acceptanceItemRepo) FilterExistingPNs(ctx context.Context, pns []string) (existingPNs []string, err error) {
	err = r.db.Model(&productModel.Product{}).
		Where("pn IN ?", pns).Pluck("pn", &existingPNs).Error
	return
}
