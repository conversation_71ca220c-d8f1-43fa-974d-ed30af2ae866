package acceptance

import (
	"backend/internal/modules/hardware_maintenance/common/types"
	"backend/internal/modules/hardware_maintenance/model/acceptance"
	"context"
	"strings"

	"gorm.io/gorm"
)

// ComponentModelPNMappingRepository 设备型号到PN映射仓储接口
type ComponentModelPNMappingRepository interface {
	Create(ctx context.Context, mapping *acceptance.ComponentModelPNMapping) error
	GetByID(ctx context.Context, id uint) (*acceptance.ComponentModelPNMapping, error)
	Update(ctx context.Context, mapping *acceptance.ComponentModelPNMapping) error
	Delete(ctx context.Context, id uint) error
	List(ctx context.Context, filter types.ComponentModelPNMappingFilter) ([]*acceptance.ComponentModelPNMapping, int64, error)
	GetByModelComponent(ctx context.Context, model, componentType string) (*acceptance.ComponentModelPNMapping, error)
	BatchGetByModelComponents(ctx context.Context, queries []ModelComponent) ([]*acceptance.ComponentModelPNMapping, error)
	GetDB() *gorm.DB
	WithTx(tx *gorm.DB) ComponentModelPNMappingRepository
}

// ModelComponent 查询参数结构
type ModelComponent struct {
	Model         string
	ComponentType string
}

type componentModelPNMappingRepo struct {
	db *gorm.DB
}

// NewComponentModelPNMappingRepository 创建设备型号到PN映射仓储
func NewComponentModelPNMappingRepository(db *gorm.DB) ComponentModelPNMappingRepository {
	return &componentModelPNMappingRepo{db: db}
}

func (r *componentModelPNMappingRepo) Create(ctx context.Context, mapping *acceptance.ComponentModelPNMapping) error {
	return r.db.WithContext(ctx).Create(mapping).Error
}

func (r *componentModelPNMappingRepo) GetByID(ctx context.Context, id uint) (*acceptance.ComponentModelPNMapping, error) {
	var mapping acceptance.ComponentModelPNMapping
	err := r.db.WithContext(ctx).First(&mapping, id).Error
	if err != nil {
		return nil, err
	}
	return &mapping, nil
}

func (r *componentModelPNMappingRepo) Update(ctx context.Context, mapping *acceptance.ComponentModelPNMapping) error {
	return r.db.WithContext(ctx).Save(mapping).Error
}

func (r *componentModelPNMappingRepo) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&acceptance.ComponentModelPNMapping{}, id).Error
}

func (r *componentModelPNMappingRepo) List(ctx context.Context, filter types.ComponentModelPNMappingFilter) ([]*acceptance.ComponentModelPNMapping, int64, error) {
	var mappings []*acceptance.ComponentModelPNMapping
	var total int64

	query := r.db.WithContext(ctx).Model(&acceptance.ComponentModelPNMapping{})

	// 应用过滤条件
	if len(filter.IDs) > 0 {
		query = query.Where("id IN ?", filter.IDs)
	}
	if filter.Model != nil && *filter.Model != "" {
		query = query.Where("model LIKE ?", "%"+*filter.Model+"%")
	}
	if filter.ComponentType != nil && *filter.ComponentType != "" {
		query = query.Where("component_type = ?", *filter.ComponentType)
	}
	if filter.PN != nil && *filter.PN != "" {
		query = query.Where("pn LIKE ?", "%"+*filter.PN+"%")
	}
	if filter.Status != nil && *filter.Status != "" {
		query = query.Where("status = ?", *filter.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// 排序
	query = query.Order("created_at DESC")

	if err := query.Find(&mappings).Error; err != nil {
		return nil, 0, err
	}

	return mappings, total, nil
}

func (r *componentModelPNMappingRepo) GetByModelComponent(ctx context.Context, model, componentType string) (*acceptance.ComponentModelPNMapping, error) {
	var mapping acceptance.ComponentModelPNMapping
	err := r.db.WithContext(ctx).Where("model = ? AND component_type = ? AND status = 'ACTIVE'",
		model, componentType).First(&mapping).Error
	if err != nil {
		return nil, err
	}
	return &mapping, nil
}

func (r *componentModelPNMappingRepo) BatchGetByModelComponents(ctx context.Context, queries []ModelComponent) ([]*acceptance.ComponentModelPNMapping, error) {
	if len(queries) == 0 {
		return []*acceptance.ComponentModelPNMapping{}, nil
	}

	var mappings []*acceptance.ComponentModelPNMapping

	// 构建OR条件查询
	var conditions []string
	var args []interface{}

	for _, q := range queries {
		conditions = append(conditions, "(model = ? AND component_type = ?)")
		args = append(args, q.Model, q.ComponentType)
	}

	whereClause := "(" + strings.Join(conditions, " OR ") + ") AND status = 'ACTIVE'"

	err := r.db.WithContext(ctx).Where(whereClause, args...).Find(&mappings).Error
	return mappings, err
}

func (r *componentModelPNMappingRepo) GetDB() *gorm.DB {
	return r.db
}

func (r *componentModelPNMappingRepo) WithTx(tx *gorm.DB) ComponentModelPNMappingRepository {
	if tx == nil {
		return r
	}
	return &componentModelPNMappingRepo{db: tx}
}
