package acceptance

import (
	"backend/internal/modules/hardware_maintenance/common/types"
	"backend/internal/modules/hardware_maintenance/model/acceptance"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AcceptanceOrderRepository interface {
	// 基本操作
	Create(ctx context.Context, order *acceptance.AcceptanceOrder, items []*acceptance.AcceptanceItem) error
	Update(ctx context.Context, order *acceptance.AcceptanceOrder) error
	UpdateState(ctx context.Context, order *acceptance.AcceptanceOrder) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*acceptance.AcceptanceOrder, error)
	GetByOrderCode(ctx context.Context, code string) (*acceptance.AcceptanceOrder, error)
	List(ctx context.Context, filter types.AcceptanceOrderFilter) ([]*acceptance.AcceptanceOrder, int64, error)
	ListWithoutItems(ctx context.Context, filter types.AcceptanceOrderFilter) ([]*acceptance.AcceptanceOrder, int64, error)

	// 扩展操作
	ListWithItems(ctx context.Context, page, pageSize int, status, query string) ([]*acceptance.AcceptanceOrder, int64, error)
	GetByIDWithItems(ctx context.Context, id uint) (*acceptance.AcceptanceOrder, error)

	UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error

	UpdateWithItems(ctx context.Context, order *acceptance.AcceptanceOrder, newItems []*acceptance.AcceptanceItem) error

	// 创建History
	CreateStatusHistory(ctx context.Context, history *acceptance.AcceptanceOrderHistory) error
	GetLastOperationHistoryByOrderID(ctx context.Context, orderId uint) (*acceptance.AcceptanceOrderHistory, error)
	SaveOperationHistory(ctx context.Context, history *acceptance.AcceptanceOrderHistory) error
	// DB 访问
	GetDB() *gorm.DB
	WithTx(tx *gorm.DB) AcceptanceOrderRepository

	// GetOperationHistoriesByOrderID 获取指定工单的所有操作历史
	GetOperationHistoriesByOrderID(ctx context.Context, orderId uint) ([]*acceptance.AcceptanceOrderHistory, error)
}

type acceptanceOrderRepo struct {
	db *gorm.DB
}

func NewAcceptanceOrderRepository(db *gorm.DB) AcceptanceOrderRepository {
	return &acceptanceOrderRepo{db: db}
}

func (r *acceptanceOrderRepo) Create(ctx context.Context, order *acceptance.AcceptanceOrder, items []*acceptance.AcceptanceItem) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 插入 order
		if err := tx.Create(order).Error; err != nil {
			return err
		}

		for _, item := range items {
			item.OrderID = order.ID
		}

		// 插入 items（如果存在）
		if len(items) > 0 {
			if err := tx.Create(&items).Error; err != nil {
				return err
			}
		}

		// 成功则提交事务
		return nil
	})
}

func (r *acceptanceOrderRepo) Update(ctx context.Context, order *acceptance.AcceptanceOrder) error {
	return r.db.WithContext(ctx).Save(order).Error
}

func (r *acceptanceOrderRepo) UpdateState(ctx context.Context, order *acceptance.AcceptanceOrder) error {
	fields := []string{"status"}
	if order.InitiatorID > 0 {
		fields = append(fields, "initiator_id", "initiator_name")
	}

	if order.ApproverID > 0 {
		fields = append(fields, "approver_id", "approver_name")
	}

	if order.HandlerID > 0 {
		fields = append(fields, "handler_id", "handler_name")
	}

	return r.db.WithContext(ctx).Model(order).Select(fields).Updates(order).Error
}

func (r *acceptanceOrderRepo) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&acceptance.AcceptanceOrder{}, id).Error
}

func (r *acceptanceOrderRepo) GetByID(ctx context.Context, id uint) (*acceptance.AcceptanceOrder, error) {
	var order acceptance.AcceptanceOrder
	err := r.db.WithContext(ctx).Preload("Items").First(&order, id).Error
	return &order, err
}

func (r *acceptanceOrderRepo) GetByOrderCode(ctx context.Context, code string) (*acceptance.AcceptanceOrder, error) {
	var order acceptance.AcceptanceOrder
	err := r.db.WithContext(ctx).Where("order_code = ?", code).First(&order).Error
	return &order, err
}

func (r *acceptanceOrderRepo) List(ctx context.Context, filter types.AcceptanceOrderFilter) ([]*acceptance.AcceptanceOrder, int64, error) {
	var (
		orders   []*acceptance.AcceptanceOrder
		total    int64
		page     = filter.Page
		pageSize = filter.PageSize
	)

	db := r.db.WithContext(ctx).Model(&acceptance.AcceptanceOrder{}).Debug()
	if filter.Status != nil {
		db = db.Where("status = ?", string(*filter.Status))
	}
	if filter.Title != nil {
		db = db.Where("title LIKE ?", "%"+*filter.Title+"%")
	}
	if len(filter.IDs) > 0 {
		db = db.Where("id in ?", filter.IDs)
	}

	//fmt.Println("[]ids = ", filter.IDs)
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = db.Order("id DESC").
		Preload("Items").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&orders).Error
	return orders, total, err
}

// ListWithoutItems 获取验收工单列表但不包含Items字段
func (r *acceptanceOrderRepo) ListWithoutItems(ctx context.Context, filter types.AcceptanceOrderFilter) ([]*acceptance.AcceptanceOrder, int64, error) {
	var (
		orders   []*acceptance.AcceptanceOrder
		total    int64
		page     = filter.Page
		pageSize = filter.PageSize
	)

	db := r.db.WithContext(ctx).Model(&acceptance.AcceptanceOrder{}).Debug()
	if len(filter.IDs) > 0 {
		db = db.Where("id in ?", filter.IDs)
	}
	if filter.Status != nil {
		db = db.Where("status = ?", string(*filter.Status))
	}
	if filter.Title != nil {
		db = db.Where("title LIKE ?", "%"+*filter.Title+"%")
	}
	if len(filter.IDs) > 0 {
		db = db.Where("id in ?", filter.IDs)
	}

	if filter.Initiator != nil {
		db = db.Where("initiator_name = ?", *filter.Approver)

	}

	if filter.Approver != nil {
		db = db.Where("approver_name = ?", *filter.Approver)
	}

	// 创建时间范围起
	if filter.StartTime != nil {
		db = db.Where("created_at >= ?", *filter.StartTime)
	}

	// 创建时间范围止
	if filter.EndTime != nil {
		db = db.Where("created_at <= ?", *filter.EndTime)
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = db.Order("id DESC").
		// 不加载Items
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&orders).Error
	return orders, total, err
}

func (r *acceptanceOrderRepo) ListWithItems(ctx context.Context, page, pageSize int, status, query string) ([]*acceptance.AcceptanceOrder, int64, error) {
	var (
		orders []*acceptance.AcceptanceOrder
		total  int64
	)

	db := r.db.WithContext(ctx).Model(&acceptance.AcceptanceOrder{})
	if status != "" {
		db = db.Where("status = ?", status)
	}
	if query != "" {
		db = db.Where("title LIKE ?", "%"+query+"%")
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = db.Preload("Items").
		Order("created_at DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&orders).Error
	return orders, total, err
}

func (r *acceptanceOrderRepo) GetByIDWithItems(ctx context.Context, id uint) (*acceptance.AcceptanceOrder, error) {
	var order acceptance.AcceptanceOrder
	err := r.db.WithContext(ctx).
		Preload("Items").
		First(&order, id).Error
	return &order, err
}

func (r *acceptanceOrderRepo) GetDB() *gorm.DB {
	return r.db
}

func (r *acceptanceOrderRepo) SaveOperationHistory(ctx context.Context, history *acceptance.AcceptanceOrderHistory) error {
	return r.db.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}}, // 冲突字段，通常是主键或唯一索引
		UpdateAll: true,                          // 冲突时更新所有字段
	}).Create(history).Error
}

func (r *acceptanceOrderRepo) CreateStatusHistory(ctx context.Context, history *acceptance.AcceptanceOrderHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

func (r *acceptanceOrderRepo) UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&acceptance.AcceptanceOrder{}).
		Where("id = ?", id).Updates(fields).Error
}

func (r *acceptanceOrderRepo) UpdateWithItems(ctx context.Context, order *acceptance.AcceptanceOrder, newItems []*acceptance.AcceptanceItem) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 更新 AcceptanceOrder 主体
		if err := tx.Save(order).Error; err != nil {
			return err
		}

		// 如果没有需要处理的 item，则直接返回
		if len(newItems) == 0 {
			return nil
		}

		// 2. 准备批量更新/插入的数据，确保每个 item 都关联到正确的 order ID
		for _, item := range newItems {
			item.OrderID = order.ID
		}

		// 3. 批量更新或创建 (Upsert)
		// 使用 OnConflict 子句，当主键（ID）冲突时，更新指定的列。
		// 这将有效地在一个数据库查询中处理所有更新和创建操作。
		err := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},                                                                                                 // 冲突检查的列
			DoUpdates: clause.AssignmentColumns([]string{"order_id", "device_sn", "vendor", "model", "template_name", "is_pass", "reason", "extra"}), // 冲突时更新的列
		}).Create(&newItems).Error

		if err != nil {
			return err
		}

		return nil
	})
}

func (r *acceptanceOrderRepo) GetLastOperationHistoryByOrderID(ctx context.Context, orderId uint) (*acceptance.AcceptanceOrderHistory, error) {
	var history acceptance.AcceptanceOrderHistory
	err := r.db.WithContext(ctx).Where("acceptance_order_id = ?", orderId).
		Order("id").First(&history).Error
	if err != nil {
		return nil, err
	}
	return &history, nil
}

func (r *acceptanceOrderRepo) WithTx(tx *gorm.DB) AcceptanceOrderRepository {
	if tx == nil {
		return r
	}
	return &acceptanceOrderRepo{tx}
}

// GetOperationHistoriesByOrderID 获取指定工单的所有操作历史
func (r *acceptanceOrderRepo) GetOperationHistoriesByOrderID(ctx context.Context, orderId uint) ([]*acceptance.AcceptanceOrderHistory, error) {
	var histories []*acceptance.AcceptanceOrderHistory
	err := r.db.WithContext(ctx).Where("acceptance_order_id = ?", orderId).Order("operation_time DESC").Find(&histories).Error
	if err != nil {
		return nil, err
	}
	return histories, nil
}
