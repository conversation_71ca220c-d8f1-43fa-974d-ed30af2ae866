package racking

import (
	"backend/internal/modules/hardware_maintenance/common/types"
	"backend/internal/modules/hardware_maintenance/model/racking"
	"context"
	"fmt"

	"gorm.io/gorm/clause"

	"gorm.io/gorm"
)

type AssetRackingTicketRepository interface {
	// 工单基本操作
	Create(ctx context.Context, ticket *racking.AssetRackingTicket) error
	Update(ctx context.Context, ticket *racking.AssetRackingTicket) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*racking.AssetRackingTicket, error)
	List(ctx context.Context, filter types.RackingTicketFilter) ([]*racking.AssetRackingTicket, int64, error)
	UpdateStatus(ctx context.Context, id uint, status string) error
	UpdateFieldsByID(ctx context.Context, id uint, fields map[string]interface{}) error
	// 查询附带 items
	GetWithItems(ctx context.Context, id uint) (*racking.AssetRackingTicket, error)
	CreateTicketAndItems(ctx context.Context, ticket *racking.AssetRackingTicket, items []*racking.AssetRackingItem) error
	UpdateItems(ctx context.Context, ticketID uint, items []*racking.AssetRackingItem) error

	// History相关
	GetLastOperationHistoryByTicketID(ctx context.Context, id uint) (*racking.AssetRackingTicketHistory, error)
	SaveOperationHistory(ctx context.Context, history *racking.AssetRackingTicketHistory) error
	GetOperationHistoriesByTicketID(ctx context.Context, ticketID uint) ([]*racking.AssetRackingTicketHistory, error)

	GetDB() *gorm.DB
	WithTx(db *gorm.DB) AssetRackingTicketRepository
}

type assetRackingTicketRepo struct {
	db *gorm.DB
}

func NewAssetRackingTicketRepository(db *gorm.DB) AssetRackingTicketRepository {
	return &assetRackingTicketRepo{db: db}
}

func (r *assetRackingTicketRepo) Create(ctx context.Context, ticket *racking.AssetRackingTicket) error {
	return r.db.WithContext(ctx).Create(ticket).Error
}

func (r *assetRackingTicketRepo) Update(ctx context.Context, ticket *racking.AssetRackingTicket) error {
	return r.db.WithContext(ctx).Save(ticket).Error
}

func (r *assetRackingTicketRepo) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&racking.AssetRackingTicket{}, id).Error
}

func (r *assetRackingTicketRepo) GetByID(ctx context.Context, id uint) (*racking.AssetRackingTicket, error) {
	var ticket racking.AssetRackingTicket
	err := r.db.WithContext(ctx).First(&ticket, id).Error
	return &ticket, err
}

func (r *assetRackingTicketRepo) List(ctx context.Context, filter types.RackingTicketFilter) ([]*racking.AssetRackingTicket, int64, error) {
	var (
		tickets        []*racking.AssetRackingTicket
		total          int64
		page, pageSize = filter.Page, filter.PageSize
	)

	db := r.db.WithContext(ctx).Model(&racking.AssetRackingTicket{})

	// 条件构造
	if len(filter.IDs) > 0 {
		db = db.Where("id IN ?", filter.IDs)
	}
	if filter.TicketNo != nil {
		db = db.Where("ticket_no = ?", *filter.TicketNo)
	}
	if filter.Title != nil {
		db = db.Where("title LIKE ?", "%"+*filter.Title+"%")
	}
	if filter.Status != nil {
		db = db.Where("status = ?", *filter.Status)
	}
	if filter.Applicant != nil {
		db = db.Where("applicant_name = ?", *filter.Applicant)
	}
	if filter.StartTime != nil {
		db = db.Where("created_at >= ?", *filter.StartTime)
	}
	if filter.EndTime != nil {
		db = db.Where("created_at <= ?", *filter.EndTime)
	}

	// 查询总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页参数处理
	if filter.Page < 1 {
		page = 1
	}
	if filter.PageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 查询分页数据
	if err := db.Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&tickets).Error; err != nil {
		return nil, 0, err
	}

	fmt.Printf("tickets: %v\n", tickets)
	return tickets, total, nil
}

func (r *assetRackingTicketRepo) UpdateStatus(ctx context.Context, id uint, status string) error {
	result := r.db.WithContext(ctx).
		Model(&racking.AssetRackingTicket{}).
		Where("id = ?", id).
		Update("status", status)

	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("工单 %d 不存在或状态未变", id)
	}
	return nil
}

func (r *assetRackingTicketRepo) GetWithItems(ctx context.Context, id uint) (*racking.AssetRackingTicket, error) {
	var ticket racking.AssetRackingTicket
	err := r.db.WithContext(ctx).
		Preload("Items").
		Preload("Items.Device").
		First(&ticket, id).Error
	return &ticket, err
}

func (r *assetRackingTicketRepo) CreateTicketAndItems(ctx context.Context, ticket *racking.AssetRackingTicket, items []*racking.AssetRackingItem) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 插入工单
		if err := tx.WithContext(ctx).Create(ticket).Error; err != nil {
			return fmt.Errorf("创建订工单失败: %w", err)
		}

		for _, item := range items {
			item.TicketID = ticket.ID
		}

		if len(items) > 0 {
			if err := tx.WithContext(ctx).Create(items).Error; err != nil {
				return fmt.Errorf("创建订工单失败: %w", err)
			}
		}
		return nil
	})
}

func (r *assetRackingTicketRepo) UpdateFieldsByID(ctx context.Context, id uint, fields map[string]interface{}) error {
	return r.db.WithContext(ctx).
		Model(&racking.AssetRackingTicket{}).
		Where("id = ?", id).
		Updates(fields).Error
}

func (r *assetRackingTicketRepo) UpdateItems(ctx context.Context, ticketID uint, items []*racking.AssetRackingItem) error {
	tx := r.db.WithContext(ctx)

	if err := tx.Where("ticket_id = ?", ticketID).Delete(&racking.AssetRackingItem{}).Error; err != nil {
		return fmt.Errorf("删除旧的上架项失败: %w", err)
	}

	for _, item := range items {
		item.ID = 0
		item.TicketID = ticketID
	}

	// 3. 批量插入新的 item（如果有）
	if len(items) > 0 {
		if err := tx.Create(&items).Error; err != nil {
			return fmt.Errorf("插入新的上架项失败: %w", err)
		}
	}

	return nil
}

func (r *assetRackingTicketRepo) GetLastOperationHistoryByTicketID(ctx context.Context, ticketID uint) (*racking.AssetRackingTicketHistory, error) {
	var history racking.AssetRackingTicketHistory
	err := r.db.WithContext(ctx).Where("racking_ticket_id = ?", ticketID).
		Order("id").First(&history).Error
	if err != nil {
		return nil, err
	}
	return &history, nil
}

func (r *assetRackingTicketRepo) SaveOperationHistory(ctx context.Context, history *racking.AssetRackingTicketHistory) error {
	return r.db.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}}, // 冲突字段，通常是主键或唯一索引
		UpdateAll: true,                          // 冲突时更新所有字段
	}).Create(history).Error

}

func (r *assetRackingTicketRepo) GetOperationHistoriesByTicketID(ctx context.Context, ticketID uint) ([]*racking.AssetRackingTicketHistory, error) {
	var histories []*racking.AssetRackingTicketHistory
	err := r.db.WithContext(ctx).Where("racking_ticket_id = ?", ticketID).Order("operation_time DESC").Find(&histories).Error
	if err != nil {
		return nil, err
	}
	return histories, nil
}

func (r *assetRackingTicketRepo) GetDB() *gorm.DB {
	return r.db
}

func (r *assetRackingTicketRepo) WithTx(tx *gorm.DB) AssetRackingTicketRepository {
	if tx == nil {
		return r
	}
	return &assetRackingTicketRepo{
		db: tx,
	}
}
