package acceptance

import (
	"backend/internal/common/constants"
	"backend/internal/modules/cmdb/service/asset"
	"backend/internal/modules/cmdb/service/product"
	fileService "backend/internal/modules/file/service"
	"backend/internal/modules/hardware_maintenance/common"
	"backend/internal/modules/hardware_maintenance/common/types"
	model "backend/internal/modules/hardware_maintenance/model/acceptance"
	"backend/internal/modules/hardware_maintenance/service/acceptance"
	"backend/response"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"net/http"
	"strconv"
)

const (
	DefaultPage     = 1
	DefaultPageSize = 20
)

// AcceptanceHandler 验收处理器
type AcceptanceController struct {
	service        acceptance.AcceptanceService
	fileSvc        fileService.FileService
	productService product.ProductService
	deviceService  asset.DeviceService
	logger         *zap.Logger
}

// NewAcceptanceHandler 创建验收控制器
func NewAcceptanceController(service acceptance.AcceptanceService, fileSvc fileService.FileService, productService product.ProductService, deviceService asset.DeviceService, logger *zap.Logger) *AcceptanceController {
	return &AcceptanceController{service: service, fileSvc: fileSvc, productService: productService, deviceService: deviceService, logger: logger}
}

// RegisterRoutes 登记验收路由
func (c *AcceptanceController) RegisterRoutes(r *gin.RouterGroup) {
	acceptanceRouter := r.Group("/asset/acceptance")
	{
		acceptanceRouter.GET("", c.List)                                                           //获取所有工单
		acceptanceRouter.GET("/:id", c.GetByID)                                                    //根据ID获取入库单详情
		acceptanceRouter.GET("/:id/items", c.ListAcceptanceItems)                                  //根据ID获取工单的验收项列表
		acceptanceRouter.POST("/create_order", c.CreateOrder)                                      //更新工单状态
		acceptanceRouter.PUT("/:id/audit", c.AuditAcceptanceOrder)                                 // 审核工单
		acceptanceRouter.POST("/:id/upload_check", c.UploadCheckResult)                            // 上传检测结果
		acceptanceRouter.PUT("/:id/complete", c.CompleteAcceptanceOrder)                           // 完成工单
		acceptanceRouter.GET("/:id/history", c.GetAcceptanceOrderHistory)                          // 获取工单操作历史
		acceptanceRouter.GET("/item_components/:id", c.ListInspectingComponentsByItemID)           // 获取验收条目组件信息
		acceptanceRouter.PUT("/update_inspecting_component_info", c.UpdateInspectingComponentInfo) // 更新正在验收的组件信息
		acceptanceRouter.PUT("/fill_component_pn", c.FillComponentPNs)

		// TODO acceptanceRouter.GET("/accepted", c.ListAcceptedAsset)           // 获取已验收资产列表

		//  暂留
		acceptanceRouter.PUT("/:id/cancel", c.CancelAcceptanceOrder) // 取消工单
		acceptanceRouter.PUT("/:id/assign", c.AssignAcceptanceOrder) // 分配工单
	}

	// 设备型号到PN映射管理路由
	mappingRouter := r.Group("/asset/acceptance/device_model_pn_mapping")
	{
		mappingRouter.GET("", c.ListComponentModelPNMappings)             // 获取映射列表
		mappingRouter.GET("/:id", c.GetComponentModelPNMappingByID)       // 根据ID获取映射详情
		mappingRouter.POST("", c.CreateComponentModelPNMapping)           // 创建映射
		mappingRouter.PUT("/:id", c.UpdateComponentModelPNMapping)        // 更新映射
		mappingRouter.DELETE("/:id", c.DeleteComponentModelPNMapping)     // 删除映射
		mappingRouter.GET("/query", c.QueryDeviceModelPNByModelComponent) // 根据型号、组件类型查询PN
	}
}

// GetByID 根据ID获取验收工单
// @Summary 获取验收单详情
// @Description 根据ID获取验收单详情
// @Tags 资产验收
// @Accept json
// @Produce json
// @Param id path int true "验收工单ID"
// @Success 200 {object} response.Response{data=acceptance.AcceptanceOrder} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "验收工单不存在"
// @Router /hardware_maintenance/asset/acceptance/{id} [get]
func (h *AcceptanceController) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil || id < 1 {
		response.Fail(c, http.StatusBadRequest, "无效的ID")
		return
	}

	p, err := h.service.GetAcceptanceOrderByID(c, uint(id))
	if err != nil {
		if err == acceptance.ErrAcceptanceOrderNotFound {
			response.Fail(c, http.StatusNotFound, err.Error())
			return
		}
		response.Fail(c, http.StatusInternalServerError, "内部服务错误")
		return
	}

	response.Success(c, p, "获取成功")
}

// List 获取验收工单列表
// @Summary 获取验收工单列表
// @Description 获取验收工单列表，支持分页和筛选
// @Tags 资产验收
// @Accept json
// @Produce json
// @Param page query int true "页码"
// @Param pageSize query int true "每页数量"
// @Param title query string false "工单标题"
// @Param status query string false "工单状态"
// @Param orderNo query string false "工单号"
// @Param initiatorName query string false "发起人"
// @Success 200 {object} response.ResponseStruct{data=ListResult} "成功"
// @Failure 400 {object} response.ResponseStruct "请求错误"
// @Router /hardware_maintenance/asset/acceptance [get]
func (c *AcceptanceController) List(ctx *gin.Context) {
	var query types.AcceptanceOrderFilter
	if err := ctx.ShouldBindQuery(&query); err != nil {
		fmt.Printf("err is %v\n", err)
		response.Fail(ctx, http.StatusBadRequest, "无效的查询参数")
		return
	}

	if query.Page == 0 {
		query.Page = DefaultPage
	}

	if query.PageSize == 0 {
		query.PageSize = DefaultPageSize
	}

	// 使用不返回Items的查询方法
	result, total, err := c.service.ListAcceptanceOrderWithoutItems(ctx, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取验收工单列表失败")
		return
	}

	// 构建返回结果
	listResult := map[string]interface{}{
		"list":  result,
		"total": total, // 使用服务返回的total
	}

	response.Success(ctx, listResult, "获取验收工单列表成功")
}

// CreateOrder 创建验收工单
func (c *AcceptanceController) CreateOrder(ctx *gin.Context) {
	// 1. 校验参数
	// 包含 标题,列表参数
	// 列表中每行包含 SN 厂商  型号 套餐模型
	// 或者是文件导入
	// 1.1 校验列表和文件是否都为空，为空返回错误
	var req types.CreateAcceptanceOrderRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, 400, "参数绑定失败: "+err.Error())
		return
	}

	// 2. 解析上传数据
	// 获取上传文件（可选）
	file, _ := ctx.FormFile("file")
	if len(req.Title) == 0 || (len(req.Items) == 0 && file == nil) {
		response.Fail(ctx, 400, "标题或验收条目为空")
		return
	}

	var (
		items []*model.AcceptanceItem
		sns   []string
	)
	if file != nil {
		// TODO 解析文件

	} else {
		// 解析json body, 填入
		items = make([]*model.AcceptanceItem, 0, len(req.Items))
		for _, item := range req.Items {
			items = append(items, &model.AcceptanceItem{
				DeviceID:     item.DeviceID,
				DeviceSN:     item.DeviceSN,
				Vendor:       item.Vendor,
				Model:        item.Model,
				TemplateName: item.TemplateName,
			})
			sns = append(sns, item.DeviceSN)
		}
	}

	// 校验设备SN和套餐存不存在,有无重复SN
	noExistingSNs, repeatSNs, err := c.service.CheckSNs(ctx, items)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}

	noExistingTemplates, err := c.service.FilterNoExistingTemplates(ctx, items)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 如果SNs和模板不存在，提示用户哪些SN和templates填写错误
	if len(noExistingSNs) > 0 || len(noExistingTemplates) > 0 {
		resp := types.CreateAcceptanceOrderInvalidResponse{
			InvalidSNs:           noExistingSNs,
			InvalidTemplateNames: noExistingTemplates,
			RepeatedSns:          repeatSNs,
		}
		response.Response(ctx, http.StatusBadRequest, resp, "验收设备列表中SN或者模板不存在")
		return
	}

	// TODO 校验资产状态，只有已出库和该配中的设备才能被验收

	// TODO  在验收流程中的不能重复验收，但是验收通过和验收不通过的支持重复验收
	devices, _, err := c.deviceService.ListBySNs(ctx, sns)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 校验设备状态，只有已出库的才能进行验收
	var invalidStatusDevices []string
	for _, device := range devices {
		// 只有已出库、改配中的的才能进行验收
		if device.AssetStatus != constants.AssetStatusOutStock {
			invalidStatusDevices = append(invalidStatusDevices, device.SN)
		}
	}

	if len(invalidStatusDevices) > 0 {
		errStr := invalidStatusDevices[0]
		for i := 1; i < len(invalidStatusDevices); i++ {
			errStr += ", " + invalidStatusDevices[i]
		}

		response.Response(ctx, http.StatusBadRequest, map[string]interface{}{
			"invalidStatusDevice": invalidStatusDevices, //状态不是已出库的设备
		}, fmt.Sprintf("只有已出库的设备才能验收，无法验收SN为 %s 的设备", errStr))
		return
	}

	// 校验是否验收过以及是否在验收中
	//var unavailableForAcceptanceDevices []string
	// 查sn以及is_pass为null或者1的设备

	// 3. 数据库创建验收表单，开启工作流
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
	}

	order := &model.AcceptanceOrder{
		OrderNo:       common.GenerateOrderNo(common.AcceptanceOrderNoPrefix),
		Title:         req.Title,
		DeviceCount:   len(items),
		Status:        string(common.OrderStatus_Pending),
		InitiatorID:   userID, // 获取用户
		InitiatorName: userName,
		Description:   req.Description,
	}
	err = c.service.CreateAcceptanceOrder(ctx, order, items)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "创建验收工单成功")
}

// AuditAcceptanceOrder 审核验收工单
func (c *AcceptanceController) AuditAcceptanceOrder(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
	}
	var req types.AuditAcceptanceOrderRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// check一下状态
	order, err := c.service.GetAcceptanceOrderByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	if order.Status != string(common.OrderStatus_Pending) {
		response.Fail(ctx, http.StatusBadRequest, "无法审核状态为"+order.Status+"的工单")
		return
	}

	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
	}

	req.OperatorID = userID
	req.OperatorName = userName
	err = c.service.AuditAcceptanceOrder(ctx, uint(id), &req)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "审核工单成功")
}

// CancelAcceptanceOrder 硬件团队取消验收单
func (c *AcceptanceController) CancelAcceptanceOrder(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
	}
	var req types.CancelAcceptanceOrderRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// check一下状态
	order, err := c.service.GetAcceptanceOrderByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	if order.Status != string(common.OrderStatus_Pending) {
		response.Fail(ctx, http.StatusBadRequest, "无法取消状态为"+order.Status+"的工单")
		return
	}

	err = c.service.CancelAcceptanceOrder(ctx, uint(id), &req)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Success(ctx, nil, "取消工单成功")
}

// AssignAcceptanceOrder 分配验收工单
func (c *AcceptanceController) AssignAcceptanceOrder(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
	}
	var req types.AssignAcceptanceOrderRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// check一下状态
	order, err := c.service.GetAcceptanceOrderByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	if order.Status != string(common.OrderStatus_Approved) {
		response.Fail(ctx, http.StatusBadRequest, "无法分配状态为"+order.Status+"的工单")
		return
	}

	err = c.service.AssignAcceptanceOrder(ctx, uint(id), &req)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Success(ctx, nil, "分配工单成功")
}

// UploadCheckResult 硬件团队上传硬件检测结果（支持多个文件）
func (c *AcceptanceController) UploadCheckResult(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
	}
	form, err := ctx.MultipartForm()
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取上传文件失败: "+err.Error())
		return
	}

	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
	}

	files := form.File["files"]
	if len(files) == 0 {
		response.Fail(ctx, http.StatusBadRequest, "未选择任何文件")
		return
	}

	// 根据工单ID读取sn信息，导入文件中，只有存在于工单对应的item中的设备信息才被加载进数据库中
	items, err := c.service.ListAcceptanceItemsByOrderID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "内部错误")
		return
	}

	deviceSNMap := make(map[string]struct{}, 0)
	for _, item := range items {
		deviceSNMap[item.DeviceSN] = struct{}{}
	}

	// 只有在验收列表中的才会进List中
	var serverInfoList []*types.ServerInfoRaw
	for _, file := range files {
		if !strings.HasSuffix(strings.ToLower(file.Filename), ".json") {
			response.Fail(ctx, http.StatusBadRequest, "仅支持上传JSON文件: "+file.Filename)
			return
		}
		f, err := file.Open()
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "读取JSON文件失败: "+err.Error())
			return
		}
		defer func() {
			cerr := f.Close()
			if cerr != nil {
				c.logger.Warn("关闭文件失败", zap.Error(cerr))
			}
		}()
		var fileData []*types.ServerInfoRaw
		if err := json.NewDecoder(f).Decode(&fileData); err != nil {
			response.Fail(ctx, http.StatusBadRequest, "解析JSON文件失败: "+err.Error())
			return
		}
		for _, data := range fileData {
			if _, ok := deviceSNMap[data.ID]; ok {
				serverInfoList = append(serverInfoList, data)
			}
		}
	}

	// 保留上传逻辑， 如果有后置方便处理
	// 获取关联参数
	moduleType := common.AcceptanceOrderFileModuleType
	moduleID := uint(id)
	description := ctx.PostForm("description")
	// 处理模块ID
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "批量上传文件失败: "+err.Error())
		return
	}
	// 调用服务批量上传文件
	result, err := c.fileSvc.UploadFiles(ctx, files, moduleType, moduleID, description)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "批量上传文件失败: "+err.Error())
		return
	}

	err = c.service.UploadCheckTrigger(ctx, uint(id), userID, userName, serverInfoList)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "内部错误: "+err.Error())
		return
	}

	response.Success(ctx, result, "上传成功")
}

// TODO 软件团队完结工单
func (c *AcceptanceController) CompleteAcceptanceOrder(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
	}

	// 重新写一个吧
	order, err := c.service.GetAcceptanceOrderByIDWithItems(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	itemsMap := make(map[string]*model.AcceptanceItem)
	for _, item := range order.Items {
		itemsMap[item.DeviceSN] = &item
	}

	if order.Status != string(common.OrderStatus_Collected) {
		response.Fail(ctx, http.StatusBadRequest, "无法完结状态为"+order.Status+"的工单")
		return
	}

	var req types.CompleteAcceptanceOrderRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 将请求中item转化成model形式
	items := make([]*model.AcceptanceItem, 0, len(req.Items))
	for _, item := range req.Items {
		if itemsMap[item.DeviceSN] == nil {
			continue
		}
		orderItem := itemsMap[item.DeviceSN]
		orderItem.DeviceSN = item.DeviceSN
		orderItem.IsPass = &item.IsPass
		orderItem.Reason = item.Reason
		items = append(items, orderItem)
		//fmt.Println("req.item.Device:", item.DeviceSN, "req.item.isPass: ", item.IsPass)
	}

	resp, err := c.checkPNs(ctx, order, items, true)
	if err != nil {
		response.Response(ctx, http.StatusInternalServerError, resp, err.Error())
		return
	}

	// if len(resp.NoExistingPNInfos) > 0 || len(resp.InvalidDeviceSNs) > 0 || len(resp.DbExistingComponentSN) > 0 { // 先不要设置这么严格吧
	if len(resp.NoExistingPNInfos) > 0 || len(resp.InvalidDeviceSNs) > 0 {
		response.Response(ctx, http.StatusOK, resp, "验收项中有PN或设备SN不合法，请补充")
		return
	}

	// 获取用户名字
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
	}
	req.OperatorID = userID
	req.OperatorName = userName

	err = c.service.CompleteAcceptanceOrder(ctx, uint(id), items, &req)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "内部错误: "+err.Error())
		return
	}

	// TODO 重新查询并返回结果

	response.Success(ctx, nil, "完结工单成功")

}

// ListAcceptanceItems 获取指定工单ID的验收项列表
// @Summary 获取验收项列表
// @Description 根据工单ID获取验收项列表
// @Tags 资产验收
// @Accept json
// @Produce json
// @Param id path int true "验收工单ID"
// @Success 200 {object} response.Response{data=[]model.AcceptanceItem} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "验收工单不存在"
// @Router /hardware_maintenance/asset/acceptance/{id}/items [get]
func (c *AcceptanceController) ListAcceptanceItems(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil || id < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 先检查工单是否存在
	_, err = c.service.GetAcceptanceOrderByID(ctx, uint(id))
	if err != nil {
		if err == acceptance.ErrAcceptanceOrderNotFound {
			response.Fail(ctx, http.StatusNotFound, err.Error())
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "内部服务错误")
		return
	}

	// 直接获取工单的验收项列表
	items, err := c.service.ListAcceptanceItemsByOrderID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取验收项失败")
		return
	}

	// 构建返回结果
	listResult := map[string]interface{}{
		"list":  items,
		"total": len(items),
	}

	response.Success(ctx, listResult, "获取验收项成功")
}

// 校验PN存不存在
func (c *AcceptanceController) checkPNs(ctx context.Context, order *model.AcceptanceOrder, items []*model.AcceptanceItem, onlyPass bool) (types.CompleteAcceptanceOrderInValidResponse, error) {
	var (
		resp             types.CompleteAcceptanceOrderInValidResponse
		passDeviceSNMap  = make(map[string]bool)
		invalidDeviceSNs = make([]string, 0)
		componentSNs     = make([]string, 0)
	)
	if order == nil {
		return resp, errors.New("验收工单为空")
	}
	// 收集所有itemID
	itemIDs := make([]uint, 0, len(items))
	for _, item := range items {
		if item.IsPass != nil && *item.IsPass {
			// 如果验收通过，则将设备SN加入到passDeviceSNMap中
			itemIDs = append(itemIDs, item.ID) //只有验收通过的组件才会被检查
			passDeviceSNMap[item.DeviceSN] = true
		}
	}
	// 从InspectingComponentInfo表查所有组件
	acceptedComponents, err := c.service.ListAcceptedComponentsByItemIDs(ctx, itemIDs)
	if err != nil {
		return resp, err
	}

	// 收集所有PN
	var (
		pns               = make([]string, 0)
		pnInfoMap         = make(map[string]*types.NoExistingPNInfo)
		noExistingPNInfos = make([]*types.NoExistingPNInfo, 0)
		modelOnceMap      = make(map[string]bool)

		existingComponentSNs = make([]string, 0)
	)

	// 需要保证只入一次
	for _, ac := range acceptedComponents {
		if ac.PN != "" {
			pns = append(pns, ac.PN)
			pnInfoMap[ac.PN] = &types.NoExistingPNInfo{
				PN:            ac.PN,
				ComponentType: ac.ComponentType,
				Model:         ac.Model,
				Description:   ac.Description,
			}
		} else {
			// 确保这里只加入一次
			if len(ac.Model) > 0 && !modelOnceMap[ac.Model] {
				noExistingPNInfos = append(noExistingPNInfos, &types.NoExistingPNInfo{
					ComponentType: ac.ComponentType,
					Model:         ac.Model,
					Description:   ac.Description,
				})
				modelOnceMap[ac.Model] = true
			}

		}

		if passDeviceSNMap[ac.DeviceSN] {
			delete(passDeviceSNMap, ac.DeviceSN)
		}
		componentSNs = append(componentSNs, ac.SN)
	}
	// 查询设备组件表，如果设备组件表中存在，那么就返回对应的错误提示
	existingComponents, err := c.service.ListServerComponentsBySNs(ctx, componentSNs)
	if err != nil {
		return resp, err
	}

	for _, component := range existingComponents {
		existingComponentSNs = append(existingComponentSNs, component.SN)
	}
	resp.DbExistingComponentSN = existingComponentSNs

	pns = common.DeduplicateStrings(pns)
	// 查询cmdb产品表
	products, err := c.productService.ListByPNs(ctx, pns)

	if err != nil {
		return resp, err
	}
	existPNs := make([]string, 0, len(products))
	for _, prod := range products {
		existPNs = append(existPNs, prod.PN)
	}
	noExistPNs := common.StringDifference(pns, existPNs)

	for _, pn := range noExistPNs {
		noExistingPNInfos = append(noExistingPNInfos, pnInfoMap[pn])
	}
	resp.NoExistingPNInfos = noExistingPNInfos

	// 如果验收通过的设备不在上传文件中，则进行报错
	for invalidDeviceSN := range passDeviceSNMap {
		invalidDeviceSNs = append(invalidDeviceSNs, invalidDeviceSN)
	}

	resp.InvalidDeviceSNs = invalidDeviceSNs
	resp.DbExistingComponentSN = []string{} // 先不要求这么严格，验收操作可能太繁琐容易出错
	return resp, nil
}

// GetAcceptanceOrderHistory 获取验收工单操作历史
func (c *AcceptanceController) GetAcceptanceOrderHistory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	histories, err := c.service.GetAcceptanceOrderHistories(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取操作历史失败: "+err.Error())
		return
	}

	response.Success(ctx, histories, "获取操作历史成功")
}

// ListItemComponents 获取指定验收项ID的组件信息
// @Summary 获取验收项下的组件信息
// @Description 根据验收项ID获取所有组件信息
// @Tags 资产验收
// @Accept json
// @Produce json
// @Param item_id path int true "验收项ID"
// @Success 200 {object} response.Response{data=[]model.InspectingComponentInfo} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "验收项不存在"
// @Router /hardware_maintenance/asset/acceptance/item/{item_id}/components [get]
func (c *AcceptanceController) ListItemComponents(ctx *gin.Context) {
	itemIDStr := ctx.Param("item_id")
	itemID, err := strconv.ParseUint(itemIDStr, 10, 32)
	if err != nil || itemID < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的验收项ID")
		return
	}
	components, err := c.service.ListAcceptedComponentsByItemIDs(ctx, []uint{uint(itemID)})
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取组件信息失败")
		return
	}
	response.Success(ctx, components, "获取组件信息成功")
}

// ListInspectingComponentsByItemID 获取指定验收项ID的正在验收组件信息
// @Summary 获取验收项下正在验收的组件信息
// @Description 根据验收项ID获取所有正在验收的组件信息
// @Tags 资产验收
// @Accept json
// @Produce json
// @Param item_id path int true "验收项ID"
// @Success 200 {object} response.Response{data=[]model.InspectingComponentInfo} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "验收项不存在"
// @Router /hardware_maintenance/asset/acceptance/item/{item_id}/inspecting_components [get]
func (c *AcceptanceController) ListInspectingComponentsByItemID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil || id < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	components, err := c.service.ListAcceptedComponentsByItemIDs(ctx, []uint{uint(id)})
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取正在验收的组件信息失败")
		return
	}
	response.Success(ctx, components, "获取正在验收的组件信息成功")
}

// UpdateInspectingComponentInfo 更新正在验收的组件信息
// @Summary 更新正在验收的组件信息
// @Description 根据组件ID更新正在验收的组件信息
// @Tags 资产验收
// @Accept json
// @Produce json
// @Param id path int true "组件ID"
// @Param body body UpdateInspectingComponentInfoRequest true "要更新的组件信息"
// @Success 200 {object} response.Response "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "组件不存在"
// @Router /hardware_maintenance/asset/acceptance/inspecting_component/{id} [put]
func (c *AcceptanceController) UpdateInspectingComponentInfo(ctx *gin.Context) {

	var req types.UpdateInspectingComponentInfoReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	if req.ItemID < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的组件ID")
		return
	}

	if err := c.service.UpdateInspectingComponentInfo(ctx, &req); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新组件信息失败: "+err.Error())
		return
	}
	response.Success(ctx, nil, "更新组件信息成功")
}

// ListComponentModelPNMappings 获取设备型号到PN映射列表
// @Summary 获取设备型号到PN映射列表
// @Description 获取设备型号到PN映射列表，支持分页和筛选
// @Tags 设备型号PN映射
// @Accept json
// @Produce json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param model query string false "设备型号"
// @Param component_type query string false "组件类型"
// @Param pn query string false "PN号"
// @Param status query string false "状态"
// @Success 200 {object} response.Response{data=map[string]interface{}} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Router /hardware_maintenance/asset/device-model-pn-mapping [get]
func (c *AcceptanceController) ListComponentModelPNMappings(ctx *gin.Context) {
	var filter types.ComponentModelPNMappingFilter
	if err := ctx.ShouldBindQuery(&filter); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的查询参数: "+err.Error())
		return
	}

	// 设置默认分页参数
	if filter.Page == 0 {
		filter.Page = DefaultPage
	}
	if filter.PageSize == 0 {
		filter.PageSize = DefaultPageSize
	}

	mappings, total, err := c.service.ListComponentModelPNMappings(ctx, filter)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取映射列表失败: "+err.Error())
		return
	}

	// 构建返回结果
	result := map[string]interface{}{
		"list":  mappings,
		"total": total,
	}

	response.Success(ctx, result, "获取映射列表成功")
}

// GetComponentModelPNMappingByID 根据ID获取设备型号到PN映射详情
// @Summary 根据ID获取设备型号到PN映射详情
// @Description 根据ID获取设备型号到PN映射详情
// @Tags 设备型号PN映射
// @Accept json
// @Produce json
// @Param id path int true "映射ID"
// @Success 200 {object} response.Response{data=acceptance.ComponentModelPNMapping} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "映射不存在"
// @Router /hardware_maintenance/asset/device-model-pn-mapping/{id} [get]
func (c *AcceptanceController) GetComponentModelPNMappingByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil || id < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	mapping, err := c.service.GetComponentModelPNMappingByID(ctx, uint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.Fail(ctx, http.StatusNotFound, "映射不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取映射详情失败: "+err.Error())
		return
	}

	response.Success(ctx, mapping, "获取映射详情成功")
}

// CreateComponentModelPNMapping 创建设备型号到PN映射
// @Summary 创建设备型号到PN映射
// @Description 创建设备型号到PN映射
// @Tags 设备型号PN映射
// @Accept json
// @Produce json
// @Param req body types.CreateComponentModelPNMappingRequest true "创建映射请求"
// @Success 200 {object} response.Response{data=acceptance.ComponentModelPNMapping} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Router /hardware_maintenance/asset/device-model-pn-mapping [post]
func (c *AcceptanceController) CreateComponentModelPNMapping(ctx *gin.Context) {
	var req types.CreateComponentModelPNMappingRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	mapping, err := c.service.CreateComponentModelPNMapping(ctx, &req)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建映射失败: "+err.Error())
		return
	}

	response.Success(ctx, mapping, "创建映射成功")
}

// UpdateComponentModelPNMapping 更新设备型号到PN映射
// @Summary 更新设备型号到PN映射
// @Description 更新设备型号到PN映射
// @Tags 设备型号PN映射
// @Accept json
// @Produce json
// @Param id path int true "映射ID"
// @Param req body types.UpdateComponentModelPNMappingRequest true "更新映射请求"
// @Success 200 {object} response.Response{data=acceptance.ComponentModelPNMapping} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "映射不存在"
// @Router /hardware_maintenance/asset/device-model-pn-mapping/{id} [put]
func (c *AcceptanceController) UpdateComponentModelPNMapping(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil || id < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var req types.UpdateComponentModelPNMappingRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	mapping, err := c.service.UpdateComponentModelPNMapping(ctx, uint(id), &req)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.Fail(ctx, http.StatusNotFound, "映射不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "更新映射失败: "+err.Error())
		return
	}

	response.Success(ctx, mapping, "更新映射成功")
}

// DeleteComponentModelPNMapping 删除设备型号到PN映射
// @Summary 删除设备型号到PN映射
// @Description 删除设备型号到PN映射
// @Tags 设备型号PN映射
// @Accept json
// @Produce json
// @Param id path int true "映射ID"
// @Success 200 {object} response.Response "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "映射不存在"
// @Router /hardware_maintenance/asset/device-model-pn-mapping/{id} [delete]
func (c *AcceptanceController) DeleteComponentModelPNMapping(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil || id < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	err = c.service.DeleteComponentModelPNMapping(ctx, uint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.Fail(ctx, http.StatusNotFound, "映射不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "删除映射失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除映射成功")
}

// QueryDeviceModelPNByModelComponent 根据型号、组件类型查询PN
// @Summary 根据型号、组件类型查询PN
// @Description 根据型号、组件类型查询PN
// @Tags 设备型号PN映射
// @Accept json
// @Produce json
// @Param model query string true "设备型号"
// @Param component_type query string true "组件类型"
// @Success 200 {object} response.Response{data=acceptance.ComponentModelPNMapping} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "映射不存在"
// @Router /hardware_maintenance/asset/device-model-pn-mapping/query [get]
func (c *AcceptanceController) QueryDeviceModelPNByModelComponent(ctx *gin.Context) {
	model := ctx.Query("model")
	componentType := ctx.Query("component_type")

	if model == "" || componentType == "" {
		response.Fail(ctx, http.StatusBadRequest, "model、component_type参数不能为空")
		return
	}

	mapping, err := c.service.GetComponentPNByModelComponent(ctx, model, componentType)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.Fail(ctx, http.StatusNotFound, "未找到对应的PN映射")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "查询PN映射失败: "+err.Error())
		return
	}

	response.Success(ctx, mapping, "查询PN映射成功")
}

func (c *AcceptanceController) FillComponentPNs(ctx *gin.Context) {
	var req types.FillComponentPNRequest
	if err := ctx.BindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数有误")
		return
	}

	// 修改对应items中的CPU和GPU的PN
	if err := c.service.FillComponentPNs(ctx, req.ItemIDs, req.OverWriteExisting); err != nil {
		response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, nil, "修改成功")

}
