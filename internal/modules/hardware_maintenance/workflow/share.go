package workflow

import (
	"backend/internal/modules/hardware_maintenance/common"
	"time"
)

type CallbackSignal struct {
	SignalID string
	Error    string
	Data     interface{}
}

const (
	// 任务队列常量
	AcceptanceOrderTaskQueue = "ACCEPTANCE_ORDER_TASK_QUEUE_1"
	RackingTicketTaskQueue   = "RACKING_TICKET_TASK_QUEUE"

	// 工作流ID前缀常量
	PrefixAcceptanceOrderWorkflowID = "acceptance_order_"
	PrefixRackingTicketWorkflowID   = "racking_order_"
	// 工作流控制信号名称常量
	WorkflowControlSignalName = "workflow_control_signal" // 工作流控制信号名称
	SIG_AcceptanceOrder       = "acceptance_order_signal" // 验收工单通过信号
	SIG_RackingTicket         = "racking_order_signal"    // 上架工单更新信号
	// 工作流活动超时常量
	ActivityStartToCloseTimeout = 24 * time.Hour
	ActivityHeartbeatTimeout    = 10 * time.Second

	// 重试策略常量
	InitialInterval    = 1 * time.Second
	BackoffCoefficient = 2.0
	MaximumInterval    = 100 * time.Second
	MaxAttempts        = 3
)

// AcceptanceOrderWorkflowInput 验收工单工作流输入
type AcceptanceOrderWorkflowInput struct {
	OrderID       uint               `json:"order_id"`
	InitiatorID   uint               `json:"initiator_id"`
	InitiatorName string             `json:"initiatorName"`
	OperatorID    uint               `json:"operator_id"`   // 操作人ID
	OperatorName  string             `json:"operator_name"` // 操作人姓名
	Completed     bool               `json:"completed"`     // 是否已完成
	Status        common.OrderStatus `json:"status"`
}

// rackingTicketWorkflowInput 验收工单工作流输入
type RackingTicketWorkflowInput struct {
	TicketID     uint               `json:"ticket_id"`
	OperatorID   uint               `json:"operator_id"`   // 操作人ID
	OperatorName string             `json:"operator_name"` // 操作人姓名
	Completed    bool               `json:"completed"`     // 是否已完成
	Status       common.OrderStatus `json:"status"`
}

type WorkflowControlSignal struct {
	Stage        string                 // 目标阶段
	Status       string                 //状态
	OperatorID   uint                   // 操作人ID
	OperatorName string                 // 操作人姓名
	Comments     string                 // 备注
	Data         map[string]interface{} `json:"data"` // 额外数据
	Timestamp    int64                  // 操作时间戳
}

type AcceptanceOrderSignal struct {
	Stage        string                 // 目标阶段
	Status       string                 //状态
	OperatorID   uint                   // 操作人ID
	OperatorName string                 // 操作人姓名
	Comments     string                 // 备注
	Data         map[string]interface{} `json:"data"` // 额外数据
	Timestamp    int64                  // 操作时间戳
}
