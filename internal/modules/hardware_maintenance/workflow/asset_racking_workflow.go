package workflow

import (
	"backend/internal/modules/hardware_maintenance/common"
	model "backend/internal/modules/hardware_maintenance/model/racking"
	"errors"
	"fmt"
	"time"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

// RackingTicketWorkflow 验收工单工作流
func RackingTicketWorkflow(ctx workflow.Context, input RackingTicketWorkflowInput) error {
	if input.Completed {
		logger := workflow.GetLogger(ctx)
		logger.Info("工作流已完成，直接返回", "racking_ticket", input.TicketID)
		return nil
	}

	// 配置活动选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: ActivityStartToCloseTimeout,
		HeartbeatTimeout:    ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        InitialInterval,
			BackoffCoefficient:     BackoffCoefficient,
			MaximumInterval:        MaximumInterval,
			MaximumAttempts:        MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	logger := workflow.GetLogger(ctx)
	logger.Info("rackingTicket workflow started", "OrderID", input.TicketID)

	state := &OrderWorkFlowState{
		OrderID:       input.TicketID,
		OperatorID:    input.OperatorID,
		CurrentStatus: common.OrderStatus_Pending,
	}

	// TODO 发送飞书通知，参考出库

	var isWorkflowComplete bool
	completeChan := make(chan struct{})

	// 这测SetUpdateHandler,接收更新信号
	err := workflow.SetUpdateHandler(ctx, SIG_RackingTicket, func(ctx workflow.Context, signal WorkflowControlSignal) error {
		logger.Info("收到UpdateHandler信号",
			"Racking TicketID", input.TicketID,
			"current state", state.CurrentStatus,
			"signal status", signal.Status,
			"operator", signal.OperatorName)
		ctx = workflow.WithActivityOptions(ctx, ao)

		fmt.Println("正在处理工作流信号")
		// 更新状态
		err := handleRackingWorkFlowSignal(ctx, state, signal)
		if err != nil {
			return err
		}

		if state.IsCompleted {
			isWorkflowComplete = true
			close(completeChan)
		}
		return nil
	})

	if err != nil {
		logger.Info("SetQueryHandler failed.", "error", err)
		return err
	}

	err = workflow.Await(ctx, func() bool {
		select {
		case <-completeChan:
			return true
		default:
			return isWorkflowComplete
		}
	})

	if err != nil {
		logger.Error("Workflow await failed.", "error:", err, zap.Error(err))
		return err
	}
	return nil
}

func handleRackingWorkFlowSignal(ctx workflow.Context, state *OrderWorkFlowState, signal WorkflowControlSignal) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("收到状态变更信号",
		"TicketID", state.OrderID,
		"From", state.CurrentStatus,
		"To", signal.Status,
		"Operator", signal.OperatorName)
	//fmt.Println("接收到信号%v\n", signal)

	// 更新操作时间
	state.LastOperationTime = workflow.Now(ctx)

	// check信号状态
	ticket, err := getRackingTicketByIdActivity(ctx, state.OrderID)
	if err != nil {
		logger.Error("获取工单状态失败", "OrderID", state.OrderID, "Error", err)
		state.LastError = fmt.Sprintf("获取工单状态失败: %v", err)
		return err
	}

	currentStatus, targetStatus := common.OrderStatus(ticket.Status), common.OrderStatus(signal.Status)
	if !common.CheckStatusTransfer(currentStatus, targetStatus, common.RackingTicketStatusTransferMap) {
		errorMsg := fmt.Sprintf("非法的状态转变: %s -> %s", ticket.Status, signal.Status)
		logger.Error(errorMsg, "currentStatus", ticket.Status, "targetStatus", signal.Status)
		state.LastError = errorMsg
		return errors.New(errorMsg)
	}

	// 执行更新rackingTicket活动
	if err := updateRackingTicketStatusActivity(ctx, ticket, signal); err != nil {
		errorMsg := fmt.Sprintf("状态更新失败: %v", err)
		logger.Error(errorMsg, "OrderID", state.OrderID, "Error", err)
		state.LastError = errorMsg
		return err
	}

	// 清除之前的错误
	state.LastError = ""
	newStatus := common.OrderStatus(signal.Status)

	// 执行状态特定逻辑
	switch newStatus {
	case common.OrderStatus_Approved:
		afterApproved(ctx, state, signal)
	case common.OrderStatus_Canceled:
		afterCanceled(ctx, state, signal)
	case common.OrderStatus_Rejected:
		afterRejected(ctx, state, signal)
		state.IsCompleted = true
	case common.OrderStatus_In_Progress:
		afterInProgress(ctx, state, signal)
	//case common.OrderStatus_Collected:
	//	if err := afterCollected(ctx, state, signal); err != nil {
	//		return err
	//	}
	case common.OrderStatus_Completed:
		afterCompleted(ctx, state, signal)
		state.IsCompleted = true

	default:
		logger.Warn("未知状态", "Status", newStatus)
	}

	// 记一下操作历史
	if err := recordOperationHistoryActivity(ctx, ticket.ID, signal.OperatorID, signal.OperatorName, string(state.CurrentStatus), signal.Status, signal.Comments, state.LastOperationTime); err != nil {
		errorMsg := fmt.Sprintf("状态更新失败: %v", err)
		logger.Error(errorMsg, "OrderID", state.OrderID, "Error", err)
		state.LastError = errorMsg
		return err
	}
	// 更新工作流状态
	state.CurrentStatus = newStatus
	return err
}

func getRackingTicketByIdActivity(ctx workflow.Context, ticketID uint) (*model.AssetRackingTicket, error) {
	var ticket model.AssetRackingTicket
	err := workflow.ExecuteActivity(ctx, "GetRackingTicketByIdActivity", ticketID).Get(ctx, &ticket)
	return &ticket, err
}

func updateRackingTicketStatusActivity(ctx workflow.Context, ticket *model.AssetRackingTicket, signal WorkflowControlSignal) error {
	err := workflow.ExecuteActivity(ctx, "UpdateRackingTicketStatusActivity",
		ticket, signal.OperatorID, signal.OperatorName, signal.Data).Get(ctx, nil)
	if err != nil {
		fmt.Println("workflow.ExecuteActivity error, err is:", err)
		return err
	}
	return err
}

func recordOperationHistoryActivity(ctx workflow.Context, ticketID uint, operatorId uint,
	operatorName string, preStatus, curStatus, remark string, operationTime time.Time) error {
	err := workflow.ExecuteActivity(ctx, "RecordOperationHistoryActivity",
		ticketID, operatorId, operatorName, preStatus, curStatus, remark, operationTime).Get(ctx, nil)
	if err != nil {
		fmt.Println("workflow.ExecuteActivity error, err is:", err)
		return err
	}
	return err
}
