package activities

import (
	"backend/internal/modules/hardware_maintenance/common/types"
	racking2 "backend/internal/modules/hardware_maintenance/model/racking"
	"backend/internal/modules/hardware_maintenance/service/racking"
	"context"
	"encoding/json"
	"errors"
	"time"

	"go.temporal.io/sdk/activity"
	"gorm.io/gorm"
)

type RackingTicketActivity struct {
	rackService racking.RackingService
	db          *gorm.DB
}

func InitRackingTicketActivity(rackService racking.RackingService, db *gorm.DB) *RackingTicketActivity {
	return &RackingTicketActivity{
		rackService: rackService,
		db:          db,
	}
}

// UpdateAcceptanceOrderStatusActivity 更新状态
func (a *RackingTicketActivity) UpdateRackingTicketStatusActivity(ctx context.Context, ticket *racking2.AssetRackingTicket, operatorID uint, operatorName string, data map[string]interface{}) error {
	// 1. 修改验收单状态
	logger := activity.GetLogger(ctx)
	var (
		err error
		req types.TransitionRackingTicketRequest
	)
	// 解析req
	// 从data中解析出数据
	str, ok := data["req"].(string)
	if !ok {
		logger.Error("data[\"req\"].(string) fail")
		return errors.New("data[\"req\"].(string) fail")
	}

	err = json.Unmarshal([]byte(str), &req)
	if err != nil {
		logger.Error("json.Unmarshal([]byte(str), &req) fail", "err", err)
		return err
	}

	err = a.rackService.UpdateRackingTicketStatus(ctx, ticket.ID, operatorID, operatorName, req)
	return err
}

func (a *RackingTicketActivity) GetRackingTicketByIdActivity(ctx context.Context, id uint) (*racking2.AssetRackingTicket, error) {
	return a.rackService.GetRackingTicketByID(ctx, id)
}

func (a *RackingTicketActivity) RecordOperationHistoryActivity(ctx context.Context, id uint, operatorId uint,
	operatorName string, preStatus, curStatus, remark string, operationTime time.Time) error {
	return a.rackService.RecordOperationHistory(ctx, id, operatorId,
		operatorName, preStatus, curStatus, remark, operationTime)
}
