package racking

import (
	"backend/internal/common/constants"
	asset2 "backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/repository/asset"
	"backend/internal/modules/hardware_maintenance/common"
	"backend/internal/modules/hardware_maintenance/common/types"
	"backend/internal/modules/hardware_maintenance/model/racking"
	rack2 "backend/internal/modules/hardware_maintenance/repository/racking"
	"backend/internal/modules/hardware_maintenance/workflow"
	"backend/pkg/utils"
	"context"
	"errors"
	"fmt"
	"time"

	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

var StatusNameMap = map[common.OrderStatus]string{
	common.OrderStatus_Pending:     "待审核",
	common.OrderStatus_Approved:    "已审核",
	common.OrderStatus_In_Progress: "上架中",
	common.OrderStatus_Completed:   "已完结",
}

// RackingService 资产上架服务接口
type RackingService interface {
	GetRackingTicketByID(ctx context.Context, id uint) (*racking.AssetRackingTicket, error)
	GetRackingTicketByIDWithItems(ctx context.Context, id uint) (*racking.AssetRackingTicket, error)
	ListRackingTickets(ctx context.Context, filters types.RackingTicketFilter) ([]*racking.AssetRackingTicket, int64, error)
	UpdateRackingTicketStatus(ctx context.Context, id uint, operatorID uint, operatorName string, req types.TransitionRackingTicketRequest) error
	CreateRackingTicket(ctx context.Context, order *racking.AssetRackingTicket, items []*racking.AssetRackingItem, operatorID uint, operatorName string) error
	CompleteRackingTicket(ctx context.Context, id uint, req []types.RackItem) error

	// 记录操作历史
	RecordOperationHistory(ctx context.Context, id uint, operatorId uint,
		operatorName string, preStatus, curStatus,
		remark string, operationTime time.Time) error

	TriggerWorkflow(ctx context.Context, orderID uint, signal workflow.WorkflowControlSignal) error

	// 获取操作历史
	GetRackingOperationHistories(ctx context.Context, ticketID uint) ([]*racking.AssetRackingTicketHistory, error)
}

// rackingService 资产上架服务实现

type rackingService struct {
	repo             rack2.AssetRackingTicketRepository
	resourceRepo     asset.ResourceRepository
	deviceRepo       asset.DeviceRepository
	statusChangeRepo asset.StatusChangeRepository
	logger           *zap.Logger
	temporalClient   client.Client
}

// NewRackingService 创建资产上架服务
func NewRackingService(repo rack2.AssetRackingTicketRepository, resourceRepo asset.ResourceRepository, deviceRepo asset.DeviceRepository, statusChangeRepo asset.StatusChangeRepository, logger *zap.Logger, temporalClient client.Client) RackingService {
	return &rackingService{repo: repo, resourceRepo: resourceRepo, deviceRepo: deviceRepo, statusChangeRepo: statusChangeRepo, logger: logger, temporalClient: temporalClient}
}

// GetRackingTicketByID 根据ID获取资产上架工单
func (s *rackingService) GetRackingTicketByID(ctx context.Context, id uint) (*racking.AssetRackingTicket, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *rackingService) GetRackingTicketByIDWithItems(ctx context.Context, id uint) (*racking.AssetRackingTicket, error) {
	return s.repo.GetWithItems(ctx, id)
}

// ListRackingTickets 分页获取资产上架工单列表
func (s *rackingService) ListRackingTickets(ctx context.Context, filters types.RackingTicketFilter) ([]*racking.AssetRackingTicket, int64, error) {
	return s.repo.List(ctx, filters)
}

// UpdateRackingTicketStatus 更新资产上架工单状态
func (s *rackingService) UpdateRackingTicketStatus(ctx context.Context, id uint, operatorID uint, operatorName string, req types.TransitionRackingTicketRequest) error {
	// 验证状态是否有效
	now := time.Now()
	ticket, err := s.repo.GetWithItems(ctx, id)
	if err != nil {
		return err
	}
	target := common.OrderStatus(req.Status)
	current := common.OrderStatus(ticket.Status)
	if !common.CheckStatusTransfer(current, target, common.RackingTicketStatusTransferMap) {
		return fmt.Errorf("无法将状态为%s的工单变为%s", StatusNameMap[current], StatusNameMap[target])
	}

	switch common.OrderStatus(req.Status) {
	case common.OrderStatus_Rejected, common.OrderStatus_Approved:
		err = s.repo.UpdateFieldsByID(ctx, id, map[string]interface{}{
			"status":        req.Status,
			"approver_id":   operatorID,
			"approver_name": operatorName,
			"approved_time": now,
		})
		if err != nil {
			return err
		}
	case common.OrderStatus_In_Progress:
		err = s.repo.UpdateFieldsByID(ctx, id, map[string]interface{}{
			"status":       req.Status,
			"handler_id":   operatorID,
			"handler_name": operatorName,
		})
	case common.OrderStatus_Completed:
		// 修改审核状态
		ticket.HandlerID = operatorID
		ticket.HandlerName = operatorName
		ticket.CompletedTime = &now
		ticket.Status = req.Status
		ticket.Comment = req.Comment

		assetIDs := make([]uint, 0, len(req.RackItems))
		assetItemMap := make(map[uint]types.RackItem)
		for _, item := range req.RackItems {
			assetItemMap[item.AssetID] = item
			assetIDs = append(assetIDs, item.AssetID)
		}

		// 获取resource
		// TODO 改成查找资产状态，不要从resource中preload资产信息
		resources, err := s.resourceRepo.ListByAssetIDsWithDevice(ctx, assetIDs)
		if err != nil {
			return err
		}
		for i := range resources {
			item := assetItemMap[resources[i].AssetID]
			resources[i].CabinetID = item.CabinetID
			resources[i].Project = ticket.Project
			resources[i].RackPosition = item.RackPosition
			resources[i].RackingTime = utils.Date(now)
		}

		// 填充items
		ticketItems := ticket.Items
		for i := range ticketItems {
			item := assetItemMap[ticketItems[i].AssetID]
			ticketItems[i].CabinetID = item.CabinetID
			ticketItems[i].RoomID = item.RoomID
			ticketItems[i].RackPosition = item.RackPosition
			ticketItems[i].VpcMAC = item.VpcMAC
			ticketItems[i].BmcMAC = item.BmcMAC
		}

		// 使用事务对数据进行更新
		// TODO 加operatorID等以进行log存储
		err = s.updateRackingTicketAndResource(ctx, ticket, ticketItems, resources, operatorID, operatorName, now)
		return err
	}
	return err

}

func (s *rackingService) CreateRackingTicket(ctx context.Context, ticket *racking.AssetRackingTicket, items []*racking.AssetRackingItem, operatorID uint, operatorName string) error {
	if err := s.repo.CreateTicketAndItems(ctx, ticket, items); err != nil {
		return err
	}
	now := time.Now()
	// 记录创建操作历史
	if err := s.RecordOperationHistory(ctx, ticket.ID, operatorID, operatorName,
		"", string(common.OrderStatus_Pending),
		ticket.Remark, now); err != nil {
		s.logger.Warn("s.RecordOperationHistory Fail, err: ", zap.Error(err))
		return err
	}
	// 启动工作流
	if s.temporalClient != nil {
		workflowID := fmt.Sprintf("%s%d", workflow.PrefixRackingTicketWorkflowID, ticket.ID)
		_, err := s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
			ID:        workflowID,
			TaskQueue: workflow.RackingTicketTaskQueue,
		}, workflow.RackingTicketWorkflow, workflow.RackingTicketWorkflowInput{
			TicketID:     ticket.ID,
			OperatorID:   ticket.ApplicantID,
			OperatorName: ticket.ApplicantName,
			Status:       common.OrderStatus_Pending,
			Completed:    false,
		})
		if err != nil {
			// 仅记录日志，不影响主流程
			fmt.Printf("启动工作流失败: %v\n", err)
		}
	} else {
		return errors.New("工作流未启动")
	}
	return nil

}

func (s *rackingService) CompleteRackingTicket(ctx context.Context, id uint, req []types.RackItem) error {
	return nil
}

// TriggerWorkflow 给工作流发送信号，监听发送信号后的结果
func (s *rackingService) TriggerWorkflow(ctx context.Context, orderID uint, signal workflow.WorkflowControlSignal) error {

	// 构造工作流ID
	workflowID := fmt.Sprintf("%s%d", workflow.PrefixRackingTicketWorkflowID, orderID)

	s.logger.Info("准备触发工作流信号",
		zap.String("workflowID", workflowID))

	// 更新工作流状态
	updateOptions := client.UpdateWorkflowOptions{
		WorkflowID:   workflowID,
		UpdateName:   workflow.SIG_RackingTicket,
		WaitForStage: client.WorkflowUpdateStageCompleted,
		Args:         []interface{}{signal},
	}

	updateHandle, err := s.temporalClient.UpdateWorkflow(ctx, updateOptions)
	if err != nil {
		s.logger.Error("更新工作流状态失败",
			zap.Error(err),
			zap.String("workflowID", workflowID),
			zap.String("status", signal.Status),
			zap.String("operatorName", signal.OperatorName),
			zap.Uint("OrderId", orderID))
		return fmt.Errorf("更新工作流状态失败: %w", err)
	}

	err = updateHandle.Get(ctx, nil)
	if err != nil {
		s.logger.Error("Unable to get update result", zap.Error(err))
		return err
	}

	s.logger.Info("成功更新工作流",
		zap.String("workflowID", workflowID),
		zap.String("status", signal.Status),
		zap.String("operatorName", signal.OperatorName),
		zap.Uint("OrderId", orderID))

	return nil
}

func (s *rackingService) updateRackingTicketAndResource(ctx context.Context, ticket *racking.AssetRackingTicket, items []*racking.AssetRackingItem, resources []*asset2.Resource, operatorID uint, operatorName string, operationTime time.Time) error {
	var (
		workFlowID = fmt.Sprintf("%s%d", workflow.PrefixRackingTicketWorkflowID, ticket.ID)
		reason     string
	)

	tx := s.repo.GetDB().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	rackingTicketRepo := s.repo.WithTx(tx)
	err := rackingTicketRepo.Update(ctx, ticket)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 更新记录表项
	if err := rackingTicketRepo.UpdateItems(ctx, ticket.ID, items); err != nil {
		tx.Rollback()
		return err
	}

	// 更新resource相关的数据以及device资产状态,
	resourceRepo := s.resourceRepo.WithTx(tx)
	deviceRepo := s.deviceRepo.WithTx(tx)
	statusChangeRepo := s.statusChangeRepo.WithTx(tx)
	newAssetStatus := constants.AssetStatusOnRack

	for _, res := range resources {
		reason = fmt.Sprintf("资产上架，项目：%s，包间：%s，机柜：%s，机架位：%d", res.Project, res.Cabinet.Room.Name, res.Cabinet.Name, res.RackPosition)
		// 记录changeLog
		statusChangeLog := &asset2.StatusChangeLog{
			AssetID:           res.AssetID,
			OldAssetStatus:    res.Device.AssetStatus,
			NewAssetStatus:    newAssetStatus,
			OldBizStatus:      res.BizStatus,
			NewBizStatus:      res.BizStatus,
			OldHardwareStatus: res.Device.HardwareStatus,
			NewHardwareStatus: res.Device.HardwareStatus,
			ChangeReason:      reason,
			OperatorID:        operatorID,
			OperatorName:      operatorName,
			ApproverID:        ticket.ApproverID,
			ApproverName:      ticket.ApproverName,
			WorkflowID:        workFlowID,
			Source:            constants.SourceTypeOnRack,
			TicketNo:          ticket.TicketNo,
		}

		err = statusChangeRepo.LogStatusChange(ctx, statusChangeLog)
		if err != nil {
			tx.Rollback()
			return err
		}

		err = resourceRepo.Update(ctx, res)
		if err != nil {
			tx.Rollback()
			return err
		}
		// 设置成闲置中
		res.Device.AssetStatus = newAssetStatus
		res.Device.LastStatusChange = utils.Date(operationTime)
		err = deviceRepo.Update(ctx, &res.Device)
		if err != nil {
			tx.Rollback()
			return err
		}

	}

	tx.Commit()
	return nil
}

// RecordOperationHistory 记录操作历史
func (s *rackingService) RecordOperationHistory(ctx context.Context, ticketID uint, operatorId uint,
	operatorName string, preStatus, newStatus, remark string, operationTime time.Time) error {
	activityCategory := ""
	// 查一下上一个记录
	if newStatus != string(common.OrderStatus_Pending) {
		lastHistory, err := s.repo.GetLastOperationHistoryByTicketID(ctx, ticketID)
		if err != nil {
			return err
		}
		// 修改上一个状态的持续时间
		lastHistory.Duration = int(operationTime.Sub(lastHistory.CreatedAt).Minutes())
		err = s.repo.SaveOperationHistory(ctx, lastHistory)
		if err != nil {
			return err
		}
	}

	switch newStatus {
	case string(common.OrderStatus_Pending):
		activityCategory = common.History_Activity_Create
	case string(common.OrderStatus_Approved):
		activityCategory = common.History_Activity_Approve
	case string(common.OrderStatus_Rejected):
		activityCategory = common.History_Activity_Reject
	case string(common.OrderStatus_In_Progress):
		activityCategory = common.History_Activity_Handle
	case string(common.OrderStatus_Completed):
		activityCategory = common.History_Activity_Complete
	}

	history := &racking.AssetRackingTicketHistory{
		RackingTicketID:  ticketID,
		PreviousStatus:   preStatus,
		NewStatus:        newStatus,
		OperatorID:       operatorId,
		OperatorName:     operatorName,
		OperationTime:    operationTime,
		ActivityCategory: activityCategory,
		Remarks:          remark,
	}
	return s.repo.SaveOperationHistory(ctx, history)
}

func (s *rackingService) GetRackingOperationHistories(ctx context.Context, ticketID uint) ([]*racking.AssetRackingTicketHistory, error) {
	return s.repo.GetOperationHistoriesByTicketID(ctx, ticketID)
}
