package controller

import (
	"backend/internal/modules/server/model"
	"backend/internal/modules/server/service"
	"backend/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type GpuServerController struct {
	gpuService *service.GpuServerService
}

func NewGpuServerController(s *service.GpuServerService) *GpuServerController {
	return &GpuServerController{
		gpuService: s,
	}
}

// CreateGpuServer godoc
// @Summary 创建 GPU 服务器记录
// @Description 创建一条新的 GPU 服务器记录
// @Tags GPU服务器
// @Accept json
// @Produce json
// @Param req body model.GpuServer true "GPU服务器记录"
// @Success 200 {object} model.GpuServer "创建成功"
// @Failure 400 {string} string "请求参数错误"
// @Router /cmdb/gpuServer [post]
func (ctrl *GpuServerController) CreateGpuServer(c *gin.Context) {
	var req model.GpuServer
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Response(c, http.StatusBadRequest, nil, "请求参数错误")
		return
	}
	if err := ctrl.gpuService.CreateGpuServer(c.Request.Context(), &req); err != nil {
		response.Response(c, http.StatusBadRequest, nil, err.Error())
		return
	}
	response.Success(c, req, "创建成功")
}

// GetGpuServer godoc
// @Summary 获取 GPU 服务器记录
// @Description 根据 ID 获取 GPU 服务器记录
// @Tags GPU服务器
// @Security Bearer
// @Produce json
// @Param id path int true "GPU服务器记录ID"
// @Success 200 {object} model.GpuServer "获取成功"
// @Failure 404 {string} string "记录未找到"
// @Router /cmdb/gpuServer/{id} [get]
func (ctrl *GpuServerController) GetGpuServer(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		response.Response(c, http.StatusBadRequest, nil, "ID格式错误")
		return
	}

	gpu, err := ctrl.gpuService.GetGpuServerByID(c.Request.Context(), uint(id))
	if err != nil {
		response.Response(c, http.StatusNotFound, nil, "记录未找到")
		return
	}
	response.Success(c, gpu, "获取成功")
}

// UpdateGpuServerPut godoc
// @Summary 更新 GPU 服务器记录（全部更新）
// @Description 前端传递完整的记录数据进行更新
// @Tags GPU服务器
// @Security Bearer
// @Accept json
// @Produce json
// @Param id path int true "GPU服务器记录ID"
// @Param req body model.GpuServer true "完整的GPU服务器记录"
// @Success 200 {object} model.GpuServer "更新成功"
// @Failure 400 {string} string "请求参数错误或记录不存在"
// @Router /cmdb/gpuServer/{id} [put]
func (ctrl *GpuServerController) UpdateGpuServerPut(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		response.Response(c, http.StatusBadRequest, nil, "ID格式错误")
		return
	}

	var req model.GpuServer
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Response(c, http.StatusBadRequest, nil, "请求参数错误")
		return
	}

	// 先获取现有记录
	existing, err := ctrl.gpuService.GetGpuServerByID(c.Request.Context(), uint(id))
	if err != nil {
		response.Response(c, http.StatusNotFound, nil, "记录未找到")
		return
	}

	// 合并更新：仅更新传入的非空字段
	if req.Project != "" {
		existing.Project = req.Project
	}
	if req.Hostname != "" {
		existing.Hostname = req.Hostname
	}
	if req.SN != "" {
		existing.SN = req.SN
	}
	// 依此类推：对其它字段也执行类似的非空判断

	// 调用服务层更新记录
	if err := ctrl.gpuService.UpdateGpuServer(c.Request.Context(), existing); err != nil {
		response.Response(c, http.StatusBadRequest, nil, err.Error())
		return
	}

	updated, err := ctrl.gpuService.GetGpuServerByID(c.Request.Context(), uint(id))
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "更新后查询记录失败")
		return
	}

	response.Success(c, updated, "更新成功")
}

// DeleteGpuServer godoc
// @Summary 删除 GPU 服务器记录
// @Description 根据 ID 删除 GPU 服务器记录
// @Tags GPU服务器
// @Security Bearer
// @Produce json
// @Param id path int true "GPU服务器记录ID"
// @Success 200 {string} string "删除成功"
// @Failure 404 {string} string "记录未找到"
// @Router /cmdb/gpuServer/{id} [delete]
func (ctrl *GpuServerController) DeleteGpuServer(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		response.Response(c, http.StatusBadRequest, nil, "ID格式错误")
		return
	}
	if err := ctrl.gpuService.DeleteGpuServer(c.Request.Context(), uint(id)); err != nil {
		response.Response(c, http.StatusNotFound, nil, err.Error())
		return
	}
	response.Success(c, nil, "删除成功")
}

// ListGpuServer godoc
// @Summary 查询 GPU 服务器记录
// @Description 根据各字段的查询条件过滤 GPU 服务器记录，同时支持分页查询；返回的数据包含记录列表（items）和总记录数（total），未传查询条件时返回所有记录
// @Tags GPU服务器
// @Security Bearer
// @Produce json
// @Param project query string false "项目"
// @Param data_center query string false "机房"
// @Param package query string false "包间"
// @Param cabinet query string false "机柜"
// @Param hostname query string false "主机名"
// @Param sn query string false "SN"
// @Param primary_owner query string false "主责任人"
// @Param backup_owner query string false "备份责任人"
// @Param vpc_ip query string false "VPC_IP"
// @Param vpc_mask query string false "VPC掩码"
// @Param bmc_ip query string false "BMC_IP"
// @Param bmc_mask query string false "BMC掩码"
// @Param bmc_gateway query string false "BMC网关"
// @Param cpu_type query string false "CPU类型"
// @Param vpc_eth0_mac query string false "VPC_eth0_MAC"
// @Param bmc_mac query string false "BMC_MAC"
// @Param device_type query string false "设备类型"
// @Param height query int false "高度"
// @Param gpu_model query string false "GPU卡型号"
// @Param cluster query string false "集群"
// @Param manufacturer query string false "厂商"
// @Param model query string false "型号"
// @Param business_status query string false "业务状态"
// @Param asset_status query string false "资产状态"
// @Param is_backup query bool false "是否备机"
// @Param page query int false "当前页码"
// @Param pageSize query int false "每页记录数"
// @Success 200 {object} map[string]interface{} "查询成功，返回数据格式为 {items: [...], total: 数值}"
// @Router /cmdb/gpuServer [get]
func (ctrl *GpuServerController) ListGpuServer(c *gin.Context) {
	var query model.GpuServerQuery
	// 自动绑定查询参数，注意 gin 默认支持绑定 URL 中的数字字符串到 int 类型
	if err := c.ShouldBindQuery(&query); err != nil {
		response.Response(c, http.StatusBadRequest, nil, "查询参数错误")
		return
	}

	// 如果需要，也可以手动解析分页参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil {
			query.Page = page
		}
	}
	if pageSizeStr := c.Query("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil {
			query.PageSize = ps
		}
	}

	gpuList, total, err := ctrl.gpuService.QueryGpuServers(c.Request.Context(), &query)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "查询记录失败")
		return
	}
	response.Success(c, gin.H{
		"items": gpuList,
		"total": total,
	}, "查询成功")
}
