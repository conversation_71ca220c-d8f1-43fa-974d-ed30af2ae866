package controller

import (
	"backend/internal/modules/server/service"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ServerController 定义服务器相关接口控制器
type ServerController struct {
	serverService service.ServerService
}

// NewServerController 创建 ServerController 控制器实例
func NewServerController(serverService service.ServerService) *ServerController {
	return &ServerController{serverService: serverService}
}

// GetServerInfoHandler 处理 GET /api/servers/:sn 请求，返回服务器详细信息
func (sc *ServerController) GetServerInfoHandler(c *gin.Context) {
	sn := c.Param("sn")
	serverInfo, err := sc.serverService.GetServerInfo(sn)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}
	c.<PERSON><PERSON>(http.StatusOK, serverInfo)
}
