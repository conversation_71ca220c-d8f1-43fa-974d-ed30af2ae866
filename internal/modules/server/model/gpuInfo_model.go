package model

import "gorm.io/gorm"

// GpuServer 表示 GPU 服务器记录
type GpuServer struct {
	gorm.Model
	// 基本信息
	Project    string `gorm:"type:varchar(100);not null" json:"project"`     // 项目
	DataCenter string `gorm:"type:varchar(100);not null" json:"data_center"` // 机房
	Package    string `gorm:"type:varchar(100)" json:"package"`              // 包间
	Cabinet    string `gorm:"type:varchar(100)" json:"cabinet"`              // 机柜
	Hostname   string `gorm:"type:varchar(100);not null" json:"hostname"`    // 主机名
	SN         string `gorm:"type:varchar(100);unique;not null" json:"sn"`   // SN

	// 责任信息
	PrimaryOwner string `gorm:"type:varchar(50);not null" json:"primary_owner"` // 主责任人
	BackupOwner  string `gorm:"type:varchar(50)" json:"backup_owner"`           // 备份责任人

	// 网络及硬件信息
	VPcIP        string `gorm:"column:vpc_ip;type:varchar(50);not null" json:"vpc_ip"`    // VPC_IP
	VPCMask      string `gorm:"column:vpc_mask;type:varchar(50)" json:"vpc_mask"`         // VPC掩码
	BMCIP        string `gorm:"column:bmc_ip;type:varchar(50)" json:"bmc_ip"`             // BMC_IP
	BMCMask      string `gorm:"column:bmc_mask;type:varchar(50)" json:"bmc_mask"`         // BMC掩码
	BMCGateway   string `gorm:"column:bmc_gateway;type:varchar(50)" json:"bmc_gateway"`   // BMC网关
	CPUType      string `gorm:"column:cpu_type;type:varchar(50)" json:"cpu_type"`         // CPU类型
	VPCEth0Mac   string `gorm:"column:vpc_eth0_mac;type:varchar(50)" json:"vpc_eth0_mac"` // VPC_eth0_MAC
	BMCMac       string `gorm:"column:bmc_mac;type:varchar(50)" json:"bmc_mac"`           // BMC_MAC
	DeviceType   string `gorm:"column:device_type;type:varchar(50)" json:"device_type"`   // 设备类型
	Height       int    `gorm:"type:int" json:"height"`                                   // 高度
	GPUModel     string `gorm:"column:gpu_model;type:varchar(50)" json:"gpu_model"`       // GPU卡型号
	Cluster      string `gorm:"type:varchar(50)" json:"cluster"`                          // 集群
	Manufacturer string `gorm:"type:varchar(50)" json:"manufacturer"`                     // 厂商
	MachineModel string `gorm:"type:varchar(50)" json:"machine_model"`                    // 型号

	// 状态信息
	BusinessStatus     string `gorm:"type:varchar(50)" json:"business_status"` // 业务状态
	AssetStatus        string `gorm:"type:varchar(50)" json:"asset_status"`    // 资产状态
	IsBackup           bool   `json:"is_backup"`                               // 是否备机
	StatusChangeRecord string `gorm:"type:text" json:"status_change_record"`   // 机器状态变更记录

}

// GpuServerQuery 用于按各字段过滤查询 GPU 服务器记录
type GpuServerQuery struct {
	Project        string `form:"project" json:"project"`
	DataCenter     string `form:"data_center" json:"data_center"`
	Package        string `form:"package" json:"package"`
	Cabinet        string `form:"cabinet" json:"cabinet"`
	Hostname       string `form:"hostname" json:"hostname"`
	SN             string `form:"sn" json:"sn"`
	PrimaryOwner   string `form:"primary_owner" json:"primary_owner"`
	BackupOwner    string `form:"backup_owner" json:"backup_owner"`
	VpcIP          string `form:"vpc_ip" json:"vpc_ip"`
	VpcMask        string `form:"vpc_mask" json:"vpc_mask"`
	BmcIP          string `form:"bmc_ip" json:"bmc_ip"`
	BmcMask        string `form:"bmc_mask" json:"bmc_mask"`
	BmcGateway     string `form:"bmc_gateway" json:"bmc_gateway"`
	CPUType        string `form:"cpu_type" json:"cpu_type"`
	VpcEth0Mac     string `form:"vpc_eth0_mac" json:"vpc_eth0_mac"`
	BmcMac         string `form:"bmc_mac" json:"bmc_mac"`
	DeviceType     string `form:"device_type" json:"device_type"`
	Height         *int   `form:"height" json:"height"`
	GPUModel       string `form:"gpu_model" json:"gpu_model"`
	Cluster        string `form:"cluster" json:"cluster"`
	Manufacturer   string `form:"manufacturer" json:"manufacturer"`
	MachineModel   string `gorm:"type:varchar(50)" json:"machine_model"`
	BusinessStatus string `form:"business_status" json:"business_status"`
	AssetStatus    string `form:"asset_status" json:"asset_status"`
	IsBackup       *bool  `form:"is_backup" json:"is_backup"`

	// 分页参数
	Page     int `form:"page" json:"page"`
	PageSize int `form:"page_size" json:"page_size"`
}
