package model

import (
	"time"
)

// ServerInfo 聚合服务器详细信息，用于前端展示
type ServerInfo struct {
	SN               string    `json:"sn"`                // 资产SN
	AssetID          string    `json:"asset_id"`          // 资产编码
	TemplateName     string    `json:"template_name"`     // 套餐名称
	HostName         string    `json:"host_name"`         // 主机名（若无额外字段，则默认使用 SN）
	CPUInfo          string    `json:"cpu_info"`          // CPU信息
	MemoryInfo       string    `json:"memory_info"`       // 内存信息（附单位，如 GB）
	HDDInfo          string    `json:"hdd_info"`          // HDD硬盘信息
	SSDInfo          string    `json:"ssd_info"`          // SSD信息
	GPUInfo          string    `json:"gpu_info"`          // GPU信息
	PowerInfo        string    `json:"power_info"`        // 电源信息
	NICInfo          string    `json:"nic_info"`          // 网卡信息
	DeviceU          int       `json:"device_u"`          // 设备 U 数
	ServerType       string    `json:"server_type"`       // 服务器类型
	ServerStatus     string    `json:"server_status"`     // 服务器状态
	ArrivalTime      time.Time `json:"arrival_time"`      // 到货时间
	WarrantyExpire   time.Time `json:"warranty_expire"`   // 过保时间
	RegionName       string    `json:"region_name"`       // 地理位置 – Region名称
	AZName           string    `json:"az_name"`           // 地理位置 – AZ名称
	DataCenterName   string    `json:"data_center_name"`  // 机房名称
	RoomName         string    `json:"room_name"`         // 房间名称
	RackPosition     string    `json:"rack_position"`     // 机架位名称
	SupplierName     string    `json:"supplier_name"`     // 供应商名称
	Brand            string    `json:"brand"`             // 厂商名称
	OrderNumber      string    `json:"order_number"`      // 订单号（或采购合同号）
	OSVersion        string    `json:"os_version"`        // 系统版本（操作系统版本）
	PowerConsumption string    `json:"power_consumption"` // 服务器功耗/W
	ParentSN         string    `json:"parent_sn"`         // 父设备 SN（JBOD）
	UplinkNetworkSN  string    `json:"uplink_network_sn"` // 上联网络设备 SN
	CreateTime       time.Time `json:"create_time"`       // 创建时间
	UpdateTime       time.Time `json:"update_time"`       // 最后更新时间
	InternalIP       string    `json:"internal_ip"`       // 内网 IP（仅内网）
	IPInfo           string    `json:"ip_info"`           // ip 地址信息
	ComponentInfo    string    `json:"component_info"`    // 配件信息（例如组件列表 JSON）
}
