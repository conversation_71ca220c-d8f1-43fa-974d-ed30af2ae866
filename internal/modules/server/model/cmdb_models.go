package model

import (
	"time"
)

// 资产设备表（包括服务器、网络设备等）
type AssetDevice struct {
	AssetID        string    `gorm:"column:asset_id;primaryKey" json:"asset_id"`    // 资产编号
	PurchaseOrder  string    `gorm:"column:purchase_order" json:"purchase_order"`   // 采购合同
	SN             string    `gorm:"column:sn" json:"sn"`                           // 资产SN
	Brand          string    `gorm:"column:brand" json:"brand"`                     // 厂商
	Model          string    `gorm:"column:model" json:"model"`                     // 型号
	PurchaseDate   time.Time `gorm:"column:purchase_date" json:"purchase_date"`     // 购买时间
	WarrantyExpire time.Time `gorm:"column:warranty_expire" json:"warranty_expire"` // 过保时间
	AssetStatus    string    `gorm:"column:asset_status" json:"asset_status"`       // 资产状态
	HardwareStatus string    `gorm:"column:hardware_status" json:"hardware_status"` // 硬件状态
	Price          float64   `gorm:"column:price" json:"price"`                     // 金额
	ResidualValue  float64   `gorm:"column:residual_value" json:"residual_value"`   // 残值
	AssetType      string    `gorm:"column:asset_type" json:"asset_type"`           // 资产类型
}

// 备件资产表
type AssetSpare struct {
	SpareID        string    `gorm:"column:spare_id;primaryKey" json:"spare_id"`    // 备件资产ID
	SN             string    `gorm:"column:sn" json:"sn"`                           // SN
	SpareType      string    `gorm:"column:type" json:"type"`                       // 类型（关键字 type 改为 SpareType）
	Model          string    `gorm:"column:model" json:"model"`                     // 型号
	PN             string    `gorm:"column:pn" json:"pn"`                           // PN
	SourceType     string    `gorm:"column:source_type" json:"source_type"`         // 来源类型
	RelatedAsset   string    `gorm:"column:related_asset" json:"related_asset"`     // 关联资产
	PurchaseDate   time.Time `gorm:"column:purchase_date" json:"purchase_date"`     // 购买时间
	AssetStatus    string    `gorm:"column:asset_status" json:"asset_status"`       // 资产状态
	HardwareStatus string    `gorm:"column:hardware_status" json:"hardware_status"` // 硬件状态
}

// 资源表（资产与物理位置关联）
type Resource struct {
	ResourceID   string `gorm:"column:resource_id;primaryKey" json:"resource_id"` // 资源ID
	SN           string `gorm:"column:sn" json:"sn"`                              // SN
	Cluster      string `gorm:"column:cluster" json:"cluster"`                    // 集群
	VpcIP        string `gorm:"column:vpc_ip" json:"vpc_ip"`                      // VPC_IP
	AssetID      string `gorm:"column:asset_id" json:"asset_id"`                  // 资产编号
	BizStatus    string `gorm:"column:biz_status" json:"biz_status"`              // 业务状态
	ResStatus    string `gorm:"column:res_status" json:"res_status"`              // 资源状态
	Project      string `gorm:"column:project" json:"project"`                    // 所属项目
	CabinetID    string `gorm:"column:cabinet_id" json:"cabinet_id"`              // 机柜ID
	RackPosition int    `gorm:"column:rack_position" json:"rack_position"`        // 机架位
	BmcIP        string `gorm:"column:bmc_ip" json:"bmc_ip"`                      // BMC_IP
	IsBackup     bool   `gorm:"column:is_backup" json:"is_backup"`                // 是否备机
}

// 服务器套餐模板（包含规格信息、冗余的配件列表）
type MachineTemplate struct {
	TemplateID       string `gorm:"column:template_id;primaryKey" json:"template_id"`  // 模板ID
	TemplateName     string `gorm:"column:template_name" json:"template_name"`         // 模板名称
	CPUModel         string `gorm:"column:cpu_model" json:"cpu_model"`                 // CPU型号
	MemoryCapacity   int    `gorm:"column:memory_capacity" json:"memory_capacity"`     // 内存容量(GB)
	GPUModel         string `gorm:"column:gpu_model" json:"gpu_model"`                 // GPU型号
	DiskType         string `gorm:"column:disk_type" json:"disk_type"`                 // 存储类型
	ComponentList    string `gorm:"column:component_list" json:"component_list"`       // 配件列表（JSON格式）
	TemplateCategory string `gorm:"column:template_category" json:"template_category"` // 模板类别
}

// 服务器资产扩展表（记录服务器专属信息，如 IP、操作系统及关联模板）
type ServerAsset struct {
	ServerID   string `gorm:"column:server_id;primaryKey" json:"server_id"` // 服务器ID
	AssetID    string `gorm:"column:asset_id" json:"asset_id"`              // 资产编号
	TemplateID string `gorm:"column:template_id" json:"template_id"`        // 套餐模板ID
	IP         string `gorm:"column:ip" json:"ip"`                          // IP地址
	OS         string `gorm:"column:os" json:"os"`                          // 操作系统
}

// 模板组件配置（描述模板内的各配件）
type TemplateComponent struct {
	TemplateID string `gorm:"column:template_id;primaryKey" json:"template_id"` // 模板ID
	ProductID  string `gorm:"column:product_id;primaryKey" json:"product_id"`   // 配件产品ID
	Quantity   int    `gorm:"column:quantity" json:"quantity"`                  // 配件数量
	Slot       string `gorm:"column:slot" json:"slot"`                          // 安装槽位
}

// 产品表（记录产品规格、PN、产品类别等）
type Product struct {
	ProductID       string `gorm:"column:product_id;primaryKey" json:"product_id"`  // 产品ID
	MaterialType    string `gorm:"column:material_type" json:"material_type"`       // 物料类型
	Brand           string `gorm:"column:brand" json:"brand"`                       // 品牌
	Model           string `gorm:"column:model" json:"model"`                       // 型号
	Spec            string `gorm:"column:spec" json:"spec"`                         // 规格
	Unit            string `gorm:"column:unit" json:"unit"`                         // 单位
	PN              string `gorm:"column:pn" json:"pn"`                             // 原厂PN
	ProductCategory string `gorm:"column:product_category" json:"product_category"` // 产品类别
}

// 库存明细
type InventoryDetail struct {
	SN            string    `gorm:"column:sn;primaryKey" json:"sn"`              // 物料SN
	ProductID     string    `gorm:"column:product_id" json:"product_id"`         // 产品ID
	Warehouse     string    `gorm:"column:warehouse" json:"warehouse"`           // 库房
	CurrentStock  int       `gorm:"column:current_stock" json:"current_stock"`   // 当前库存
	WarrantyStart time.Time `gorm:"column:warranty_start" json:"warranty_start"` // 维保开始
	WarrantyEnd   time.Time `gorm:"column:warranty_end" json:"warranty_end"`     // 维保结束
}

// 入库单
type Inbound struct {
	InboundID     string    `gorm:"column:inbound_id;primaryKey" json:"inbound_id"` // 入库单号
	InboundTime   time.Time `gorm:"column:inbound_time" json:"inbound_time"`        // 入库时间
	PurchaseOrder string    `gorm:"column:purchase_order" json:"purchase_order"`    // 采购合同
	SupplierID    string    `gorm:"column:supplier_id" json:"supplier_id"`          // 供应商
	Quantity      int       `gorm:"column:quantity" json:"quantity"`                // 数量
}

// 出库单
type Outbound struct {
	OutboundID   string    `gorm:"column:outbound_id;primaryKey" json:"outbound_id"` // 出库单号
	OutboundTime time.Time `gorm:"column:outbound_time" json:"outbound_time"`        // 出库时间
	OutType      string    `gorm:"column:out_type" json:"out_type"`                  // 出库类型
	SalesOrder   string    `gorm:"column:sales_order" json:"sales_order"`            // 销售订单
}

// 采购合同表
type PurchaseOrder struct {
	PONumber    string    `gorm:"column:po_number;primaryKey" json:"po_number"` // 采购合同号
	SupplierID  string    `gorm:"column:supplier_id" json:"supplier_id"`        // 供应商
	OrderDate   time.Time `gorm:"column:order_date" json:"order_date"`          // 下单时间
	TotalAmount float64   `gorm:"column:total_amount" json:"total_amount"`      // 总金额
}

// 供应商表
type Supplier struct {
	SupplierID string `gorm:"column:supplier_id;primaryKey" json:"supplier_id"` // 供应商ID
	Name       string `gorm:"column:name" json:"name"`                          // 名称
	Contact    string `gorm:"column:contact" json:"contact"`                    // 联系人
	Phone      string `gorm:"column:phone" json:"phone"`                        // 电话
}

// 位置管理-区域表
type Region struct {
	RegionID    string `gorm:"column:region_id;primaryKey" json:"region_id"` // 区域ID
	Name        string `gorm:"column:name" json:"name"`                      // 区域名称
	Description string `gorm:"column:description" json:"description"`        // 描述
}

// 位置管理-可用区表
type AZ struct {
	AZID        string `gorm:"column:az_id;primaryKey" json:"az_id"`  // 可用区ID
	Name        string `gorm:"column:name" json:"name"`               // 可用区名称
	RegionID    string `gorm:"column:region_id" json:"region_id"`     // 所属区域
	Description string `gorm:"column:description" json:"description"` // 描述
}

// 位置管理-机房表
type DataCenter struct {
	DataCenterID string `gorm:"column:data_center_id;primaryKey" json:"data_center_id"` // 机房ID
	Name         string `gorm:"column:name" json:"name"`                                // 机房名称
	AZID         string `gorm:"column:az_id" json:"az_id"`                              // 所属AZ
	Address      string `gorm:"column:address" json:"address"`                          // 地址
	Description  string `gorm:"column:description" json:"description"`                  // 描述
}

// 位置管理-房间表
type Room struct {
	RoomID       string `gorm:"column:room_id;primaryKey" json:"room_id"`    // 房间ID
	Name         string `gorm:"column:name" json:"name"`                     // 房间名称
	DataCenterID string `gorm:"column:data_center_id" json:"data_center_id"` // 所属机房
	Floor        string `gorm:"column:floor" json:"floor"`                   // 楼层
	Description  string `gorm:"column:description" json:"description"`       // 描述
}

// 位置管理-机柜表
type Cabinet struct {
	CabinetID     string `gorm:"column:cabinet_id;primaryKey" json:"cabinet_id"` // 机柜ID
	Name          string `gorm:"column:name" json:"name"`                        // 机柜名称
	RoomID        string `gorm:"column:room_id" json:"room_id"`                  // 所属房间
	CapacityUnits int    `gorm:"column:capacity_units" json:"capacity_units"`    // 机柜容量(U)
	Description   string `gorm:"column:description" json:"description"`          // 描述
}
