package service

import (
	"backend/internal/modules/server/model"
	"backend/internal/modules/server/repository"
	"fmt"
)

// ServerService 定义服务器相关业务接口
type ServerService interface {
	// 根据 SN 获取服务器详细信息
	GetServerInfo(sn string) (*model.ServerInfo, error)
	// 批量验证服务器SN是否存在
	ValidateServerSNs(sns []string) (map[string]bool, error)
}

type serverService struct {
	repo repository.ServerRepository
}

// NewServerService 创建新的 ServerService 实例
func NewServerService(repo repository.ServerRepository) ServerService {
	return &serverService{repo: repo}
}

// GetServerInfo 通过仓库层接口获取服务器信息，并在业务层进行必要包装
func (s *serverService) GetServerInfo(sn string) (*model.ServerInfo, error) {
	return s.repo.GetServerInfoBySN(sn)
}

// ValidateServerSNs 批量验证服务器SN是否存在
func (s *serverService) ValidateServerSNs(sns []string) (map[string]bool, error) {
	if len(sns) == 0 {
		return nil, nil // 空列表视为无错误
	}

	// 调用仓库层的批量查询接口
	existingSNs, err := s.repo.ListServerSNs(sns)
	if err != nil {
		return nil, fmt.Errorf("批量查询服务器SN失败: %w", err)
	}

	// 构建存在性映射
	existsMap := make(map[string]bool, len(existingSNs))
	for _, sn := range existingSNs {
		existsMap[sn] = true
	}

	return existsMap, nil
}
