package repository

import (
	"backend/internal/modules/server/model"
	"context"
	"time"

	"gorm.io/gorm"
)

type GpuServerRepository struct {
	db *gorm.DB
}

func NewGpuServerRepository(db *gorm.DB) *GpuServerRepository {
	return &GpuServerRepository{db: db}
}

// CreateGpuServer 插入一条新的 GPU 服务器记录
func (r *GpuServerRepository) CreateGpuServer(ctx context.Context, gpu *model.GpuServer) error {
	return r.db.WithContext(ctx).Create(gpu).Error
}

// GetGpuServerByID 根据 ID 查询 GPU 服务器记录
func (r *GpuServerRepository) GetGpuServerByID(ctx context.Context, id uint) (*model.GpuServer, error) {
	var gpu model.GpuServer
	if err := r.db.WithContext(ctx).First(&gpu, id).Error; err != nil {
		return nil, err
	}
	return &gpu, nil
}

// UpdateGpuServer 更新一条 GPU 服务器记录，同时忽略 CreatedAt 字段
func (r *GpuServerRepository) UpdateGpuServer(ctx context.Context, gpu *model.GpuServer) error {
	// omit CreatedAt，避免传递零值导致 MySQL 拒绝更新
	return r.db.WithContext(ctx).Omit("CreatedAt").Save(gpu).Error
}

// UpdateGpuServerFields 根据传入的字段 map 更新 GPU 服务器记录，并忽略 created_at 字段
func (r *GpuServerRepository) UpdateGpuServerFields(ctx context.Context, id uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).
		Model(&model.GpuServer{}).
		Where("id = ?", id).
		// 忽略 created_at 字段，让数据库保持原值
		Omit("created_at", "CreatedAt").
		Updates(updates).
		Error
}

// DeleteGpuServer 根据 ID 软删除一条 GPU 服务器记录，更新 is_deleted 字段为 true
func (r *GpuServerRepository) DeleteGpuServer(ctx context.Context, id uint) error {
	updates := map[string]interface{}{
		"is_deleted": true,
		"deleted_at": time.Now(),
	}
	return r.db.WithContext(ctx).
		Model(&model.GpuServer{}).
		Where("id = ?", id).
		Updates(updates).Error
}

// ListGpuServer 返回所有的 GPU 服务器记录
func (r *GpuServerRepository) ListGpuServer(ctx context.Context) ([]model.GpuServer, error) {
	var gpus []model.GpuServer
	if err := r.db.WithContext(ctx).Find(&gpus).Error; err != nil {
		return nil, err
	}
	return gpus, nil
}

// QueryGpuServers 根据传入的查询条件过滤 GPU 服务器记录
func (r *GpuServerRepository) QueryGpuServers(ctx context.Context, query *model.GpuServerQuery) ([]model.GpuServer, int64, error) {
	var gpus []model.GpuServer
	var total int64

	// 构造基本查询对象
	dbQuery := r.db.WithContext(ctx).
		Model(&model.GpuServer{})
	if query.Project != "" {
		dbQuery = dbQuery.Where("project = ?", query.Project)
	}
	if query.DataCenter != "" {
		dbQuery = dbQuery.Where("data_center = ?", query.DataCenter)
	}
	if query.Package != "" {
		// 注意 package 为 MySQL 保留字，如有冲突可加上反引号
		dbQuery = dbQuery.Where("`package` = ?", query.Package)
	}
	if query.Cabinet != "" {
		dbQuery = dbQuery.Where("cabinet = ?", query.Cabinet)
	}
	if query.Hostname != "" {
		dbQuery = dbQuery.Where("hostname = ?", query.Hostname)
	}
	if query.SN != "" {
		dbQuery = dbQuery.Where("sn = ?", query.SN)
	}
	if query.PrimaryOwner != "" {
		dbQuery = dbQuery.Where("primary_owner = ?", query.PrimaryOwner)
	}
	if query.BackupOwner != "" {
		dbQuery = dbQuery.Where("backup_owner = ?", query.BackupOwner)
	}
	if query.VpcIP != "" {
		dbQuery = dbQuery.Where("vpc_ip = ?", query.VpcIP)
	}
	if query.VpcMask != "" {
		dbQuery = dbQuery.Where("vpc_mask = ?", query.VpcMask)
	}
	if query.BmcIP != "" {
		dbQuery = dbQuery.Where("bmc_ip = ?", query.BmcIP)
	}
	if query.BmcMask != "" {
		dbQuery = dbQuery.Where("bmc_mask = ?", query.BmcMask)
	}
	if query.BmcGateway != "" {
		dbQuery = dbQuery.Where("bmc_gateway = ?", query.BmcGateway)
	}
	if query.CPUType != "" {
		dbQuery = dbQuery.Where("cpu_type = ?", query.CPUType)
	}
	if query.VpcEth0Mac != "" {
		dbQuery = dbQuery.Where("vpc_eth0_mac = ?", query.VpcEth0Mac)
	}
	if query.BmcMac != "" {
		dbQuery = dbQuery.Where("bmc_mac = ?", query.BmcMac)
	}
	if query.DeviceType != "" {
		dbQuery = dbQuery.Where("device_type = ?", query.DeviceType)
	}
	if query.Height != nil {
		dbQuery = dbQuery.Where("height = ?", *query.Height)
	}
	if query.GPUModel != "" {
		dbQuery = dbQuery.Where("gpu_model = ?", query.GPUModel)
	}
	if query.Cluster != "" {
		dbQuery = dbQuery.Where("cluster = ?", query.Cluster)
	}
	if query.Manufacturer != "" {
		dbQuery = dbQuery.Where("manufacturer = ?", query.Manufacturer)
	}
	if query.MachineModel != "" {
		dbQuery = dbQuery.Where("model = ?", query.MachineModel)
	}
	if query.BusinessStatus != "" {
		dbQuery = dbQuery.Where("business_status = ?", query.BusinessStatus)
	}
	if query.AssetStatus != "" {
		dbQuery = dbQuery.Where("asset_status = ?", query.AssetStatus)
	}
	if query.IsBackup != nil {
		dbQuery = dbQuery.Where("is_backup = ?", *query.IsBackup)
	}

	// 先统计总记录数
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 设置分页参数
	page := query.Page
	pageSize := query.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 执行分页查询
	if err := dbQuery.Offset(offset).Limit(pageSize).Find(&gpus).Error; err != nil {
		return nil, 0, err
	}

	return gpus, total, nil
}
