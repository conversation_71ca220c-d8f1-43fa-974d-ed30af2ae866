package importhandler

import (
	"context"
	"errors"
	"strconv"
	"strings"

	"gorm.io/gorm"

	"backend/internal/modules/cmdb/model/location"
	fileservice "backend/internal/modules/file/service"
)

// CabinetHandler 机柜导入处理器
type CabinetHandler struct {
	*BaseHandler
	DB *gorm.DB
}

// NewCabinetHandler 创建机柜导入处理器
func NewCabinetHandler(db *gorm.DB) *CabinetHandler {
	// 创建文件服务
	fileSvc := fileservice.NewFileService(db)

	return &CabinetHandler{
		BaseHandler: NewBaseHandler(db, fileSvc),
		DB:          db,
	}
}

// GetRequiredHeaders 获取必需的表头字段
func (h *CabinetHandler) GetRequiredHeaders() []string {
	return []string{"机柜名称", "房间名称"}
}

// ValidateHeaders 验证CSV表头
func (h *CabinetHandler) ValidateHeaders(headers []string) error {
	requiredHeaders := h.GetRequiredHeaders()

	for _, required := range requiredHeaders {
		found := false
		for _, header := range headers {
			// 清理表头，移除可能的星号(*)标记和空白字符
			cleanHeader := strings.TrimSpace(header)
			if strings.HasPrefix(cleanHeader, "*") {
				cleanHeader = strings.TrimSpace(cleanHeader[1:])
			}

			if cleanHeader == required {
				found = true
				break
			}
		}

		if !found {
			return errors.New("CSV文件缺少必需字段: " + required)
		}
	}

	return nil
}

// ParseRecord 解析CSV记录
func (h *CabinetHandler) ParseRecord(record []string, headerMap map[string]int, rowNum int) (interface{}, error) {
	// 获取字段索引
	nameIndex, hasName := headerMap["机柜名称"]
	roomNameIndex, hasRoomName := headerMap["房间名称"]
	capacityUnitsIndex, hasCapacityUnits := headerMap["容量(U)"]
	rowIndex, hasRow := headerMap["行"]
	columnIndex, hasColumn := headerMap["列"]
	cabinetTypeIndex, hasCabinetType := headerMap["机柜类型"]
	networkEnvIndex, hasNetworkEnv := headerMap["网络环境"]
	bondTypeIndex, hasBondType := headerMap["Bond类型"]
	totalPowerIndex, hasTotalPower := headerMap["总功耗(kW)"]
	descriptionIndex, hasDescription := headerMap["描述"]
	statusIndex, hasStatus := headerMap["状态"]

	// 验证必填字段
	if !hasName || nameIndex >= len(record) {
		return nil, errors.New("缺少机柜名称")
	}

	name := strings.TrimSpace(record[nameIndex])
	if name == "" {
		return nil, errors.New("机柜名称不能为空")
	}

	if !hasRoomName || roomNameIndex >= len(record) {
		return nil, errors.New("缺少房间名称")
	}

	roomName := strings.TrimSpace(record[roomNameIndex])
	if roomName == "" {
		return nil, errors.New("房间名称不能为空")
	}

	// 创建机柜对象
	cabinet := &location.Cabinet{
		Name: name,
	}

	// 设置容量单位
	if hasCapacityUnits && capacityUnitsIndex < len(record) {
		capacityStr := strings.TrimSpace(record[capacityUnitsIndex])
		if capacityStr != "" {
			capacity, err := strconv.Atoi(capacityStr)
			if err != nil {
				return nil, errors.New("容量(U)必须是数字")
			}
			cabinet.CapacityUnits = capacity
		} else {
			cabinet.CapacityUnits = 42 // 设置默认值
		}
	} else {
		cabinet.CapacityUnits = 42 // 设置默认值
	}

	// 设置行
	if hasRow && rowIndex < len(record) {
		cabinet.Row = strings.TrimSpace(record[rowIndex])
	}

	// 设置列
	if hasColumn && columnIndex < len(record) {
		cabinet.Column = strings.TrimSpace(record[columnIndex])
	}

	// 设置机柜类型
	if hasCabinetType && cabinetTypeIndex < len(record) {
		cabinet.CabinetType = strings.TrimSpace(record[cabinetTypeIndex])
	}

	// 设置网络环境
	if hasNetworkEnv && networkEnvIndex < len(record) {
		cabinet.NetworkEnvironment = strings.TrimSpace(record[networkEnvIndex])
	}

	// 设置Bond类型
	if hasBondType && bondTypeIndex < len(record) {
		cabinet.BondType = strings.TrimSpace(record[bondTypeIndex])
	}

	// 设置总功耗
	if hasTotalPower && totalPowerIndex < len(record) {
		powerStr := strings.TrimSpace(record[totalPowerIndex])
		if powerStr != "" {
			power, err := strconv.ParseFloat(powerStr, 64)
			if err != nil {
				return nil, errors.New("总功耗(kW)必须是数字")
			}
			cabinet.TotalPower = power
		}
	}

	// 设置描述
	if hasDescription && descriptionIndex < len(record) {
		cabinet.Description = strings.TrimSpace(record[descriptionIndex])
	}

	// 设置状态
	if hasStatus && statusIndex < len(record) {
		status := strings.TrimSpace(record[statusIndex])
		if status == "禁用" {
			cabinet.Status = "disabled"
		} else {
			cabinet.Status = "active" // 默认为启用
		}
	} else {
		cabinet.Status = "active" // 默认为启用
	}

	// 查找房间ID
	var room location.Room
	if err := h.DB.Where("name = ?", roomName).First(&room).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("找不到名称为 '" + roomName + "' 的房间")
		}
		return nil, errors.New("查询房间信息失败: " + err.Error())
	}
	cabinet.RoomID = room.ID

	return cabinet, nil
}

// SaveRecord 保存机柜记录到数据库
func (h *CabinetHandler) SaveRecord(ctx context.Context, tx *gorm.DB, record interface{}) error {
	cabinet, ok := record.(*location.Cabinet)
	if !ok {
		return errors.New("记录类型错误")
	}

	// 从上下文中获取导入选项
	var options ImportOptions
	optionsValue := ctx.Value(importOptionsKey)
	if optionsValue != nil {
		var ok bool
		options, ok = optionsValue.(ImportOptions)
		if !ok {
			return errors.New("导入选项类型错误")
		}
	}

	// 获取房间信息，用于查询和错误提示
	var room location.Room
	if err := tx.First(&room, cabinet.RoomID).Error; err != nil {
		return errors.New("查询房间信息失败: " + err.Error())
	}

	// 保存原始房间名称，便于后续使用
	roomName := room.Name

	// 检查是否存在相同名称且在相同房间的机柜
	var existingCabinet location.Cabinet
	var isExist bool
	var isSoftDeleted bool

	// 构建查询 - 使用机柜名称和房间名称查询
	query := tx
	if options.Overwrite {
		query = tx.Unscoped() // 在覆盖模式下包括软删除的记录
	}

	// 使用JOIN查询确保通过房间名称匹配
	q := query.Joins("JOIN rooms ON cabinets.room_id = rooms.id").
		Where("cabinets.name = ? AND rooms.name = ?", cabinet.Name, roomName)

	// 执行查询
	if err := q.First(&existingCabinet).Error; err == nil {
		isExist = true
		// 检查是否为软删除状态
		if options.Overwrite {
			isSoftDeleted = !existingCabinet.DeletedAt.Time.IsZero()
		}
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 如果存在相同名称且在相同房间的机柜且未设置覆盖选项，则返回错误
	if isExist && !options.Overwrite {
		return errors.New("在房间「" + roomName + "」中已存在相同名称的机柜: " + cabinet.Name)
	}

	// 如果设置了覆盖选项且存在相同名称且在相同房间的机柜，则更新现有记录
	if isExist && options.Overwrite {
		// 更新机柜数据，保留原ID和创建时间
		cabinet.ID = existingCabinet.ID
		cabinet.CreatedAt = existingCabinet.CreatedAt

		// 如果记录被软删除，先恢复它
		if isSoftDeleted {
			if err := tx.Unscoped().Model(&existingCabinet).Update("deleted_at", nil).Error; err != nil {
				return errors.New("恢复软删除的机柜失败: " + err.Error())
			}
		}

		// 更新机柜记录
		if err := tx.Model(&existingCabinet).Updates(cabinet).Error; err != nil {
			return errors.New("更新机柜失败: " + err.Error())
		}

		return nil
	}

	// 创建新机柜
	if err := tx.Create(cabinet).Error; err != nil {
		return errors.New("创建机柜失败: " + err.Error())
	}

	return nil
}
