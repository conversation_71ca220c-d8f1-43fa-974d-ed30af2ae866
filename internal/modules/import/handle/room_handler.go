package importhandler

import (
	"context"
	"errors"
	"strings"

	"gorm.io/gorm"

	"backend/internal/modules/cmdb/model/location"
	fileservice "backend/internal/modules/file/service"
)

// RoomHandler 房间导入处理器
type RoomHandler struct {
	*BaseHandler
	DB *gorm.DB
}

// NewRoomHandler 创建房间导入处理器
func NewRoomHandler(db *gorm.DB) *RoomHandler {
	// 创建文件服务
	fileSvc := fileservice.NewFileService(db)

	return &RoomHandler{
		BaseHandler: NewBaseHandler(db, fileSvc),
		DB:          db,
	}
}

// GetRequiredHeaders 获取必需的表头字段
func (h *RoomHandler) GetRequiredHeaders() []string {
	return []string{"房间名称", "机房名称"}
}

// ValidateHeaders 验证CSV表头
func (h *RoomHandler) ValidateHeaders(headers []string) error {
	requiredHeaders := h.GetRequiredHeaders()

	for _, required := range requiredHeaders {
		found := false
		for _, header := range headers {
			// 清理表头，移除可能的星号(*)标记和空白字符
			cleanHeader := strings.TrimSpace(header)
			if strings.HasPrefix(cleanHeader, "*") {
				cleanHeader = strings.TrimSpace(cleanHeader[1:])
			}

			if cleanHeader == required {
				found = true
				break
			}
		}

		if !found {
			return errors.New("CSV文件缺少必需字段: " + required)
		}
	}

	return nil
}

// ParseRecord 解析CSV记录
func (h *RoomHandler) ParseRecord(record []string, headerMap map[string]int, rowNum int) (interface{}, error) {
	// 获取字段索引
	nameIndex, hasName := headerMap["房间名称"]
	dataCenterNameIndex, hasDataCenterName := headerMap["机房名称"]
	descriptionIndex, hasDescription := headerMap["描述"]
	statusIndex, hasStatus := headerMap["状态"]

	// 验证必填字段
	if !hasName || nameIndex >= len(record) {
		return nil, errors.New("缺少房间名称")
	}

	name := strings.TrimSpace(record[nameIndex])
	if name == "" {
		return nil, errors.New("房间名称不能为空")
	}

	if !hasDataCenterName || dataCenterNameIndex >= len(record) {
		return nil, errors.New("缺少机房名称")
	}

	dataCenterName := strings.TrimSpace(record[dataCenterNameIndex])
	if dataCenterName == "" {
		return nil, errors.New("机房名称不能为空")
	}

	// 创建房间对象
	room := &location.Room{
		Name: name,
	}

	// 设置描述
	if hasDescription && descriptionIndex < len(record) {
		room.Description = strings.TrimSpace(record[descriptionIndex])
	}

	// 设置状态
	if hasStatus && statusIndex < len(record) {
		status := strings.TrimSpace(record[statusIndex])
		if status == "禁用" {
			room.Status = "disabled"
		} else {
			room.Status = "active" // 默认为启用
		}
	} else {
		room.Status = "active" // 默认为启用
	}

	// 查找机房ID
	var dataCenter location.DataCenter
	if err := h.DB.Where("name = ?", dataCenterName).First(&dataCenter).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("找不到名称为 '" + dataCenterName + "' 的机房")
		}
		return nil, errors.New("查询机房信息失败: " + err.Error())
	}
	room.DataCenterID = dataCenter.ID

	return room, nil
}

// SaveRecord 保存房间记录到数据库
func (h *RoomHandler) SaveRecord(ctx context.Context, tx *gorm.DB, record interface{}) error {
	room, ok := record.(*location.Room)
	if !ok {
		return errors.New("记录类型错误")
	}

	// 从上下文中获取导入选项
	var options ImportOptions
	optionsValue := ctx.Value(importOptionsKey)
	if optionsValue != nil {
		var ok bool
		options, ok = optionsValue.(ImportOptions)
		if !ok {
			return errors.New("导入选项类型错误")
		}
	}

	// 检查是否存在相同名称的房间
	var existingRoom location.Room
	var isExist bool
	var isSoftDeleted bool

	// 如果覆盖模式，查询所有记录（包括软删除的）
	if options.Overwrite {
		if err := tx.Unscoped().Where("name = ?", room.Name).First(&existingRoom).Error; err == nil {
			isExist = true
			// 检查是否为软删除状态
			isSoftDeleted = !existingRoom.DeletedAt.Time.IsZero()
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	} else {
		// 非覆盖模式下，只查询未删除的记录
		if err := tx.Where("name = ?", room.Name).First(&existingRoom).Error; err == nil {
			isExist = true
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}

	// 如果存在相同名称的房间且未设置覆盖选项，则返回错误
	if isExist && !options.Overwrite {
		return errors.New("已存在相同名称的房间: " + room.Name)
	}

	// 如果设置了覆盖选项且存在相同名称的房间（无论是否被软删除），则更新现有记录
	if isExist && options.Overwrite {
		// 更新房间数据，保留原ID和创建时间
		room.ID = existingRoom.ID
		room.CreatedAt = existingRoom.CreatedAt

		// 如果记录被软删除，先恢复它
		if isSoftDeleted {
			if err := tx.Unscoped().Model(&existingRoom).Update("deleted_at", nil).Error; err != nil {
				return errors.New("恢复软删除的房间失败: " + err.Error())
			}
		}

		// 更新房间记录
		if err := tx.Model(&existingRoom).Updates(room).Error; err != nil {
			return errors.New("更新房间失败: " + err.Error())
		}

		return nil
	}

	// 创建新房间
	if err := tx.Create(room).Error; err != nil {
		return errors.New("创建房间失败: " + err.Error())
	}

	return nil
}
