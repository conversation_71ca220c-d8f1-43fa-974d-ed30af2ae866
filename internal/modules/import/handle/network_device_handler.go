package importhandler

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"

	"backend/internal/modules/cmdb/model/asset"
	fileservice "backend/internal/modules/file/service"
	"backend/pkg/utils"
)

// NetworkDeviceHandler 网络设备导入处理器
type NetworkDeviceHandler struct {
	*BaseHandler
	DB *gorm.DB
}

// NewNetworkDeviceHandler 创建网络设备导入处理器
func NewNetworkDeviceHandler(db *gorm.DB) *NetworkDeviceHandler {
	// 创建文件服务
	fileSvc := fileservice.NewFileService(db)

	return &NetworkDeviceHandler{
		BaseHandler: NewBaseHandler(db, fileSvc),
		DB:          db,
	}
}

// GetRequiredHeaders 获取必需的表头字段
func (h *NetworkDeviceHandler) GetRequiredHeaders() []string {
	return []string{"SN", "资产类型", "设备角色"}
}

// ValidateHeaders 验证CSV表头
func (h *NetworkDeviceHandler) ValidateHeaders(headers []string) error {
	requiredHeaders := h.GetRequiredHeaders()

	for _, required := range requiredHeaders {
		found := false
		for _, header := range headers {
			// 清理表头，移除可能的星号(*)标记和空白字符
			cleanHeader := strings.TrimSpace(header)
			if strings.HasPrefix(cleanHeader, "*") {
				cleanHeader = strings.TrimSpace(cleanHeader[1:])
			}

			if cleanHeader == required {
				found = true
				break
			}
		}

		if !found {
			return errors.New("CSV文件缺少必需字段: " + required)
		}
	}

	return nil
}

// ParseRecord 解析CSV记录为网络设备对象
func (h *NetworkDeviceHandler) ParseRecord(record []string, headerMap map[string]int, rowNum int) (interface{}, error) {
	// 获取字段索引
	snIndex, hasSN := headerMap["SN"]
	brandIndex, hasBrand := headerMap["厂商"]
	modelIndex, hasModel := headerMap["型号"]
	assetTypeIndex, hasAssetType := headerMap["资产类型"]
	assetStatusIndex, hasAssetStatus := headerMap["资产状态"]
	hardwareStatusIndex, hasHardwareStatus := headerMap["硬件状态"]
	roleIndex, hasRole := headerMap["设备角色"]
	templateNameIndex, hasTemplateName := headerMap["套餐模板"]
	firmwareVersionIndex, hasFirmwareVersion := headerMap["固件版本"]
	mgmtAddressIndex, hasMgmtAddress := headerMap["管理地址"]
	loopbackAddressIndex, hasLoopbackAddress := headerMap["Loopback地址"]
	portsIndex, hasPorts := headerMap["端口数量"]
	portSpeedIndex, hasPortSpeed := headerMap["端口速率"]
	layerIndex, hasLayer := headerMap["网络层级"]
	routingProtocolsIndex, hasRoutingProtocols := headerMap["路由协议"]
	stackSupportIndex, hasStackSupport := headerMap["支持堆叠"]
	stackIDIndex, hasStackID := headerMap["堆叠ID"]
	stackRoleIndex, hasStackRole := headerMap["堆叠角色"]
	cabinetNameIndex, hasCabinetName := headerMap["机柜名称"]
	rackPositionIndex, hasRackPosition := headerMap["机架位"]
	projectIndex, hasProject := headerMap["项目"]
	bizStatusIndex, hasBizStatus := headerMap["业务状态"]
	purchaseOrderIndex, hasPurchaseOrder := headerMap["采购合同"]
	purchaseDateIndex, hasPurchaseDate := headerMap["购买时间"]
	warrantyExpireIndex, hasWarrantyExpire := headerMap["过保时间"]
	priceIndex, hasPrice := headerMap["金额"]
	residualValueIndex, hasResidualValue := headerMap["残值"]
	rackingTimeIndex, hasRackingTime := headerMap["上架时间"]
	deliveryTimeIndex, hasDeliveryTime := headerMap["交付时间"]
	remarkIndex, hasRemark := headerMap["备注"]

	// 验证必填字段
	if !hasSN || snIndex >= len(record) {
		return nil, errors.New("缺少SN")
	}
	sn := strings.TrimSpace(record[snIndex])
	if sn == "" {
		return nil, errors.New("SN不能为空")
	}

	if !hasAssetType || assetTypeIndex >= len(record) {
		return nil, errors.New("缺少资产类型")
	}
	assetType := strings.TrimSpace(record[assetTypeIndex])
	if assetType == "" {
		return nil, errors.New("资产类型不能为空")
	}

	if !hasRole || roleIndex >= len(record) {
		return nil, errors.New("缺少设备角色")
	}
	role := strings.TrimSpace(record[roleIndex])
	if role == "" {
		return nil, errors.New("设备角色不能为空")
	}

	// 将中文资产类型转换为系统使用的英文代码
	switch assetType {
	case "网络设备":
		assetType = "network"
	case "交换机":
		assetType = "switch"
	case "路由器":
		assetType = "router"
	case "防火墙":
		assetType = "firewall"
	case "负载均衡":
		assetType = "loadbalancer"
	default:
		// 保留原值，可能是英文代码
	}

	// 获取可选字段
	var brand, model, assetStatus, hardwareStatus string
	var templateName, firmwareVersion, managementAddress, loopbackAddress string
	var portSpeed, routingProtocols, stackRole, cabinetName, project, bizStatus string
	var purchaseOrder, remark string
	var ports, stackID, rackPosition int
	var stackSupport bool
	var layer int
	var price, residualValue float64
	var purchaseDate, warrantyExpire, rackingTime, deliveryTime time.Time

	if hasBrand && brandIndex < len(record) {
		brand = strings.TrimSpace(record[brandIndex])
	}

	if hasModel && modelIndex < len(record) {
		model = strings.TrimSpace(record[modelIndex])
	}

	if hasAssetStatus && assetStatusIndex < len(record) {
		assetStatus = strings.TrimSpace(record[assetStatusIndex])
		// 将中文状态转换为系统使用的英文代码
		switch assetStatus {
		case "待入库":
			assetStatus = "pending_storage"
		case "已入库":
			assetStatus = "in_storage"
		case "待出库":
			assetStatus = "pending_outbound"
		case "已出库":
			assetStatus = "outbound"
		case "闲置中":
			assetStatus = "idle"
		case "使用中":
			assetStatus = "in_use"
		case "维修中":
			assetStatus = "maintaining"
		case "待报废":
			assetStatus = "pending_scrap"
		case "已报废":
			assetStatus = "scrapped"
		default:
			// 保留原值，可能是英文代码
		}
	} else {
		assetStatus = "in_use" // 默认使用中
	}

	if hasHardwareStatus && hardwareStatusIndex < len(record) {
		hardwareStatus = strings.TrimSpace(record[hardwareStatusIndex])
		// 将中文状态转换为系统使用的英文代码
		switch hardwareStatus {
		case "正常":
			hardwareStatus = "normal"
		case "故障":
			hardwareStatus = "faulty"
		case "警告":
			hardwareStatus = "warning"
		default:
			// 保留原值，可能是英文代码
		}
	} else {
		hardwareStatus = "normal" // 默认正常
	}

	if hasTemplateName && templateNameIndex < len(record) {
		templateName = strings.TrimSpace(record[templateNameIndex])
	}

	if hasFirmwareVersion && firmwareVersionIndex < len(record) {
		firmwareVersion = strings.TrimSpace(record[firmwareVersionIndex])
	}

	if hasMgmtAddress && mgmtAddressIndex < len(record) {
		managementAddress = strings.TrimSpace(record[mgmtAddressIndex])
	}

	if hasLoopbackAddress && loopbackAddressIndex < len(record) {
		loopbackAddress = strings.TrimSpace(record[loopbackAddressIndex])
	}

	if hasPorts && portsIndex < len(record) {
		portsStr := strings.TrimSpace(record[portsIndex])
		if portsStr != "" {
			var err error
			ports, err = strconv.Atoi(portsStr)
			if err != nil {
				return nil, errors.New("端口数量格式不正确")
			}
		}
	}

	if hasPortSpeed && portSpeedIndex < len(record) {
		portSpeed = strings.TrimSpace(record[portSpeedIndex])
	}

	if hasLayer && layerIndex < len(record) {
		layerStr := strings.TrimSpace(record[layerIndex])
		if layerStr != "" {
			var err error
			layer, err = strconv.Atoi(layerStr)
			if err != nil {
				return nil, errors.New("网络层级格式不正确")
			}
		}
	}

	if hasRoutingProtocols && routingProtocolsIndex < len(record) {
		routingProtocols = strings.TrimSpace(record[routingProtocolsIndex])
	}

	if hasStackSupport && stackSupportIndex < len(record) {
		stackSupportStr := strings.TrimSpace(record[stackSupportIndex])
		stackSupport = stackSupportStr == "是" || stackSupportStr == "true" || stackSupportStr == "1"
	}

	if hasStackID && stackIDIndex < len(record) {
		stackIDStr := strings.TrimSpace(record[stackIDIndex])
		if stackIDStr != "" {
			var err error
			stackID, err = strconv.Atoi(stackIDStr)
			if err != nil {
				return nil, errors.New("堆叠ID格式不正确")
			}
		}
	}

	if hasStackRole && stackRoleIndex < len(record) {
		stackRole = strings.TrimSpace(record[stackRoleIndex])
	}

	if hasCabinetName && cabinetNameIndex < len(record) {
		cabinetName = strings.TrimSpace(record[cabinetNameIndex])
	}

	if hasRackPosition && rackPositionIndex < len(record) {
		rackPositionStr := strings.TrimSpace(record[rackPositionIndex])
		if rackPositionStr != "" {
			var err error
			rackPosition, err = strconv.Atoi(rackPositionStr)
			if err != nil {
				return nil, errors.New("机架位格式不正确")
			}
		}
	}

	if hasProject && projectIndex < len(record) {
		project = strings.TrimSpace(record[projectIndex])
	}

	if hasBizStatus && bizStatusIndex < len(record) {
		bizStatus = strings.TrimSpace(record[bizStatusIndex])
	} else {
		bizStatus = "active" // 默认正常
	}

	if hasPurchaseOrder && purchaseOrderIndex < len(record) {
		purchaseOrder = strings.TrimSpace(record[purchaseOrderIndex])
	}

	if hasPrice && priceIndex < len(record) {
		priceStr := strings.TrimSpace(record[priceIndex])
		if priceStr != "" {
			var err error
			price, err = strconv.ParseFloat(priceStr, 64)
			if err != nil {
				return nil, errors.New("金额格式不正确")
			}
		}
	}

	if hasResidualValue && residualValueIndex < len(record) {
		residualValueStr := strings.TrimSpace(record[residualValueIndex])
		if residualValueStr != "" {
			var err error
			residualValue, err = strconv.ParseFloat(residualValueStr, 64)
			if err != nil {
				return nil, errors.New("残值格式不正确")
			}
		}
	}

	if hasRemark && remarkIndex < len(record) {
		remark = strings.TrimSpace(record[remarkIndex])
	}

	// 解析日期字段
	if hasPurchaseDate && purchaseDateIndex < len(record) {
		dateStr := strings.TrimSpace(record[purchaseDateIndex])
		if dateStr != "" {
			var err error
			purchaseDate, err = parseDateWithMultipleFormats(dateStr)
			if err != nil {
				return nil, errors.New("购买时间格式不正确")
			}
		}
	}

	if hasWarrantyExpire && warrantyExpireIndex < len(record) {
		dateStr := strings.TrimSpace(record[warrantyExpireIndex])
		if dateStr != "" {
			var err error
			warrantyExpire, err = parseDateWithMultipleFormats(dateStr)
			if err != nil {
				return nil, errors.New("过保时间格式不正确")
			}
		}
	}

	if hasRackingTime && rackingTimeIndex < len(record) {
		dateStr := strings.TrimSpace(record[rackingTimeIndex])
		if dateStr != "" {
			var err error
			rackingTime, err = parseDateWithMultipleFormats(dateStr)
			if err != nil {
				return nil, errors.New("上架时间格式不正确")
			}
		}
	}

	if hasDeliveryTime && deliveryTimeIndex < len(record) {
		dateStr := strings.TrimSpace(record[deliveryTimeIndex])
		if dateStr != "" {
			var err error
			deliveryTime, err = parseDateWithMultipleFormats(dateStr)
			if err != nil {
				return nil, errors.New("交付时间格式不正确")
			}
		}
	}

	// 查询机柜ID
	var cabinetID uint
	if cabinetName != "" {
		var cabinet struct {
			ID uint `json:"id"`
		}
		if err := h.DB.Table("cabinets").Where("name = ?", cabinetName).First(&cabinet).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 机柜不存在，暂时不返回错误，而是不设置机柜ID
				// return nil, errors.New("未找到名称为 " + cabinetName + " 的机柜")
			} else {
				return nil, errors.New("查询机柜时出错: " + err.Error())
			}
		} else {
			cabinetID = cabinet.ID
		}
	}

	// 查询模板ID
	var templateID uint
	if templateName != "" {
		var template struct {
			ID uint `json:"id"`
		}
		if err := h.DB.Table("machine_templates").Where("template_name = ?", templateName).First(&template).Error; err != nil {
			// 检查错误信息，如果包含"表不存在"或"不存在的记录"，则忽略错误
			if !errors.Is(err, gorm.ErrRecordNotFound) && !strings.Contains(err.Error(), "doesn't exist") {
				return nil, errors.New("查询模板时出错: " + err.Error())
			}
			// 模板不存在或表不存在，不设置模板ID，继续处理
		} else {
			templateID = template.ID
		}
	}

	// 创建设备信息
	device := &asset.Device{
		SN:             sn,
		Brand:          brand,
		Model:          model,
		AssetType:      assetType,
		AssetStatus:    assetStatus,
		HardwareStatus: hardwareStatus,
		TemplateID:     templateID,
		PurchaseOrder:  purchaseOrder,
		Price:          price,
		ResidualValue:  residualValue,
		Remark:         remark,
	}

	// 设置可选的日期字段
	if !purchaseDate.IsZero() {
		device.PurchaseDate = utils.Date(purchaseDate)
	}

	if !warrantyExpire.IsZero() {
		device.WarrantyExpire = utils.Date(warrantyExpire)
	}

	// 创建网络设备信息
	networkDevice := struct {
		Device        *asset.Device
		NetworkDevice *asset.NetworkDevice
		// 额外字段，用于保存导入时的信息
		CabinetID    uint
		Project      string
		BizStatus    string
		RackPosition int
		RackingTime  string
		DeliveryTime string
	}{
		Device: device,
		NetworkDevice: &asset.NetworkDevice{
			Role:              role,
			FirmwareVersion:   firmwareVersion,
			ManagementAddress: managementAddress,
			LoopbackAddress:   loopbackAddress,
			Ports:             ports,
			PortSpeed:         portSpeed,
			Layer:             layer,
			RoutingProtocols:  routingProtocols,
			StackSupport:      stackSupport,
			StackID:           stackID,
			StackRole:         stackRole,
		},
		CabinetID:    cabinetID,
		Project:      project,
		BizStatus:    bizStatus,
		RackPosition: rackPosition,
	}

	// 设置可选的日期字段
	if !rackingTime.IsZero() {
		networkDevice.RackingTime = rackingTime.Format("2006-01-02")
	}

	if !deliveryTime.IsZero() {
		networkDevice.DeliveryTime = deliveryTime.Format("2006-01-02")
	}

	return networkDevice, nil
}

// SaveRecord 保存网络设备记录到数据库
func (h *NetworkDeviceHandler) SaveRecord(ctx context.Context, tx *gorm.DB, record interface{}) error {
	deviceWithNetworkDevice, ok := record.(struct {
		Device        *asset.Device
		NetworkDevice *asset.NetworkDevice
		// 额外字段，用于保存导入时的信息
		CabinetID    uint
		Project      string
		BizStatus    string
		RackPosition int
		RackingTime  string
		DeliveryTime string
	})
	if !ok {
		return errors.New("记录类型错误")
	}

	// 从上下文中获取导入选项
	var options ImportOptions
	optionsValue := ctx.Value(importOptionsKey)
	if optionsValue != nil {
		var ok bool
		options, ok = optionsValue.(ImportOptions)
		if !ok {
			return errors.New("导入选项类型错误")
		}
	}

	// 检查是否存在相同SN的设备（包括软删除的记录）
	var existingDevice asset.Device
	var isExist bool
	var isSoftDeleted bool

	// 如果覆盖模式，查询所有记录（包括软删除的）
	if options.Overwrite {
		if err := tx.Unscoped().Where("sn = ?", deviceWithNetworkDevice.Device.SN).First(&existingDevice).Error; err == nil {
			isExist = true
			// 检查是否为软删除状态
			isSoftDeleted = !existingDevice.DeletedAt.Time.IsZero()
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	} else {
		// 非覆盖模式下，只查询未删除的记录
		if err := tx.Where("sn = ?", deviceWithNetworkDevice.Device.SN).First(&existingDevice).Error; err == nil {
			isExist = true
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}

	// 如果存在相同的设备且未设置覆盖选项，则返回错误
	if isExist && !options.Overwrite {
		return errors.New("已存在相同SN的设备: " + deviceWithNetworkDevice.Device.SN)
	}

	// 开始事务
	if err := tx.Transaction(func(tx *gorm.DB) error {
		if isExist && options.Overwrite {
			// 更新设备基本信息
			deviceWithNetworkDevice.Device.ID = existingDevice.ID
			deviceWithNetworkDevice.Device.CreatedAt = existingDevice.CreatedAt

			// 如果记录被软删除，先恢复它
			if isSoftDeleted {
				if err := tx.Unscoped().Model(&existingDevice).Update("deleted_at", nil).Error; err != nil {
					return errors.New("恢复软删除的设备失败: " + err.Error())
				}
			}

			if err := tx.Model(&existingDevice).Updates(deviceWithNetworkDevice.Device).Error; err != nil {
				return errors.New("更新设备基本信息失败: " + err.Error())
			}

			// 检查是否已存在关联的网络设备（包括软删除的）
			var existingNetworkDevice asset.NetworkDevice
			var networkDeviceSoftDeleted bool

			if err := tx.Unscoped().Where("device_id = ?", existingDevice.ID).First(&existingNetworkDevice).Error; err == nil {
				// 存在网络设备，检查是否被软删除
				networkDeviceSoftDeleted = !existingNetworkDevice.DeletedAt.Time.IsZero()

				// 如果网络设备被软删除，先恢复它
				if networkDeviceSoftDeleted {
					if err := tx.Unscoped().Model(&existingNetworkDevice).Update("deleted_at", nil).Error; err != nil {
						return errors.New("恢复软删除的网络设备失败: " + err.Error())
					}
				}

				// 更新网络设备
				deviceWithNetworkDevice.NetworkDevice.ID = existingNetworkDevice.ID
				deviceWithNetworkDevice.NetworkDevice.DeviceID = existingDevice.ID
				deviceWithNetworkDevice.NetworkDevice.CreatedAt = existingNetworkDevice.CreatedAt

				if err := tx.Model(&existingNetworkDevice).Updates(deviceWithNetworkDevice.NetworkDevice).Error; err != nil {
					return errors.New("更新网络设备信息失败: " + err.Error())
				}
			} else if errors.Is(err, gorm.ErrRecordNotFound) {
				// 不存在网络设备，创建新的
				deviceWithNetworkDevice.NetworkDevice.DeviceID = existingDevice.ID
				if err := tx.Create(deviceWithNetworkDevice.NetworkDevice).Error; err != nil {
					return errors.New("创建网络设备信息失败: " + err.Error())
				}
			} else {
				return errors.New("查询网络设备时出错: " + err.Error())
			}
		} else {
			// 创建新设备
			if err := tx.Create(deviceWithNetworkDevice.Device).Error; err != nil {
				return errors.New("创建设备基本信息失败: " + err.Error())
			}

			// 设置网络设备的设备ID
			deviceWithNetworkDevice.NetworkDevice.DeviceID = deviceWithNetworkDevice.Device.ID

			// 创建网络设备
			if err := tx.Create(deviceWithNetworkDevice.NetworkDevice).Error; err != nil {
				return errors.New("创建网络设备信息失败: " + err.Error())
			}
		}

		return nil
	}); err != nil {
		return err
	}

	return nil
}

// 辅助函数：使用多种格式解析日期
func parseDateWithMultipleFormats(dateStr string) (time.Time, error) {
	formats := []string{
		// 标准格式（两位数月日）
		"2006-01-02",
		"2006/01/02",
		"2006-01-02 15:04:05",
		"2006/01/02 15:04:05",
		// 单数字月日格式
		"2006-1-2",
		"2006/1/2",
		"2006-1-2 15:04:05",
		"2006/1/2 15:04:05",
	}

	for _, format := range formats {
		if date, err := time.Parse(format, dateStr); err == nil {
			return date, nil
		}
	}

	return time.Time{}, errors.New("无法解析日期: " + dateStr)
}
