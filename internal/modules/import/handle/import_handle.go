package importhandler

import (
	"bytes"
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"gorm.io/gorm"

	filemodel "backend/internal/modules/file/model"
	fileservice "backend/internal/modules/file/service"
	importmodel "backend/internal/modules/import/model"
)

// 为context key创建自定义类型，避免与其他包的字符串key冲突
type contextKey string

const (
	// ImportOptionsKey 是导入选项的上下文键
	importOptionsKey contextKey = "importOptions"
	// GinContextKey 是Gin上下文的键
	ginContextKey contextKey = "ginContext"
	// FileNameKey 是文件名的键
	fileNameKey contextKey = "fileName"
	// ModuleTypeKey 是模块类型的键
	moduleTypeKey contextKey = "moduleType"
)

// ImportHandler 导入处理器接口
type ImportHandler interface {
	// GetRequiredHeaders 获取必需的表头字段
	GetRequiredHeaders() []string

	// ValidateHeaders 验证CSV表头
	ValidateHeaders(headers []string) error

	// ParseRecord 解析CSV记录为模型对象
	ParseRecord(record []string, headerMap map[string]int, rowNum int) (interface{}, error)

	// SaveRecord 保存记录到数据库
	SaveRecord(ctx context.Context, tx *gorm.DB, record interface{}) error
}

// ImportOptions 导入选项
type ImportOptions struct {
	// Overwrite 是否覆盖已存在的记录
	Overwrite bool
	// AppendMode 追加模式："top"-顶部追加, "bottom"-底部追加, ""-不指定(默认)
	AppendMode string
	// HandleSoftDeleted 处理软删除记录的方式: restore=恢复, ignore=忽略, error=报错(默认)
	HandleSoftDeleted string
}

// BaseHandler 基础导入处理器
type BaseHandler struct {
	DB          *gorm.DB
	Options     ImportOptions
	FileService fileservice.FileService
}

// NewBaseHandler 创建基础导入处理器
func NewBaseHandler(db *gorm.DB, fileService fileservice.FileService) *BaseHandler {
	return &BaseHandler{
		DB:          db,
		FileService: fileService,
		Options: ImportOptions{
			Overwrite:  false,
			AppendMode: "",
		},
	}
}

// SetOptions 设置导入选项
func (h *BaseHandler) SetOptions(options ImportOptions) {
	h.Options = options
}

// 检测CSV文件的分隔符
func detectDelimiter(data []byte) rune {
	// 检查前几行来确定可能的分隔符
	dataStr := string(data)
	lines := strings.Split(dataStr, "\n")

	// 只分析前10行或文件的所有行（取较小值）
	maxLines := 10
	if len(lines) < maxLines {
		maxLines = len(lines)
	}

	// 候选分隔符
	delimiters := []rune{',', '\t', ';', '|'}
	counts := make(map[rune]int)

	for i := 0; i < maxLines; i++ {
		if len(lines[i]) == 0 {
			continue
		}

		for _, d := range delimiters {
			counts[d] += strings.Count(lines[i], string(d))
		}
	}

	// 找到出现次数最多的分隔符
	maxCount := 0
	var bestDelimiter = ',' // 默认为逗号

	for d, count := range counts {
		if count > maxCount {
			maxCount = count
			bestDelimiter = d
		}
	}

	return bestDelimiter
}

// 转换编码
func convertToUTF8(data []byte) []byte {
	// 检查是否已经是UTF-8
	if utf8.Valid(data) {
		// 处理BOM标记
		if bytes.HasPrefix(data, []byte{0xEF, 0xBB, 0xBF}) {
			return data[3:] // 移除BOM
		}
		return data
	}

	// 尝试GBK到UTF-8的转换
	reader := transform.NewReader(bytes.NewReader(data), simplifiedchinese.GBK.NewDecoder())
	d, err := io.ReadAll(reader)
	if err != nil {
		// 如果转换失败，返回原始数据
		return data
	}
	return d
}

// SaveImportFile 保存导入的文件
func (h *BaseHandler) SaveImportFile(ctx *gin.Context, file multipart.File, fileName string, moduleType string) (*filemodel.FileResponse, error) {
	// 创建基础存储目录
	uploadDir := "storage/uploads/imports"

	// 根据处理器类型(moduleType)确定存储目录
	docDir := filepath.Join(uploadDir, moduleType)
	if err := os.MkdirAll(docDir, 0750); err != nil {
		return nil, errors.New("创建导入文件目录失败: " + err.Error())
	}

	// 生成唯一文件名
	fileExt := filepath.Ext(fileName)
	storedFileName := uuid.New().String() + fileExt
	storagePath := filepath.Join(docDir, storedFileName)

	// 创建目标文件 - 安全处理路径
	// #nosec G304 -- 文件路径在应用内部构建，不直接来自用户输入
	out, err := os.Create(storagePath)
	if err != nil {
		return nil, errors.New("创建文件失败: " + err.Error())
	}
	defer func() {
		if closeErr := out.Close(); closeErr != nil {
			fmt.Printf("关闭文件失败: %s, 错误: %v\n", storagePath, closeErr)
		}
	}()

	// 复位文件指针
	if _, err = file.Seek(0, io.SeekStart); err != nil {
		return nil, errors.New("重置文件指针失败: " + err.Error())
	}

	// 复制文件内容到目标文件
	fileSize, err := io.Copy(out, file)
	if err != nil {
		// 删除已创建的文件
		if removeErr := os.Remove(storagePath); removeErr != nil {
			fmt.Printf("删除文件失败: %s, 错误: %v\n", storagePath, removeErr)
		}
		return nil, errors.New("复制文件内容失败: " + err.Error())
	}

	// 获取当前用户ID
	var userID uint = 0
	if userIDValue, exists := ctx.Get("userID"); exists {
		if id, ok := userIDValue.(uint); ok {
			userID = id
		}
	}

	// 获取MIME类型
	mimeType := "text/csv"
	if fileExt == ".xlsx" || fileExt == ".xls" {
		mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	}

	// 生成访问URL
	baseURL := getBaseURL(ctx)
	fileURL := fmt.Sprintf("%s/api/v1/file/view/%s", baseURL, storedFileName)

	// 创建文件记录
	fileRecord := filemodel.File{
		FileName:    fileName,
		StoragePath: storagePath,
		FileType:    filemodel.FileTypeCSV,
		FileSize:    fileSize,
		MimeType:    mimeType,
		URL:         fileURL,
		UploadedBy:  userID,
		ModuleType:  moduleType,
		ModuleID:    0,
		Description: "导入文件",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 保存到数据库
	if err := h.DB.Create(&fileRecord).Error; err != nil {
		// 删除已上传的文件
		if removeErr := os.Remove(storagePath); removeErr != nil {
			fmt.Printf("删除文件失败: %s, 错误: %v\n", storagePath, removeErr)
		}
		return nil, errors.New("保存文件记录失败: " + err.Error())
	}

	// 返回文件响应
	return &filemodel.FileResponse{
		ID:         fileRecord.ID,
		URL:        fileURL,
		FileName:   fileName,
		FileSize:   fileSize,
		FileType:   fileRecord.FileType,
		UploadedBy: fileRecord.UploadedBy,
	}, nil
}

// getBaseURL 获取基础URL
func getBaseURL(ctx *gin.Context) string {
	scheme := "http"
	if ctx.Request.TLS != nil {
		scheme = "https"
	}
	return fmt.Sprintf("%s://%s", scheme, ctx.Request.Host)
}

// ProcessImport 处理导入
func (h *BaseHandler) ProcessImport(ctx context.Context, handler ImportHandler, reader io.Reader) (*importmodel.ImportResult, error) {
	// 保存导入的文件
	// 如果上下文中包含gin.Context和文件名
	var fileResp *filemodel.FileResponse
	ginCtx, isGinCtx := ctx.Value(ginContextKey).(*gin.Context)
	fileName, hasFileName := ctx.Value(fileNameKey).(string)
	moduleType, hasModuleType := ctx.Value(moduleTypeKey).(string)
	file, isFile := reader.(multipart.File)

	if isGinCtx && hasFileName && isFile && h.FileService != nil {
		// 保存文件
		var err error
		// 使用从上下文获取的模块类型，如果未提供则使用默认值
		importModuleType := "import"
		if hasModuleType && moduleType != "" {
			importModuleType = moduleType
		}

		fileResp, err = h.SaveImportFile(ginCtx, file, fileName, importModuleType)
		if err != nil {
			return nil, err
		}

		// 重置文件指针位置以便处理
		if _, err = file.Seek(0, io.SeekStart); err != nil {
			return nil, errors.New("重置文件指针失败: " + err.Error())
		}
	}

	// 读取所有数据
	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, errors.New("读取CSV文件失败: " + err.Error())
	}

	// 转换编码为UTF-8
	data = convertToUTF8(data)

	// 检测分隔符
	delimiter := detectDelimiter(data)

	// 创建CSV读取器
	csvReader := csv.NewReader(bytes.NewReader(data))
	csvReader.Comma = delimiter // 设置检测到的分隔符
	csvReader.LazyQuotes = true
	csvReader.TrimLeadingSpace = true
	csvReader.FieldsPerRecord = -1 // 允许每行的字段数不同

	// 读取CSV表头
	headers, err := csvReader.Read()
	if err != nil {
		return nil, errors.New("读取CSV表头失败: " + err.Error())
	}

	// 如果第一行是注释或空行，尝试读取下一行
	if len(headers) == 1 && strings.HasPrefix(headers[0], "#") {
		headers, err = csvReader.Read()
		if err != nil {
			return nil, errors.New("读取CSV表头失败: " + err.Error())
		}
	}

	// 清理表头中的空白字符和BOM
	for i, header := range headers {
		// 移除BOM和空白
		header = strings.TrimSpace(header)
		if i == 0 && strings.HasPrefix(header, "\uFEFF") {
			header = strings.TrimPrefix(header, "\uFEFF")
		}
		headers[i] = header
	}

	// 验证表头
	if err := handler.ValidateHeaders(headers); err != nil {
		return nil, err
	}

	// 创建表头映射
	headerMap := make(map[string]int)
	for i, header := range headers {
		// 清理表头，移除星号(*)标记和空白字符
		cleanHeader := strings.TrimSpace(header)
		if strings.HasPrefix(cleanHeader, "*") {
			cleanHeader = strings.TrimSpace(cleanHeader[1:])
		}
		headerMap[cleanHeader] = i
	}

	// 初始化导入结果
	result := &importmodel.ImportResult{
		Total:        0,
		SuccessCount: 0,
		FailCount:    0,
		Data:         make([]interface{}, 0),
		Failures:     make([]importmodel.ImportError, 0),
	}

	// 如果文件已保存，添加文件信息到结果中
	if fileResp != nil {
		result.FileInfo = fileResp
	}

	// 开始事务
	tx := h.DB.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 将导入选项传递给SaveRecord
	// 将处理器的选项传递给SaveRecord方法的上下文
	// 使得各个处理器实现可以根据选项决定导入行为
	ctxWithOptions := context.WithValue(ctx, importOptionsKey, h.Options)

	// 逐行读取并处理CSV数据
	rowNum := 2 // 从第2行开始计数（表头是第1行）
	for {
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			result.Failures = append(result.Failures, importmodel.ImportError{
				Row:     rowNum,
				Message: "读取行数据失败: " + err.Error(),
				Data:    make(map[string]string),
			})
			result.FailCount++
			rowNum++
			continue
		}

		// 跳过空行或注释行
		if len(record) == 0 || (len(record) == 1 && (record[0] == "" || strings.HasPrefix(record[0], "#"))) {
			rowNum++
			continue
		}

		result.Total++

		// 解析记录
		modelData, err := handler.ParseRecord(record, headerMap, rowNum)
		if err != nil {
			// 记录错误
			rowData := make(map[string]string)
			for i, header := range headers {
				if i < len(record) {
					rowData[header] = record[i]
				}
			}

			result.Failures = append(result.Failures, importmodel.ImportError{
				Row:     rowNum,
				Message: err.Error(),
				Data:    rowData,
			})
			result.FailCount++
			rowNum++
			continue
		}

		// 保存记录
		if err := handler.SaveRecord(ctxWithOptions, tx, modelData); err != nil {
			// 记录错误
			rowData := make(map[string]string)
			for i, header := range headers {
				if i < len(record) {
					rowData[header] = record[i]
				}
			}

			result.Failures = append(result.Failures, importmodel.ImportError{
				Row:     rowNum,
				Message: "保存数据失败: " + err.Error(),
				Data:    rowData,
			})
			result.FailCount++
			rowNum++
			continue
		}

		// 记录成功
		if result.Data == nil {
			result.Data = make([]interface{}, 0)
		}
		dataSlice, ok := result.Data.([]interface{})
		if !ok {
			return nil, errors.New("导入结果数据类型错误")
		}
		result.Data = append(dataSlice, modelData)
		result.SuccessCount++
		rowNum++
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, errors.New("提交事务失败: " + err.Error())
	}

	return result, nil
}

// ProcessOutboundSpareImport 处理出库备件导入
func (h *BaseHandler) ProcessOutboundSpareImport(ctx context.Context, handler ImportHandler, reader io.Reader) (*importmodel.ImportResult, error) {
	// 保存导入的文件
	// 如果上下文中包含gin.Context和文件名
	var fileResp *filemodel.FileResponse
	ginCtx, isGinCtx := ctx.Value(ginContextKey).(*gin.Context)
	fileName, hasFileName := ctx.Value(fileNameKey).(string)
	moduleType, hasModuleType := ctx.Value(moduleTypeKey).(string)
	file, isFile := reader.(multipart.File)

	if isGinCtx && hasFileName && isFile && h.FileService != nil {
		// 保存文件
		var err error
		// 使用从上下文获取的模块类型，如果未提供则使用默认值
		importModuleType := "outbound-spare"
		if hasModuleType && moduleType != "" {
			importModuleType = moduleType
		}

		fileResp, err = h.SaveImportFile(ginCtx, file, fileName, importModuleType)
		if err != nil {
			return nil, err
		}

		// 重置文件指针位置以便处理
		if _, err = file.Seek(0, io.SeekStart); err != nil {
			return nil, errors.New("重置文件指针失败: " + err.Error())
		}
	}

	// 读取所有数据
	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, errors.New("读取CSV文件失败: " + err.Error())
	}

	// 转换编码为UTF-8
	data = convertToUTF8(data)

	// 检测分隔符
	delimiter := detectDelimiter(data)

	// 创建CSV读取器
	csvReader := csv.NewReader(bytes.NewReader(data))
	csvReader.Comma = delimiter // 设置检测到的分隔符
	csvReader.LazyQuotes = true
	csvReader.TrimLeadingSpace = true
	csvReader.FieldsPerRecord = -1 // 允许每行的字段数不同

	// 读取CSV表头
	headers, err := csvReader.Read()
	if err != nil {
		return nil, errors.New("读取CSV表头失败: " + err.Error())
	}

	// 如果第一行是注释或空行，尝试读取下一行
	if len(headers) == 1 && strings.HasPrefix(headers[0], "#") {
		headers, err = csvReader.Read()
		if err != nil {
			return nil, errors.New("读取CSV表头失败: " + err.Error())
		}
	}

	// 清理表头中的空白字符和BOM
	for i, header := range headers {
		// 移除BOM和空白
		header = strings.TrimSpace(header)
		if i == 0 && strings.HasPrefix(header, "\uFEFF") {
			header = strings.TrimPrefix(header, "\uFEFF")
		}
		headers[i] = header
	}

	// 验证表头
	if err := handler.ValidateHeaders(headers); err != nil {
		return nil, err
	}

	// 创建表头映射
	headerMap := make(map[string]int)
	for i, header := range headers {
		// 清理表头，移除星号(*)标记和空白字符
		cleanHeader := strings.TrimSpace(header)
		if strings.HasPrefix(cleanHeader, "*") {
			cleanHeader = strings.TrimSpace(cleanHeader[1:])
		}
		headerMap[cleanHeader] = i
	}

	// 初始化导入结果
	result := &importmodel.ImportResult{
		Total:        0,
		SuccessCount: 0,
		FailCount:    0,
		Data:         make([]interface{}, 0),
		Failures:     make([]importmodel.ImportError, 0),
	}

	// 如果文件已保存，添加文件信息到结果中
	if fileResp != nil {
		result.FileInfo = fileResp
	}

	// 逐行读取并处理CSV数据
	rowNum := 2 // 从第2行开始计数（表头是第1行）
	for {
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			result.Failures = append(result.Failures, importmodel.ImportError{
				Row:     rowNum,
				Message: "读取行数据失败: " + err.Error(),
				Data:    make(map[string]string),
			})
			result.FailCount++
			rowNum++
			continue
		}

		// 跳过空行或注释行
		if len(record) == 0 || (len(record) == 1 && (record[0] == "" || strings.HasPrefix(record[0], "#"))) {
			rowNum++
			continue
		}

		result.Total++

		// 解析记录
		modelData, err := handler.ParseRecord(record, headerMap, rowNum)
		if err != nil {
			// 记录错误
			rowData := make(map[string]string)
			for i, header := range headers {
				if i < len(record) {
					rowData[header] = record[i]
				}
			}

			result.Failures = append(result.Failures, importmodel.ImportError{
				Row:     rowNum,
				Message: err.Error(),
				Data:    rowData,
			})
			result.FailCount++
			rowNum++
			continue
		}

		// 记录成功
		if result.Data == nil {
			result.Data = make([]interface{}, 0)
		}
		dataSlice, ok := result.Data.([]interface{})
		if !ok {
			return nil, errors.New("导入结果数据类型错误")
		}
		result.Data = append(dataSlice, modelData)
		result.SuccessCount++
		rowNum++
	}

	return result, nil
}
