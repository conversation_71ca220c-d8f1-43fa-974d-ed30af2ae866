package importhandler

import (
	"context"
	"errors"
	"strings"

	"gorm.io/gorm"

	"backend/internal/modules/cmdb/model/product"
	fileservice "backend/internal/modules/file/service"
)

// ProductHandler 产品规格导入处理器
type ProductHandler struct {
	*BaseHandler
	DB *gorm.DB
}

// NewProductHandler 创建产品规格导入处理器
func NewProductHandler(db *gorm.DB) *ProductHandler {
	// 创建文件服务
	fileSvc := fileservice.NewFileService(db)

	return &ProductHandler{
		BaseHandler: NewBaseHandler(db, fileSvc),
		DB:          db,
	}
}

// GetRequiredHeaders 获取必需的表头字段
func (h *ProductHandler) GetRequiredHeaders() []string {
	return []string{"物料类型", "规格", "PN号码", "产品类别"}
}

// ValidateHeaders 验证CSV表头
func (h *ProductHandler) ValidateHeaders(headers []string) error {
	requiredHeaders := h.GetRequiredHeaders()

	for _, required := range requiredHeaders {
		found := false
		for _, header := range headers {
			// 清理表头，移除可能的星号(*)标记和空白字符
			cleanHeader := strings.TrimSpace(header)
			if strings.HasPrefix(cleanHeader, "*") {
				cleanHeader = strings.TrimSpace(cleanHeader[1:])
			}

			if cleanHeader == required {
				found = true
				break
			}
		}

		if !found {
			return errors.New("CSV文件缺少必需字段: " + required)
		}
	}

	return nil
}

// ParseRecord 解析CSV记录为产品规格对象
func (h *ProductHandler) ParseRecord(record []string, headerMap map[string]int, rowNum int) (interface{}, error) {
	// 获取字段索引
	materialTypeIndex, hasMaterialType := headerMap["物料类型"]
	brandIndex, hasBrand := headerMap["品牌"]
	modelIndex, hasModel := headerMap["型号"]
	specIndex, hasSpec := headerMap["规格"]
	pnIndex, hasPN := headerMap["PN号码"]
	productCategoryIndex, hasProductCategory := headerMap["产品类别"]

	// 验证必填字段
	if !hasMaterialType || materialTypeIndex >= len(record) {
		return nil, errors.New("缺少物料类型")
	}
	materialType := strings.TrimSpace(record[materialTypeIndex])
	if materialType == "" {
		return nil, errors.New("物料类型不能为空")
	}

	// 品牌不再是必填项
	var brand string
	if hasBrand && brandIndex < len(record) {
		brand = strings.TrimSpace(record[brandIndex])
	}

	// 型号不再是必填项
	var model string
	if hasModel && modelIndex < len(record) {
		model = strings.TrimSpace(record[modelIndex])
	}

	if !hasSpec || specIndex >= len(record) {
		return nil, errors.New("缺少规格")
	}
	spec := strings.TrimSpace(record[specIndex])
	if spec == "" {
		return nil, errors.New("规格不能为空")
	}

	if !hasPN || pnIndex >= len(record) {
		return nil, errors.New("缺少PN号码")
	}
	pn := strings.TrimSpace(record[pnIndex])
	if pn == "" {
		return nil, errors.New("PN号码不能为空")
	}

	// 创建产品对象
	productObj := &product.Product{
		MaterialType: materialType,
		Brand:        brand,
		Model:        model,
		Spec:         spec,
		PN:           pn,
	}

	// 设置可选字段
	if hasProductCategory && productCategoryIndex < len(record) {
		productObj.ProductCategory = strings.TrimSpace(record[productCategoryIndex])
	}

	return productObj, nil
}

// SaveRecord 保存产品规格记录到数据库
func (h *ProductHandler) SaveRecord(ctx context.Context, tx *gorm.DB, record interface{}) error {
	productObj, ok := record.(*product.Product)
	if !ok {
		return errors.New("记录类型错误")
	}

	// 从上下文中获取导入选项
	var options ImportOptions
	optionsValue := ctx.Value(importOptionsKey)
	if optionsValue != nil {
		var ok bool
		options, ok = optionsValue.(ImportOptions)
		if !ok {
			return errors.New("导入选项类型错误")
		}
	}

	// 检查是否存在相同的产品（使用物料类型、品牌、型号、规格和PN组合来判断）
	var existingProduct product.Product
	var isExist bool
	var isSoftDeleted bool

	// 如果覆盖模式，查询所有记录（包括软删除的）
	if options.Overwrite {
		if err := tx.Unscoped().Where("material_type = ? AND brand = ? AND model = ? AND spec = ? AND pn = ?",
			productObj.MaterialType, productObj.Brand, productObj.Model, productObj.Spec, productObj.PN).
			First(&existingProduct).Error; err == nil {
			isExist = true
			// 检查是否为软删除状态
			isSoftDeleted = !existingProduct.DeletedAt.Time.IsZero()
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	} else {
		// 非覆盖模式下，只查询未删除的记录
		if err := tx.Where("material_type = ? AND brand = ? AND model = ? AND spec = ? AND pn = ?",
			productObj.MaterialType, productObj.Brand, productObj.Model, productObj.Spec, productObj.PN).
			First(&existingProduct).Error; err == nil {
			isExist = true
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}

	// 如果存在相同的产品且未设置覆盖选项，则返回错误
	if isExist && !options.Overwrite {
		return errors.New("已存在相同的产品规格")
	}

	// 如果设置了覆盖选项且存在相同的产品（无论是否被软删除），则更新现有记录
	if isExist && options.Overwrite {
		// 更新产品数据，保留原ID和创建时间
		productObj.ID = existingProduct.ID
		productObj.CreatedAt = existingProduct.CreatedAt

		// 如果记录被软删除，先恢复它
		if isSoftDeleted {
			if err := tx.Unscoped().Model(&existingProduct).Update("deleted_at", nil).Error; err != nil {
				return errors.New("恢复软删除的产品规格失败: " + err.Error())
			}
		}

		// 更新产品记录
		if err := tx.Model(&existingProduct).Updates(productObj).Error; err != nil {
			return errors.New("更新产品规格失败: " + err.Error())
		}

		return nil
	}

	// 创建新产品规格
	if err := tx.Create(productObj).Error; err != nil {
		return errors.New("创建产品规格失败: " + err.Error())
	}

	return nil
}
