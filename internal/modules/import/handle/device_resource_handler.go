package importhandler

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"

	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/location"
	"backend/internal/modules/cmdb/model/template"
	fileservice "backend/internal/modules/file/service"
	"backend/pkg/utils"
)

// DeviceResourceHandler 设备资源关联导入处理器
type DeviceResourceHandler struct {
	*BaseHandler
	DB *gorm.DB
}

// NewDeviceResourceHandler 创建设备资源关联导入处理器
func NewDeviceResourceHandler(db *gorm.DB) *DeviceResourceHandler {
	// 创建文件服务
	fileSvc := fileservice.NewFileService(db)

	return &DeviceResourceHandler{
		BaseHandler: NewBaseHandler(db, fileSvc),
		DB:          db,
	}
}

// GetRequiredHeaders 获取必需的表头字段
func (h *DeviceResourceHandler) GetRequiredHeaders() []string {
	return []string{"SN", "资产类型", "房间名称", "机柜名称", "套餐模板"}
}

// ValidateHeaders 验证CSV表头
func (h *DeviceResourceHandler) ValidateHeaders(headers []string) error {
	requiredHeaders := h.GetRequiredHeaders()

	for _, required := range requiredHeaders {
		found := false
		for _, header := range headers {
			// 清理表头，移除可能的星号(*)标记和空白字符
			cleanHeader := strings.TrimSpace(header)
			if strings.HasPrefix(cleanHeader, "*") {
				cleanHeader = strings.TrimSpace(cleanHeader[1:])
			}

			if cleanHeader == required {
				found = true
				break
			}
		}

		if !found {
			return errors.New("CSV文件缺少必需字段: " + required)
		}
	}

	return nil
}

// 定义包含设备和资源信息的组合结构
type DeviceResourceData struct {
	Device   *asset.Device
	Resource *asset.Resource
}

// ParseRecord 解析CSV记录为设备和资源对象
func (h *DeviceResourceHandler) ParseRecord(record []string, headerMap map[string]int, rowNum int) (interface{}, error) {
	// 获取设备相关字段索引
	snIndex, hasSN := headerMap["SN"]
	brandIndex, hasBrand := headerMap["厂商"]
	modelIndex, hasModel := headerMap["型号"]
	assetTypeIndex, hasAssetType := headerMap["资产类型"]
	assetStatusIndex, hasAssetStatus := headerMap["资产状态"]
	hardwareStatusIndex, hasHardwareStatus := headerMap["硬件状态"]
	poIndex, hasPO := headerMap["采购合同"]
	purchaseDateIndex, hasPurchaseDate := headerMap["采购时间"]
	warrantyExpireIndex, hasWarrantyExpire := headerMap["过保时间"]
	priceIndex, hasPrice := headerMap["金额"]
	residualValueIndex, hasResidualValue := headerMap["残值"]
	templateNameIndex, hasTemplateName := headerMap["套餐模板"]

	// 获取资源相关字段索引
	bizStatusIndex, hasBizStatus := headerMap["业务状态"]
	resStatusIndex, hasResStatus := headerMap["资源状态"]
	projectIndex, hasProject := headerMap["所属项目"]
	cabinetNameIndex, hasCabinetName := headerMap["机柜名称"]
	roomNameIndex, hasRoomName := headerMap["房间名称"]
	rackPosIndex, hasRackPos := headerMap["机架位"]
	bmcIPIndex, hasBmcIP := headerMap["BMC_IP"]
	vpcIPIndex, hasVpcIP := headerMap["VPC_IP"]
	tenantIPIndex, hasTenantIP := headerMap["租户IP"]
	hostnameIndex, hasHostname := headerMap["主机名"]
	clusterIndex, hasCluster := headerMap["集群"]
	isBackupIndex, hasIsBackup := headerMap["是否备机"]
	rackingTimeIndex, hasRackingTime := headerMap["上架时间"]
	deliveryTimeIndex, hasDeliveryTime := headerMap["交付时间"]
	remarkIndex, hasRemark := headerMap["备注"]

	// 验证必填字段
	if !hasSN || snIndex >= len(record) {
		return nil, errors.New("缺少SN")
	}

	sn := strings.TrimSpace(record[snIndex])
	if sn == "" {
		return nil, errors.New("SN不能为空")
	}

	if !hasAssetType || assetTypeIndex >= len(record) {
		return nil, errors.New("缺少资产类型")
	}

	assetType := strings.TrimSpace(record[assetTypeIndex])
	if assetType == "" {
		return nil, errors.New("资产类型不能为空")
	}

	// 创建设备对象
	device := &asset.Device{
		SN:        sn,
		AssetType: convertAssetType(assetType),
	}

	// 设置设备其他属性
	if hasBrand && brandIndex < len(record) {
		device.Brand = strings.TrimSpace(record[brandIndex])
	}

	if hasModel && modelIndex < len(record) {
		device.Model = strings.TrimSpace(record[modelIndex])
	}

	if hasAssetStatus && assetStatusIndex < len(record) {
		device.AssetStatus = convertAssetStatus(strings.TrimSpace(record[assetStatusIndex]))
	} else {
		device.AssetStatus = asset.AssetStatusPendingStorage // 默认状态
	}

	if hasHardwareStatus && hardwareStatusIndex < len(record) {
		device.HardwareStatus = convertHardwareStatus(strings.TrimSpace(record[hardwareStatusIndex]))
	} else {
		device.HardwareStatus = asset.HardwareStatusNormal // 默认状态
	}

	if hasPO && poIndex < len(record) {
		device.PurchaseOrder = strings.TrimSpace(record[poIndex])
	}

	if hasPurchaseDate && purchaseDateIndex < len(record) {
		dateStr := strings.TrimSpace(record[purchaseDateIndex])
		if dateStr != "" {
			// 尝试多种格式解析日期
			formats := []string{
				"2006-01-02",
				"2006/01/02",
				"2006/1/02",
				"2006/01/2",
				"2006/1/2",
				"2006.01.02",
				"2006.1.02",
				"2006.01.2",
				"2006.1.2",
			}

			var date time.Time
			var err error
			var parsed bool

			for _, format := range formats {
				date, err = time.Parse(format, dateStr)
				if err == nil {
					parsed = true
					device.PurchaseDate = utils.Date(date)
					break
				}
			}

			if !parsed {
				return nil, errors.New("采购时间格式错误，支持的格式: YYYY-MM-DD, YYYY/MM/DD，请确保日期格式正确")
			}
		}
	}

	if hasWarrantyExpire && warrantyExpireIndex < len(record) {
		dateStr := strings.TrimSpace(record[warrantyExpireIndex])
		if dateStr != "" {
			// 尝试多种格式解析日期
			formats := []string{
				"2006-01-02",
				"2006/01/02",
				"2006/1/02",
				"2006/01/2",
				"2006/1/2",
				"2006.01.02",
				"2006.1.02",
				"2006.01.2",
				"2006.1.2",
			}

			var date time.Time
			var err error
			var parsed bool

			for _, format := range formats {
				date, err = time.Parse(format, dateStr)
				if err == nil {
					parsed = true
					device.WarrantyExpire = utils.Date(date)
					break
				}
			}

			if !parsed {
				return nil, errors.New("过保时间格式错误，支持的格式: YYYY-MM-DD, YYYY/MM/DD，请确保日期格式正确")
			}
		}
	}

	if hasPrice && priceIndex < len(record) {
		priceStr := strings.TrimSpace(record[priceIndex])
		if priceStr != "" {
			price, err := strconv.ParseFloat(priceStr, 64)
			if err != nil {
				return nil, errors.New("金额格式错误")
			}
			device.Price = price
		}
	}

	if hasResidualValue && residualValueIndex < len(record) {
		valueStr := strings.TrimSpace(record[residualValueIndex])
		if valueStr != "" {
			value, err := strconv.ParseFloat(valueStr, 64)
			if err != nil {
				return nil, errors.New("残值格式错误")
			}
			device.ResidualValue = value
		}
	}

	// 处理套餐模板
	if hasTemplateName && templateNameIndex < len(record) {
		templateName := strings.TrimSpace(record[templateNameIndex])
		if templateName != "" {
			// 查找套餐模板ID
			var template template.MachineTemplate
			if err := h.DB.Where("template_name = ?", templateName).First(&template).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return nil, errors.New("找不到名称为 '" + templateName + "' 的套餐模板")
				}
				return nil, errors.New("查询套餐模板失败: " + err.Error())
			}
			device.TemplateID = template.ID
		}
	}

	if hasRemark && remarkIndex < len(record) {
		device.Remark = strings.TrimSpace(record[remarkIndex])
	}

	device.LastStatusChange = utils.Date(time.Now())

	// 创建资源对象
	resource := &asset.Resource{
		SN: sn, // 使用相同的SN
	}

	// 设置主机名
	if hasHostname && hostnameIndex < len(record) {
		resource.Hostname = strings.TrimSpace(record[hostnameIndex])
	}

	// 设置租户IP
	if hasTenantIP && tenantIPIndex < len(record) {
		resource.TenantIP = strings.TrimSpace(record[tenantIPIndex])
	}

	// 设置资源其他属性
	if hasBizStatus && bizStatusIndex < len(record) {
		resource.BizStatus = convertBizStatus(strings.TrimSpace(record[bizStatusIndex]))
	} else {
		resource.BizStatus = asset.BizStatusMaintaining // 默认状态
	}

	if hasResStatus && resStatusIndex < len(record) {
		resource.ResStatus = convertResStatus(strings.TrimSpace(record[resStatusIndex]))
	} else {
		resource.ResStatus = asset.ResStatusUnallocated // 默认状态
	}

	if hasProject && projectIndex < len(record) {
		resource.Project = strings.TrimSpace(record[projectIndex])
	}

	// 处理房间和机柜信息
	var roomID uint

	// 首先检查必需字段
	if !hasRoomName || roomNameIndex >= len(record) {
		return nil, errors.New("缺少房间名称")
	}

	roomName := strings.TrimSpace(record[roomNameIndex])
	if roomName == "" {
		return nil, errors.New("房间名称不能为空")
	}

	if !hasCabinetName || cabinetNameIndex >= len(record) {
		return nil, errors.New("缺少机柜名称")
	}

	cabinetName := strings.TrimSpace(record[cabinetNameIndex])
	if cabinetName == "" {
		return nil, errors.New("机柜名称不能为空")
	}

	// 如果指定了房间，先获取房间ID
	var room location.Room
	if err := h.DB.Where("name = ?", roomName).First(&room).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("找不到名称为 '" + roomName + "' 的房间")
		}
		return nil, errors.New("查询房间信息失败: " + err.Error())
	}
	roomID = room.ID
	resource.RoomID = roomID

	// 在指定房间中查找机柜
	var cabinet location.Cabinet
	if err := h.DB.Where("name = ? AND room_id = ?", cabinetName, roomID).First(&cabinet).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("在房间 '" + roomName + "' 中找不到名称为 '" + cabinetName + "' 的机柜")
		}
		return nil, errors.New("查询机柜信息失败: " + err.Error())
	}
	resource.CabinetID = cabinet.ID

	if hasRackPos && rackPosIndex < len(record) {
		rackPosStr := strings.TrimSpace(record[rackPosIndex])
		if rackPosStr != "" {
			rackPos, err := strconv.Atoi(rackPosStr)
			if err != nil {
				return nil, errors.New("机架位必须是数字")
			}
			resource.RackPosition = rackPos
		}
	}

	if hasBmcIP && bmcIPIndex < len(record) {
		resource.BmcIP = strings.TrimSpace(record[bmcIPIndex])
	}

	if hasVpcIP && vpcIPIndex < len(record) {
		resource.VpcIP = strings.TrimSpace(record[vpcIPIndex])
	}

	if hasCluster && clusterIndex < len(record) {
		resource.Cluster = strings.TrimSpace(record[clusterIndex])
	}

	if hasIsBackup && isBackupIndex < len(record) {
		isBackupStr := strings.TrimSpace(record[isBackupIndex])
		if isBackupStr != "" {
			resource.IsBackup = isBackupStr == "是" || isBackupStr == "true" || isBackupStr == "1"
		}
	}

	if hasRackingTime && rackingTimeIndex < len(record) {
		dateStr := strings.TrimSpace(record[rackingTimeIndex])
		if dateStr != "" {
			// 尝试多种格式解析日期
			formats := []string{
				"2006-01-02",
				"2006/01/02",
				"2006/1/02",
				"2006/01/2",
				"2006/1/2",
				"2006.01.02",
				"2006.1.02",
				"2006.01.2",
				"2006.1.2",
			}

			var date time.Time
			var err error
			var parsed bool

			for _, format := range formats {
				date, err = time.Parse(format, dateStr)
				if err == nil {
					parsed = true
					resource.RackingTime = utils.Date(date)
					break
				}
			}

			if !parsed {
				return nil, errors.New("上架时间格式错误，支持的格式: YYYY-MM-DD, YYYY/MM/DD，请确保日期格式正确")
			}
		}
	}

	if hasDeliveryTime && deliveryTimeIndex < len(record) {
		dateStr := strings.TrimSpace(record[deliveryTimeIndex])
		if dateStr != "" {
			// 尝试多种格式解析日期
			formats := []string{
				"2006-01-02",
				"2006/01/02",
				"2006/1/02",
				"2006/01/2",
				"2006/1/2",
				"2006.01.02",
				"2006.1.02",
				"2006.01.2",
				"2006.1.2",
			}

			var date time.Time
			var err error
			var parsed bool

			for _, format := range formats {
				date, err = time.Parse(format, dateStr)
				if err == nil {
					parsed = true
					resource.DeliveryTime = utils.Date(date)
					break
				}
			}

			if !parsed {
				return nil, errors.New("交付时间格式错误，支持的格式: YYYY-MM-DD, YYYY/MM/DD，请确保日期格式正确")
			}
		}
	}

	resource.LastBizStatusChange = utils.Date(time.Now())

	return &DeviceResourceData{
		Device:   device,
		Resource: resource,
	}, nil
}

// SaveRecord 保存设备和资源记录到数据库
func (h *DeviceResourceHandler) SaveRecord(ctx context.Context, tx *gorm.DB, record interface{}) error {
	data, ok := record.(*DeviceResourceData)
	if !ok {
		return errors.New("记录类型错误")
	}

	// 从上下文中获取导入选项
	var options ImportOptions
	optionsValue := ctx.Value(importOptionsKey)
	if optionsValue != nil {
		var ok bool
		options, ok = optionsValue.(ImportOptions)
		if !ok {
			return errors.New("导入选项类型错误")
		}
	}

	// 检查是否存在相同SN的设备（包括软删除的记录）
	var existingDevice asset.Device
	var existingResource asset.Resource
	var isExist bool
	var isSoftDeleted bool

	// 如果覆盖模式，查询所有记录（包括软删除的）
	if options.Overwrite {
		if err := tx.Unscoped().Where("sn = ?", data.Device.SN).First(&existingDevice).Error; err == nil {
			isExist = true
			// 检查是否为软删除状态
			isSoftDeleted = !existingDevice.DeletedAt.Time.IsZero()
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	} else {
		// 非覆盖模式下，只查询未删除的记录
		if err := tx.Where("sn = ?", data.Device.SN).First(&existingDevice).Error; err == nil {
			isExist = true
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}

	// 如果存在相同SN的设备且未设置覆盖选项，则返回错误
	if isExist && !options.Overwrite {
		return errors.New("已存在相同SN的设备: " + data.Device.SN)
	}

	// 如果设置了覆盖选项且存在相同SN的设备（无论是否被软删除），则更新现有记录
	if isExist && options.Overwrite {
		// 先查找关联的资源记录（包括已删除的）
		if err := tx.Unscoped().Where("asset_id = ?", existingDevice.ID).First(&existingResource).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			// 如果资源不存在，后面会创建新的
		}

		// 更新设备数据，保留原ID和创建时间
		data.Device.ID = existingDevice.ID
		data.Device.CreatedAt = existingDevice.CreatedAt

		// 如果记录被软删除，先恢复它
		if isSoftDeleted {
			if err := tx.Unscoped().Model(&existingDevice).Update("deleted_at", nil).Error; err != nil {
				return errors.New("恢复软删除的设备失败: " + err.Error())
			}
		}

		// 更新设备记录
		if err := tx.Model(&existingDevice).Updates(data.Device).Error; err != nil {
			return errors.New("更新设备失败: " + err.Error())
		}

		// 如果资源记录存在，则更新资源
		if existingResource.ID > 0 {
			// 更新资源数据，保留原ID
			data.Resource.ID = existingResource.ID
			data.Resource.AssetID = existingDevice.ID
			data.Resource.CreatedAt = existingResource.CreatedAt

			// 如果资源被软删除，先恢复它
			if !existingResource.DeletedAt.Time.IsZero() {
				if err := tx.Unscoped().Model(&existingResource).Update("deleted_at", nil).Error; err != nil {
					return errors.New("恢复软删除的资源失败: " + err.Error())
				}
			}

			// 更新资源记录
			if err := tx.Model(&existingResource).Updates(data.Resource).Error; err != nil {
				return errors.New("更新资源失败: " + err.Error())
			}
		} else {
			// 如果资源不存在，则创建新资源
			data.Resource.AssetID = existingDevice.ID
			if err := tx.Create(data.Resource).Error; err != nil {
				return errors.New("创建资源失败: " + err.Error())
			}
		}

		return nil
	}

	// 检查机柜ID是否存在
	if data.Resource.CabinetID > 0 {
		var cabinet location.Cabinet
		if err := tx.First(&cabinet, data.Resource.CabinetID).Error; err != nil {
			return errors.New("机柜ID不存在: " + strconv.FormatUint(uint64(data.Resource.CabinetID), 10))
		}
	}

	// 检查房间ID是否存在
	if data.Resource.RoomID > 0 {
		var room location.Room
		if err := tx.First(&room, data.Resource.RoomID).Error; err != nil {
			return errors.New("房间ID不存在: " + strconv.FormatUint(uint64(data.Resource.RoomID), 10))
		}
	}

	// 创建新设备
	if err := tx.Create(data.Device).Error; err != nil {
		return errors.New("创建设备失败: " + err.Error())
	}

	// 设置资源的AssetID为新创建的设备ID
	data.Resource.AssetID = data.Device.ID

	// 创建资源
	if err := tx.Create(data.Resource).Error; err != nil {
		return errors.New("创建资源失败: " + err.Error())
	}

	return nil
}

// 辅助函数 - 将中文状态转换为英文代码
func convertAssetType(assetType string) string {
	switch assetType {
	case "服务器":
		return asset.AssetTypeServer
	case "GPU服务器":
		return asset.AssetTypeGPUServer
	case "交换机":
		return asset.AssetTypeSwitch
	case "路由器":
		return asset.AssetTypeRouter
	case "防火墙":
		return asset.AssetTypeFirewall
	case "网络设备":
		return asset.AssetTypeNetwork
	case "存储设备":
		return asset.AssetTypeStorage
	default:
		return assetType
	}
}

func convertAssetStatus(status string) string {
	if status == "" {
		return asset.AssetStatusPendingStorage
	}

	switch status {
	case "待入库":
		return asset.AssetStatusPendingStorage
	case "已入库":
		return asset.AssetStatusInStorage
	case "待出库":
		return asset.AssetStatusPendingOutbound
	case "已出库":
		return asset.AssetStatusOutbound
	case "闲置中":
		return asset.AssetStatusIdle
	case "使用中":
		return asset.AssetStatusInUse
	case "维修中":
		return asset.AssetStatusMaintaining
	case "待报废":
		return asset.AssetStatusPendingScrap
	case "已报废":
		return asset.AssetStatusScrapped
	default:
		return status
	}
}

func convertHardwareStatus(status string) string {
	if status == "" {
		return asset.HardwareStatusNormal
	}

	switch status {
	case "正常":
		return asset.HardwareStatusNormal
	case "故障":
		return asset.HardwareStatusFaulty
	case "警告":
		return asset.HardwareStatusWaning
	default:
		return status
	}
}

func convertBizStatus(status string) string {
	if status == "" {
		return asset.BizStatusMaintaining
	}

	switch status {
	case "ACTIVE":
		return asset.BizStatusActive
	case "MAINTAINING":
		return asset.BizStatusMaintaining
	case "OUTAGE":
		return asset.BizStatusOutage
	default:
		return status
	}
}

func convertResStatus(status string) string {
	if status == "" {
		return asset.ResStatusUnallocated
	}

	switch status {
	case "已分配":
		return asset.ResStatusAllocated
	case "未分配":
		return asset.ResStatusUnallocated
	default:
		return status
	}
}
