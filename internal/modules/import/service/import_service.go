package service

import (
	"backend/internal/modules/import/model"
	"context"
	"io"
)

// ImportOptions 导入选项
type ImportOptions struct {
	// Overwrite 是否覆盖已存在的记录
	Overwrite bool
	// ImportMode 导入模式: overwrite=覆盖, append_bottom=底部追加, append_top=顶部追加
	ImportMode string
	// HandleSoftDeleted 处理软删除记录的方式: restore=恢复, ignore=忽略, error=报错(默认)
	HandleSoftDeleted string
}

// ImportService 导入服务接口
type ImportService interface {
	// ImportData 导入数据
	// 修改返回类型为 *model.ImportResult
	ImportData(ctx context.Context, modelType string, reader io.Reader, options ...ImportOptions) (*model.ImportResult, error)

	// GetImportTemplate 获取导入模板
	GetImportTemplate(modelType string) ([]byte, string, error)

	// 出库备件导入
	ImportOutboundSpareData(ctx context.Context, modelType string, reader io.Reader, options ...ImportOptions) (*model.ImportResult, error)
}
