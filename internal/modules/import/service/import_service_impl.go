package service

import (
	"context"
	"errors"
	"io"
	"log"
	"strings"

	"gorm.io/gorm"

	assetRepo "backend/internal/modules/cmdb/repository/asset"
	inventoryRepo "backend/internal/modules/cmdb/repository/inventory"
	assetSvc "backend/internal/modules/cmdb/service/asset"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"
	fileservice "backend/internal/modules/file/service"
	importhandler "backend/internal/modules/import/handle"
	"backend/internal/modules/import/model"
)

// 为context key创建自定义类型，避免与其他包的字符串key冲突
type contextKey string

const (
	// ModuleTypeKey 是模块类型的键
	moduleTypeKey contextKey = "moduleType"
)

// importServiceImpl 导入服务实现
type importServiceImpl struct {
	db       *gorm.DB
	handlers map[string]importhandler.ImportHandler
}

// NewImportService 创建导入服务
func NewImportService(db *gorm.DB) ImportService {
	// 创建服务实例
	service := &importServiceImpl{
		db:       db,
		handlers: make(map[string]importhandler.ImportHandler),
	}

	// 注册处理器
	service.handlers["region"] = importhandler.NewRegionHandler(db)
	service.handlers["device-resource"] = importhandler.NewDeviceResourceHandler(db)
	service.handlers["spare"] = importhandler.NewSpareHandler(db)
	service.handlers["network-device"] = importhandler.NewNetworkDeviceHandler(db)
	service.handlers["product"] = importhandler.NewProductHandler(db)
	service.handlers["room"] = importhandler.NewRoomHandler(db)
	service.handlers["cabinet"] = importhandler.NewCabinetHandler(db)
	service.handlers["outbound-spare"] = importhandler.NewOutboundSpareHandler(db)

	return service
}

// ImportData 导入数据
func (s *importServiceImpl) ImportData(ctx context.Context, modelType string, reader io.Reader, options ...ImportOptions) (*model.ImportResult, error) {

	// 获取对应的处理器
	handler, exists := s.handlers[strings.ToLower(modelType)]
	if !exists {
		return nil, errors.New("不支持的模型类型: " + modelType)
	}

	// 创建文件服务
	fileService := fileservice.NewFileService(s.db)

	// 创建基础处理器并设置选项
	baseHandler := importhandler.NewBaseHandler(s.db, fileService)

	// 如果提供了选项，则设置
	if len(options) > 0 {
		// 设置处理选项
		handlerOptions := importhandler.ImportOptions{
			Overwrite: options[0].Overwrite,
		}

		// 设置导入模式
		if options[0].ImportMode != "" {
			// 如果是非覆盖模式，设置相应的导入模式
			// 覆盖模式已经由Overwrite字段处理
			switch options[0].ImportMode {
			case "append_bottom":
				// 处理底部追加模式
				handlerOptions.AppendMode = "bottom"
			case "append_top":
				// 处理顶部追加模式
				handlerOptions.AppendMode = "top"
			}
		}

		// 设置处理软删除记录的方式
		if options[0].HandleSoftDeleted != "" {
			handlerOptions.HandleSoftDeleted = options[0].HandleSoftDeleted
		}

		baseHandler.SetOptions(handlerOptions)
	}

	// 将模块类型添加到上下文
	ctxWithModuleType := context.WithValue(ctx, moduleTypeKey, modelType)

	// 使用基础处理器处理导入
	result, err := baseHandler.ProcessImport(ctxWithModuleType, handler, reader)

	// 如果是备件导入且成功，自动调用库存同步任务
	if err == nil && strings.ToLower(modelType) == "spare" && result.SuccessCount > 0 {
		// 获取备件服务
		spareService := s.getSpareService()
		if spareService != nil {
			// 执行库存同步
			syncErr := spareService.SyncInventoryWithSpareStatus(ctx)
			if syncErr != nil {
				// 记录错误但不影响导入结果
				log.Printf("备件导入后同步库存失败: %v", syncErr)
			} else {
				log.Printf("备件导入后同步库存成功，共同步 %d 个备件", result.SuccessCount)
			}
		}
	}

	return result, err
}

// 获取备件服务实例
func (s *importServiceImpl) getSpareService() assetSvc.SpareService {
	// 从依赖注入容器获取SpareService实例
	spareRepo := assetRepo.NewSpareRepository(s.db)
	deviceRepo:=assetRepo.NewDeviceRepository(s.db)
	deviceService := assetSvc.NewDeviceService(deviceRepo)
	inventoryRepo := inventoryRepo.NewInventoryRepository(s.db)
	inventoryService := inventorySvc.NewInventoryService(inventoryRepo, s.db)

	return assetSvc.NewSpareService(spareRepo, inventoryService,deviceService)
}

// GetImportTemplate 获取导入模板
func (s *importServiceImpl) GetImportTemplate(modelType string) ([]byte, string, error) {
	switch strings.ToLower(modelType) {
	case "region":
		// Region导入模板
		content := "\uFEFF*区域名称,状态,描述信息\n示例区域,启用/禁用,这是示例区域的描述\n"
		return []byte(content), "region_import_template.csv", nil

	case "launch":
		// Launch导入模板
		content := "\uFEFF*设备SN,厂商,型号,*主机名,*VPC_IP,*BMC_IP\n" + "# * 表示必填字段\n" + "# 必填字段: SN, 主机名, VPC_IP, BMC_IP\n" +
			"SN12345,Super,SYS-821GE,ytp-3j10-gpu066,**********,***********,\n"
		return []byte(content), "device_resource_import_template.csv", nil

	case "device-resource":
		// 服务器资源关联导入模板
		// 添加状态类型选择提示
		assetStatusOptions := "待入库|已入库|待出库|已出库|闲置中|使用中|维修中|待报废|已报废"
		hardwareStatusOptions := "正常|故障|警告"
		bizStatusOptions := "ACTIVE|MAINTAINING|OUTAGE"
		resStatusOptions := "已分配|未分配"
		assetTypeOptions := "GPU服务器|服务器|网络设备|存储设备"

		// 构建包含状态选项的模板
		content := "\uFEFF*SN,厂商,型号,*资产类型,资产状态,硬件状态,采购合同,采购时间,过保时间,金额,残值,*套餐模板,业务状态,资源状态,所属项目,*房间名称,*机柜名称,机架位,主机名,BMC_IP,VPC_IP,租户IP,集群,是否备机,上架时间,交付时间,备注\n" +
			"# * 表示必填字段\n" +
			"# 必填字段: SN, 资产类型, 房间名称, 机柜名称、套餐模版\n" +
			"# 资产类型可选值: " + assetTypeOptions + "\n" +
			"# 资产状态可选值: " + assetStatusOptions + "\n" +
			"# 硬件状态可选值: " + hardwareStatusOptions + "\n" +
			"# 业务状态可选值: " + bizStatusOptions + "\n" +
			"# 资源状态可选值: " + resStatusOptions + "\n" +
			"# 是否备机可选值: 是|否\n" +
			"# 日期格式: YYYY-MM-DD 或 YYYY/MM/DD\n" +
			"# 注意: 房间名称和机柜名称必须是系统中已存在的，且机柜必须属于指定的房间\n" +
			"SN12345,Dell,R740,服务器,待入库,正常,PO12345,2023-01-01,2026-01-01,10000,1000,标准套餐A,ACTIVE,未分配,测试项目,机房A-1001,A01,42,host-srv01,***********,********,**********,ClusterA,否,2023-01-01,2023-01-15,示例服务器\n"
		return []byte(content), "device_resource_import_template.csv", nil

	case "spare":
		// 备件导入模板
		sourceTypeOptions := "新购|拆机|调拨|其他"
		assetStatusOptions := "待入库|已入库|待出库|已出库|闲置中|使用中|维修中|待报废|已报废"
		hardwareStatusOptions := "正常|故障|警告"

		// 构建包含选项的模板
		content := "\uFEFFSN,*产品PN号码,品牌,*仓库,*来源类型,*资产状态,*硬件状态,金额,存放位置,固件版本,购买时间,过保时间,关联资产SN,备注\n" +
			"# * 表示必填字段\n" +
			"# 必填字段: 产品PN号码, 仓库, 来源类型, 资产状态, 硬件状态\n" +
			"# 注意: SN为选填字段，可以为空\n" +
			"# 产品PN号码必须是系统中已存在的产品，可在\"PN信息或者规格信息管理\"中查看\n" +
			"# 品牌为选填字段，但当存在相同PN不同品牌的产品时，需要提供品牌以确定具体产品\n" +
			"# 仓库可以填写仓库名称或编码，系统会自动匹配，可在\"仓库管理\"中查看\n" +
			"# 来源类型可选值: " + sourceTypeOptions + "\n" +
			"# 资产状态可选值: " + assetStatusOptions + "\n" +
			"# 硬件状态可选值: " + hardwareStatusOptions + "\n" +
			"# 日期格式: YYYY-MM-DD 或 YYYY/MM/DD\n" +
			"SPRCPU001,BX806958280,Intel,主数据中心备件仓,新购,待入库,正常,8000,架位A-3,V1.0,2023-01-01,2026-01-01,,Intel CPU备件\n" +
			",M393A2K43BB1-CRC,Samsung,WH-002,拆机,闲置中,正常,600,架位B-32,V2.0,2023-02-15,2025-02-15,SN12345,从服务器SN12345拆下\n"
		return []byte(content), "spare_import_template.csv", nil

	case "network-device":
		// 网络设备导入模板
		assetStatusOptions := "待入库|已入库|待出库|已出库|闲置中|使用中|维修中|待报废|已报废"
		hardwareStatusOptions := "正常|故障|警告"
		bizStatusOptions := "ACTIVE|MAINTAINING|OUTAGE"
		roleOptions := "S0|S1|GS0|GS1|RGW|RSW|DAS|MS0|MS1|BS0|BS1|DCI"
		assetTypeOptions := "网络设备|交换机|路由器|防火墙|负载均衡"
		layerOptions := "2|3"
		stackRoleOptions := "master|standby|member"

		// 构建包含选项的模板
		content := "\uFEFF*SN,厂商,型号,*资产类型,资产状态,硬件状态,*设备角色,套餐模板,固件版本,管理地址,Loopback地址,端口数量,端口速率,网络层级,路由协议,支持堆叠,堆叠ID,堆叠角色,机柜名称,机架位,项目,业务状态,采购合同,购买时间,过保时间,金额,残值,上架时间,交付时间,备注\n" +
			"# * 表示必填字段\n" +
			"# 必填字段: SN, 资产类型, 设备角色\n" +
			"# 资产类型可选值: " + assetTypeOptions + "\n" +
			"# 资产状态可选值: " + assetStatusOptions + "\n" +
			"# 硬件状态可选值: " + hardwareStatusOptions + "\n" +
			"# 设备角色可选值: " + roleOptions + "\n" +
			"# 业务状态可选值: " + bizStatusOptions + "\n" +
			"# 网络层级可选值: " + layerOptions + "\n" +
			"# 支持堆叠可选值: 是|否\n" +
			"# 堆叠角色可选值: " + stackRoleOptions + "\n" +
			"# 日期格式: YYYY-MM-DD 或 YYYY/MM/DD\n" +
			"SN12345,华为,CE8860,交换机,使用中,正常,S0,8Q-TOR-T3-B,V200R005C10,***********,********,48,100G,3,OSPF/BGP,是,1,master,A01,42,核心网络项目,ACTIVE,PO12345,2023-01-01,2026-01-01,80000,8000,2023-01-15,2023-01-20,核心交换机\n"
		return []byte(content), "network_device_import_template.csv", nil

	case "product":
		// 产品规格导入模板
		materialTypeOptions := "CPU|内存|硬盘|SSD|显卡|主板|机箱|电源模块|风扇|散热器|跳线|BMC板|网卡|线缆|光模块|raid卡|GPU底板|Switch板|背板|服务器|交换机|路由器|维保"
		productCategoryOptions := "服务器|存储设备|网络设备|网络设备配件|配件|线缆|维保|其他"

		// 构建包含选项的模板
		content := "\uFEFF*物料类型,品牌,型号,*规格,*PN号码,*产品类别\n" +
			"# * 表示必填字段\n" +
			"# 必填字段: 物料类型, 规格, PN号码, 产品类别\n" +
			"# 物料类型可选值: " + materialTypeOptions + "\n" +
			"# 产品类别可选值: " + productCategoryOptions + "\n" +
			"CPU,Intel,Xeon Gold 6248R,24核48线程 3.0GHz,BX806956248R,配件\n" +
			"内存,Samsung,DDR4,64GB 2666MHz ECC,M393A8G40MB2-CTD,配件\n" +
			"硬盘,希捷,Exos,16TB 7200RPM SATA,ST16000NM000J,配件\n" +
			"交换机,华为,CloudEngine,48口100G,02350YAR,网络设备\n"
		return []byte(content), "product_import_template.csv", nil

	case "room":
		// 房间导入模板
		statusOptions := "启用|禁用"

		// 构建包含选项的模板
		content := "\uFEFF*房间名称,*机房名称,描述,状态\n" +
			"# * 表示必填字段\n" +
			"# 必填字段: 房间名称, 机房名称\n" +
			"# 机房名称必须是系统中已存在的机房，请先确保机房信息已经导入系统\n" +
			"# 状态可选值: " + statusOptions + "\n" +
			"# 导入选项说明:\n" +
			"# 1. 覆盖模式: 如选择覆盖模式，系统将使用导入的数据更新已存在的同名房间\n" +
			"# 2. 追加模式: 如选择追加模式，系统将只添加不存在的房间，已存在的同名房间将被跳过\n" +
			"机房A-1001,数据中心A,主机房A区1001室,启用\n" +
			"设备间B-2001,数据中心B,备用设备间,启用\n"
		return []byte(content), "room_import_template.csv", nil

	case "cabinet":
		// 机柜导入模板
		statusOptions := "启用|禁用"

		// 构建包含选项的模板
		content := "\uFEFF*机柜名称,*房间名称,容量(U),行,列,机柜类型,网络环境,Bond类型,总功耗(kW),描述,状态\n" +
			"# * 表示必填字段\n" +
			"# 必填字段: 机柜名称, 房间名称\n" +
			"# 房间名称必须是系统中已存在的房间，请先确保房间信息已经导入系统\n" +
			"# 容量(U)默认值为42，可以根据实际情况修改\n" +
			"# 状态可选值: " + statusOptions + "\n" +
			"# 导入选项说明:\n" +
			"# 1. 覆盖模式: 如选择覆盖模式，系统将使用导入的数据更新已存在的同名机柜\n" +
			"# 2. 追加模式: 如选择追加模式，系统将只添加不存在的机柜，已存在的同名机柜将被跳过\n" +
			"A01,机房A-1001,42,A,01,标准机柜,生产网,双网卡,3.5,标准服务器机柜,启用\n" +
			"B02,机房A-1001,48,B,02,高密度机柜,生产网,链路聚合,5.0,高密度服务器机柜,启用\n" +
			"N01,设备间B-2001,42,N,01,网络机柜,管理网,双网卡,2.0,核心网络交换机机柜,启用\n"
		return []byte(content), "cabinet_import_template.csv", nil

	case "outbound-spare":
		// 出库备件导入模板
		//sourceTypeOptions := "新购|拆机|维修|其他"
		//assetStatusOptions := "待入库|已入库|待出库|已出库|闲置中|使用中|维修中|待报废|已报废"
		//hardwareStatusOptions := "正常|故障|警告"

		// 构建包含选项的模板
		content := "\uFEFF物料类别,品牌,型号,规格, *产品PN号码,*SN\n" +
			"# * 表示必填字段\n" +
			"# 必填字段: SN，产品PN号码\n" +
			//"# 注意: SN为选填字段，可以为空\n" +
			"# 产品PN号码必须是系统中已存在的产品，可在\"PN或者规格信息\"中查看\n" +
			"# 品牌为选填字段，但当存在相同PN不同品牌的产品时，需要提供品牌以确定具体产品\n" +
			//"# 仓库可以填写仓库名称或编码，系统会自动匹配，可在\"仓库管理\"中查看\n" +
			//"# 来源类型可选值: " + sourceTypeOptions + "\n" +
			//"# 资产状态可选值: " + assetStatusOptions + "\n" +
			//"# 硬件状态可选值: " + hardwareStatusOptions + "\n" +
			//"# 日期格式: YYYY-MM-DD 或 YYYY/MM/DD\n" +
			//"SPRCPU001,BX806958280,Intel,主数据中心备件仓,新购,待入库,正常,8000,架位A-3,V1.0,2023-01-01,2026-01-01,,Intel CPU备件\n" +
			//",M393A2K43BB1-CRC,Samsung,WH-002,拆机,闲置中,正常,600,架位B-32,V2.0,2023-02-15,2025-02-15,SN12345,从服务器SN12345拆下\n"
			"CPU,AMD,AMD EPYC™ 9654 2.40GHz 96,AMD EPYC™ 9654 2.40GHz 96,84Y+-SGP-J3420,738F4H2601523"
		return []byte(content), "outbound_spare_import_template", nil

	case "acceptance_device_list":
		// 验收设备列表导入模板
		// 设备SN
		content := "\uFEFF设备SN\n"
		return []byte(content), "acceptance_device_import_template", nil

	case "racking_device_list":
		// 上架设备列表导入模板
		// 设备SN
		// 套餐模板

		content := "\uFEFF设备SN\n"
		return []byte(content), "racking_device_import_template", nil
	default:
		return nil, "", errors.New("不支持的模型类型: " + modelType)
	}
}

// ImportOutboundSpareData 导入出库备件数据(仅校验)
func (s *importServiceImpl) ImportOutboundSpareData(ctx context.Context, modelType string, reader io.Reader, options ...ImportOptions) (*model.ImportResult, error) {

	// 获取对应的处理器
	handler, exists := s.handlers[strings.ToLower(modelType)]
	if !exists {
		return nil, errors.New("不支持的模型类型: " + modelType)
	}

	// 创建文件服务
	fileService := fileservice.NewFileService(s.db)

	// 创建基础处理器并设置选项
	baseHandler := importhandler.NewBaseHandler(s.db, fileService)

	// 如果提供了选项，则设置
	if len(options) > 0 {
		// 设置处理选项
		handlerOptions := importhandler.ImportOptions{
			Overwrite: options[0].Overwrite,
		}

		baseHandler.SetOptions(handlerOptions)
	}

	// 将模块类型添加到上下文
	ctxWithModuleType := context.WithValue(ctx, moduleTypeKey, modelType)

	// 使用基础处理器处理导入(不保存到数据库)
	result, err := baseHandler.ProcessOutboundSpareImport(ctxWithModuleType, handler, reader)

	// 如果出库备件导入成功，自动调用库存同步任务
	if err == nil && strings.ToLower(modelType) == "outbound-spare" && result != nil && result.SuccessCount > 0 {
		// 获取备件服务
		spareService := s.getSpareService()
		if spareService != nil {
			// 执行库存同步
			syncErr := spareService.SyncInventoryWithSpareStatus(ctx)
			if syncErr != nil {
				// 记录错误但不影响导入结果
				log.Printf("备件出库导入后同步库存失败: %v", syncErr)
			} else {
				log.Printf("备件出库导入后同步库存成功，共同步 %d 个备件", result.SuccessCount)
			}
		}
	}

	return result, err
}
