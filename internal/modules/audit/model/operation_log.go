package audit

import (
	"time"

	"gorm.io/gorm"
)

// OperationLog 操作日志模型，记录前端操作
type OperationLog struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// 操作模块信息
	Module    string `json:"module" gorm:"type:varchar(100);not null;comment:操作模块"`
	Operation string `json:"operation" gorm:"type:varchar(100);not null;comment:操作类型"`

	// 登录信息
	UserID    uint   `json:"user_id" gorm:"comment:操作用户ID"`
	Username  string `json:"username" gorm:"type:varchar(100);comment:操作用户名"`
	IPAddress string `json:"ip_address" gorm:"type:varchar(50);comment:操作者IP地址"`
	UserAgent string `json:"user_agent" gorm:"type:text;comment:用户代理信息"`

	// 请求信息
	RequestURL    string `json:"request_url" gorm:"type:varchar(255);comment:请求地址"`
	RequestMethod string `json:"request_method" gorm:"type:varchar(10);comment:请求方式"`
	OperationFunc string `json:"operation_func" gorm:"type:varchar(200);comment:操作方法"`
	RequestBody   string `json:"request_body" gorm:"type:longtext;comment:请求参数"`
	ResponseBody  string `json:"response_body" gorm:"type:longtext;comment:返回参数"`

	// 结果信息
	Status        string    `json:"status" gorm:"type:varchar(20);comment:操作状态"`
	ExecutionTime int64     `json:"execution_time" gorm:"comment:执行时间(毫秒)"`
	OperationTime time.Time `json:"operation_time" gorm:"comment:操作时间"`
}

// GetTableName 指定表名
func (OperationLog) GetTableName() string {
	return "audit_operation_logs"
}
