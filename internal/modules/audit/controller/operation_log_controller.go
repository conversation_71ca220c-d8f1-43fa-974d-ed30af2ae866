package controller

import (
	"backend/internal/modules/audit/service"
	"backend/response"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// OperationLogController 操作日志控制器
type OperationLogController struct {
	service service.OperationLogService
}

// NewOperationLogController 创建操作日志控制器
func NewOperationLogController(service service.OperationLogService) *OperationLogController {
	return &OperationLogController{service: service}
}

// List 获取操作日志列表
// @Summary 获取操作日志列表
// @Description 分页获取操作日志列表
// @Tags 审计-操作日志
// @Accept json
// @Produce json
// @Param module query string false "操作模块"
// @Param operation query string false "操作类型"
// @Param username query string false "操作用户"
// @Param start_time query string false "开始时间 格式：2006-01-02 15:04:05"
// @Param end_time query string false "结束时间 格式：2006-01-02 15:04:05"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /audit/operation-logs [get]
func (c *OperationLogController) List(ctx *gin.Context) {
	page, pageErr := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if pageErr != nil || page < 1 {
		page = 1
	}

	pageSize, pageSizeErr := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	if pageSizeErr != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	module := ctx.Query("module")
	operation := ctx.Query("operation")
	username := ctx.Query("username")
	requestMethod := ctx.Query("request_method")

	startTimeStr := ctx.Query("start_time")
	endTimeStr := ctx.Query("end_time")

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse("2006-01-02 15:04:05", startTimeStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "无效的开始时间格式")
			return
		}
	}

	if endTimeStr != "" {
		endTime, err = time.Parse("2006-01-02 15:04:05", endTimeStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "无效的结束时间格式")
			return
		}
	}

	logs, total, err := c.service.GetLogs(ctx, module, operation, username, requestMethod, startTime, endTime, page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取操作日志失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  logs,
		"total": total,
	}, "获取操作日志成功")
}
