package hooks

import (
	audit "backend/internal/modules/audit/model"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"reflect"
	"strings"
	"time"
	"unsafe"

	"gorm.io/gorm"
)

// 上下文键
type contextKey string

const (
	userIDKey    contextKey = "user_id"
	usernameKey  contextKey = "username"
	ipAddressKey contextKey = "ip_address"
	userAgentKey contextKey = "user_agent"

	// 父操作相关键
	ParentOperationRecordedKey contextKey = "parent_operation_recorded"
	ParentOperationUserIDKey   contextKey = "parent_operation_userid"
	ParentOperationUsernameKey contextKey = "parent_operation_username"
)

// AuditManager GORM 审计钩子管理器
type AuditManager struct {
	DB *gorm.DB
}

// NewAuditManager 创建审计管理器
func NewAuditManager(db *gorm.DB) *AuditManager {
	return &AuditManager{DB: db}
}

// RegisterHooks 注册 GORM 钩子
func (am *AuditManager) RegisterHooks() {
	// 注册创建钩子
	if err := am.DB.Callback().Create().After("gorm:create").Register("audit:create", am.afterCreate); err != nil {
		// 记录错误，但不终止程序
		log.Printf("注册审计创建钩子失败: %v", err)
	}

	// 注册更新钩子
	if err := am.DB.Callback().Update().After("gorm:update").Register("audit:update", am.afterUpdate); err != nil {
		// 记录错误，但不终止程序
		log.Printf("注册审计更新钩子失败: %v", err)
	}

	// 注册删除钩子
	if err := am.DB.Callback().Delete().After("gorm:delete").Register("audit:delete", am.afterDelete); err != nil {
		// 记录错误，但不终止程序
		log.Printf("注册审计删除钩子失败: %v", err)
	}
}

// isAuditTable 检查表名是否为审计表
func isAuditTable(tableName string) bool {
	// 直接检查表名
	if tableName == "audit_change_logs" ||
		tableName == "audit_operation_logs" ||
		tableName == (&audit.OperationLog{}).GetTableName() {
		return true
	}

	// 检查表名是否包含audit关键字（额外保护）
	if strings.Contains(strings.ToLower(tableName), "audit_") ||
		strings.Contains(strings.ToLower(tableName), "_log") ||
		strings.Contains(strings.ToLower(tableName), "_logs") {
		return true
	}

	// 忽略以history和historys结尾的表
	if strings.HasSuffix(strings.ToLower(tableName), "history") ||
		strings.HasSuffix(strings.ToLower(tableName), "histories") {
		return true
	}

	return false
}

// shouldSkipLogging 判断是否应该跳过日志记录
func shouldSkipLogging(db *gorm.DB) bool {
	// 1. 检查是否是审计表操作
	tableName := db.Statement.Table
	if isAuditTable(tableName) {
		return true
	}

	// 2. 检查上下文中是否有标记表示这是一个已经记录过的操作
	// 这可以避免重复记录同一个HTTP请求触发的多个数据库操作
	if db.Statement.Context != nil {
		if parentOp, exists := db.Statement.Context.Value(ParentOperationRecordedKey).(bool); exists && parentOp {
			return true
		}
	}

	return false
}

// afterCreate 创建后钩子
func (am *AuditManager) afterCreate(db *gorm.DB) {
	// 跳过错误记录
	if db.Error != nil {
		return
	}

	// 获取表名
	tableName := db.Statement.Table
	if tableName == "" {
		return
	}

	// 跳过审计表自身的变更和已记录的父操作触发的变更
	if shouldSkipLogging(db) {
		return
	}

	// 获取实体 ID
	entityID, err := extractEntityID(db)
	if err != nil || entityID == 0 {
		return
	}

	// 获取用户信息
	userID, username, ipAddress, userAgent := getContextInfo(db.Statement.Context)

	// 确保有操作人信息
	if username == "" {
		// 尝试从父操作继承用户信息
		if parentUsername, ok := db.Statement.Context.Value(ParentOperationUsernameKey).(string); ok && parentUsername != "" {
			username = parentUsername
		} else {
			username = "系统操作"
		}
	}

	// 提取模型数据
	changeData := extractModelData(db.Statement.Dest)

	// 生成详细的创建记录

	// 创建操作日志
	operationLog := &audit.OperationLog{
		Module:        tableName,
		Operation:     "create",
		UserID:        userID,
		Username:      username,
		IPAddress:     ipAddress,
		UserAgent:     userAgent,
		RequestBody:   changeData,
		Status:        "正常",
		OperationTime: time.Now(),
	}

	// 保存操作日志 - 不使用钩子避免循环调用
	if err := am.DB.Session(&gorm.Session{SkipHooks: true}).Create(operationLog).Error; err != nil {
		fmt.Printf("创建审计日志失败: %v\n", err)
	}
}

// afterUpdate 更新后钩子
func (am *AuditManager) afterUpdate(db *gorm.DB) {
	// 跳过错误记录
	if db.Error != nil {
		return
	}

	// 获取表名
	tableName := db.Statement.Table
	if tableName == "" {
		return
	}

	// 跳过审计表自身的变更和已记录的父操作触发的变更
	if shouldSkipLogging(db) {
		return
	}

	// 获取实体 ID
	entityID, err := extractEntityID(db)
	if err != nil || entityID == 0 {
		return
	}

	// 获取用户信息
	userID, username, ipAddress, userAgent := getContextInfo(db.Statement.Context)

	// 确保有操作人信息
	if username == "" {
		// 尝试从父操作继承用户信息
		if parentUsername, ok := db.Statement.Context.Value(ParentOperationUsernameKey).(string); ok && parentUsername != "" {
			username = parentUsername
		} else {
			username = "系统操作"
		}
	}

	// 提取模型数据
	changeData := extractModelData(db.Statement.Dest)

	// 创建操作日志
	operationLog := &audit.OperationLog{
		Module:        tableName,
		Operation:     "update",
		UserID:        userID,
		Username:      username,
		IPAddress:     ipAddress,
		UserAgent:     userAgent,
		RequestBody:   changeData,
		Status:        "正常",
		OperationTime: time.Now(),
	}

	// 保存操作日志 - 不使用钩子避免循环调用
	if err := am.DB.Session(&gorm.Session{SkipHooks: true}).Create(operationLog).Error; err != nil {
		fmt.Printf("创建审计日志失败: %v\n", err)
	}
}

// afterDelete 删除后钩子
func (am *AuditManager) afterDelete(db *gorm.DB) {
	// 跳过错误记录
	if db.Error != nil {
		return
	}

	// 获取表名
	tableName := db.Statement.Table
	if tableName == "" {
		return
	}

	// 跳过审计表自身的变更和已记录的父操作触发的变更
	if shouldSkipLogging(db) {
		return
	}

	// 获取实体 ID
	entityID, err := extractEntityID(db)
	if err != nil || entityID == 0 {
		return
	}

	// 获取用户信息
	userID, username, ipAddress, userAgent := getContextInfo(db.Statement.Context)

	// 确保有操作人信息
	if username == "" {
		// 尝试从父操作继承用户信息
		if parentUsername, ok := db.Statement.Context.Value(ParentOperationUsernameKey).(string); ok && parentUsername != "" {
			username = parentUsername
		} else {
			username = "系统操作"
		}
	}

	// 提取模型数据
	changeData := extractModelData(db.Statement.Dest)

	// 创建操作日志
	operationLog := &audit.OperationLog{
		Module:        tableName,
		Operation:     "delete",
		UserID:        userID,
		Username:      username,
		IPAddress:     ipAddress,
		UserAgent:     userAgent,
		RequestBody:   changeData,
		Status:        "正常",
		OperationTime: time.Now(),
	}

	// 保存操作日志 - 不使用钩子避免循环调用
	if err := am.DB.Session(&gorm.Session{SkipHooks: true}).Create(operationLog).Error; err != nil {
		fmt.Printf("创建审计日志失败: %v\n", err)
	}
}

// 提取实体 ID
func extractEntityID(db *gorm.DB) (uint, error) {
	// 尝试从主键中提取 ID
	if db.Statement.Schema != nil && db.Statement.Schema.PrioritizedPrimaryField != nil {
		dest := db.Statement.Dest
		if dest == nil {
			return 0, fmt.Errorf("目标对象为空")
		}

		// 使用反射获取主键值
		val := reflect.ValueOf(dest)
		if val.Kind() == reflect.Ptr {
			val = val.Elem()
		}

		if val.Kind() != reflect.Struct {
			return 0, fmt.Errorf("目标对象不是结构体")
		}

		// 获取主键字段
		primaryField := db.Statement.Schema.PrioritizedPrimaryField.Name
		idField := val.FieldByName(primaryField)

		if !idField.IsValid() {
			return 0, fmt.Errorf("无法获取主键字段: %s", primaryField)
		}

		// 转换为 uint
		switch idField.Kind() {
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			return uint(idField.Uint()), nil
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			intValue := idField.Int()
			if intValue < 0 {
				return 0, fmt.Errorf("负数ID值: %d", intValue)
			}
			// 在64位系统上，int64的最大值大于uint的最大值
			// 在32位系统上，int64的最大值大于uint的最大值
			// 使用简单的方法：只要值为正且在int范围内，就可以安全转换
			if intValue > int64(^uint32(0)) && unsafe.Sizeof(uint(0)) <= 4 {
				return 0, fmt.Errorf("ID值超出uint范围: %d", intValue)
			}
			return uint(intValue), nil
		default:
			return 0, fmt.Errorf("主键类型不支持: %s", idField.Kind())
		}
	}

	// 如果无法从主键获取，尝试从 WHERE 条件中提取
	if len(db.Statement.Clauses) > 0 {
		if whereClause, ok := db.Statement.Clauses["WHERE"]; ok {
			if whereClause.Expression != nil {
				// 这里可以尝试解析 WHERE 条件，但比较复杂
				// 简单起见，我们记录一个占位符
				return 1, nil // 使用占位符 ID
			}
		}
	}

	return 0, fmt.Errorf("无法确定实体 ID")
}

// 提取模型数据
func extractModelData(model interface{}) string {
	if model == nil {
		return "{}"
	}

	// 特殊处理: 文件上传
	// 检测是否为文件上传相关模型，避免记录二进制文件内容
	modelType := reflect.TypeOf(model).String()
	if strings.Contains(modelType, "File") || strings.Contains(modelType, "file") {
		// 简化文件数据记录，避免记录二进制内容
		simplified := map[string]interface{}{
			"message": "文件上传内容已省略",
			"type":    modelType,
		}
		data, err := json.Marshal(simplified)
		if err != nil {
			return "{\"message\":\"文件上传内容已省略\"}"
		}
		return string(data)
	}

	// 使用反射获取模型数据
	val := reflect.ValueOf(model)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	// 如果不是结构体，直接序列化
	if val.Kind() != reflect.Struct {
		// 检查是否可能是文件内容
		if _, ok := model.([]byte); ok {
			return "{\"message\":\"二进制数据已省略\"}"
		}

		data, err := json.Marshal(model)
		if err != nil {
			return "{}"
		}

		// 检查序列化后的数据是否包含二进制特征
		if containsBinaryData(string(data)) {
			return "{\"message\":\"检测到二进制数据，已省略\"}"
		}

		// 限制数据大小，防止过大
		if len(data) > 65000 { // 安全阈值，MySQL TEXT类型允许65535字节
			// 截断数据并添加标记
			truncated := data[:65000]
			return fmt.Sprintf("{\"truncated\":true,\"data\":%s...\"}", string(truncated[:65000-20]))
		}

		return string(data)
	}

	// 创建一个 map 来存储字段值
	result := make(map[string]interface{})
	typ := val.Type()

	for i := 0; i < val.NumField(); i++ {
		field := typ.Field(i)

		// 跳过未导出的字段
		if field.PkgPath != "" {
			continue
		}

		// 获取字段名
		fieldName := field.Name

		// 尝试从 json 标签获取名称
		if jsonTag := field.Tag.Get("json"); jsonTag != "" && jsonTag != "-" {
			parts := strings.Split(jsonTag, ",")
			if parts[0] != "" {
				fieldName = parts[0]
			}
		}

		// 获取字段值
		fieldValue := val.Field(i).Interface()

		// 跳过可能的二进制内容字段
		fieldTypeName := field.Type.String()
		if strings.Contains(strings.ToLower(fieldName), "file") ||
			strings.Contains(strings.ToLower(fieldName), "content") ||
			strings.Contains(strings.ToLower(fieldName), "binary") ||
			strings.Contains(strings.ToLower(fieldName), "image") ||
			strings.Contains(fieldTypeName, "[]byte") {
			result[fieldName] = "[binary content omitted]"
		} else {
			result[fieldName] = fieldValue
		}
	}

	// 序列化为 JSON，使用 HTML 转义防止特殊字符问题
	var buf strings.Builder
	encoder := json.NewEncoder(&buf)
	encoder.SetEscapeHTML(false) // 不转义 HTML，保持原始字符
	encoder.SetIndent("", "")    // 不缩进

	if err := encoder.Encode(result); err != nil {
		return "{}"
	}

	jsonStr := buf.String()
	// 移除末尾可能的换行符
	jsonStr = strings.TrimSuffix(jsonStr, "\n")

	// 限制数据大小，防止过大
	if len(jsonStr) > 65000 { // 安全阈值，MySQL TEXT类型允许65535字节
		// 创建简化版数据
		simplified := map[string]interface{}{
			"truncated": true,
			"message":   "数据过大已截断",
			"fields":    reflect.TypeOf(model).String(),
		}
		data, err := json.Marshal(simplified)
		if err != nil {
			return "{\"truncated\":true,\"message\":\"数据过大已截断且JSON序列化失败\"}"
		}
		return string(data)
	}

	return jsonStr
}

// containsBinaryData 检查字符串是否包含二进制数据特征
func containsBinaryData(s string) bool {
	// 检查是否包含常见的二进制文件头标记
	binarySignatures := []string{
		"\\x89PNG",        // PNG 文件头
		"\\xFF\\xD8\\xFF", // JPEG 文件头
		"GIF8",            // GIF 文件头
		"PK\\x03\\x04",    // ZIP 文件头
		"%PDF",            // PDF 文件头
	}

	for _, sig := range binarySignatures {
		if strings.Contains(s, sig) {
			return true
		}
	}

	// 检查是否包含过多的不可打印字符
	nonPrintableCount := 0
	for _, r := range s {
		if r < 32 && r != 9 && r != 10 && r != 13 { // 不是tab、LF、CR的控制字符
			nonPrintableCount++
			if nonPrintableCount > 5 { // 允许少量不可打印字符
				return true
			}
		}
	}

	return false
}

// 获取上下文信息
func getContextInfo(ctx context.Context) (uint, string, string, string) {
	if ctx == nil {
		return 0, "", "", ""
	}

	var userID uint
	if userIDVal, ok := ctx.Value(userIDKey).(uint); ok {
		userID = userIDVal
	}

	var username string
	if usernameVal, ok := ctx.Value(usernameKey).(string); ok {
		username = usernameVal
	}

	var ipAddress string
	if ipAddressVal, ok := ctx.Value(ipAddressKey).(string); ok {
		ipAddress = ipAddressVal
	}

	var userAgent string
	if userAgentVal, ok := ctx.Value(userAgentKey).(string); ok {
		userAgent = userAgentVal
	}

	// 尝试获取父操作的用户信息（如果存在）
	// 这个键将在操作日志中间件中设置
	if username == "" {
		if parentUsername, ok := ctx.Value(ParentOperationUsernameKey).(string); ok && parentUsername != "" {
			username = parentUsername
		}
	}

	if userID == 0 {
		if parentUserID, ok := ctx.Value(ParentOperationUserIDKey).(uint); ok && parentUserID > 0 {
			userID = parentUserID
		}
	}

	return userID, username, ipAddress, userAgent
}

// WithAuditContext 创建带有审计信息的上下文
func WithAuditContext(ctx context.Context, userID uint, username, ipAddress, userAgent string) context.Context {
	if ctx == nil {
		ctx = context.Background()
	}

	ctx = context.WithValue(ctx, userIDKey, userID)
	ctx = context.WithValue(ctx, usernameKey, username)
	ctx = context.WithValue(ctx, ipAddressKey, ipAddress)
	ctx = context.WithValue(ctx, userAgentKey, userAgent)

	return ctx
}

// WithParentOperationInfo 创建带有父操作信息的上下文
func WithParentOperationInfo(ctx context.Context, userID uint, username string) context.Context {
	if ctx == nil {
		ctx = context.Background()
	}

	ctx = context.WithValue(ctx, ParentOperationUserIDKey, userID)
	ctx = context.WithValue(ctx, ParentOperationUsernameKey, username)

	return ctx
}
