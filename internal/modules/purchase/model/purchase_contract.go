package model

import (
	"time"

	"gorm.io/gorm"
)

// PurchaseContract 采购合同模型
type PurchaseContract struct {
	ID              uint                   `json:"id" gorm:"primaryKey"`
	ContractNo      string                 `json:"contract_no" gorm:"uniqueIndex:uk_contract_no;size:50;not null;comment:合同编号"`
	InquiryID       *uint                  `json:"inquiry_id" gorm:"index:idx_inquiry_id;comment:关联询价ID"`
	ProjectID       *uint                  `json:"project_id" gorm:"index:idx_project_id;comment:项目ID"`
	OurCompanyID    uint                   `json:"our_company_id" gorm:"index:idx_our_company_id;not null;comment:我方公司ID"`
	SupplierID      uint                   `json:"supplier_id" gorm:"index:idx_supplier_id;not null;comment:供应商ID"`
	ContractTitle   string                 `json:"contract_title" gorm:"size:200;comment:合同标题"`
	ContractType    string                 `json:"contract_type" gorm:"size:50;comment:合同类型"`
	SigningDate     *time.Time             `json:"signing_date" gorm:"comment:签订日期"`
	DeliveryAddress string                 `json:"delivery_address" gorm:"size:500;comment:交货地址"`
	PaymentTerms    string                 `json:"payment_terms" gorm:"type:text;comment:付款条款"`
	WarrantyPeriod  string                 `json:"warranty_period" gorm:"size:100;comment:质保期限"`
	TotalAmount     float64                `json:"total_amount" gorm:"type:decimal(15,2);default:0.00;comment:合同总金额"`
	Status          string                 `json:"status" gorm:"size:50;comment:工作流状态"`
	Items           []PurchaseContractItem `json:"items" gorm:"foreignKey:ContractID;references:ID"` // 合同明细项
	CreatedBy       uint                   `json:"created_by" gorm:"comment:创建人ID"`
	CreatedAt       time.Time              `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedBy       *uint                  `json:"updated_by" gorm:"comment:更新人ID"`
	UpdatedAt       time.Time              `json:"updated_at"`
	DeletedAt       gorm.DeletedAt         `gorm:"index" json:"-"`

	// 关联关系
	Inquiry        *PurchaseInquiry       `json:"inquiry,omitempty" gorm:"foreignKey:InquiryID"`   // 关联询价
	OurCompany     *Company               `json:"our_company,omitempty" gorm:"foreignKey:OurCompanyID"` // 关联我方公司
	Project        *Project               `json:"project,omitempty" gorm:"foreignKey:ProjectID"`   // 关联项目
	Supplier       *Supplier              `json:"supplier,omitempty" gorm:"foreignKey:SupplierID"` // 关联供应商

	// 关联信息
	SupplierName   string `json:"supplier_name" gorm:"-"`
	ProjectName    string `json:"project_name" gorm:"-"`
	OurCompanyName string `json:"our_company_name" gorm:"-"`
	InquiryNo      string `json:"inquiry_no" gorm:"-"`
	CreatorName    string `json:"creator_name" gorm:"-"`
	UpdaterName    string `json:"updater_name" gorm:"-"`
}

// PurchaseContractItem 采购合同明细项模型
type PurchaseContractItem struct {
	ID               uint       `json:"id" gorm:"primaryKey"`
	ContractID       uint       `json:"contract_id" gorm:"index:idx_contract_id;not null;comment:合同ID"`
	InquiryItemID    *uint      `json:"inquiry_item_id" gorm:"index:idx_inquiry_item_id;comment:关联询价明细ID"`
	ProductID        *uint      `json:"product_id" gorm:"index:idx_product_id;comment:产品ID"`
	MaterialType     string     `gorm:"column:material_type;type:varchar(50);comment:物料类型" json:"material_type"`
	Model            string     `json:"model" gorm:"size:100;comment:型号"`
	Brand            string     `json:"brand" gorm:"size:100;comment:品牌"`
	PN               string     `json:"pn" gorm:"size:100;comment:原厂PN"`
	Spec             string     `json:"spec" gorm:"type:text;comment:规格"`
	Unit             string     `json:"unit" gorm:"size:20;comment:单位"`
	ContractQuantity int        `json:"contract_quantity" gorm:"not null;comment:本次订单数量"`
	ContractPrice    float64    `json:"contract_price" gorm:"type:decimal(10,2);not null;comment:采购单价"`
	ContractAmount   float64    `json:"contract_amount" gorm:"type:decimal(15,2);not null;comment:采购总价"`
	DeliveryDate     *time.Time `json:"delivery_date" gorm:"comment:预计交货日期"`
	Remark           string     `json:"remark" gorm:"type:text;comment:备注"`
	CreatedAt        time.Time  `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedAt        time.Time  `json:"updated_at"`
}

// TableName 指定数据库表名
func (PurchaseContract) TableName() string {
	return "purchase_contracts"
}

// TableName 指定数据库表名
func (PurchaseContractItem) TableName() string {
	return "purchase_contract_items"
}

// PurchaseContractFilter 采购合同查询过滤条件
type PurchaseContractFilter struct {
	ContractNo   string     `form:"contract_no" json:"contract_no"`     // 合同编号
	ContractType string     `form:"contract_type" json:"contract_type"` // 合同类型
	InquiryID    *uint      `form:"inquiry_id" json:"inquiry_id"`       // 询价单ID
	InquiryNo    string     `form:"inquiry_no" json:"inquiry_no"`       // 询价单号
	RequestID    *uint      `form:"request_id" json:"request_id"`       // 申请单ID
	ProjectID    *uint      `form:"project_id" json:"project_id"`       // 项目ID
	SupplierID   *uint      `form:"supplier_id" json:"supplier_id"`     // 供应商ID
	Status       string     `form:"status" json:"status"`               // 工作流状态
	CreatedBy    *uint      `form:"created_by" json:"created_by"`       // 创建人
	StartDate    *time.Time `form:"start_date" json:"start_date"`       // 开始日期
	EndDate      *time.Time `form:"end_date" json:"end_date"`           // 结束日期
}
