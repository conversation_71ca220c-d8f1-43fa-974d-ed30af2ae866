package model

import (
	"errors"
	"time"

	"gorm.io/gorm"
)

// 错误定义
var (
	ErrInvalidInquiryQuantity          = errors.New("询价数量必须大于0")
	ErrInquiryQuantityExceedsRemaining = errors.New("询价数量超过剩余未询价数量")
	ErrMaterialTypeRequired            = errors.New("物料类型不能为空")
	ErrInquiryNotFound                 = errors.New("询价单不存在")
	ErrInvalidInquiryStatus            = errors.New("无效的询价单状态")
	ErrInquiryAlreadyCompleted         = errors.New("询价单已完成所有采购需求，不能再添加明细")
)

// PurchaseInquiry 采购询价表模型
type PurchaseInquiry struct {
	ID                  uint           `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	InquiryNo           string         `gorm:"column:inquiry_no;type:varchar(50);uniqueIndex:uk_inquiry_no;not null;comment:询价单号" json:"inquiry_no"`
	RequestID           *uint          `gorm:"column:request_id;index:idx_request_id;comment:关联采购申请ID" json:"request_id"`
	ProjectID           *uint          `gorm:"column:project_id;index:idx_project_id;comment:项目ID" json:"project_id"`
	SupplierID          uint           `gorm:"column:supplier_id;index:idx_supplier_id;not null;comment:供应商ID" json:"supplier_id"`
	TotalAmount         float64        `gorm:"column:total_amount;type:decimal(15,2);default:0.00;comment:询价总金额" json:"total_amount"`
	SupplierDescription string         `gorm:"column:supplier_description;type:text;comment:供应商选择说明" json:"supplier_description"`
	Status              string         `gorm:"column:status;type:varchar(20);default:draft;comment:状态" json:"status"`
	CreatedBy           uint           `gorm:"column:created_by;comment:创建人ID" json:"created_by"`
	CreatedAt           time.Time      `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedBy           uint           `gorm:"column:updated_by;comment:更新人ID" json:"updated_by"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`

	// 关联关系
	Request         *PurchaseRequest      `json:"request,omitempty" gorm:"foreignKey:RequestID"`          // 关联的采购申请
	Items           []PurchaseInquiryItem `gorm:"foreignKey:InquiryID" json:"items"`   // 询价明细

	// 关联数据（不保存到数据库）
	RequestNo       string                `gorm:"-" json:"request_no,omitempty"`       // 关联的采购申请单号
	ProjectName     string                `gorm:"-" json:"project_name,omitempty"`     // 项目名称
	SupplierName    string                `gorm:"-" json:"supplier_name,omitempty"`    // 供应商名称
	SupplierContact string                `gorm:"-" json:"supplier_contact,omitempty"` // 供应商联系人
	CreatorName     string                `gorm:"-" json:"creator_name,omitempty"`     // 创建人姓名
}

// TableName 定义表名
func (PurchaseInquiry) TableName() string {
	return "purchase_inquiries"
}

// PurchaseInquiryItem 采购询价明细表模型
type PurchaseInquiryItem struct {
	ID            uint `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	InquiryID     uint `gorm:"column:inquiry_id;index:idx_inquiry_id;not null;comment:询价ID" json:"inquiry_id"`
	RequestItemID uint `gorm:"column:request_item_id;index:idx_request_item_id;not null;comment:申请明细ID" json:"request_item_id"`
	// 物料信息字段
	MaterialType   string `gorm:"column:material_type;type:varchar(50);comment:物料类型" json:"material_type"`
	Model          string `gorm:"column:model;type:varchar(100);comment:型号" json:"model"`
	Brand          string `gorm:"column:brand;type:varchar(100);comment:品牌" json:"brand"`
	PN             string `gorm:"column:pn;type:varchar(100);comment:PN号" json:"pn"`
	Specifications string `gorm:"column:spec;type:text;comment:规格说明" json:"spec"`
	Unit           string `gorm:"column:unit;type:varchar(20);comment:单位" json:"unit"`
	// 询价数量信息
	InquiredQuantity       int `gorm:"column:inquired_quantity;type:int(11);default:0;comment:已询比价数量" json:"inquired_quantity"`
	CurrentInquiryQuantity int `gorm:"column:current_inquiry_quantity;type:int(11);not null;comment:本次询比价数量" json:"current_inquiry_quantity"`
	// 预算信息
	BudgetPrice  *float64  `gorm:"column:budget_price;type:decimal(10,2);comment:预算单价" json:"budget_price"`
	BudgetAmount *float64  `gorm:"column:budget_amount;type:decimal(15,2);comment:预算总金额" json:"budget_amount"`
	Remark       string    `gorm:"column:remark;type:text;comment:内部备注" json:"remark"`
	CreatedAt    time.Time `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// 关联数据（不保存到数据库）
	RequestItem *PurchaseRequestItem `gorm:"foreignKey:RequestItemID;references:ID" json:"request_item,omitempty"` // 关联的申请明细（包含物料信息）

	// 虚拟字段（不保存到数据库）
	RequestQuantity    int `gorm:"-" json:"request_quantity"`    // 申请需求数量
	UninquiredQuantity int `gorm:"-" json:"uninquired_quantity"` // 未询价数量
}

// TableName 定义表名
func (PurchaseInquiryItem) TableName() string {
	return "purchase_inquiry_items"
}

// PurchaseInquiryFilter 采购询价查询过滤条件
type PurchaseInquiryFilter struct {
	InquiryNo  string `form:"inquiry_no"`
	RequestID  *uint  `form:"request_id"`
	RequestNo  string `form:"request_no"`
	ProjectID  *uint  `form:"project_id"`
	SupplierID *uint  `form:"supplier_id"`
	Status     string `form:"status"`
	CreatedBy  *uint  `form:"created_by"`
	StartDate  string `form:"start_date"`
	EndDate    string `form:"end_date"`
}

// InquiryQuantitySummary 询价数量汇总
type InquiryQuantitySummary struct {
	RequestItemID      uint `json:"request_item_id"`
	TotalQuantity      int  `json:"total_quantity"`      // 申请总数量
	InquiredQuantity   int  `json:"inquired_quantity"`   // 已询价数量
	UninquiredQuantity int  `json:"uninquired_quantity"` // 未询价数量
}

// CalculateBudgetAmount 计算预算总金额
func (item *PurchaseInquiryItem) CalculateBudgetAmount() {
	if item.BudgetPrice != nil && item.CurrentInquiryQuantity > 0 {
		amount := *item.BudgetPrice * float64(item.CurrentInquiryQuantity)
		item.BudgetAmount = &amount
	}
}

// CalculateQuantities 计算数量关系（保留原方法以兼容现有代码）
func (item *PurchaseInquiryItem) CalculateQuantities() {
	// 这个方法保留是为了兼容，但在新模型中已不再使用
	// 数量信息现在从 InquiryRequestItemMap 和 PurchaseRequestItem 中获取
}

// Validate 验证询价明细数据（保留原方法以兼容现有代码）
func (item *PurchaseInquiryItem) Validate() error {
	// 在新模型中，询价明细主要验证报价信息
	return nil
}

// CanAddItems 检查是否可以添加询价明细
func (inquiry *PurchaseInquiry) CanAddItems() error {
	// 允许在任何状态下添加询价明细
	return nil
}

// CheckAndUpdateCompletionStatus 检查是否所有申请明细都已完成询价
func (inquiry *PurchaseInquiry) CheckAndUpdateCompletionStatus(allRequestItems []PurchaseRequestItem) bool {
	if inquiry.RequestID == nil {
		return false
	}

	// 计算每个申请明细的总询价数量
	requestItemInquiredMap := make(map[uint]int)
	for _, item := range inquiry.Items {
		requestItemInquiredMap[item.RequestItemID] += item.CurrentInquiryQuantity
	}

	// 检查是否所有申请明细都已完成询价
	allCompleted := true
	for _, requestItem := range allRequestItems {
		inquiredQuantity := requestItemInquiredMap[requestItem.ID]
		if inquiredQuantity < requestItem.Quantity {
			allCompleted = false
			break
		}
	}

	return allCompleted
}
