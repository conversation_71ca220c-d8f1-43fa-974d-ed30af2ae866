package model

import (
	"time"

	"gorm.io/gorm"
)

// Invoice 发票模型
type Invoice struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	InvoiceNo     string         `json:"invoice_no" gorm:"uniqueIndex:uk_invoice_no;size:100;not null;comment:发票号"`
	ContractID    uint           `json:"contract_id" gorm:"index:idx_contract_id;not null;comment:关联合同ID"`
	InvoiceAmount float64        `json:"invoice_amount" gorm:"type:decimal(15,2);not null;comment:本次开票金额"`
	Remark        string         `json:"remark" gorm:"type:text;comment:备注"`
	CreatedBy     uint           `json:"created_by" gorm:"index;comment:创建人ID"`
	CreatedAt     time.Time      `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedBy     *uint          `json:"updated_by" gorm:"index;comment:更新人ID"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Contract *PurchaseContract `json:"contract,omitempty" gorm:"foreignKey:ContractID"` // 关联合同

	// 关联信息（查询时填充，不存储到数据库）
	ContractNo     string `json:"contract_no" gorm:"-"`      // 合同编号
	SupplierName   string `json:"supplier_name" gorm:"-"`    // 供应商名称
	ProjectName    string `json:"project_name" gorm:"-"`     // 项目名称
	OurCompanyName string `json:"our_company_name" gorm:"-"` // 我方公司名称
	CreatorName    string `json:"creator_name" gorm:"-"`     // 创建人姓名
	UpdaterName    string `json:"updater_name" gorm:"-"`     // 更新人姓名
}

// TableName 指定数据库表名
func (Invoice) TableName() string {
	return "invoices"
}

// InvoiceFilter 发票查询过滤条件
type InvoiceFilter struct {
	InvoiceNo  string     `form:"invoice_no" json:"invoice_no"`     // 发票号
	ContractID *uint      `form:"contract_id" json:"contract_id"`   // 合同ID
	ContractNo string     `form:"contract_no" json:"contract_no"`   // 合同编号
	CreatedBy  *uint      `form:"created_by" json:"created_by"`     // 创建人
	StartDate  *time.Time `form:"start_date" json:"start_date"`     // 开始日期
	EndDate    *time.Time `form:"end_date" json:"end_date"`         // 结束日期
	MinAmount  *float64   `form:"min_amount" json:"min_amount"`     // 最小金额
	MaxAmount  *float64   `form:"max_amount" json:"max_amount"`     // 最大金额
}
