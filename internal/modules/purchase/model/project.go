package model

import (
	"time"

	"gorm.io/gorm"
)

// Project 项目信息表模型
type Project struct {
	ID             uint           `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	ProjectCode    string         `gorm:"column:project_code;type:varchar(50);uniqueIndex:uk_project_code;not null;comment:项目编号" json:"project_code"`
	ProjectName    string         `gorm:"column:project_name;type:varchar(200);index:idx_project_name;not null;comment:项目名称" json:"project_name"`
	CustomerName   string         `gorm:"column:customer_name;type:varchar(200);index:idx_customer_name;not null;comment:所属客户" json:"customer_name"`
	ContractInfo   string         `gorm:"column:contract_info;type:text;comment:合同信息" json:"contract_info"`
	ProjectManager string         `gorm:"column:project_manager;type:varchar(100);comment:项目经理" json:"project_manager"`
	ProjectAddress string         `gorm:"column:project_address;type:varchar(500);comment:项目地址" json:"project_address"`
	Contact<PERSON>erson  string         `gorm:"column:contact_person;type:varchar(100);index:idx_contact_person;comment:联系人" json:"contact_person"`
	ContactPhone   string         `gorm:"column:contact_phone;type:varchar(50);comment:联系电话" json:"contact_phone"`
	ContactEmail   string         `gorm:"column:contact_email;type:varchar(100);comment:联系邮箱" json:"contact_email"`
	ProjectStatus  int8           `gorm:"column:project_status;type:tinyint(1);default:1;comment:项目状态(1:进行中,0:已结束)" json:"project_status"`
	StartDate      *time.Time     `gorm:"column:start_date;type:date;comment:项目开始日期" json:"start_date"`
	EndDate        *time.Time     `gorm:"column:end_date;type:date;comment:项目结束日期" json:"end_date"`
	Description    string         `gorm:"column:description;type:text;comment:项目描述" json:"description"`
	CreatedBy      uint           `gorm:"column:created_by;type:bigint(20);comment:创建人ID" json:"created_by"`
	CreatedAt      time.Time      `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedBy      uint           `gorm:"column:updated_by;type:bigint(20);comment:更新人ID" json:"updated_by"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"deleted_at"`
}

// TableName 定义表名
func (Project) TableName() string {
	return "projects"
}

// ProjectFilter 项目查询过滤条件
type ProjectFilter struct {
	ProjectCode    string `form:"project_code"`
	ProjectName    string `form:"project_name"`
	CustomerName   string `form:"customer_name"`
	ProjectManager string `form:"project_manager"`
	ContactPerson  string `form:"contact_person"`
	ProjectStatus  *int8  `form:"project_status"`
	StartDateBegin string `form:"start_date_begin"`
	StartDateEnd   string `form:"start_date_end"`
}

// PaginationOptions 分页选项
type PaginationOptions struct {
	Page     int `form:"page" binding:"required,min=1"`
	PageSize int `form:"pageSize" binding:"required,min=1,max=100"`
}
