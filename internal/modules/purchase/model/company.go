package model

import (
	"time"

	"gorm.io/gorm"
)

// Company 公司档案表模型
type Company struct {
	ID                uint           `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	CompanyCode       string         `gorm:"column:company_code;type:varchar(50);uniqueIndex:uk_company_code;not null;comment:公司编码" json:"company_code"`
	CompanyName       string         `gorm:"column:company_name;type:varchar(200);index:idx_company_name;not null;comment:公司名称" json:"company_name"`
	ShortName         string         `gorm:"column:short_name;type:varchar(100);comment:简称" json:"short_name"`
	LegalPerson       string         `gorm:"column:legal_person;type:varchar(100);comment:法人代表" json:"legal_person"`
	UnifiedCreditCode string         `gorm:"column:unified_credit_code;type:varchar(50);comment:统一社会信用代码" json:"unified_credit_code"`
	TaxNumber         string         `gorm:"column:tax_number;type:varchar(50);comment:税号" json:"tax_number"`
	RegisteredAddress string         `gorm:"column:registered_address;type:varchar(500);comment:注册地址" json:"registered_address"`
	BusinessAddress   string         `gorm:"column:business_address;type:varchar(500);comment:经营地址" json:"business_address"`
	BankName          string         `gorm:"column:bank_name;type:varchar(200);comment:开户银行" json:"bank_name"`
	BankAccount       string         `gorm:"column:bank_account;type:varchar(50);comment:银行账号" json:"bank_account"`
	ContactPerson     string         `gorm:"column:contact_person;type:varchar(100);comment:联系人" json:"contact_person"`
	ContactPhone      string         `gorm:"column:contact_phone;type:varchar(50);comment:联系电话" json:"contact_phone"`
	InvoiceTitle      string         `gorm:"column:invoice_title;type:varchar(200);comment:发票抬头" json:"invoice_title"`
	InvoiceAddress    string         `gorm:"column:invoice_address;type:varchar(500);comment:发票地址" json:"invoice_address"`
	Status            int8           `gorm:"column:status;type:tinyint(1);default:1;comment:状态(1:启用,0:禁用)" json:"status"`
	CreatedBy         uint           `gorm:"column:created_by;type:bigint(20);comment:创建人ID" json:"created_by"`
	CreatedAt         time.Time      `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedBy         uint           `gorm:"column:updated_by;type:bigint(20);comment:更新人ID" json:"updated_by"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"deleted_at"`
}

// TableName 定义表名
func (Company) TableName() string {
	return "companies"
}

// CompanyFilter 公司查询过滤条件
type CompanyFilter struct {
	CompanyCode   string `form:"company_code"`
	CompanyName   string `form:"company_name"`
	ShortName     string `form:"short_name"`
	ContactPerson string `form:"contact_person"`
	ContactPhone  string `form:"contact_phone"`
	Status        *int8  `form:"status"`
}
