package model

import (
	"gorm.io/gorm"
	"time"
)

// PaymentRequest 付款申请表
type PaymentRequest struct {
	ID                          uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	PaymentNo                   string         `gorm:"type:varchar(50);not null;uniqueIndex:uk_payment_no" json:"payment_no"`     // 付款申请单号
	ContractID                  uint           `gorm:"not null;index:idx_contract_id" json:"contract_id"`                         // 关联合同ID
	SupplierID                  uint           `gorm:"not null;index:idx_supplier_id" json:"supplier_id"`                         // 供应商ID
	CompanyID                   uint           `gorm:"not null;index:idx_company_id" json:"company_id"`                           // 公司ID
	PaymentType                 string         `gorm:"type:varchar(20)" json:"payment_type"`                                      // 付款类型
	ContractTotalAmount         float64        `gorm:"type:decimal(15,2);default:0.00" json:"contract_total_amount"`              // 合同总金额
	PaidAmount                  float64        `gorm:"type:decimal(15,2);default:0.00" json:"paid_amount"`                        // 已付款金额
	UnpaidAmount                float64        `gorm:"type:decimal(15,2);default:0.00" json:"unpaid_amount"`                      // 未付款金额
	CurrentPaymentAmount        float64        `gorm:"type:decimal(15,2);not null" json:"current_payment_amount"`                 // 本次申请付款金额
	PaymentReason               string         `gorm:"type:text" json:"payment_reason"`                                           // 付款事由
	CumulativePaidAfterPayment  float64        `gorm:"type:decimal(15,2);default:0.00" json:"cumulative_paid_after_payment"`      // 本次支付后累计付款金额
	RemainingUnpaidAfterPayment float64        `gorm:"type:decimal(15,2);default:0.00" json:"remaining_unpaid_after_payment"`     // 本次支付后合同结余未支付金额
	BankName                    string         `gorm:"type:varchar(200)" json:"bank_name"`                                        // 收款银行
	BankAccount                 string         `gorm:"type:varchar(50)" json:"bank_account"`                                      // 收款账号
	Remark                      string         `gorm:"type:text" json:"remark"`                                                   // 备注
	Status                      string         `gorm:"type:varchar(20);default:'purchase_review';index:idx_status" json:"status"` // 工单状态
	CreatedBy                   *uint          `gorm:"index" json:"created_by"`                                                   // 创建人ID
	CreatedAt                   time.Time      `gorm:"type:datetime(3);not null" json:"created_at"`                               // 创建时间
	UpdatedBy                   *uint          `gorm:"index" json:"updated_by"`                                                   // 更新人ID
	UpdatedAt                   *time.Time     `gorm:"type:datetime(3)" json:"updated_at"`                                        // 更新时间
	DeletedAt                   gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`                             //删除时间

	// 关联关系
	Contract *PurchaseContract    `gorm:"foreignKey:ContractID" json:"contract,omitempty"` // 关联合同
	Supplier *Supplier            `gorm:"foreignKey:SupplierID" json:"supplier,omitempty"` // 关联供应商
	Company  *Company              `gorm:"foreignKey:CompanyID" json:"company,omitempty"`   // 关联公司
	Items    []PaymentRequestItem `gorm:"foreignKey:PaymentID" json:"items,omitempty"`     // 付款申请明细
}

// PaymentRequestItem 付款申请明细表
type PaymentRequestItem struct {
	ID                     uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	PaymentID              uint       `gorm:"not null;index:idx_payment_id" json:"payment_id"`             // 付款申请ID
	ContractItemID         uint       `gorm:"not null;index:idx_contract_item_id" json:"contract_item_id"` // 合同明细ID
	PaidQuantity           int        `gorm:"default:0" json:"paid_quantity"`                              // 已付款数量
	PaidAmount             float64    `gorm:"type:decimal(15,2);default:0.00" json:"paid_amount"`          // 已付款金额
	UnpaidQuantity         int        `gorm:"default:0" json:"unpaid_quantity"`                            // 未付款数量
	UnpaidAmount           float64    `gorm:"type:decimal(15,2);default:0.00" json:"unpaid_amount"`        // 未付款金额
	CurrentPaymentQuantity int        `gorm:"not null" json:"current_payment_quantity"`                    // 本次申请付款数量
	CurrentPaymentAmount   float64    `gorm:"type:decimal(15,2);not null" json:"current_payment_amount"`   // 本次申请付款金额
	Remark                 string     `gorm:"type:text" json:"remark"`                                     // 备注
	CreatedAt              time.Time  `gorm:"type:datetime(3);not null" json:"created_at"`                 // 创建时间
	UpdatedAt              *time.Time `gorm:"type:datetime(3)" json:"updated_at"`                          // 更新时间

	// 关联关系
	PaymentRequest *PaymentRequest       `gorm:"foreignKey:PaymentID" json:"payment_request,omitempty"`    // 关联付款申请
	ContractItem   *PurchaseContractItem `gorm:"foreignKey:ContractItemID" json:"contract_item,omitempty"` // 关联合同明细
}

// TableName 设置表名
func (PaymentRequest) TableName() string {
	return "payment_requests"
}

// TableName 设置表名
func (PaymentRequestItem) TableName() string {
	return "payment_request_items"
}
