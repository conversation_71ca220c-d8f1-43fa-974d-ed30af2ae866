package dto

import (
	"time"
)

// CreateShipmentDTO 创建发货记录的数据传输对象
type CreateShipmentDTO struct {
	ContractID          uint                    `json:"contract_id" binding:"required"` // 关联合同ID
	TrackingInfo        string                  `json:"tracking_info"`                  // 物流信息
	DeliveryAddress     string                  `json:"delivery_address"`               // 收货地址
	ExpectedArrivalDate *time.Time              `json:"expected_arrival_date"`          // 预计到货时间
	ShipmentNotice      string                  `json:"shipment_notice"`                // 发货通知内容
	Remark              string                  `json:"remark"`                         // 备注
	Items               []CreateShipmentItemDTO `json:"items" binding:"required,min=1"` // 发货明细项
	CreatedBy           uint                    `json:"created_by"`                     // 创建人ID（由控制器设置）
}

// CreateShipmentItemDTO 创建发货明细项的数据传输对象
type CreateShipmentItemDTO struct {
	ContractItemID          uint     `json:"contract_item_id" binding:"required"`                // 合同明细ID
	CurrentShipmentQuantity int      `json:"current_shipment_quantity" binding:"required,gte=0"` // 本次发货数量
	CurrentShipmentAmount   float64  `json:"current_shipment_amount" binding:"required,gt=0"`    // 本次发货金额
	SerialNumbers           []string `json:"serial_numbers"`                                     // SN号列表
	Remark                  string   `json:"remark"`                                             // 备注
}

// UpdateShipmentDTO 更新发货记录的数据传输对象
type UpdateShipmentDTO struct {
	TrackingInfo        string                  `json:"tracking_info"`        // 物流信息
	DeliveryAddress     string                  `json:"delivery_address"`     // 收货地址
	ExpectedArrivalDate *time.Time              `json:"expected_arrival_date"` // 预计到货时间
	ShipmentNotice      string                  `json:"shipment_notice"`      // 发货通知内容
	Remark              string                  `json:"remark"`               // 备注
	Items               []UpdateShipmentItemDTO `json:"items"`                // 发货明细项
}

// UpdateShipmentItemDTO 更新发货明细项的数据传输对象
type UpdateShipmentItemDTO struct {
	ID                      uint     `json:"id"`                                                 // 明细ID
	ContractItemID          uint     `json:"contract_item_id" binding:"required"`                // 合同明细ID
	CurrentShipmentQuantity int      `json:"current_shipment_quantity" binding:"required,gte=0"` // 本次发货数量
	CurrentShipmentAmount   float64  `json:"current_shipment_amount" binding:"required,gt=0"`    // 本次发货金额
	SerialNumbers           []string `json:"serial_numbers"`                                     // SN号列表
	Remark                  string   `json:"remark"`                                             // 备注
}

// ShipmentQueryDTO 发货记录查询参数
type ShipmentQueryDTO struct {
	Page       int        `form:"page" json:"page"`               // 页码
	PageSize   int        `form:"page_size" json:"page_size"`     // 每页数量
	ShipmentNo string     `form:"shipment_no" json:"shipment_no"` // 发货通知单号
	ContractID *uint      `form:"contract_id" json:"contract_id"` // 合同ID
	ContractNo string     `form:"contract_no" json:"contract_no"` // 合同编号
	SupplierID *uint      `form:"supplier_id" json:"supplier_id"` // 供应商ID
	Status     string     `form:"status" json:"status"`           // 状态
	IsComplete *bool      `form:"is_complete" json:"is_complete"` // 是否全部发货
	CreatedBy  *uint      `form:"created_by" json:"created_by"`   // 创建人
	StartDate  *time.Time `form:"start_date" json:"start_date"`   // 开始日期
	EndDate    *time.Time `form:"end_date" json:"end_date"`       // 结束日期
}

// ShipmentListDTO 发货记录列表项
type ShipmentListDTO struct {
	ID              uint                     `json:"id"`               // 主键ID
	ShipmentNo      string                   `json:"shipment_no"`      // 发货通知单号
	ContractID      uint                     `json:"contract_id"`      // 关联合同ID
	ContractNo      string                   `json:"contract_no"`      // 合同编号
	SupplierID      uint                     `json:"supplier_id"`      // 供应商ID
	SupplierName    string                   `json:"supplier_name"`    // 供应商名称
	OurCompanyName  string                   `json:"our_company_name"` // 我方公司名称
	TrackingInfo        string                   `json:"tracking_info"`        // 物流信息
	DeliveryAddress     string                   `json:"delivery_address"`     // 收货地址
	ExpectedArrivalDate *time.Time               `json:"expected_arrival_date"` // 预计到货时间
	ShipmentNotice      string                   `json:"shipment_notice"`      // 发货通知
	TotalQuantity   int                      `json:"total_quantity"`   // 发货总数量
	TotalAmount     float64                  `json:"total_amount"`     // 发货总金额
	IsComplete      bool                     `json:"is_complete"`      // 是否全部发货
	Status          string                   `json:"status"`           // 状态
	CreatedBy       *uint                    `json:"created_by"`       // 创建人ID
	CreatedByName   string                   `json:"created_by_name"`  // 创建人姓名
	CreatedAt       time.Time                `json:"created_at"`       // 创建时间
	Items           []*ShipmentItemDetailDTO `json:"items"`            // 发货明细列表
}

// ShipmentDetailDTO 发货记录详情
type ShipmentDetailDTO struct {
	ID              uint                    `json:"id"`               // 主键ID
	ShipmentNo      string                  `json:"shipment_no"`      // 发货通知单号
	ContractID      uint                    `json:"contract_id"`      // 关联合同ID
	ContractNo      string                  `json:"contract_no"`      // 合同编号
	SupplierID      uint                    `json:"supplier_id"`      // 供应商ID
	SupplierName    string                  `json:"supplier_name"`    // 供应商名称
	OurCompanyName  string                  `json:"our_company_name"` // 我方公司名称
	TrackingInfo        string     `json:"tracking_info"`        // 物流信息
	DeliveryAddress     string     `json:"delivery_address"`     // 收货地址
	ExpectedArrivalDate *time.Time `json:"expected_arrival_date"` // 预计到货时间
	ShipmentNotice      string     `json:"shipment_notice"`      // 发货通知
	TotalQuantity   int                     `json:"total_quantity"`   // 发货总数量
	TotalAmount     float64                 `json:"total_amount"`     // 发货总金额
	IsComplete      bool                    `json:"is_complete"`      // 是否全部发货
	Remark          string                  `json:"remark"`           // 备注
	Status          string                  `json:"status"`           // 状态
	CreatedBy       *uint                   `json:"created_by"`       // 创建人ID
	CreatedByName   string                  `json:"created_by_name"`  // 创建人姓名
	CreatedAt       time.Time               `json:"created_at"`       // 创建时间
	UpdatedBy       *uint                   `json:"updated_by"`       // 更新人ID
	UpdatedByName   string                  `json:"updated_by_name"`  // 更新人姓名
	UpdatedAt       *time.Time              `json:"updated_at"`       // 更新时间
	Items           []ShipmentItemDetailDTO `json:"items"`            // 发货明细
}

// ShipmentItemDetailDTO 发货明细详情
type ShipmentItemDetailDTO struct {
	ID                      uint       `json:"id"`                        // 主键ID
	ShipmentID              uint       `json:"shipment_id"`               // 发货ID
	ContractItemID          uint       `json:"contract_item_id"`          // 合同明细ID
	MaterialType            string     `json:"material_type,omitempty"`   // 物料类型
	Model                   string     `json:"model,omitempty"`           // 型号
	Brand                   string     `json:"brand,omitempty"`           // 品牌
	PN                      string     `json:"pn,omitempty"`              // 原厂PN
	Spec                    string     `json:"spec,omitempty"`            // 规格
	Unit                    string     `json:"unit,omitempty"`            // 单位
	ShippedQuantity         int        `json:"shipped_quantity"`          // 已发货数量
	ShippedAmount           float64    `json:"shipped_amount"`            // 已发货金额
	UnshippedQuantity       int        `json:"unshipped_quantity"`        // 未发货数量
	UnshippedAmount         float64    `json:"unshipped_amount"`          // 未发货金额
	CurrentShipmentQuantity int      `json:"current_shipment_quantity"` // 本次发货数量
	CurrentShipmentAmount   float64  `json:"current_shipment_amount"`   // 本次发货金额
	SerialNumbers           []string `json:"serial_numbers"`            // SN号列表
	Remark                  string     `json:"remark"`                    // 备注
	CreatedAt               time.Time  `json:"created_at"`                // 创建时间
	UpdatedAt               *time.Time `json:"updated_at"`                // 更新时间

	// 合同相关信息
	ContractQuantity        int        `json:"contract_quantity,omitempty"` // 合同数量
	ContractPrice           float64    `json:"contract_price,omitempty"`    // 合同单价
	ContractAmount          float64    `json:"contract_amount,omitempty"`   // 合同总金额

	// 付款相关信息
	PaidQuantity            float64    `json:"paid_quantity"`               // 已付款数量
	PaidAmount              float64    `json:"paid_amount"`                 // 已付款金额
	UnpaidQuantity          float64    `json:"unpaid_quantity"`             // 未付款数量
	UnpaidAmount            float64    `json:"unpaid_amount"`               // 未付款金额
}

// ShipmentApprovalDTO 发货审批参数
type ShipmentApprovalDTO struct {
	ShipmentID   uint   `json:"shipment_id" binding:"required"`   // 发货记录ID
	CurrentStage string `json:"current_stage" binding:"required"` // 当前阶段
	Action       string `json:"action" binding:"required"`        // 操作类型：approve/reject
	Comments     string `json:"comments"`                         // 审批意见
}

// ShipmentRollbackDTO 发货回退参数
type ShipmentRollbackDTO struct {
	ShipmentID   uint   `json:"shipment_id"`                      // 发货记录ID（从URL路径获取）
	CurrentStage string `json:"current_stage" binding:"required"` // 当前阶段
	RollbackTo   string `json:"rollback_to" binding:"required"`   // 回退到的状态
	Comments     string `json:"comments"`                         // 回退原因
	ApproverID   uint   `json:"-"`                                // 审批人ID（由控制器设置）
}

// ShipmentStatisticsDTO 发货统计信息
type ShipmentStatisticsDTO struct {
	TotalCount      int64   `json:"total_count"`      // 总发货记录数
	DraftCount      int64   `json:"draft_count"`      // 草稿状态数量
	PendingCount    int64   `json:"pending_count"`    // 待审批数量
	CompletedCount  int64   `json:"completed_count"`  // 已完成数量
	CancelledCount  int64   `json:"cancelled_count"`  // 已取消数量
	TotalAmount     float64 `json:"total_amount"`     // 总发货金额
	CompletedAmount float64 `json:"completed_amount"` // 已完成发货金额
}
