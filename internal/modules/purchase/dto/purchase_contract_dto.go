package dto

import (
	// 引入公共模型
	"backend/internal/modules/purchase/model"
	"time"
)

// CreatePurchaseContractDTO 创建采购合同的数据传输对象
type CreatePurchaseContractDTO struct {
	ContractNo      string                  `json:"contract_no" binding:"required"`    // 合同编号
	InquiryID       *uint                   `json:"inquiry_id"`                        // 关联询价单ID
	ProjectID       *uint                   `json:"project_id"`                        // 项目ID
	OurCompanyID    uint                    `json:"our_company_id" binding:"required"` // 我方公司ID
	SupplierID      uint                    `json:"supplier_id" binding:"required"`    // 供应商ID
	ContractTitle   string                  `json:"contract_title"`                    // 合同标题
	ContractType    string                  `json:"contract_type"`                     // 合同类型
	SigningDate     *time.Time              `json:"signing_date"`                      // 签订日期
	DeliveryAddress string                  `json:"delivery_address"`                  // 交货地址
	PaymentTerms    string                  `json:"payment_terms"`                     // 付款条款
	WarrantyPeriod  string                  `json:"warranty_period"`                   // 质保期限
	TotalAmount     float64                 `json:"total_amount"`                      // 合同总金额
	Items           []CreateContractItemDTO `json:"items" binding:"required,min=1"`    // 合同明细项
}

// CreateContractItemDTO 创建合同明细项的数据传输对象
type CreateContractItemDTO struct {
	InquiryItemID    *uint      `json:"inquiry_item_id"`                            // 关联询价明细ID
	ProductID        *uint      `json:"product_id"`                                 // 产品ID
	MaterialType     string     `json:"material_type"`                              // 物料类型
	Model            string     `json:"model"`                                      // 型号
	Brand            string     `json:"brand"`                                      // 品牌
	PN               string     `json:"pn"`                                         // 原厂PN
	Spec             string     `json:"spec"`                                       // 规格
	Unit             string     `json:"unit"`                                       // 单位
	ContractQuantity int        `json:"contract_quantity" binding:"required,min=1"` // 合同数量
	ContractPrice    float64    `json:"contract_price" binding:"required"`          // 合同单价
	ContractAmount   float64    `json:"contract_amount" binding:"required"`         // 合同总价
	DeliveryDate     *time.Time `json:"delivery_date"`                              // 预计交货日期
	Remark           string     `json:"remark"`                                     // 备注
}

// UpdatePurchaseContractDTO 更新采购合同的数据传输对象
type UpdatePurchaseContractDTO struct {
	ID              uint                    `json:"id" binding:"required"` // 合同ID
	InquiryID       *uint                   `json:"inquiry_id"`            // 关联询价单ID
	ProjectID       *uint                   `json:"project_id"`            // 项目ID
	OurCompanyID    uint                    `json:"our_company_id"`        // 我方公司ID
	SupplierID      uint                    `json:"supplier_id"`           // 供应商ID
	ContractTitle   string                  `json:"contract_title"`        // 合同标题
	ContractType    string                  `json:"contract_type"`         // 合同类型
	SigningDate     *time.Time              `json:"signing_date"`          // 签订日期
	DeliveryAddress string                  `json:"delivery_address"`      // 交货地址
	PaymentTerms    string                  `json:"payment_terms"`         // 付款条款
	WarrantyPeriod  string                  `json:"warranty_period"`       // 质保期限
	TotalAmount     float64                 `json:"total_amount"`          // 合同总金额
	Items           []UpdateContractItemDTO `json:"items"`                 // 合同明细项
	UpdatedBy       uint                    `json:"updated_by"`            // 更新人
}

// UpdateContractItemDTO 更新合同明细项的数据传输对象
type UpdateContractItemDTO struct {
	ID               uint       `json:"id,omitempty"`      // 明细ID，新增时为空
	InquiryItemID    *uint      `json:"inquiry_item_id"`   // 关联询价明细ID
	ProductID        *uint      `json:"product_id"`        // 产品ID
	MaterialType     string     `json:"material_type"`     // 物料类型
	Model            string     `json:"model"`             // 型号
	Brand            string     `json:"brand"`             // 品牌
	PN               string     `json:"pn"`                // 原厂PN
	Spec             string     `json:"spec"`              // 规格
	Unit             string     `json:"unit"`              // 单位
	ContractQuantity int        `json:"contract_quantity"` // 合同数量
	ContractPrice    float64    `json:"contract_price"`    // 合同单价
	ContractAmount   float64    `json:"contract_amount"`   // 合同总价
	DeliveryDate     *time.Time `json:"delivery_date"`     // 预计交货日期
	Remark           string     `json:"remark"`            // 备注
}

// PurchaseContractListQuery 采购合同列表查询参数
type PurchaseContractListQuery struct {
	model.PurchaseContractFilter
	model.PaginationOptions
}

// PurchaseContractListResult 采购合同列表查询结果
type PurchaseContractListResult struct {
	Total int64                     `json:"total"`
	List  []*model.PurchaseContract `json:"list"`
}

// ContractApprovalDTO 合同审批数据传输对象
type ContractApprovalDTO struct {
	ContractID   uint   `json:"contract_id" binding:"required"`
	ApproverID   uint   `json:"approver_id"`               // 从JWT自动获取
	Action       string `json:"action" binding:"required"` // approve, reject
	Comments     string `json:"comments"`
	CurrentStage string `json:"current_stage" binding:"required"` // 当前审批阶段
}

// ContractRollbackDTO 合同回退数据传输对象
type ContractRollbackDTO struct {
	ContractID   uint   `json:"contract_id" binding:"required"`
	ApproverID   uint   `json:"approver_id"`                      // 从JWT自动获取
	RollbackTo   string `json:"rollback_to" binding:"required"`   // 回退到的阶段
	CurrentStage string `json:"current_stage" binding:"required"` // 当前阶段
	Comments     string `json:"comments"`
}

// ContractWorkflowStatusQueryDTO 合同工作流状态查询数据
type ContractWorkflowStatusQueryDTO struct {
	ContractID uint `json:"contract_id" binding:"required"`
}

// ContractWorkflowStatusResponseDTO 合同工作流状态响应数据
type ContractWorkflowStatusResponseDTO struct {
	ContractID      uint                         `json:"contract_id"`
	CurrentStage    string                       `json:"current_stage"`
	CanRollback     bool                         `json:"can_rollback"`
	StageHistory    []string                     `json:"stage_history"`
	ApprovalHistory []ContractApprovalHistoryDTO `json:"approval_history"`
	RollbackHistory []ContractRollbackHistoryDTO `json:"rollback_history"`
	Result          string                       `json:"result"`
	StartedAt       time.Time                    `json:"started_at"`
	CompletedAt     *time.Time                   `json:"completed_at"`
}

// ContractApprovalHistoryDTO 合同审批历史数据
type ContractApprovalHistoryDTO struct {
	ApproverID   uint      `json:"approver_id"`
	ApproverName string    `json:"approver_name"`
	Action       string    `json:"action"`
	Comments     string    `json:"comments"`
	CurrentStage string    `json:"current_stage"`
	Timestamp    time.Time `json:"timestamp"`
}

// ContractRollbackHistoryDTO 合同回退历史数据
type ContractRollbackHistoryDTO struct {
	ApproverID   uint      `json:"approver_id"`
	ApproverName string    `json:"approver_name"`
	RollbackTo   string    `json:"rollback_to"`
	CurrentStage string    `json:"current_stage"`
	Comments     string    `json:"comments"`
	Timestamp    time.Time `json:"timestamp"`
}

// PurchaseContractDetailDTO 采购合同详情数据传输对象
type PurchaseContractDetailDTO struct {
	*model.PurchaseContract
	ProjectName    string `json:"project_name"`     // 项目名称
	OurCompanyName string `json:"our_company_name"` // 我方公司名称
}

// ContractItemDetailDTO 合同明细详情数据传输对象
type ContractItemDetailDTO struct {
	*model.PurchaseContractItem
	// 附加信息
	SupplierName string `json:"supplier_name,omitempty"`
}

// InquiryItemPurchaseStatsDTO 询价明细采购统计信息
type InquiryItemPurchaseStatsDTO struct {
	InquiryItemID       uint    `json:"inquiry_item_id"`      // 询价明细ID
	MaterialType        string  `json:"material_type"`        // 物料类型
	Model               string  `json:"model"`                // 型号
	Brand               string  `json:"brand"`                // 品牌
	PN                  string  `json:"pn"`                   // PN号
	Spec                string  `json:"spec"`                 // 规格
	Unit                string  `json:"unit"`                 // 单位
	InquiryQuantity     int     `json:"inquiry_quantity"`     // 询价数量
	InquiryBudget       float64 `json:"inquiry_budget"`       // 询价预算
	PurchasedQuantity   int     `json:"purchased_quantity"`   // 已采购数量
	PurchasedAmount     float64 `json:"purchased_amount"`     // 已采购金额
	UnpurchasedQuantity int     `json:"unpurchased_quantity"` // 未采购数量
	UnpurchasedBudget   float64 `json:"unpurchased_budget"`   // 未采购预算
}

// ContractHistoryDTO 合同历史记录数据传输对象
type ContractHistoryDTO struct {
	ID             uint      `json:"id"`
	BusinessType   string    `json:"business_type"`
	BusinessID     uint      `json:"business_id"`
	PreviousStatus string    `json:"previous_status"`
	NewStatus      string    `json:"new_status"`
	Action         string    `json:"action"`
	OperatorID     uint      `json:"operator_id"`
	OperatorName   string    `json:"operator_name"`
	OperationTime  time.Time `json:"operation_time"`
	Comments       string    `json:"comments"`
	CreatedAt      time.Time `json:"created_at"`
}
