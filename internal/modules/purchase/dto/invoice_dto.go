package dto

import (
	"backend/internal/modules/purchase/model"
	"time"
)

// CreateInvoiceDTO 创建发票的数据传输对象
type CreateInvoiceDTO struct {
	ContractID    uint    `json:"contract_id" binding:"required"`         // 关联合同ID
	InvoiceNo     string  `json:"invoice_no" binding:"required"`          // 发票号
	InvoiceAmount float64 `json:"invoice_amount" binding:"required,gt=0"` // 本次开票金额
	Remark        string  `json:"remark"`                                 // 备注
	CreatedBy     uint    `json:"-"`                                      // 创建人ID（系统自动设置）
}

// UpdateInvoiceDTO 更新发票的数据传输对象
type UpdateInvoiceDTO struct {
	InvoiceNo     string  `json:"invoice_no"`                     // 发票号
	InvoiceAmount float64 `json:"invoice_amount" binding:"gt=0"`  // 本次开票金额
	Remark        string  `json:"remark"`                         // 备注
	UpdatedBy     uint    `json:"-"`                              // 更新人ID（系统自动设置）
}

// InvoiceQueryDTO 发票查询参数
type InvoiceQueryDTO struct {
	Page       int      `form:"page,default=1" binding:"min=1"`               // 页码
	PageSize   int      `form:"page_size,default=10" binding:"min=1,max=100"` // 每页数量
	InvoiceNo  string   `form:"invoice_no"`                                   // 发票号
	ContractID *uint    `form:"contract_id"`                                  // 关联合同ID
	ContractNo string   `form:"contract_no"`                                  // 合同编号
	CreatedBy  *uint    `form:"created_by"`                                   // 创建人ID
	StartDate  string   `form:"start_date"`                                   // 开始日期
	EndDate    string   `form:"end_date"`                                     // 结束日期
	MinAmount  *float64 `form:"min_amount"`                                   // 最小金额
	MaxAmount  *float64 `form:"max_amount"`                                   // 最大金额
}

// InvoiceDetailDTO 发票详情数据传输对象
type InvoiceDetailDTO struct {
	*model.Invoice
	// 附加信息
	ContractTotalAmount float64 `json:"contract_total_amount,omitempty"` // 合同总金额
	TotalInvoicedAmount float64 `json:"total_invoiced_amount,omitempty"` // 已开票总金额
	RemainingAmount     float64 `json:"remaining_amount,omitempty"`      // 剩余可开票金额
}

// InvoiceListDTO 发票列表数据传输对象
type InvoiceListDTO struct {
	ID             uint      `json:"id"`
	InvoiceNo      string    `json:"invoice_no"`
	ContractID     uint      `json:"contract_id"`
	ContractNo     string    `json:"contract_no"`
	SupplierName   string    `json:"supplier_name"`
	ProjectName    string    `json:"project_name"`
	OurCompanyName string    `json:"our_company_name"`
	InvoiceAmount  float64   `json:"invoice_amount"`
	Remark         string    `json:"remark"`
	CreatedBy      uint      `json:"created_by"`
	CreatorName    string    `json:"creator_name"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedBy      *uint     `json:"updated_by"`
	UpdaterName    string    `json:"updater_name"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// InvoiceSummaryDTO 发票汇总数据传输对象
type InvoiceSummaryDTO struct {
	ContractID            uint    `json:"contract_id"`            // 合同ID
	ContractNo            string  `json:"contract_no"`            // 合同编号
	ContractTotalAmount   float64 `json:"contract_total_amount"`  // 合同总金额
	TotalInvoicedAmount   float64 `json:"total_invoiced_amount"`  // 已开票总金额
	RemainingAmount       float64 `json:"remaining_amount"`       // 剩余可开票金额
	InvoiceCount          int     `json:"invoice_count"`          // 发票数量
}

// InvoiceValidationDTO 发票验证数据传输对象
type InvoiceValidationDTO struct {
	ContractID      uint    `json:"contract_id"`      // 合同ID
	InvoiceAmount   float64 `json:"invoice_amount"`   // 开票金额
	ExcludeInvoiceID *uint  `json:"exclude_invoice_id,omitempty"` // 排除的发票ID（用于更新时验证）
}
