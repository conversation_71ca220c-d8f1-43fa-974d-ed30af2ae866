package dto

import (
	"backend/internal/modules/purchase/model"
	"time"
)

// CreatePurchaseRequestDTO 创建采购申请的数据传输对象
type CreatePurchaseRequestDTO struct {
	ProjectID            *uint                          `json:"project_id"`
	RequestType          []string                       `json:"request_type" binding:"required"`
	UrgencyLevel         string                         `json:"urgency_level" binding:"required"`
	ExpectedDeliveryDate *time.Time                     `json:"expected_delivery_date"`
	Reason               string                         `json:"reason" binding:"required"`
	ReceiveAddress       string                         `json:"receive_address"`
	Receiver             string                         `json:"receiver"`
	ReceiverPhone        string                         `json:"receiver_phone"`
	Items                []CreatePurchaseRequestItemDTO `json:"items" binding:"required,min=1"`
	CreatedBy            uint                           `json:"created_by"`
}

// CreatePurchaseRequestItemDTO 创建采购申请明细的数据传输对象
type CreatePurchaseRequestItemDTO struct {
	ProductID      *uint  `json:"product_id"`
	MaterialType   string `json:"material_type" binding:"required"`
	Model          string `json:"model"`
	Brand          string `json:"brand" binding:"required"`
	PN             string `json:"pn"`
	Specifications string `json:"spec"`
	Unit           string `json:"unit"`
	Quantity       int    `json:"quantity" binding:"required,min=1"`
	Remark         string `json:"remark"`
}

// UpdatePurchaseRequestDTO 更新采购申请的数据传输对象
type UpdatePurchaseRequestDTO struct {
	ID                   uint                           `json:"id" binding:"required"`
	ProjectID            *uint                          `json:"project_id"`
	RequestType          []string                       `json:"request_type"`
	UrgencyLevel         string                         `json:"urgency_level"`
	ExpectedDeliveryDate *time.Time                     `json:"expected_delivery_date"`
	Reason               string                         `json:"reason"`
	ReceiveAddress       string                         `json:"receive_address"`
	Receiver             string                         `json:"receiver"`
	ReceiverPhone        string                         `json:"receiver_phone"`
	Items                []UpdatePurchaseRequestItemDTO `json:"items"`
	UpdatedBy            uint                           `json:"updated_by"`
}

// UpdatePurchaseRequestItemDTO 更新采购申请明细的数据传输对象
type UpdatePurchaseRequestItemDTO struct {
	ID             uint   `json:"id"`
	ProductID      *uint  `json:"product_id"`
	MaterialType   string `json:"material_type"`
	Model          string `json:"model"`
	Brand          string `json:"brand"`
	PN             string `json:"pn"`
	Specifications string `json:"spec"`
	Unit           string `json:"unit"`
	Quantity       int    `json:"quantity"`
	Remark         string `json:"remark"`
}

// UpdatePurchaseRequestStatusDTO 更新采购申请状态的数据传输对象
type UpdatePurchaseRequestStatusDTO struct {
	ID        uint   `json:"id" binding:"required"`
	Status    string `json:"status" binding:"required"`
	UpdatedBy uint   `json:"updated_by"`
}

// PurchaseRequestListQuery 采购申请列表查询参数
type PurchaseRequestListQuery struct {
	model.PurchaseRequestFilter
	model.PaginationOptions
}

// PurchaseRequestListResult 采购申请列表查询结果
type PurchaseRequestListResult struct {
	Total int64                    `json:"total"`
	List  []*model.PurchaseRequest `json:"list"`
}

// WorkflowApprovalDTO 工作流审批数据
type WorkflowApprovalDTO struct {
	RequestID    uint   `json:"request_id" binding:"required"`
	Action       string `json:"action" binding:"required"` // approve, reject
	Comments     string `json:"comments"`
	CurrentStage string `json:"current_stage" binding:"required"` // 当前审批阶段
	ApproverID   uint   `json:"-"`                                // 审批人ID（由控制器设置）
}

// WorkflowRollbackDTO 工作流回退数据
type WorkflowRollbackDTO struct {
	RequestID    uint   `json:"request_id" binding:"required"`
	ApproverID   uint   `json:"approver_id" binding:"required"`
	RollbackTo   string `json:"rollback_to" binding:"required"`   // 回退到的阶段
	CurrentStage string `json:"current_stage" binding:"required"` // 当前阶段
	Comments     string `json:"comments"`
}

// WorkflowStatusQueryDTO 工作流状态查询数据
type WorkflowStatusQueryDTO struct {
	RequestID uint `json:"request_id" binding:"required"`
}

// WorkflowStatusResponseDTO 工作流状态响应数据
type WorkflowStatusResponseDTO struct {
	RequestID       uint                 `json:"request_id"`
	CurrentStage    string               `json:"current_stage"`
	CanRollback     bool                 `json:"can_rollback"`
	StageHistory    []string             `json:"stage_history"`
	ApprovalHistory []ApprovalHistoryDTO `json:"approval_history"`
	RollbackHistory []RollbackHistoryDTO `json:"rollback_history"`
	Result          string               `json:"result"`
	StartedAt       time.Time            `json:"started_at"`
	CompletedAt     *time.Time           `json:"completed_at"`
}

// ApprovalHistoryDTO 审批历史数据
type ApprovalHistoryDTO struct {
	ApproverID   uint      `json:"approver_id"`
	Action       string    `json:"action"`
	Comments     string    `json:"comments"`
	CurrentStage string    `json:"current_stage"`
	Timestamp    time.Time `json:"timestamp"`
}

// RollbackHistoryDTO 回退历史数据
type RollbackHistoryDTO struct {
	ApproverID   uint      `json:"approver_id"`
	RollbackTo   string    `json:"rollback_to"`
	CurrentStage string    `json:"current_stage"`
	Comments     string    `json:"comments"`
	Timestamp    time.Time `json:"timestamp"`
}

// PurchaseRequestHistoryDTO 采购申请历史记录数据
type PurchaseRequestHistoryDTO struct {
	ID             uint      `json:"id"`
	BusinessType   string    `json:"business_type"`
	BusinessID     uint      `json:"business_id"`
	PreviousStatus string    `json:"previous_status"`
	NewStatus      string    `json:"new_status"`
	Action         string    `json:"action"`
	OperatorID     uint      `json:"operator_id"`
	OperatorName   string    `json:"operator_name"`
	OperationTime  time.Time `json:"operation_time"`
	Comments       string    `json:"comments"`
	CreatedAt      time.Time `json:"created_at"`
}
