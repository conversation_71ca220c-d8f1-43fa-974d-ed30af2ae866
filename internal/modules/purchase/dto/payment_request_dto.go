package dto

import (
	"backend/internal/modules/purchase/model"
	"time"
)

// CreatePaymentRequestDTO 创建付款申请的数据传输对象
type CreatePaymentRequestDTO struct {
	ContractID           uint                          `json:"contract_id" binding:"required"`                 // 关联合同ID
	PaymentType          string                        `json:"payment_type" binding:"required"`                // 付款类型
	CurrentPaymentAmount float64                       `json:"current_payment_amount" binding:"required,gt=0"` // 本次申请付款金额
	PaymentReason        string                        `json:"payment_reason" binding:"required"`              // 付款事由
	Remark               string                        `json:"remark"`                                         // 备注
	Items                []CreatePaymentRequestItemDTO `json:"items" binding:"required,min=1"`                 // 付款申请明细项
	CreatedBy            uint                          `json:"-"`                                              // 创建人ID（系统自动设置，不需要前端传输）
}

// CreatePaymentRequestItemDTO 创建付款申请明细项的数据传输对象
type CreatePaymentRequestItemDTO struct {
	ContractItemID         uint    `json:"contract_item_id" binding:"required"`               // 合同明细ID
	CurrentPaymentQuantity int     `json:"current_payment_quantity" binding:"required,gte=0"` // 本次申请付款数量
	CurrentPaymentAmount   float64 `json:"current_payment_amount" binding:"required,gt=0"`    // 本次申请付款金额
	Remark                 string  `json:"remark"`                                            // 备注
}

// UpdatePaymentRequestDTO 更新付款申请的数据传输对象
type UpdatePaymentRequestDTO struct {
	PaymentType          string                        `json:"payment_type"`                          // 付款类型
	CurrentPaymentAmount float64                       `json:"current_payment_amount" binding:"gt=0"` // 本次申请付款金额
	PaymentReason        string                        `json:"payment_reason"`                        // 付款事由
	Remark               string                        `json:"remark"`                                // 备注
	Items                []UpdatePaymentRequestItemDTO `json:"items"`                                 // 付款申请明细项
	UpdatedBy            uint                          `json:"updated_by" binding:"required"`         // 更新人ID
}

// UpdatePaymentRequestItemDTO 更新付款申请明细项的数据传输对象
type UpdatePaymentRequestItemDTO struct {
	ID                     uint    `json:"id,omitempty"`                             // 明细ID，新增时为空
	ContractItemID         uint    `json:"contract_item_id" binding:"required"`      // 合同明细ID
	CurrentPaymentQuantity int     `json:"current_payment_quantity" binding:"gte=0"` // 本次申请付款数量
	CurrentPaymentAmount   float64 `json:"current_payment_amount" binding:"gt=0"`    // 本次申请付款金额
	Remark                 string  `json:"remark"`                                   // 备注
}

// PaymentRequestQueryDTO 付款申请查询参数
type PaymentRequestQueryDTO struct {
	Page        int      `form:"page,default=1" binding:"min=1"`               // 页码
	PageSize    int      `form:"page_size,default=10" binding:"min=1,max=100"` // 每页数量
	PaymentNo   string   `form:"payment_no"`                                   // 付款申请单号
	ContractID  *uint    `form:"contract_id"`                                  // 关联合同ID
	ContractNo  string   `form:"contract_no"`                                  // 合同编号
	SupplierID  *uint    `form:"supplier_id"`                                  // 供应商ID
	PaymentType string   `form:"payment_type"`                                 // 付款类型
	Status      string   `form:"status"`                                       // 状态
	CreatedBy   *uint    `form:"created_by"`                                   // 创建人ID
	StartDate   string   `form:"start_date"`                                   // 开始日期
	EndDate     string   `form:"end_date"`                                     // 结束日期
	MinAmount   *float64 `form:"min_amount"`                                   // 最小金额
	MaxAmount   *float64 `form:"max_amount"`                                   // 最大金额
}

// PaymentRequestDetailDTO 付款申请详情数据传输对象
type PaymentRequestDetailDTO struct {
	*model.PaymentRequest
	// 附加信息
	ContractNo     string `json:"contract_no,omitempty"`     // 合同编号
	SupplierName   string `json:"supplier_name,omitempty"`   // 供应商名称
	OurCompanyName string `json:"our_company_name,omitempty"` // 我方公司名称
	CreatorName    string `json:"creator_name,omitempty"`    // 创建人姓名
	UpdaterName    string `json:"updater_name,omitempty"`    // 更新人姓名
}

// PaymentRequestItemDetailDTO 付款申请明细详情数据传输对象
type PaymentRequestItemDetailDTO struct {
	*model.PaymentRequestItem
	// 附加信息
	ContractItemNo string `json:"contract_item_no,omitempty"` // 合同明细编号
}

// PaymentRequestListDTO 付款申请列表数据传输对象
type PaymentRequestListDTO struct {
	ID                   uint                           `json:"id"`
	PaymentNo            string                         `json:"payment_no"`
	ContractID           uint                           `json:"contract_id"`
	ContractNo           string                         `json:"contract_no"`
	SupplierID           uint                           `json:"supplier_id"`
	SupplierName         string                         `json:"supplier_name"`
	OurCompanyName       string                         `json:"our_company_name"`
	BankName             string                         `json:"bank_name"`     // 开户银行
	BankAccount          string                         `json:"bank_account"`  // 银行账户
	PaymentType          string                         `json:"payment_type"`
	ContractTotalAmount  float64                        `json:"contract_total_amount"`
	CurrentPaymentAmount float64                        `json:"current_payment_amount"`
	Status               string                         `json:"status"`
	CreatedBy            *uint                          `json:"created_by"`
	CreatorName          string                         `json:"creator_name"`
	CreatedAt            time.Time                      `json:"created_at"`
	Items                []*PaymentRequestItemDetailDTO `json:"items"` // 付款明细列表
}

// PaymentRequestApprovalDTO 付款申请审批数据传输对象
type PaymentRequestApprovalDTO struct {
	PaymentID uint   `json:"payment_id" binding:"required"`                  // 付款申请ID
	Action    string `json:"action" binding:"required,oneof=approve reject"` // 审批动作
	Comments  string `json:"comments"`                                       // 审批意见
	Stage     string `json:"stage" binding:"required"`                       // 审批阶段
}

// PaymentRequestRollbackDTO 付款申请回退数据传输对象
type PaymentRequestRollbackDTO struct {
	PaymentID  uint   `json:"payment_id" binding:"required"`  // 付款申请ID
	RollbackTo string `json:"rollback_to" binding:"required"` // 回退到的阶段
	Comments   string `json:"comments" binding:"required"`    // 回退原因
}

// PaymentRequestHistoryDTO 付款申请历史记录DTO
type PaymentRequestHistoryDTO struct {
	ID             uint      `json:"id"`              // 历史记录ID
	BusinessType   string    `json:"business_type"`   // 业务类型
	BusinessID     uint      `json:"business_id"`     // 业务ID
	PreviousStatus string    `json:"previous_status"` // 原状态
	NewStatus      string    `json:"new_status"`      // 新状态
	Action         string    `json:"action"`          // 操作动作
	OperatorID     uint      `json:"operator_id"`     // 操作人ID
	OperatorName   string    `json:"operator_name"`   // 操作人姓名
	OperationTime  time.Time `json:"operation_time"`  // 操作时间
	Comments       string    `json:"comments"`        // 操作意见
	CreatedAt      time.Time `json:"created_at"`      // 创建时间

	// 显示字段
	PreviousStatusDisplay string `json:"previous_status_display"` // 原状态显示名称
	NewStatusDisplay      string `json:"new_status_display"`      // 新状态显示名称
	ActionDisplay         string `json:"action_display"`          // 操作动作显示名称
}
