package controller

import (
	"net/http"
	"strconv"

	"backend/internal/common/utils"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/service"
	"backend/response"

	"github.com/gin-gonic/gin"
)

// ShipmentController 发货管理控制器
type ShipmentController struct {
	service service.ShipmentService
}

// NewShipmentController 创建发货管理控制器
func NewShipmentController(service service.ShipmentService) *ShipmentController {
	return &ShipmentController{
		service: service,
	}
}

// RegisterRoutes 注册发货管理相关路由
func (c *ShipmentController) RegisterRoutes(router *gin.RouterGroup) {
	shipmentGroup := router.Group("/shipments")
	{
		// 基本CRUD操作
		shipmentGroup.POST("", c.Create)                          // 创建发货记录
		shipmentGroup.GET("", c.List)                             // 获取发货记录列表
		shipmentGroup.GET("/:id", c.GetByID)                      // 根据ID获取发货记录
		shipmentGroup.GET("/by-no/:shipment_no", c.GetByShipmentNo) // 根据发货通知单号获取发货记录
		shipmentGroup.PUT("/:id", c.Update)                       // 更新发货记录
		shipmentGroup.DELETE("/:id", c.Delete)                    // 删除发货记录

		// 工作流操作
		shipmentGroup.POST("/:id/submit", c.Submit)   // 提交发货记录
		shipmentGroup.POST("/:id/cancel", c.Cancel)   // 取消发货记录
		shipmentGroup.POST("/approve", c.Approve)     // 审批发货记录
		shipmentGroup.POST("/:id/rollback", c.Rollback) // 回退发货记录

		// 查询操作
		shipmentGroup.GET("/by-contract/:contract_id", c.GetByContractID) // 根据合同ID获取发货记录列表
		shipmentGroup.GET("/:id/history", c.GetShipmentHistory)           // 获取发货记录历史记录
		shipmentGroup.GET("/statistics", c.GetStatistics)                 // 获取发货统计信息
	}
}

// Create 创建发货记录
// @Summary 创建发货记录
// @Description 创建新的发货记录
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param shipment body dto.CreateShipmentDTO true "发货记录信息"
// @Success 200 {object} response.ResponseStruct{data=model.Shipment} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments [post]
func (c *ShipmentController) Create(ctx *gin.Context) {
	var createDTO dto.CreateShipmentDTO
	if err := ctx.ShouldBindJSON(&createDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 从JWT获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	createDTO.CreatedBy = userID

	shipment, err := c.service.Create(ctx.Request.Context(), &createDTO)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, shipment, "创建发货记录成功")
}

// GetByID 根据ID获取发货记录
// @Summary 根据ID获取发货记录
// @Description 根据ID获取发货记录详情
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param id path int true "发货记录ID"
// @Success 200 {object} response.ResponseStruct{data=dto.ShipmentDetailDTO} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "发货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/{id} [get]
func (c *ShipmentController) GetByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的发货记录ID")
		return
	}

	shipment, err := c.service.GetByID(ctx.Request.Context(), uint(id))
	if err != nil {
		if err.Error() == "发货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "发货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取发货记录失败")
		}
		return
	}

	response.Success(ctx, shipment, "获取发货记录成功")
}

// GetByShipmentNo 根据发货通知单号获取发货记录
// @Summary 根据发货通知单号获取发货记录
// @Description 根据发货通知单号获取发货记录详情
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param shipment_no path string true "发货通知单号"
// @Success 200 {object} response.ResponseStruct{data=dto.ShipmentDetailDTO} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "发货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/by-no/{shipment_no} [get]
func (c *ShipmentController) GetByShipmentNo(ctx *gin.Context) {
	shipmentNo := ctx.Param("shipment_no")
	if shipmentNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "发货通知单号不能为空")
		return
	}

	shipment, err := c.service.GetByShipmentNo(ctx.Request.Context(), shipmentNo)
	if err != nil {
		if err.Error() == "发货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "发货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取发货记录失败")
		}
		return
	}

	response.Success(ctx, shipment, "获取发货记录成功")
}

// Update 更新发货记录
// @Summary 更新发货记录
// @Description 更新发货记录信息
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param id path int true "发货记录ID"
// @Param shipment body dto.UpdateShipmentDTO true "发货记录信息"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "发货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/{id} [put]
func (c *ShipmentController) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的发货记录ID")
		return
	}

	var updateDTO dto.UpdateShipmentDTO
	if err := ctx.ShouldBindJSON(&updateDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.Update(ctx.Request.Context(), uint(id), &updateDTO, userID)
	if err != nil {
		if err.Error() == "发货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "发货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, err.Error())
		}
		return
	}

	response.Success(ctx, nil, "更新发货记录成功")
}

// Delete 删除发货记录
// @Summary 删除发货记录
// @Description 删除发货记录
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param id path int true "发货记录ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "发货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/{id} [delete]
func (c *ShipmentController) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的发货记录ID")
		return
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.Delete(ctx.Request.Context(), uint(id), userID)
	if err != nil {
		if err.Error() == "发货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "发货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, err.Error())
		}
		return
	}

	response.Success(ctx, nil, "删除发货记录成功")
}

// List 获取发货记录列表
// @Summary 获取发货记录列表
// @Description 获取发货记录列表，支持分页和筛选
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Param shipment_no query string false "发货通知单号"
// @Param contract_id query int false "合同ID"
// @Param contract_no query string false "合同编号"
// @Param supplier_id query int false "供应商ID"
// @Param status query string false "状态"
// @Param is_complete query bool false "是否全部发货"
// @Param created_by query int false "创建人"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} response.ResponseStruct{data=object{list=[]dto.ShipmentListDTO,total=int}} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments [get]
func (c *ShipmentController) List(ctx *gin.Context) {
	var query dto.ShipmentQueryDTO
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	shipments, total, err := c.service.List(ctx.Request.Context(), &query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取发货记录列表失败")
		return
	}

	// 返回标准的分页结构
	result := map[string]interface{}{
		"list":  shipments,
		"total": total,
	}

	response.Success(ctx, result, "获取发货记录列表成功")
}

// Submit 提交发货记录
// @Summary 提交发货记录
// @Description 提交发货记录进入审批流程
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param id path int true "发货记录ID"
// @Success 200 {object} response.Response "提交成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "发货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/{id}/submit [post]
func (c *ShipmentController) Submit(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的发货记录ID")
		return
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.Submit(ctx.Request.Context(), uint(id), userID)
	if err != nil {
		if err.Error() == "发货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "发货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, err.Error())
		}
		return
	}

	response.Success(ctx, nil, "提交发货记录成功")
}

// Cancel 取消发货记录
// @Summary 取消发货记录
// @Description 取消发货记录
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param id path int true "发货记录ID"
// @Success 200 {object} response.Response "取消成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "发货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/{id}/cancel [post]
func (c *ShipmentController) Cancel(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的发货记录ID")
		return
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.Cancel(ctx.Request.Context(), uint(id), userID)
	if err != nil {
		if err.Error() == "发货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "发货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, err.Error())
		}
		return
	}

	response.Success(ctx, nil, "取消发货记录成功")
}

// Approve 审批发货记录
// @Summary 审批发货记录
// @Description 审批发货记录
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param approval body dto.ShipmentApprovalDTO true "审批信息"
// @Success 200 {object} response.Response "审批成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/approve [post]
func (c *ShipmentController) Approve(ctx *gin.Context) {
	var approvalDTO dto.ShipmentApprovalDTO
	if err := ctx.ShouldBindJSON(&approvalDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	// 验证当前阶段
	shipment, err := c.service.GetByID(ctx.Request.Context(), approvalDTO.ShipmentID)
	if err != nil {
		if err.Error() == "发货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "发货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取发货记录失败")
		}
		return
	}

	if shipment.Status != approvalDTO.CurrentStage {
		response.Fail(ctx, http.StatusBadRequest, "当前阶段不匹配，请刷新页面后重试")
		return
	}

	err = c.service.Approve(ctx.Request.Context(), &approvalDTO, userID)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "审批发货记录成功")
}

// Rollback 回退发货记录
// @Summary 回退发货记录
// @Description 回退发货记录到指定状态
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param id path int true "发货记录ID"
// @Param rollback body dto.ShipmentRollbackDTO true "回退信息"
// @Success 200 {object} response.Response "回退成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/{id}/rollback [post]
func (c *ShipmentController) Rollback(ctx *gin.Context) {
	// 获取发货记录ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的发货记录ID")
		return
	}

	var rollbackDTO dto.ShipmentRollbackDTO
	if err := ctx.ShouldBindJSON(&rollbackDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 设置发货记录ID（安全转换）
	rollbackDTO.ShipmentID = uint(id)

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	// 验证当前阶段
	shipment, err := c.service.GetByID(ctx.Request.Context(), rollbackDTO.ShipmentID)
	if err != nil {
		if err.Error() == "发货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "发货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取发货记录失败")
		}
		return
	}

	if shipment.Status != rollbackDTO.CurrentStage {
		response.Fail(ctx, http.StatusBadRequest, "当前阶段不匹配，请刷新页面后重试")
		return
	}

	err = c.service.Rollback(ctx.Request.Context(), &rollbackDTO, userID)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "回退发货记录成功")
}

// GetByContractID 根据合同ID获取发货记录列表
// @Summary 根据合同ID获取发货记录列表
// @Description 根据合同ID获取发货记录列表
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param contract_id path int true "合同ID"
// @Success 200 {object} response.ResponseStruct{data=[]dto.ShipmentListDTO} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/by-contract/{contract_id} [get]
func (c *ShipmentController) GetByContractID(ctx *gin.Context) {
	contractIDStr := ctx.Param("contract_id")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的合同ID")
		return
	}

	shipments, err := c.service.GetByContractID(ctx.Request.Context(), uint(contractID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取发货记录列表失败")
		return
	}

	response.Success(ctx, shipments, "获取发货记录列表成功")
}

// GetShipmentHistory 获取发货记录历史记录
// @Summary 获取发货记录历史记录
// @Description 获取发货记录的审批历史记录
// @Tags 发货管理
// @Accept json
// @Produce json
// @Param id path int true "发货记录ID"
// @Success 200 {object} response.ResponseStruct{data=[]model.PurchaseApprovalHistory} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/{id}/history [get]
func (c *ShipmentController) GetShipmentHistory(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的发货记录ID")
		return
	}

	history, err := c.service.GetShipmentHistory(ctx.Request.Context(), uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取发货记录历史失败")
		return
	}

	response.Success(ctx, history, "获取发货记录历史成功")
}

// GetStatistics 获取发货统计信息
// @Summary 获取发货统计信息
// @Description 获取发货管理的统计信息
// @Tags 发货管理
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseStruct{data=dto.ShipmentStatisticsDTO} "获取成功"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/shipments/statistics [get]
func (c *ShipmentController) GetStatistics(ctx *gin.Context) {
	statistics, err := c.service.GetStatistics(ctx.Request.Context())
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取发货统计信息失败")
		return
	}

	response.Success(ctx, statistics, "获取发货统计信息成功")
}
