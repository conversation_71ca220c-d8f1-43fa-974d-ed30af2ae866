package controller

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/service"
	"backend/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// InvoiceController 发票控制器
type InvoiceController struct {
	service service.InvoiceService
}

// NewInvoiceController 创建发票控制器实例
func NewInvoiceController(service service.InvoiceService) *InvoiceController {
	return &InvoiceController{service: service}
}

// RegisterRoutes 注册路由
func (c *InvoiceController) RegisterRoutes(router *gin.RouterGroup) {
	// /purchase/invoices
	invoicesGroup := router.Group("/invoices")
	{
		// 创建发票
		invoicesGroup.POST("", c.CreateInvoice)
		// 获取发票列表
		invoicesGroup.GET("", c.ListInvoices)
		// 根据ID获取发票
		invoicesGroup.GET("/:id", c.GetInvoiceByID)
		// 根据发票号获取发票
		invoicesGroup.GET("/no/:invoice_no", c.GetInvoiceByInvoiceNo)
		// 更新发票
		invoicesGroup.PUT("/:id", c.UpdateInvoice)
		// 删除发票
		invoicesGroup.DELETE("/:id", c.DeleteInvoice)
		// 根据合同ID获取发票列表
		invoicesGroup.GET("/contract/:contract_id", c.GetInvoicesByContractID)
		// 获取合同的发票汇总信息
		invoicesGroup.GET("/contract/:contract_id/summary", c.GetInvoiceSummaryByContractID)
	}
}

// CreateInvoice 创建发票
// @Summary 创建发票
// @Description 创建新的发票记录，需要关联合同ID、发票号和开票金额
// @Tags 发票管理
// @Accept json
// @Produce json
// @Param invoice body dto.CreateInvoiceDTO true "创建发票请求"
// @Success 200 {object} response.ResponseStruct{data=model.Invoice} "创建成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 401 {object} response.ResponseStruct "未授权"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /purchase/invoices [post]
func (c *InvoiceController) CreateInvoice(ctx *gin.Context) {
	var createDTO dto.CreateInvoiceDTO
	if err := ctx.ShouldBindJSON(&createDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误")
		return
	}

	// 从JWT获取用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	createDTO.CreatedBy = userID

	invoice, err := c.service.Create(ctx, &createDTO)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "创建发票失败: "+err.Error())
		return
	}

	response.Success(ctx, invoice, "创建发票成功")
}

// ListInvoices 获取发票列表
// @Summary 获取发票列表
// @Description 分页获取发票列表，支持多种筛选条件
// @Tags 发票管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param invoice_no query string false "发票号（模糊查询）"
// @Param contract_id query int false "合同ID"
// @Param contract_no query string false "合同编号（模糊查询）"
// @Param created_by query int false "创建人ID"
// @Param start_date query string false "开始日期(YYYY-MM-DD)"
// @Param end_date query string false "结束日期(YYYY-MM-DD)"
// @Param min_amount query number false "最小金额"
// @Param max_amount query number false "最大金额"
// @Success 200 {object} response.ResponseStruct{data=response.PageResult{list=[]dto.InvoiceListDTO}} "获取成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /purchase/invoices [get]
func (c *InvoiceController) ListInvoices(ctx *gin.Context) {
	var query dto.InvoiceQueryDTO
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误")
		return
	}

	// 设置默认值
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 10
	}

	invoices, total, err := c.service.List(ctx, &query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取发票列表失败: "+err.Error())
		return
	}

	// 返回标准的分页结构
	result := map[string]interface{}{
		"list":  invoices,
		"total": total,
	}

	response.Success(ctx, result, "获取发票列表成功")
}

// GetInvoiceByID 根据ID获取发票
// @Summary 根据ID获取发票详情
// @Description 通过发票ID获取发票详细信息，包括关联的合同信息和金额统计
// @Tags 发票管理
// @Accept json
// @Produce json
// @Param id path int true "发票ID"
// @Success 200 {object} response.ResponseStruct{data=dto.InvoiceDetailDTO} "获取成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 404 {object} response.ResponseStruct "发票不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /purchase/invoices/{id} [get]
func (c *InvoiceController) GetInvoiceByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的发票ID")
		return
	}

	invoice, err := c.service.GetByID(ctx, uint(id))
	if err != nil {
		if err == service.ErrInvoiceNotFound {
			response.Fail(ctx, http.StatusNotFound, "发票不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取发票失败: "+err.Error())
		}
		return
	}

	response.Success(ctx, invoice, "获取发票成功")
}

// GetInvoiceByInvoiceNo 根据发票号获取发票
// @Summary 根据发票号获取发票详情
// @Description 通过发票号获取发票详细信息，包括关联的合同信息和金额统计
// @Tags 发票管理
// @Accept json
// @Produce json
// @Param invoice_no path string true "发票号"
// @Success 200 {object} response.ResponseStruct{data=dto.InvoiceDetailDTO} "获取成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 404 {object} response.ResponseStruct "发票不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /purchase/invoices/no/{invoice_no} [get]
func (c *InvoiceController) GetInvoiceByInvoiceNo(ctx *gin.Context) {
	invoiceNo := ctx.Param("invoice_no")
	if invoiceNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "发票号不能为空")
		return
	}

	invoice, err := c.service.GetByInvoiceNo(ctx, invoiceNo)
	if err != nil {
		if err == service.ErrInvoiceNotFound {
			response.Fail(ctx, http.StatusNotFound, "发票不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取发票失败: "+err.Error())
		}
		return
	}

	response.Success(ctx, invoice, "获取发票成功")
}

// UpdateInvoice 更新发票
// @Summary 更新发票信息
// @Description 更新发票的基本信息，包括发票号、金额、日期、状态等
// @Tags 发票管理
// @Accept json
// @Produce json
// @Param id path int true "发票ID"
// @Param invoice body dto.UpdateInvoiceDTO true "更新发票请求"
// @Success 200 {object} response.ResponseStruct "更新成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 401 {object} response.ResponseStruct "未授权"
// @Failure 404 {object} response.ResponseStruct "发票不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /purchase/invoices/{id} [put]
func (c *InvoiceController) UpdateInvoice(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的发票ID")
		return
	}

	var updateDTO dto.UpdateInvoiceDTO
	if err := ctx.ShouldBindJSON(&updateDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误")
		return
	}

	// 从JWT获取用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	updateDTO.UpdatedBy = userID

	err = c.service.Update(ctx, uint(id), &updateDTO)
	if err != nil {
		if err == service.ErrInvoiceNotFound {
			response.Fail(ctx, http.StatusNotFound, "发票不存在")
		} else {
			response.Fail(ctx, http.StatusBadRequest, "更新发票失败: "+err.Error())
		}
		return
	}

	response.Success(ctx, nil, "更新发票成功")
}

// DeleteInvoice 删除发票
// @Summary 删除发票
// @Description 删除指定的发票记录
// @Tags 发票管理
// @Accept json
// @Produce json
// @Param id path int true "发票ID"
// @Success 200 {object} response.ResponseStruct "删除成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 401 {object} response.ResponseStruct "未授权"
// @Failure 404 {object} response.ResponseStruct "发票不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /purchase/invoices/{id} [delete]
func (c *InvoiceController) DeleteInvoice(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的发票ID")
		return
	}

	// 从JWT获取用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.Delete(ctx, uint(id), userID)
	if err != nil {
		switch err {
		case service.ErrInvoiceNotFound:
			response.Fail(ctx, http.StatusNotFound, "发票不存在")
		case service.ErrInvoiceDeleteNotAllowed:
			response.Fail(ctx, http.StatusBadRequest, "不允许删除发票")
		default:
			response.Fail(ctx, http.StatusInternalServerError, "删除发票失败: "+err.Error())
		}
		return
	}

	response.Success(ctx, nil, "删除发票成功")
}

// GetInvoicesByContractID 根据合同ID获取发票列表
// @Summary 根据合同ID获取发票列表
// @Description 获取指定合同的所有发票记录
// @Tags 发票管理
// @Accept json
// @Produce json
// @Param contract_id path int true "合同ID"
// @Success 200 {object} response.ResponseStruct{data=[]model.Invoice} "获取成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /purchase/invoices/contract/{contract_id} [get]
func (c *InvoiceController) GetInvoicesByContractID(ctx *gin.Context) {
	contractIDStr := ctx.Param("contract_id")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的合同ID")
		return
	}

	invoices, err := c.service.GetByContractID(ctx, uint(contractID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取发票列表失败: "+err.Error())
		return
	}

	response.Success(ctx, invoices, "获取发票列表成功")
}

// GetInvoiceSummaryByContractID 获取合同的发票汇总信息
// @Summary 获取合同的发票汇总信息
// @Description 获取指定合同的发票汇总统计信息，包括合同总金额、已开票金额、剩余金额等
// @Tags 发票管理
// @Accept json
// @Produce json
// @Param contract_id path int true "合同ID"
// @Success 200 {object} response.ResponseStruct{data=dto.InvoiceSummaryDTO} "获取成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /purchase/invoices/contract/{contract_id}/summary [get]
func (c *InvoiceController) GetInvoiceSummaryByContractID(ctx *gin.Context) {
	contractIDStr := ctx.Param("contract_id")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的合同ID")
		return
	}

	summary, err := c.service.GetInvoiceSummaryByContractID(ctx, uint(contractID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取发票汇总信息失败: "+err.Error())
		return
	}

	response.Success(ctx, summary, "获取发票汇总信息成功")
}
