package controller

import (
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/response"
)

// ProjectController 项目信息控制器
type ProjectController struct {
	service service.ProjectService
}

// NewProjectController 创建项目信息控制器实例
func NewProjectController(service service.ProjectService) *ProjectController {
	return &ProjectController{service: service}
}

// RegisterRoutes 注册路由
func (c *ProjectController) RegisterRoutes(router *gin.RouterGroup) {
	// api/v1/projects/purchase
	projectsGroup := router.Group("/projects")
	{
		// 创建项目信息
		projectsGroup.POST("", c.CreateProject)
		// 获取项目信息列表
		projectsGroup.GET("", c.ListProjects)
		// 根据ID获取项目信息
		projectsGroup.GET("/:id", c.GetProjectByID)
		// 根据项目编号获取项目信息
		projectsGroup.GET("/code/:code", c.GetProjectByCode)
		// 更新项目信息
		projectsGroup.PUT("/:id", c.UpdateProject)
		// 删除项目信息
		projectsGroup.DELETE("/:id", c.DeleteProject)
	}
}

// CreateProject 创建项目信息
// @Summary 创建项目信息
// @Description 创建新的项目信息，项目编号可选，不提供则自动生成
// @Tags 项目管理
// @Accept json
// @Produce json
// @Param project body dto.CreateProjectDTO true "项目信息"
// @Success 201 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/projects [post]
func (c *ProjectController) CreateProject(ctx *gin.Context) {
	var createDto dto.CreateProjectDTO
	if err := ctx.ShouldBindJSON(&createDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据")
		return
	}

	project, err := c.service.CreateProject(ctx, createDto)
	if err != nil {
		if err == service.ErrInvalidProjectData {
			response.Fail(ctx, http.StatusBadRequest, "无效的项目数据")
			return
		}
		if err == service.ErrDuplicateProjectCode {
			response.Fail(ctx, http.StatusBadRequest, "项目编号已存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "创建项目失败: "+err.Error())
		return
	}

	response.Success(ctx, project, "创建项目成功")
}

// GetProjectByID 根据ID获取项目信息
// @Summary 获取项目信息详情
// @Description 通过项目ID查询项目详情
// @Tags 项目管理
// @Accept json
// @Produce json
// @Param id path int true "项目ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "项目不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/projects/{id} [get]
func (c *ProjectController) GetProjectByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	project, err := c.service.GetProjectByID(ctx, uint(id))
	if err != nil {
		if err == service.ErrProjectNotFound {
			response.Fail(ctx, http.StatusNotFound, "项目不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取项目失败")
		return
	}

	response.Success(ctx, project, "获取项目成功")
}

// GetProjectByCode 根据项目编号获取项目信息
// @Summary 通过项目编号查询项目信息
// @Description 通过项目编号查询项目详情
// @Tags 项目管理
// @Accept json
// @Produce json
// @Param code path string true "项目编号"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "项目不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/projects/code/{code} [get]
func (c *ProjectController) GetProjectByCode(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		response.Fail(ctx, http.StatusBadRequest, "项目编号不可为空")
		return
	}

	project, err := c.service.GetProjectByCode(ctx, code)
	if err != nil {
		if err == service.ErrProjectNotFound {
			response.Fail(ctx, http.StatusNotFound, "项目不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取项目失败")
		return
	}

	response.Success(ctx, project, "获取项目成功")
}

// ListProjects 获取项目信息列表
// @Summary 查询项目列表
// @Description 支持分页和筛选条件
// @Tags 项目管理
// @Accept json
// @Produce json
// @Param project_code query string false "项目编号（模糊查询）"
// @Param project_name query string false "项目名称（模糊查询）"
// @Param customer_name query string false "客户名称（模糊查询）"
// @Param project_manager query string false "项目经理（模糊查询）"
// @Param contact_person query string false "联系人（模糊查询）"
// @Param project_status query int false "项目状态"
// @Param start_date_begin query string false "开始日期起始（格式：YYYY-MM-DD）"
// @Param start_date_end query string false "开始日期结束（格式：YYYY-MM-DD）"
// @Param page query int true "页码（从1开始）"
// @Param page_size query int true "每页数量（最大100）"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/projects [get]
func (c *ProjectController) ListProjects(ctx *gin.Context) {
	var query dto.ProjectListQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的查询参数")
		return
	}

	result, err := c.service.ListProjects(ctx, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取项目列表失败")
		return
	}

	response.Success(ctx, result, "获取项目列表成功")
}

// UpdateProject 更新项目信息
// @Summary 更新项目信息
// @Description 更新指定ID的项目信息
// @Tags 项目管理
// @Accept json
// @Produce json
// @Param id path int true "项目ID"
// @Param project body dto.UpdateProjectDTO true "项目信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "项目不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/projects/{id} [put]
func (c *ProjectController) UpdateProject(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var updateDto dto.UpdateProjectDTO
	if err := ctx.ShouldBindJSON(&updateDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据"+err.Error())
		return
	}
	updateDto.ID = uint(id)

	project, err := c.service.UpdateProject(ctx, updateDto)
	if err != nil {
		if err == service.ErrProjectNotFound {
			response.Fail(ctx, http.StatusNotFound, "项目不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "更新项目失败: "+err.Error())
		return
	}

	response.Success(ctx, project, "更新项目成功")
}

// DeleteProject 删除项目信息
// @Summary 删除项目信息
// @Description 删除指定ID的项目信息
// @Tags 项目管理
// @Accept json
// @Produce json
// @Param id path int true "项目ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "项目不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/projects/{id} [delete]
func (c *ProjectController) DeleteProject(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	err = c.service.DeleteProject(ctx, uint(id))
	if err != nil {
		if err == service.ErrProjectNotFound {
			response.Fail(ctx, http.StatusNotFound, "项目不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "删除项目失败")
		return
	}

	response.Success(ctx, nil, "删除项目成功")
}
