package controller

import (
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/response"
)

// SupplierController 供应商信息控制器
type SupplierController struct {
	service service.SupplierService
}

// NewSupplierController 创建供应商信息控制器实例
func NewSupplierController(service service.SupplierService) *SupplierController {
	return &SupplierController{service: service}
}

// RegisterRoutes 注册路由
func (c *SupplierController) RegisterRoutes(router *gin.RouterGroup) {
	// api/v1/purchase/suppliers
	suppliersGroup := router.Group("/suppliers")
	{
		// 创建供应商信息
		suppliersGroup.POST("", c.CreateSupplier)
		// 获取供应商信息列表
		suppliersGroup.GET("", c.ListSuppliers)
		// 根据ID获取供应商信息
		suppliersGroup.GET("/:id", c.GetSupplierByID)
		// 根据供应商编码获取供应商信息
		suppliersGroup.GET("/code/:code", c.GetSupplierByCode)
		// 更新供应商信息
		suppliersGroup.PUT("/:id", c.UpdateSupplier)
		// 删除供应商信息
		suppliersGroup.DELETE("/:id", c.DeleteSupplier)
	}
}

// CreateSupplier 创建供应商信息
// @Summary 创建供应商信息
// @Description 创建新的供应商信息，供应商编码可选，不提供则自动生成
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param supplier body dto.CreateSupplierDTO true "供应商信息"
// @Success 201 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/suppliers [post]
func (c *SupplierController) CreateSupplier(ctx *gin.Context) {
	var createDto dto.CreateSupplierDTO
	if err := ctx.ShouldBindJSON(&createDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据"+err.Error())
		return
	}

	supplier, err := c.service.CreateSupplier(ctx, createDto)
	if err != nil {
		if err == service.ErrInvalidSupplierData {
			response.Fail(ctx, http.StatusBadRequest, "无效的供应商数据")
			return
		}
		if err == service.ErrDuplicateSupplierCode {
			response.Fail(ctx, http.StatusBadRequest, "供应商编码已存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "创建供应商失败: "+err.Error())
		return
	}

	response.Success(ctx, supplier, "创建供应商成功")
}

// GetSupplierByID 根据ID获取供应商信息
// @Summary 获取供应商信息详情
// @Description 通过供应商ID查询供应商详情
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param id path int true "供应商ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "供应商不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/suppliers/{id} [get]
func (c *SupplierController) GetSupplierByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	supplier, err := c.service.GetSupplierByID(ctx, uint(id))
	if err != nil {
		if err == service.ErrSupplierNotFound {
			response.Fail(ctx, http.StatusNotFound, "供应商不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取供应商失败")
		return
	}

	response.Success(ctx, supplier, "获取供应商成功")
}

// GetSupplierByCode 根据供应商编码获取供应商信息
// @Summary 通过供应商编码查询供应商信息
// @Description 通过供应商编码查询供应商详情
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param code path string true "供应商编码"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "供应商不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/suppliers/code/{code} [get]
func (c *SupplierController) GetSupplierByCode(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		response.Fail(ctx, http.StatusBadRequest, "供应商编码不可为空")
		return
	}

	supplier, err := c.service.GetSupplierByCode(ctx, code)
	if err != nil {
		if err == service.ErrSupplierNotFound {
			response.Fail(ctx, http.StatusNotFound, "供应商不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取供应商失败")
		return
	}

	response.Success(ctx, supplier, "获取供应商成功")
}

// ListSuppliers 获取供应商信息列表
// @Summary 查询供应商列表
// @Description 支持分页和筛选条件
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param supplier_code query string false "供应商编码（模糊查询）"
// @Param supplier_name query string false "供应商名称（模糊查询）"
// @Param short_name query string false "简称（模糊查询）"
// @Param contact_person query string false "联系人（模糊查询）"
// @Param contact_phone query string false "联系电话（模糊查询）"
// @Param status query int false "状态"
// @Param contract_date_begin query string false "合同生效日期起始（格式：YYYY-MM-DD）"
// @Param contract_date_end query string false "合同到期日期结束（格式：YYYY-MM-DD）"
// @Param page query int true "页码（从1开始）"
// @Param page_size query int true "每页数量（最大100）"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/suppliers [get]
func (c *SupplierController) ListSuppliers(ctx *gin.Context) {
	var query dto.SupplierListQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的查询参数")
		return
	}

	result, err := c.service.ListSuppliers(ctx, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取供应商列表失败")
		return
	}

	response.Success(ctx, result, "获取供应商列表成功")
}

// UpdateSupplier 更新供应商信息
// @Summary 更新供应商信息
// @Description 更新指定ID的供应商信息
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param id path int true "供应商ID"
// @Param supplier body dto.UpdateSupplierDTO true "供应商信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "供应商不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/suppliers/{id} [put]
func (c *SupplierController) UpdateSupplier(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var updateDto dto.UpdateSupplierDTO
	if err := ctx.ShouldBindJSON(&updateDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据"+err.Error())
		return
	}
	updateDto.ID = uint(id)

	supplier, err := c.service.UpdateSupplier(ctx, updateDto)
	if err != nil {
		if err == service.ErrSupplierNotFound {
			response.Fail(ctx, http.StatusNotFound, "供应商不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "更新供应商失败: "+err.Error())
		return
	}

	response.Success(ctx, supplier, "更新供应商成功")
}

// DeleteSupplier 删除供应商信息
// @Summary 删除供应商信息
// @Description 删除指定ID的供应商信息
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param id path int true "供应商ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "供应商不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/suppliers/{id} [delete]
func (c *SupplierController) DeleteSupplier(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	err = c.service.DeleteSupplier(ctx, uint(id))
	if err != nil {
		if err == service.ErrSupplierNotFound {
			response.Fail(ctx, http.StatusNotFound, "供应商不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "删除供应商失败")
		return
	}

	response.Success(ctx, nil, "删除供应商成功")
}
