package controller

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/service"
	"backend/response"
	"errors"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// PaymentRequestController 付款申请控制器
type PaymentRequestController struct {
	service service.PaymentRequestService
}

// NewPaymentRequestController 创建付款申请控制器实例
func NewPaymentRequestController(service service.PaymentRequestService) *PaymentRequestController {
	return &PaymentRequestController{
		service: service,
	}
}

// RegisterRoutes 注册付款申请相关路由
func (c *PaymentRequestController) RegisterRoutes(router *gin.RouterGroup) {
	paymentRequestGroup := router.Group("/payment-requests")
	{
		// 基本CRUD操作
		paymentRequestGroup.POST("", c.Create)                          // 创建付款申请
		paymentRequestGroup.GET("", c.List)                             // 获取付款申请列表
		paymentRequestGroup.GET("/:id", c.GetByID)                      // 根据ID获取付款申请
		paymentRequestGroup.GET("/by-no/:payment_no", c.GetByPaymentNo) // 根据付款申请单号获取付款申请
		paymentRequestGroup.PUT("/:id", c.Update)                       // 更新付款申请
		paymentRequestGroup.DELETE("/:id", c.Delete)                    // 删除付款申请

		// 工作流操作
		paymentRequestGroup.POST("/:id/submit", c.Submit) // 提交付款申请
		paymentRequestGroup.POST("/:id/cancel", c.Cancel) // 取消付款申请
		paymentRequestGroup.POST("/approve", c.Approve)   // 审批付款申请
		paymentRequestGroup.POST("/rollback", c.Rollback) // 回退付款申请

		// 查询操作
		paymentRequestGroup.GET("/by-contract/:contract_id", c.GetByContractID) // 根据合同ID获取付款申请列表
		paymentRequestGroup.GET("/:id/history", c.GetPaymentRequestHistory)     // 获取付款申请历史记录
	}
}

// Create 创建付款申请
// @Summary 创建付款申请
// @Description 创建新的付款申请
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param request body dto.CreatePaymentRequestDTO true "创建付款申请请求"
// @Success 200 {object} response.Response{data=model.PaymentRequest} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests [post]
func (c *PaymentRequestController) Create(ctx *gin.Context) {
	var createDTO dto.CreatePaymentRequestDTO
	if err := ctx.ShouldBindJSON(&createDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 从JWT获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	createDTO.CreatedBy = userID

	paymentRequest, err := c.service.Create(ctx.Request.Context(), &createDTO)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建付款申请失败")
		return
	}

	response.Success(ctx, paymentRequest, "创建付款申请成功")
}

// GetByID 根据ID获取付款申请
// @Summary 根据ID获取付款申请
// @Description 根据ID获取付款申请详情
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param id path int true "付款申请ID"
// @Success 200 {object} response.Response{data=dto.PaymentRequestDetailDTO} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "付款申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests/{id} [get]
func (c *PaymentRequestController) GetByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的付款申请ID")
		return
	}

	paymentRequest, err := c.service.GetByID(ctx.Request.Context(), uint(id))
	if err != nil {
		if err == service.ErrPaymentRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "付款申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取付款申请失败")
		return
	}

	response.Success(ctx, paymentRequest, "获取付款申请成功")
}

// GetByPaymentNo 根据付款申请单号获取付款申请
// @Summary 根据付款申请单号获取付款申请
// @Description 根据付款申请单号获取付款申请详情
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param payment_no path string true "付款申请单号"
// @Success 200 {object} response.Response{data=dto.PaymentRequestDetailDTO} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "付款申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests/by-no/{payment_no} [get]
func (c *PaymentRequestController) GetByPaymentNo(ctx *gin.Context) {
	paymentNo := ctx.Param("payment_no")
	if paymentNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "付款申请单号不能为空")
		return
	}

	paymentRequest, err := c.service.GetByPaymentNo(ctx.Request.Context(), paymentNo)
	if err != nil {
		if err == service.ErrPaymentRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "付款申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取付款申请失败")
		return
	}

	response.Success(ctx, paymentRequest, "获取付款申请成功")
}

// Update 更新付款申请
// @Summary 更新付款申请
// @Description 更新付款申请信息
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param id path int true "付款申请ID"
// @Param request body dto.UpdatePaymentRequestDTO true "更新付款申请请求"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "付款申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests/{id} [put]
func (c *PaymentRequestController) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的付款申请ID")
		return
	}

	var updateDTO dto.UpdatePaymentRequestDTO
	if err := ctx.ShouldBindJSON(&updateDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 从JWT获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	updateDTO.UpdatedBy = userID

	err = c.service.Update(ctx.Request.Context(), uint(id), &updateDTO)
	if err != nil {
		if err == service.ErrPaymentRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "付款申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "更新付款申请失败")
		return
	}

	response.Success(ctx, nil, "更新付款申请成功")
}

// Delete 删除付款申请
// @Summary 删除付款申请
// @Description 删除付款申请
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param id path int true "付款申请ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "付款申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests/{id} [delete]
func (c *PaymentRequestController) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的付款申请ID")
		return
	}

	// 从上下文获取用户ID
	userID := utils.GetUserIDWithDefault(ctx, 0)

	err = c.service.Delete(ctx.Request.Context(), uint(id), userID)
	if err != nil {
		if err == service.ErrPaymentRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "付款申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "删除付款申请失败")
		return
	}

	response.Success(ctx, nil, "删除付款申请成功")
}

// List 获取付款申请列表
// @Summary 获取付款申请列表
// @Description 获取付款申请列表，支持分页和筛选
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param payment_no query string false "付款申请单号"
// @Param contract_id query int false "合同ID"
// @Param contract_no query string false "合同编号"
// @Param supplier_id query int false "供应商ID"
// @Param payment_type query string false "付款类型"
// @Param status query string false "状态"
// @Param created_by query int false "创建人ID"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Param min_amount query number false "最小金额"
// @Param max_amount query number false "最大金额"
// @Success 200 {object} response.ResponseStruct{data=object{list=[]dto.PaymentRequestListDTO,total=int}} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests [get]
func (c *PaymentRequestController) List(ctx *gin.Context) {
	var query dto.PaymentRequestQueryDTO
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	paymentRequests, total, err := c.service.List(ctx.Request.Context(), &query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取付款申请列表失败")
		return
	}

	// 返回标准的分页结构
	result := map[string]interface{}{
		"list":  paymentRequests,
		"total": total,
	}

	response.Success(ctx, result, "获取付款申请列表成功")
}

// Submit 提交付款申请
// @Summary 提交付款申请
// @Description 提交付款申请进入审批流程
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param id path int true "付款申请ID"
// @Success 200 {object} response.Response "提交成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "付款申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests/{id}/submit [post]
func (c *PaymentRequestController) Submit(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的付款申请ID")
		return
	}

	// 从JWT获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.Submit(ctx.Request.Context(), uint(id), userID)
	if err != nil {
		if err == service.ErrPaymentRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "付款申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "提交付款申请失败")
		return
	}

	response.Success(ctx, nil, "提交付款申请成功")
}

// Cancel 取消付款申请
// @Summary 取消付款申请
// @Description 取消付款申请
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param id path int true "付款申请ID"
// @Success 200 {object} response.Response "取消成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "付款申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests/{id}/cancel [post]
func (c *PaymentRequestController) Cancel(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的付款申请ID")
		return
	}

	// 从上下文获取用户ID
	userID := utils.GetUserIDWithDefault(ctx, 0)

	err = c.service.Cancel(ctx.Request.Context(), uint(id), userID)
	if err != nil {
		if err == service.ErrPaymentRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "付款申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "取消付款申请失败")
		return
	}

	response.Success(ctx, nil, "取消付款申请成功")
}

// Approve 审批付款申请
// @Summary 审批付款申请
// @Description 审批付款申请（批准或拒绝）
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param request body dto.PaymentRequestApprovalDTO true "审批请求"
// @Success 200 {object} response.Response "审批成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "付款申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests/approve [post]
func (c *PaymentRequestController) Approve(ctx *gin.Context) {
	var approvalDTO dto.PaymentRequestApprovalDTO
	if err := ctx.ShouldBindJSON(&approvalDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 从JWT获取用户ID和用户名
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	userName, _ := utils.GetUserName(ctx)

	err = c.service.Approve(ctx.Request.Context(), &approvalDTO, userID, userName)
	if err != nil {
		if err == service.ErrPaymentRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "付款申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "审批付款申请失败"+err.Error())
		return
	}

	response.Success(ctx, nil, "审批付款申请成功")
}

// Rollback 回退付款申请
// @Summary 回退付款申请
// @Description 回退付款申请到指定阶段
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param request body dto.PaymentRequestRollbackDTO true "回退请求"
// @Success 200 {object} response.Response "回退成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "付款申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests/rollback [post]
func (c *PaymentRequestController) Rollback(ctx *gin.Context) {
	var rollbackDTO dto.PaymentRequestRollbackDTO
	if err := ctx.ShouldBindJSON(&rollbackDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 从JWT获取用户ID和用户名
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	userName, _ := utils.GetUserName(ctx)

	err = c.service.Rollback(ctx.Request.Context(), &rollbackDTO, userID, userName)
	if err != nil {
		if err == service.ErrPaymentRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "付款申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "回退付款申请失败")
		return
	}

	response.Success(ctx, nil, "回退付款申请成功")
}

// GetByContractID 根据合同ID获取付款申请列表
// @Summary 根据合同ID获取付款申请列表
// @Description 根据合同ID获取相关的付款申请列表
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param contract_id path int true "合同ID"
// @Success 200 {object} response.Response{data=[]model.PaymentRequest} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/payment-requests/by-contract/{contract_id} [get]
func (c *PaymentRequestController) GetByContractID(ctx *gin.Context) {
	contractIDStr := ctx.Param("contract_id")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的合同ID")
		return
	}

	paymentRequests, err := c.service.GetByContractID(ctx.Request.Context(), uint(contractID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取付款申请列表失败")
		return
	}

	response.Success(ctx, paymentRequests, "获取付款申请列表成功")
}

// GetPaymentRequestHistory 获取付款申请历史记录
// @Summary 获取付款申请历史记录
// @Description 根据付款申请ID获取历史操作记录
// @Tags 付款申请
// @Accept json
// @Produce json
// @Param id path int true "付款申请ID"
// @Success 200 {object} response.ResponseStruct{data=[]dto.PaymentRequestHistoryDTO} "获取成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 404 {object} response.ResponseStruct "付款申请不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /api/v1/purchase/payment-requests/{id}/history [get]
func (c *PaymentRequestController) GetPaymentRequestHistory(ctx *gin.Context) {
	// 获取付款申请ID
	paymentIDStr := ctx.Param("id")
	paymentID, err := strconv.ParseUint(paymentIDStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "付款申请ID格式错误")
		return
	}

	// 调用服务获取历史记录
	histories, err := c.service.GetPaymentRequestHistory(ctx.Request.Context(), uint(paymentID))
	if err != nil {
		if errors.Is(err, service.ErrPaymentRequestNotFound) {
			response.Fail(ctx, http.StatusNotFound, "付款申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取付款申请历史记录失败")
		return
	}

	response.Success(ctx, histories, "获取付款申请历史记录成功")
}
