package controller

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/service"
	"backend/response"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// PurchaseInquiryController 采购询价控制器
type PurchaseInquiryController struct {
	service service.PurchaseInquiryService
}

// NewPurchaseInquiryController 创建采购询价控制器实例
func NewPurchaseInquiryController(service service.PurchaseInquiryService) *PurchaseInquiryController {
	return &PurchaseInquiryController{service: service}
}

// RegisterRoutes 注册路由
func (c *PurchaseInquiryController) RegisterRoutes(router *gin.RouterGroup) {
	// /purchase/inquiries
	inquiriesGroup := router.Group("/inquiries")
	{
		// 创建采购询价
		inquiriesGroup.POST("", c.CreatePurchaseInquiry)
		// 向询价单添加明细
		inquiriesGroup.POST("/:id/items", c.AddInquiryItems)
		// 获取采购询价列表
		inquiriesGroup.GET("", c.ListPurchaseInquiries)
		// 根据ID获取采购询价
		inquiriesGroup.GET("/:id", c.GetPurchaseInquiryByID)
		// 根据询价单号获取采购询价
		inquiriesGroup.GET("/no/:inquiry_no", c.GetPurchaseInquiryByInquiryNo)
		// 更新采购询价
		inquiriesGroup.PUT("/:id", c.UpdatePurchaseInquiry)
		// 审批采购询价
		inquiriesGroup.POST("/approve", c.ApprovePurchaseInquiry)
		// 回退采购询价
		inquiriesGroup.POST("/rollback", c.RollbackPurchaseInquiry)
		// 获取采购询价历史记录
		inquiriesGroup.GET("/:id/history", c.GetPurchaseInquiryHistory)
		// 取消采购询价
		inquiriesGroup.POST("/:id/cancel", c.CancelPurchaseInquiry)
		// 删除采购询价
		inquiriesGroup.DELETE("/:id", c.DeletePurchaseInquiry)
		// 获取询价统计信息
		inquiriesGroup.GET("/statistics/:request_id", c.GetInquiryStatistics)
		// 验证询价数量
		inquiriesGroup.POST("/validate-quantity", c.ValidateInquiryQuantity)
		// 获取采购申请明细询价状态
		inquiriesGroup.GET("/request/:request_id/items-status", c.GetRequestItemsInquiryStatus)
	}
}

// CreatePurchaseInquiry 创建采购询价
// @Summary 创建采购询价
// @Description 创建新的采购询价，询价单号自动生成
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param request body dto.CreatePurchaseInquiryDTO true "采购询价信息"
// @Success 201 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries [post]
func (c *PurchaseInquiryController) CreatePurchaseInquiry(ctx *gin.Context) {
	var createDto dto.CreatePurchaseInquiryDTO
	if err := ctx.ShouldBindJSON(&createDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	// 从JWT获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	// 设置创建人ID
	createDto.CreatedBy = userID

	inquiry, err := c.service.CreatePurchaseInquiry(ctx, createDto)
	if err != nil {
		if err == service.ErrInvalidPurchaseInquiryData {
			response.Fail(ctx, http.StatusBadRequest, "无效的采购询价数据")
			return
		}
		if err == service.ErrInquirySupplierNotFound {
			response.Fail(ctx, http.StatusBadRequest, "供应商不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "创建采购询价失败: "+err.Error())
		return
	}

	response.Success(ctx, inquiry, "创建采购询价成功")
}

// AddInquiryItems 向询价单添加明细
// @Summary 向询价单添加明细
// @Description 向现有询价单添加新的询价明细
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param id path int true "询价单ID"
// @Param addInquiryItems body dto.AddInquiryItemsDTO true "添加明细信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "询价单不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/{id}/items [post]
func (c *PurchaseInquiryController) AddInquiryItems(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var addDto dto.AddInquiryItemsDTO
	if err := ctx.ShouldBindJSON(&addDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 设置询价单ID
	addDto.InquiryID = uint(id)

	// 调用服务层添加明细
	inquiry, err := c.service.AddInquiryItems(ctx, addDto)
	if err != nil {
		if err == service.ErrPurchaseInquiryNotFound {
			response.Fail(ctx, http.StatusNotFound, "询价单不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "添加询价明细失败: "+err.Error())
		return
	}

	response.Success(ctx, inquiry, "添加询价明细成功")
}

// GetPurchaseInquiryByID 根据ID获取采购询价
// @Summary 获取采购询价详情
// @Description 通过采购询价ID查询采购询价详情
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param id path int true "采购询价ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购询价不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/{id} [get]
func (c *PurchaseInquiryController) GetPurchaseInquiryByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	inquiry, err := c.service.GetPurchaseInquiryByID(ctx, uint(id))
	if err != nil {
		if err == service.ErrPurchaseInquiryNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购询价不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取采购询价失败: "+err.Error())
		return
	}

	response.Success(ctx, inquiry, "获取采购询价成功")
}

// GetPurchaseInquiryByInquiryNo 根据询价单号获取采购询价
// @Summary 通过询价单号查询采购询价
// @Description 通过询价单号查询采购询价详情
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param inquiry_no path string true "询价单号"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购询价不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/no/{inquiry_no} [get]
func (c *PurchaseInquiryController) GetPurchaseInquiryByInquiryNo(ctx *gin.Context) {
	inquiryNo := ctx.Param("inquiry_no")
	if inquiryNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "询价单号不可为空")
		return
	}

	inquiry, err := c.service.GetPurchaseInquiryByInquiryNo(ctx, inquiryNo)
	if err != nil {
		if err == service.ErrPurchaseInquiryNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购询价不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取采购询价失败: "+err.Error())
		return
	}

	response.Success(ctx, inquiry, "获取采购询价成功")
}

// ListPurchaseInquiries 获取采购询价列表
// @Summary 查询采购询价列表
// @Description 支持分页和筛选条件
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param inquiry_no query string false "询价单号（模糊查询）"
// @Param request_id query int false "采购申请ID"
// @Param request_no query string false "采购申请单号（模糊查询）"
// @Param project_id query int false "项目ID"
// @Param supplier_id query int false "供应商ID"
// @Param status query string false "状态"
// @Param created_by query int false "创建人ID"
// @Param start_date query string false "创建日期起始（格式：YYYY-MM-DD）"
// @Param end_date query string false "创建日期结束（格式：YYYY-MM-DD）"
// @Param page query int true "页码（从1开始）"
// @Param pageSize query int true "每页数量（最大100）"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries [get]
func (c *PurchaseInquiryController) ListPurchaseInquiries(ctx *gin.Context) {
	var query dto.PurchaseInquiryListQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的查询参数: "+err.Error())
		return
	}

	result, err := c.service.ListPurchaseInquiries(ctx, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取采购询价列表失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "获取采购询价列表成功")
}

// UpdatePurchaseInquiry 更新采购询价
// @Summary 更新采购询价
// @Description 更新指定ID的采购询价信息，仅草稿状态可更新
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param id path int true "采购询价ID"
// @Param request body dto.UpdatePurchaseInquiryDTO true "采购询价信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购询价不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/{id} [put]
func (c *PurchaseInquiryController) UpdatePurchaseInquiry(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var updateDto dto.UpdatePurchaseInquiryDTO
	if err := ctx.ShouldBindJSON(&updateDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	updateDto.ID = uint(id)

	inquiry, err := c.service.UpdatePurchaseInquiry(ctx, updateDto)
	if err != nil {
		if err == service.ErrPurchaseInquiryNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购询价不存在")
			return
		}
		// 移除草稿状态检查，因为现在允许在任何状态更新
		if err == service.ErrInquirySupplierNotFound {
			response.Fail(ctx, http.StatusBadRequest, "供应商不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "更新采购询价失败: "+err.Error())
		return
	}

	response.Success(ctx, inquiry, "更新采购询价成功")
}

// ApprovePurchaseInquiry 审批采购询价
// @Summary 审批采购询价
// @Description 审批或拒绝采购询价
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param request body dto.InquiryApprovalDTO true "审批信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/approve [post]
func (c *PurchaseInquiryController) ApprovePurchaseInquiry(ctx *gin.Context) {
	var approvalDto dto.InquiryApprovalDTO
	if err := ctx.ShouldBindJSON(&approvalDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	// 检查审批动作是否有效
	if approvalDto.Action != constants.ActionApprove && approvalDto.Action != constants.ActionReject {
		response.Fail(ctx, http.StatusBadRequest, "无效的审批动作，必须是approve或reject")
		return
	}

	// 检查当前阶段是否有效
	if approvalDto.CurrentStage == "" {
		response.Fail(ctx, http.StatusBadRequest, "当前审批阶段不能为空")
		return
	}

	// 提示用户current_stage应该与数据库status值匹配
	validStages := []string{constants.InquiryStatusDraft, constants.InquiryStatusFinanceReview, constants.InquiryStatusEnterpriseReview}
	isValidStage := false
	for _, stage := range validStages {
		if approvalDto.CurrentStage == stage {
			isValidStage = true
			break
		}
	}
	if !isValidStage {
		response.Fail(ctx, http.StatusBadRequest, "无效的审批阶段，当前阶段应与数据库状态匹配，例如: finance_review, enterprise_review")
		return
	}

	// 从JWT获取当前用户ID并设置为审批人ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	approvalDto.ApproverID = userID

	err = c.service.ApprovePurchaseInquiry(ctx, approvalDto)
	if err != nil {
		if strings.Contains(err.Error(), "当前阶段与询价审批状态不匹配") {
			response.Fail(ctx, http.StatusBadRequest, err.Error())
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "审批采购询价失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "审批采购询价成功")
}

// RollbackPurchaseInquiry 回退采购询价
// @Summary 回退采购询价
// @Description 将采购询价回退到之前的审批阶段
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param request body dto.InquiryRollbackDTO true "回退信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/rollback [post]
func (c *PurchaseInquiryController) RollbackPurchaseInquiry(ctx *gin.Context) {
	var rollbackDto dto.InquiryRollbackDTO
	if err := ctx.ShouldBindJSON(&rollbackDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	// 从JWT获取当前用户ID并设置为审批人ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	rollbackDto.ApproverID = userID

	err = c.service.RollbackPurchaseInquiry(ctx, rollbackDto)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "回退采购询价失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "回退采购询价成功")
}

// GetPurchaseInquiryHistory 获取采购询价历史记录
// @Summary 获取采购询价历史记录
// @Description 查询指定采购询价的操作历史记录
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param id path int true "采购询价ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购询价不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/{id}/history [get]
func (c *PurchaseInquiryController) GetPurchaseInquiryHistory(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	history, err := c.service.GetPurchaseInquiryHistory(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取采购询价历史记录失败: "+err.Error())
		return
	}

	response.Success(ctx, history, "获取采购询价历史记录成功")
}

// CancelPurchaseInquiry 取消采购询价
// @Summary 取消采购询价
// @Description 取消指定的采购询价
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param id path int true "采购询价ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购询价不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/{id}/cancel [post]
func (c *PurchaseInquiryController) CancelPurchaseInquiry(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 从JWT获取当前用户ID
	updatedBy, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.CancelPurchaseInquiry(ctx, uint(id), updatedBy)
	if err != nil {
		if err == service.ErrPurchaseInquiryNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购询价不存在")
			return
		}
		if err == service.ErrInvalidPurchaseInquiryStatus {
			response.Fail(ctx, http.StatusBadRequest, "当前状态无法取消")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "取消采购询价失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "取消采购询价成功")
}

// DeletePurchaseInquiry 删除采购询价
// @Summary 删除采购询价
// @Description 删除指定的采购询价，仅草稿状态可删除
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param id path int true "采购询价ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购询价不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/{id} [delete]
func (c *PurchaseInquiryController) DeletePurchaseInquiry(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 从JWT获取当前用户ID
	deletedBy, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.DeletePurchaseInquiry(ctx, uint(id), deletedBy)
	if err != nil {
		if err == service.ErrPurchaseInquiryNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购询价不存在")
			return
		}
		if err == service.ErrInquiryDeleteNotAllowed {
			response.Fail(ctx, http.StatusBadRequest, "只有草稿状态的询价才能删除")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "删除采购询价失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除采购询价成功")
}

// GetInquiryStatistics 获取询价统计信息
// @Summary 获取询价统计信息
// @Description 获取指定采购申请的询价进度和统计信息
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param request_id path int true "采购申请ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/request/{request_id}/statistics [get]
func (c *PurchaseInquiryController) GetInquiryStatistics(ctx *gin.Context) {
	requestIDStr := ctx.Param("request_id")
	requestID, err := strconv.ParseUint(requestIDStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的采购申请ID")
		return
	}

	statistics, err := c.service.GetInquiryStatistics(ctx, uint(requestID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取询价统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, statistics, "获取询价统计信息成功")
}

// ValidateInquiryQuantity 验证询价数量
// @Summary 验证询价数量
// @Description 验证询价数量是否超过可询价数量
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param request body dto.InquiryQuantityValidationDTO true "数量验证信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/validate-quantity [post]
func (c *PurchaseInquiryController) ValidateInquiryQuantity(ctx *gin.Context) {
	var validationDto dto.InquiryQuantityValidationDTO
	if err := ctx.ShouldBindJSON(&validationDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	err := c.service.ValidateInquiryQuantity(ctx, validationDto)
	if err != nil {
		if err == service.ErrInvalidQuantity {
			response.Fail(ctx, http.StatusBadRequest, "询价数量无效")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "验证询价数量失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "数量验证通过")
}

// GetRequestItemsInquiryStatus 获取采购申请明细询价状态
// @Summary 获取采购申请明细询价状态
// @Description 获取指定采购申请中所有明细项的询价状态，包括总数量、已询价数量、未询价数量等
// @Tags 采购询价管理
// @Accept json
// @Produce json
// @Param request_id path int true "采购申请ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/inquiries/request/{request_id}/items-status [get]
func (c *PurchaseInquiryController) GetRequestItemsInquiryStatus(ctx *gin.Context) {
	requestIDStr := ctx.Param("request_id")
	requestID, err := strconv.ParseUint(requestIDStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的采购申请ID")
		return
	}

	result, err := c.service.GetRequestItemsInquiryStatus(ctx, uint(requestID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取采购申请明细询价状态失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "获取采购申请明细询价状态成功")
}
