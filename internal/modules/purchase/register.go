package purchase

import (
	"backend/configs"
	"backend/internal/common/utils/notifier"
	productService "backend/internal/modules/cmdb/service/product"
	"backend/internal/modules/purchase/controller"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"backend/internal/modules/purchase/service"
	userService "backend/internal/modules/user/service"

	"go.temporal.io/sdk/client"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// autoMigrate 自动迁移数据库表结构
func autoMigrate(db *gorm.DB, logger *zap.Logger) error {
	logger.Info("开始采购模块数据库迁移")
	err := db.AutoMigrate(
		&model.Supplier{},
		&model.PurchaseRequest{},
		&model.PurchaseRequestItem{},
		&model.Project{},
		&model.Company{},
		&model.PurchaseApprovalHistory{},
		&model.PurchaseInquiry{},
		&model.PurchaseInquiryItem{},
		// 添加采购合同相关表
		&model.PurchaseContract{},
		&model.PurchaseContractItem{},
		&model.PaymentRequest{},
		&model.PaymentRequestItem{},
		// 添加到货管理相关表
		&model.Arrival{},
		&model.ArrivalItem{},
		// 添加发货管理相关表
		&model.Shipment{},
		&model.ShipmentItem{},
		// 添加发票管理相关表
		&model.Invoice{},
	)
	if err != nil {
		logger.Error("采购模块数据库迁移失败", zap.Error(err))
		return err
	}
	logger.Info("采购模块数据库迁移完成")
	return nil
}

// Module 采购模块
type Module struct {
	db             *gorm.DB
	logger         *zap.Logger
	temporalClient client.Client
	userService    userService.IUserService
	productService productService.ProductService

	// 通知器
	purchaseFeishuNotifier *notifier.PurchaseFeishuNotifier

	// 仓库
	supplierRepository         repository.SupplierRepository
	purchaseRequestRepository  repository.PurchaseRequestRepository
	projectRepository          repository.ProjectRepository
	companyRepository          repository.CompanyRepository
	purchaseInquiryRepository  repository.PurchaseInquiryRepository
	purchaseContractRepository repository.PurchaseContractRepository
	paymentRequestRepository   repository.PaymentRequestRepository
	arrivalRepository          repository.ArrivalRepository
	shipmentRepository         repository.ShipmentRepository
	invoiceRepository          repository.InvoiceRepository

	// 服务
	supplierService         service.SupplierService
	purchaseRequestService  service.PurchaseRequestService
	projectService          service.ProjectService
	companyService          service.CompanyService
	purchaseInquiryService  service.PurchaseInquiryService
	purchaseContractService service.PurchaseContractService
	paymentRequestService   service.PaymentRequestService
	arrivalService          service.ArrivalService
	shipmentService         service.ShipmentService
	invoiceService          service.InvoiceService

	// 控制器
	supplierController         *controller.SupplierController
	purchaseRequestController  *controller.PurchaseRequestController
	projectController          *controller.ProjectController
	companyController          *controller.CompanyController
	purchaseInquiryController  *controller.PurchaseInquiryController
	purchaseContractController *controller.PurchaseContractController
	paymentRequestController   *controller.PaymentRequestController
	arrivalController          *controller.ArrivalController
	shipmentController         *controller.ShipmentController
	invoiceController          *controller.InvoiceController
}

// NewModule 创建采购模块
func NewModule(db *gorm.DB, logger *zap.Logger, temporalClient client.Client, userService userService.IUserService, productService productService.ProductService) *Module {
	return &Module{
		db:             db,
		logger:         logger,
		temporalClient: temporalClient,
		userService:    userService,
		productService: productService,
	}
}

// Initialize 初始化模块
func (m *Module) Initialize() error {
	// 初始化仓库
	m.supplierRepository = repository.NewSupplierRepository(m.db)
	m.purchaseRequestRepository = repository.NewPurchaseRequestRepository(m.db)
	m.projectRepository = repository.NewProjectRepository(m.db)
	m.companyRepository = repository.NewCompanyRepository(m.db)
	m.purchaseInquiryRepository = repository.NewPurchaseInquiryRepository(m.db)
	m.purchaseContractRepository = repository.NewPurchaseContractRepository(m.db)
	m.paymentRequestRepository = repository.NewPaymentRequestRepository(m.db)
	m.arrivalRepository = repository.NewArrivalRepository(m.db)
	m.shipmentRepository = repository.NewShipmentRepository(m.db)
	m.invoiceRepository = repository.NewInvoiceRepository(m.db)

	// 初始化通知器
	cfg, err := configs.LoadConfig()
	if err != nil {
		return err
	}

	// 创建基础飞书通知器
	baseNotifier := notifier.NewFeishuNotifier(
		cfg.Feishu.WebhookURL,
		cfg.Feishu.Secret,
		cfg.Feishu.TicketDetailUrlTemplate,
		cfg.Feishu.RepairTicketDetailUrlTemplate,
		nil, // 采购模块不使用项目webhook映射
		m.logger,
	)

	// 创建采购专用通知器
	m.purchaseFeishuNotifier = notifier.NewPurchaseFeishuNotifier(
		baseNotifier,
		cfg.Feishu.PurchaseWebhookURL,
		cfg.Feishu.PurchaseSecret,
		cfg.Feishu.PurchaseUrlTemplates,
		cfg.Feishu.PurchaseEnabled,
	)

	// 设置数据库连接
	m.purchaseFeishuNotifier.SetDB(m.db)

	// 初始化服务
	m.supplierService = service.NewSupplierService(m.supplierRepository)
	m.projectService = service.NewProjectService(m.projectRepository)
	m.companyService = service.NewCompanyService(m.companyRepository)
	// 注意：purchaseRequestService需要依赖projectService，因此要先初始化projectService
	m.purchaseRequestService = service.NewPurchaseRequestService(m.purchaseRequestRepository, m.temporalClient, m.userService, m.projectService)
	// 初始化询比价服务
	m.purchaseInquiryService = service.NewPurchaseInquiryService(
		m.purchaseInquiryRepository,
		m.purchaseRequestRepository,
		m.temporalClient,
		m.userService,
		m.projectService,
		m.supplierService,
	)
	// 初始化合同服务
	m.purchaseContractService = service.NewPurchaseContractService(
		m.purchaseContractRepository,
		m.temporalClient,
		m.userService,
		m.supplierService,
		m.projectService,
		m.companyService,
		m.productService,
		m.purchaseInquiryService,
	)
	// 初始化付款申请服务
	m.paymentRequestService = service.NewPaymentRequestService(
		m.paymentRequestRepository,
		m.purchaseContractRepository,
		m.supplierRepository,
		m.companyService,
		m.temporalClient,
		m.userService,
	)
	// 初始化到货管理服务
	m.arrivalService = service.NewArrivalService(
		m.arrivalRepository,
		m.purchaseContractRepository,
		m.supplierRepository,
		m.companyService,
		m.temporalClient,
		m.userService,
	)
	// 初始化发货管理服务
	m.shipmentService = service.NewShipmentService(
		m.shipmentRepository,
		m.purchaseContractRepository,
		m.supplierRepository,
		m.temporalClient,
		m.userService,
	)
	// 初始化发票管理服务
	m.invoiceService = service.NewInvoiceService(
		m.invoiceRepository,
		m.purchaseContractRepository,
		m.userService,
		m.purchaseFeishuNotifier,
	)

	// 初始化控制器
	m.supplierController = controller.NewSupplierController(m.supplierService)
	m.purchaseRequestController = controller.NewPurchaseRequestController(m.purchaseRequestService)
	m.projectController = controller.NewProjectController(m.projectService)
	m.companyController = controller.NewCompanyController(m.companyService)
	// 初始化询比价控制器
	m.purchaseInquiryController = controller.NewPurchaseInquiryController(m.purchaseInquiryService)
	// 初始化合同控制器
	m.purchaseContractController = controller.NewPurchaseContractController(m.purchaseContractService)
	// 初始化付款申请控制器
	m.paymentRequestController = controller.NewPaymentRequestController(m.paymentRequestService)
	// 初始化到货管理控制器
	m.arrivalController = controller.NewArrivalController(m.arrivalService)
	// 初始化发货管理控制器
	m.shipmentController = controller.NewShipmentController(m.shipmentService)
	// 初始化发票管理控制器
	m.invoiceController = controller.NewInvoiceController(m.invoiceService)

	return nil
}

// AutoMigrate 执行数据库迁移
func (m *Module) AutoMigrate() error {
	return autoMigrate(m.db, m.logger)
}

// RegisterRoutes 注册路由
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	purchaseGroup := router.Group("/purchase")

	// 注册供应商相关路由
	m.supplierController.RegisterRoutes(purchaseGroup)

	// 注册采购申请相关路由
	m.purchaseRequestController.RegisterRoutes(purchaseGroup)

	// 注册项目相关路由
	m.projectController.RegisterRoutes(purchaseGroup)

	// 注册公司相关路由
	m.companyController.RegisterRoutes(purchaseGroup)

	// 注册采购询比价相关路由
	m.purchaseInquiryController.RegisterRoutes(purchaseGroup)

	// 注册采购合同相关路由
	m.purchaseContractController.RegisterRoutes(purchaseGroup)

	// 注册付款申请相关路由
	m.paymentRequestController.RegisterRoutes(purchaseGroup)

	// 注册到货管理相关路由
	m.arrivalController.RegisterRoutes(purchaseGroup)

	// 注册发货管理相关路由
	m.shipmentController.RegisterRoutes(purchaseGroup)

	// 注册发票管理相关路由
	m.invoiceController.RegisterRoutes(purchaseGroup)
}
