package workflow

import (
	"time"

	"backend/internal/modules/purchase/constants"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// ShipmentWorkflow 发货管理工作流
func ShipmentWorkflow(ctx workflow.Context, input ShipmentWorkflowInput) (*ShipmentWorkflowResult, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("开始发货管理工作流", "ShipmentID", input.ShipmentID, "ShipmentNo", input.ShipmentNo)

	// 初始化工作流状态
	state := &ShipmentWorkflowState{
		CurrentStage:    constants.ShipmentStagePurchaseReview,
		ApprovalHistory: []ShipmentApprovalSignal{},
		RollbackHistory: []ShipmentRollbackSignal{},
		StartedAt:       workflow.Now(ctx),
		Result:          "",
		CanRollback:     false,
		StageHistory:    []string{constants.ShipmentStagePurchaseReview},
		RequestType:     input.RequestType,
	}

	// 注册状态查询处理器
	err := workflow.SetQueryHandler(ctx, "getShipmentWorkflowState", func() (ShipmentWorkflowState, error) {
		return *state, nil
	})
	if err != nil {
		logger.Error("注册状态查询处理器失败", "error", err)
		return nil, err
	}

	// 创建信号通道
	approvalSignal := workflow.GetSignalChannel(ctx, ShipmentApprovalSignalName)
	rollbackSignal := workflow.GetSignalChannel(ctx, ShipmentRollbackSignalName)

	// 发送初始通知
	err = executeSendShipmentNotificationActivity(ctx, SendShipmentNotificationInput{
		ShipmentID:   input.ShipmentID,
		RecipientIDs: []uint{}, // 接收人列表将在活动中根据阶段动态获取
		Stage:        state.CurrentStage,
		Action:       constants.ActionSubmit,
		Comments:     "发货记录已提交，等待审批",
	})
	if err != nil {
		logger.Error("发送初始通知失败", "error", err)
		// 继续执行，不因通知失败而中断流程
	}

	// 工作流主循环
	workflowCompleted := false
	for !workflowCompleted {
		// 创建选择器
		selector := workflow.NewSelector(ctx)

		// 审批信号处理
		selector.AddReceive(approvalSignal, func(c workflow.ReceiveChannel, more bool) {
			var signal ShipmentApprovalSignal
			c.Receive(ctx, &signal)
			logger.Info("收到审批信号", "Action", signal.Action, "Stage", signal.CurrentStage)

			// 记录审批历史
			state.ApprovalHistory = append(state.ApprovalHistory, signal)

			// 更新状态
			err := executeUpdateShipmentStatusActivity(ctx, UpdateShipmentStatusInput{
				ShipmentID:   input.ShipmentID,
				Status:       getNextShipmentStage(signal.Action, state.CurrentStage, state.RequestType),
				OperatorID:   signal.ApproverID,
				OperatorName: "", // 将在活动中通过用户服务获取
				Action:       signal.Action,
				Comments:     signal.Comments,
			})
			if err != nil {
				logger.Error("更新发货记录状态失败", "error", err)
				return
			}

			// 处理审批结果
			switch signal.Action {
			case constants.ActionApprove:
				nextStage := getNextShipmentStage(signal.Action, state.CurrentStage, state.RequestType)
				if nextStage == constants.ShipmentStageCompleted {
					// 审批完成
					state.CurrentStage = nextStage
					state.Result = constants.WorkflowResultApproved
					completedTime := workflow.Now(ctx)
					state.CompletedAt = &completedTime
					logger.Info("发货管理审批完成")

					// 发送审批完成通知
					err := executeSendShipmentNotificationActivity(ctx, SendShipmentNotificationInput{
						ShipmentID:   input.ShipmentID,
						RecipientIDs: []uint{}, // 审批人列表将在活动中根据阶段动态获取
						Stage:        nextStage,
						Action:       constants.ActionApprove,
						Comments:     signal.Comments,
					})
					if err != nil {
						logger.Error("发送审批完成通知失败", "error", err)
						// 继续执行，不因通知失败而中断流程
					}

					workflowCompleted = true
				} else {
					// 进入下一个审批阶段
					state.CurrentStage = nextStage
					state.StageHistory = append(state.StageHistory, nextStage)
					state.CanRollback = canShipmentRollback(nextStage)

					// 发送下一阶段通知
					err := executeSendShipmentNotificationActivity(ctx, SendShipmentNotificationInput{
						ShipmentID:   input.ShipmentID,
						RecipientIDs: []uint{}, // 审批人列表将在活动中根据阶段动态获取
						Stage:        nextStage,
						Action:       constants.ActionApprove,
						Comments:     signal.Comments,
					})
					if err != nil {
						logger.Error("发送下一阶段通知失败", "error", err)
						// 继续执行，不因通知失败而中断流程
					}
				}

			case constants.ActionReject:
				// 审批被拒绝
				state.CurrentStage = constants.ShipmentStageCancelled
				state.Result = constants.WorkflowResultRejected
				completedTime := workflow.Now(ctx)
				state.CompletedAt = &completedTime
				logger.Info("发货管理审批被拒绝")

				// 发送拒绝通知
				err := executeSendShipmentNotificationActivity(ctx, SendShipmentNotificationInput{
					ShipmentID:   input.ShipmentID,
					RecipientIDs: []uint{}, // 审批人列表将在活动中根据阶段动态获取
					Stage:        constants.ShipmentStageCancelled,
					Action:       constants.ActionReject,
					Comments:     signal.Comments,
				})
				if err != nil {
					logger.Error("发送拒绝通知失败", "error", err)
					// 继续执行，不因通知失败而中断流程
				}

				workflowCompleted = true
			}
		})

		// 回退信号处理
		selector.AddReceive(rollbackSignal, func(c workflow.ReceiveChannel, more bool) {
			var signal ShipmentRollbackSignal
			c.Receive(ctx, &signal)
			logger.Info("收到回退信号", "RollbackTo", signal.RollbackTo)

			if !state.CanRollback {
				logger.Warn("当前状态不允许回退")
				return
			}

			// 记录回退历史
			state.RollbackHistory = append(state.RollbackHistory, signal)

			// 更新状态
			err := executeUpdateShipmentStatusActivity(ctx, UpdateShipmentStatusInput{
				ShipmentID:   input.ShipmentID,
				Status:       signal.RollbackTo,
				OperatorID:   signal.ApproverID,
				OperatorName: "", // 将在活动中通过用户服务获取
				Action:       constants.ActionRollback,
				Comments:     signal.Comments,
			})
			if err != nil {
				logger.Error("回退发货记录状态失败", "error", err)
				return
			}

			// 更新当前阶段
			state.CurrentStage = signal.RollbackTo
			state.StageHistory = append(state.StageHistory, signal.RollbackTo)

			// 发送回退通知
			err = executeSendShipmentNotificationActivity(ctx, SendShipmentNotificationInput{
				ShipmentID:   input.ShipmentID,
				RecipientIDs: []uint{}, // 审批人列表将在活动中根据阶段动态获取
				Stage:        signal.RollbackTo,
				Action:       constants.ActionRollback,
				Comments:     signal.Comments,
			})
			if err != nil {
				logger.Error("发送回退通知失败", "error", err)
				// 继续执行，不因通知失败而中断流程
			}
		})

		// 执行选择器
		selector.Select(ctx)
	}

	// 返回结果
	result := &ShipmentWorkflowResult{
		ShipmentID:  input.ShipmentID,
		FinalStatus: state.CurrentStage,
		Result:      state.Result,
		CompletedAt: workflow.Now(ctx),
	}

	logger.Info("发货管理工作流完成", "Result", result.Result, "FinalStatus", result.FinalStatus)
	return result, nil
}

// executeUpdateShipmentStatusActivity 执行更新发货记录状态活动
func executeUpdateShipmentStatusActivity(ctx workflow.Context, input UpdateShipmentStatusInput) error {
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 5,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	return workflow.ExecuteActivity(ctx, ActivityNameUpdateShipmentStatus, input).Get(ctx, nil)
}

// executeSendShipmentNotificationActivity 执行发送发货通知活动
func executeSendShipmentNotificationActivity(ctx workflow.Context, input SendShipmentNotificationInput) error {
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 5,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	return workflow.ExecuteActivity(ctx, ActivityNameSendShipmentNotification, input).Get(ctx, nil)
}
