package workflow

import (
	"fmt"
	"time"

	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/workflow/activity"
	"backend/internal/modules/purchase/workflow/activity/request"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// PurchaseRequestApprovalWorkflow 采购申请审批工作流
func PurchaseRequestApprovalWorkflow(ctx workflow.Context, input PurchaseRequestWorkflowInput) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("采购申请审批工作流开始", "requestID", input.RequestID, "requestNo", input.RequestNo)

	// 初始化工作流状态
	state := PurchaseRequestWorkflowState{
		CurrentStage:    StageProjectManagerApproval,
		ApprovalHistory: []ApprovalSignal{},
		RollbackHistory: []RollbackSignal{},
		StartedAt:       workflow.Now(ctx),
		CanRollback:     false, // 第一个阶段不能回退
		StageHistory:    []string{StageProjectManagerApproval},
	}

	// 注册状态查询处理器
	err := workflow.SetQueryHandler(ctx, "getWorkflowState", func() (PurchaseRequestWorkflowState, error) {
		return state, nil
	})
	if err != nil {
		logger.Error("注册状态查询处理器失败", "error", err)
		return err
	}

	// 设置活动选项
	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: 5 * time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Minute,
			MaximumAttempts:    5,
		},
	}
	activityCtx := workflow.WithActivityOptions(ctx, activityOptions)

	// 发送工作流开始通知 - 待审批通知
	err = workflow.ExecuteActivity(activityCtx, activity.SendFeishuPurchaseNotificationActivity,
		input, "submitted", "项目经理", "", "", "", "").Get(ctx, nil)
	if err != nil {
		logger.Error("发送工作流开始通知失败", "error", err)
		// 通知失败不影响流程继续
	}

	// 设置审批超时
	const approvalTimeout = 7 * 24 * time.Hour // 7天

	// 开始审批流程循环
	for {
		var err error

		switch state.CurrentStage {
		case StageProjectManagerApproval:
			// 同步工作流状态到数据库
			err = workflow.ExecuteActivity(activityCtx, activity.UpdateRequestStatusActivity,
				input.RequestID, state.CurrentStage, input.RequesterID, "", "进入项目经理审批阶段", constants.ActionSubmit).Get(ctx, nil)
			if err != nil {
				logger.Error("更新数据库状态失败", "error", err, "stage", state.CurrentStage)
			}

			// 项目经理审批阶段的通知在工作流开始时已发送，不再重复发送

			approvalResult, err := handleApprovalStageWithRollback(ctx, StageProjectManagerApproval, approvalTimeout, &state)
			if err != nil {
				logger.Error("项目经理审批阶段出错", "error", err)
				return err
			}

			switch approvalResult.Action {
			case constants.ActionApprove:
				moveToNextStage(ctx, &state, StagePurchasingManagerApproval)

				// 同步工作流状态到数据库 - 使用审批信号中的信息
				err = workflow.ExecuteActivity(activityCtx, activity.UpdateRequestStatusActivity,
					input.RequestID, StagePurchasingManagerApproval, approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", StagePurchasingManagerApproval)
				}

				// 发送采购负责人审批阶段通知
				err = workflow.ExecuteActivity(activityCtx, activity.SendFeishuPurchaseNotificationActivity,
					input, "submitted", "采购负责人", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送阶段变更通知失败", "error", err)
					// 通知失败不影响流程继续
				}
			case constants.ActionReject:
				logger.Info("项目经理拒绝审批，流程结束")
				completeWorkflow(ctx, &state, constants.StatusRejected)

				// 同步拒绝状态到数据库 - 使用审批信号中的信息
				err = workflow.ExecuteActivity(activityCtx, activity.UpdateRequestStatusActivity,
					input.RequestID, constants.StatusRejected, approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionReject).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", constants.StatusRejected)
				}

				// 发送拒绝通知
				err = workflow.ExecuteActivity(activityCtx, activity.SendFeishuPurchaseNotificationActivity,
					input, "rejected", "", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送拒绝通知失败", "error", err)
					// 通知失败不影响流程继续
				}

				return nil
			}

		case StagePurchasingManagerApproval:
			approvalResult, err := handleApprovalStageWithRollback(ctx, StagePurchasingManagerApproval, approvalTimeout, &state)
			if err != nil {
				logger.Error("采购负责人审批阶段出错", "error", err)
				return err
			}

			switch approvalResult.Action {
			case constants.ActionApprove:
				moveToNextStage(ctx, &state, StageCTONotification)

				// 同步工作流状态到数据库 - 这里是已批准状态
				err = workflow.ExecuteActivity(activityCtx, activity.UpdateRequestStatusActivity,
					input.RequestID, constants.StatusApproved, approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", constants.StatusApproved)
				}

				// 发送审批通过通知
				err = workflow.ExecuteActivity(activityCtx, activity.SendFeishuPurchaseNotificationActivity,
					input, "approved", "", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送审批通过通知失败", "error", err)
					// 通知失败不影响流程继续
				}
			case constants.ActionReject:
				logger.Info("采购负责人拒绝审批，流程结束")
				completeWorkflow(ctx, &state, constants.StatusRejected)

				// 同步拒绝状态到数据库
				err = workflow.ExecuteActivity(activityCtx, activity.UpdateRequestStatusActivity,
					input.RequestID, constants.StatusRejected, approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionReject).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", constants.StatusRejected)
				}

				// 发送拒绝通知
				err = workflow.ExecuteActivity(activityCtx, activity.SendFeishuPurchaseNotificationActivity,
					input, "rejected", "", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送拒绝通知失败", "error", err)
					// 通知失败不影响流程继续
				}

				return nil
			case constants.ActionRollback:
				// 处理回退逻辑在 handleApprovalStageWithRollback 中已完成

				// 同步回退状态到数据库
				err = workflow.ExecuteActivity(activityCtx, activity.UpdateRequestStatusActivity,
					input.RequestID, state.CurrentStage, approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionRollback).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", state.CurrentStage)
				}

				// 发送回退通知，从StagePurchasingManagerApproval回退到StageProjectManagerApproval
				err = workflow.ExecuteActivity(activityCtx, activity.SendFeishuPurchaseNotificationActivity,
					input, "rollback", "", "", approvalResult.Comments, "采购负责人审批", "项目经理审批").Get(ctx, nil)
				if err != nil {
					logger.Error("发送回退通知失败", "error", err)
					// 通知失败不影响流程继续
				}

				continue
			}

		case StageCTONotification:
			err = notifyCTO(ctx, input)
			if err != nil {
				logger.Error("通知CTO失败", "error", err)
				// 通知失败不影响流程继续
			}

			// 完成工作流
			logger.Info("采购申请审批通过，流程结束")
			completeWorkflow(ctx, &state, constants.StatusApproved)

			// 最终同步批准状态到数据库
			err = workflow.ExecuteActivity(activityCtx, activity.UpdateRequestStatusActivity,
				input.RequestID, constants.StatusApproved, input.RequesterID, "", "审批流程完成", constants.ActionComplete).Get(ctx, nil)
			if err != nil {
				logger.Error("更新数据库状态失败", "error", err, "stage", constants.StatusApproved)
			}

			return nil

		default:
			logger.Error("未知的审批阶段", "stage", state.CurrentStage)
			return fmt.Errorf("未知的审批阶段: %s", state.CurrentStage)
		}
	}
}

// handleApprovalStageWithRollback 处理带回退功能的审批阶段
func handleApprovalStageWithRollback(ctx workflow.Context, stage string, timeout time.Duration, state *PurchaseRequestWorkflowState) (*ApprovalResult, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("开始处理审批阶段", "stage", stage)

	// 创建审批信号通道
	approvalChan := workflow.GetSignalChannel(ctx, SignalNameApproval)
	rollbackChan := workflow.GetSignalChannel(ctx, SignalNameRollback)

	// 创建超时计时器
	timerCancellation := workflow.NewTimer(ctx, timeout)

	// 选择器，用于等待信号或超时
	selector := workflow.NewSelector(ctx)
	var approvalSignal ApprovalSignal
	var rollbackSignal RollbackSignal
	var timerFired bool
	var signalType string

	// 添加审批信号处理
	selector.AddReceive(approvalChan, func(c workflow.ReceiveChannel, more bool) {
		c.Receive(ctx, &approvalSignal)
		signalType = constants.ActionApprove
		logger.Info("收到审批信号", "approverID", approvalSignal.ApproverID, "action", approvalSignal.Action)
	})

	// 添加回退信号处理
	selector.AddReceive(rollbackChan, func(c workflow.ReceiveChannel, more bool) {
		c.Receive(ctx, &rollbackSignal)
		signalType = constants.ActionRollback
		logger.Info("收到回退信号", "approverID", rollbackSignal.ApproverID, "rollbackTo", rollbackSignal.RollbackTo)
	})

	// 添加超时处理
	selector.AddFuture(timerCancellation, func(f workflow.Future) {
		timerFired = true
		signalType = "timeout"
		logger.Info("审批超时", "stage", stage)
	})

	// 等待信号或超时
	selector.Select(ctx)

	// 如果超时，返回错误
	if timerFired {
		return nil, fmt.Errorf("审批阶段 %s 超时", stage)
	}

	// 处理审批信号
	if signalType == constants.ActionApprove {
		// 记录审批历史
		approvalSignal.CurrentStage = stage
		state.ApprovalHistory = append(state.ApprovalHistory, approvalSignal)

		// 获取用户真实姓名
		operatorName := GetUserRealNameFromActivity(ctx, approvalSignal.ApproverID)

		// 返回审批结果
		return &ApprovalResult{
			Action:       approvalSignal.Action,
			ApproverID:   approvalSignal.ApproverID,
			OperatorName: operatorName,
			Comments:     approvalSignal.Comments,
		}, nil
	}

	// 处理回退信号
	if signalType == constants.ActionRollback {
		// 检查是否可以回退
		if !canRollbackToStage(state, rollbackSignal.RollbackTo) {
			logger.Error("无法回退到指定阶段", "currentStage", stage, "rollbackTo", rollbackSignal.RollbackTo)
			return nil, fmt.Errorf("无法回退到指定阶段: %s", rollbackSignal.RollbackTo)
		}

		// 记录回退历史
		state.RollbackHistory = append(state.RollbackHistory, rollbackSignal)

		// 执行回退
		rollbackToStage(ctx, state, rollbackSignal.RollbackTo)

		// 获取用户真实姓名
		operatorName := GetUserRealNameFromActivity(ctx, rollbackSignal.ApproverID)

		// 返回回退结果
		return &ApprovalResult{
			Action:       "rollback",
			ApproverID:   rollbackSignal.ApproverID,
			OperatorName: operatorName,
			Comments:     rollbackSignal.Comments,
		}, nil
	}

	return nil, fmt.Errorf("未知的信号类型: %s", signalType)
}

// notifyCTO 通知CTO
func notifyCTO(ctx workflow.Context, input PurchaseRequestWorkflowInput) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("通知CTO", "requestID", input.RequestID, "requestNo", input.RequestNo)

	// 执行通知活动
	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: 5 * time.Minute,
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	// 将工作流输入转换为活动输入
	activityInput := activity.PurchaseRequestWorkflowInput{
		RequestID:    input.RequestID,
		RequestNo:    input.RequestNo,
		RequesterID:  input.RequesterID,
		RequestType:  input.RequestType,
		UrgencyLevel: input.UrgencyLevel,
	}

	// 调用通知活动
	return workflow.ExecuteActivity(ctx, activity.NotifyCTOActivity, activityInput).Get(ctx, nil)
}

// moveToNextStage 移动到下一阶段
func moveToNextStage(ctx workflow.Context, state *PurchaseRequestWorkflowState, nextStage string) {
	logger := workflow.GetLogger(ctx)
	logger.Info("移动到下一阶段", "from", state.CurrentStage, "to", nextStage)

	// 添加到阶段历史
	state.StageHistory = append(state.StageHistory, nextStage)

	// 更新当前阶段
	state.CurrentStage = nextStage

	// 更新回退权限，第一个阶段不能回退
	state.CanRollback = nextStage != StageProjectManagerApproval
}

// canRollbackToStage 检查是否可以回退到指定阶段
func canRollbackToStage(state *PurchaseRequestWorkflowState, targetStage string) bool {
	// 第一个阶段不能回退
	if state.CurrentStage == StageProjectManagerApproval {
		return false
	}

	// 检查目标阶段是否在历史中
	for _, stage := range state.StageHistory {
		if stage == targetStage {
			return true
		}
	}

	return false
}

// rollbackToStage 回退到指定阶段
func rollbackToStage(ctx workflow.Context, state *PurchaseRequestWorkflowState, targetStage string) {
	logger := workflow.GetLogger(ctx)
	logger.Info("回退到指定阶段", "from", state.CurrentStage, "to", targetStage)

	// 更新当前阶段
	state.CurrentStage = targetStage

	// 更新回退权限
	state.CanRollback = targetStage != StageProjectManagerApproval

	// 清理目标阶段之后的历史记录
	newStageHistory := []string{}
	for _, stage := range state.StageHistory {
		newStageHistory = append(newStageHistory, stage)
		if stage == targetStage {
			break
		}
	}
	state.StageHistory = newStageHistory
}

// completeWorkflow 完成工作流
func completeWorkflow(ctx workflow.Context, state *PurchaseRequestWorkflowState, result string) {
	now := workflow.Now(ctx)
	state.CompletedAt = &now
	state.Result = result
	state.CurrentStage = StageCompleted
}

// GetUserRealNameFromActivity 通过Activity获取用户真实姓名
func GetUserRealNameFromActivity(ctx workflow.Context, userID uint) string {
	// 设置活动选项
	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: 5 * time.Second,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Minute,
			MaximumAttempts:    3,
		},
	}
	activityCtx := workflow.WithActivityOptions(ctx, activityOptions)

	// 调用活动获取用户真实姓名
	var result string
	err := workflow.ExecuteActivity(activityCtx, request.GetUserRealNameActivity, userID).Get(ctx, &result)
	if err != nil {
		// 如果获取失败，返回默认值
		workflow.GetLogger(ctx).Error("获取用户真实姓名失败", "error", err, "userID", userID)
		return fmt.Sprintf("用户_%d", userID)
	}

	return result
}
