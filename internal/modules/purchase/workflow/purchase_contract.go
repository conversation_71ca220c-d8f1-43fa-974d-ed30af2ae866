package workflow

import (
	"time"
)

// PurchaseContractWorkflowInput 采购合同工作流输入参数
type PurchaseContractWorkflowInput struct {
	ContractID   uint    `json:"contract_id"`
	ContractNo   string  `json:"contract_no"`
	RequesterID  uint    `json:"requester_id"`
	SupplierID   uint    `json:"supplier_id"`
	TotalAmount  float64 `json:"total_amount"`
	ContractType string  `json:"contract_type"`
}

// 采购合同工作流信号常量 - 这些常量保留在这里因为它们是工作流特有的
const (
	// SignalNameContractApproval 合同审批信号
	SignalNameContractApproval = "contract_approval_signal"

	// SignalNameContractRollback 合同回退信号
	SignalNameContractRollback = "contract_rollback_signal"
)

// ContractApprovalSignal 合同审批信号
type ContractApprovalSignal struct {
	ApproverID   uint      `json:"approver_id"`   // 审批人ID
	Action       string    `json:"action"`        // 动作：approve, reject
	Comments     string    `json:"comments"`      // 审批意见
	CurrentStage string    `json:"current_stage"` // 当前阶段
	Timestamp    time.Time `json:"timestamp"`     // 时间戳
}

// ContractRollbackSignal 合同回退信号
type ContractRollbackSignal struct {
	ApproverID   uint      `json:"approver_id"`   // 审批人ID
	RollbackTo   string    `json:"rollback_to"`   // 回退到的阶段
	Comments     string    `json:"comments"`      // 回退意见
	CurrentStage string    `json:"current_stage"` // 当前阶段
	Timestamp    time.Time `json:"timestamp"`     // 时间戳
}

// ContractApprovalResult 合同审批结果
type ContractApprovalResult struct {
	Action       string `json:"action"`        // 动作：approve, reject, rollback
	ApproverID   uint   `json:"approver_id"`   // 审批人ID
	OperatorName string `json:"operator_name"` // 操作人姓名
	Comments     string `json:"comments"`      // 审批意见
}

// PurchaseContractWorkflowState 采购合同工作流状态
type PurchaseContractWorkflowState struct {
	CurrentStage    string                   `json:"current_stage"`    // 当前阶段
	ApprovalHistory []ContractApprovalSignal `json:"approval_history"` // 审批历史
	RollbackHistory []ContractRollbackSignal `json:"rollback_history"` // 回退历史
	StartedAt       time.Time                `json:"started_at"`       // 开始时间
	CompletedAt     *time.Time               `json:"completed_at"`     // 完成时间
	Result          string                   `json:"result"`           // 结果：approved, rejected
	CanRollback     bool                     `json:"can_rollback"`     // 是否可以回退
	StageHistory    []string                 `json:"stage_history"`    // 阶段历史
}
