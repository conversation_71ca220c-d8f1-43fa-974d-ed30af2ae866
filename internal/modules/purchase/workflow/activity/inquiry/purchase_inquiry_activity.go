package inquiry

import (
	"backend/internal/modules/purchase/constants"
	"context"
	"fmt"
	"sync"
	"time"

	"backend/configs"
	"backend/internal/common/utils/notifier"
	"backend/internal/infrastructure/database"
	"backend/internal/modules/purchase/model"

	"go.uber.org/zap"

	"backend/internal/common/utils"

	"gorm.io/gorm"
)

// PurchaseInquiryWorkflowInput 采购询价工作流输入参数
type PurchaseInquiryWorkflowInput struct {
	InquiryID   uint    `json:"inquiry_id"`
	InquiryNo   string  `json:"inquiry_no"`
	RequesterID uint    `json:"requester_id"`
	SupplierID  uint    `json:"supplier_id"`
	TotalAmount float64 `json:"total_amount"`
}

// WorkflowStageToInquiryDBStatus 将询价工作流阶段映射到数据库状态
func WorkflowStageToInquiryDBStatus(stage string) string {
	switch stage {
	case "finance_review":
		return constants.InquiryStatusFinanceReview
	case "enterprise_review":
		return constants.InquiryStatusEnterpriseReview
	case "approved":
		return constants.InquiryStatusApproved
	case "rejected":
		return constants.InquiryStatusRejected
	default:
		return constants.InquiryStatusDraft
	}
}

// UpdateInquiryStatusActivity 更新采购询价状态活动
func UpdateInquiryStatusActivity(ctx context.Context, inquiryID uint, stage string, updatedBy uint, operatorName string, comments string, action string) error {
	// 连接数据库
	db, err := connectDB()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取当前采购询价
	var inquiry model.PurchaseInquiry
	if err := db.First(&inquiry, inquiryID).Error; err != nil {
		return fmt.Errorf("获取采购询价失败: %w", err)
	}

	// 将工作流阶段转换为数据库状态
	status := WorkflowStageToInquiryDBStatus(stage)

	// 如果状态没有变化，不需要更新
	if inquiry.Status == status {
		return nil
	}

	// 记录旧状态
	oldStatus := inquiry.Status

	// 更新状态 - 明确指定要更新的字段
	if err := db.Model(&inquiry).Select("status", "updated_by", "updated_at").Updates(map[string]interface{}{
		"status":     status,
		"updated_by": updatedBy,
		"updated_at": time.Now(),
	}).Error; err != nil {
		return fmt.Errorf("更新采购询价状态失败: %w", err)
	}

	// 如果没有传递操作人姓名，则查询获取
	if operatorName == "" {
		operatorName = getUserRealName(db, updatedBy)
	}

	// 如果没有传递action，根据状态推断
	if action == "" {
		switch status {
		case constants.InquiryStatusFinanceReview:
			action = constants.ActionSubmit
		case constants.InquiryStatusEnterpriseReview:
			action = constants.ActionApprove
		case constants.InquiryStatusApproved:
			action = constants.ActionApprove
		case constants.InquiryStatusRejected:
			action = constants.ActionReject
		default:
			action = constants.ActionUpdate
		}
	}

	// 记录状态变更历史
	now := time.Now()
	history := model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseInquiry,
		BusinessID:     inquiryID,
		PreviousStatus: oldStatus,
		NewStatus:      status,
		Action:         action,
		OperatorID:     updatedBy,
		OperatorName:   operatorName,
		OperationTime:  now,
		Comments:       comments,
		CreatedAt:      now,
	}

	if err := db.Create(&history).Error; err != nil {
		return fmt.Errorf("记录询价状态历史失败: %w", err)
	}

	return nil
}

// SendFeishuInquiryNotificationActivity 发送飞书询价通知活动
func SendFeishuInquiryNotificationActivity(ctx context.Context, input PurchaseInquiryWorkflowInput, notifyType string, toStage string, operatorName string, comments string, fromStage string, toStageDisplay string) error {
	// 连接数据库
	db, err := connectDB()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 获取飞书通知器
	baseNotifier, err := getFeishuNotifier()
	if err != nil {
		return fmt.Errorf("获取飞书通知器失败: %w", err)
	}

	// 创建采购专用通知器
	purchaseNotifier := notifier.NewPurchaseFeishuNotifier(
		baseNotifier,
		cfg.Feishu.PurchaseWebhookURL,
		cfg.Feishu.PurchaseSecret,
		cfg.Feishu.PurchaseUrlTemplates,
		cfg.Feishu.PurchaseEnabled,
	)

	// 设置数据库连接，用于查询审批历史
	purchaseNotifier.SetDB(db)

	// 获取采购询价详情
	var inquiry model.PurchaseInquiry
	if err := db.First(&inquiry, input.InquiryID).Error; err != nil {
		return fmt.Errorf("获取采购询价详情失败: %w", err)
	}

	// 获取申请人姓名（不再使用操作人姓名）
	requesterName := getUserRealName(db, inquiry.CreatedBy)

	// 获取项目名称
	projectName := "无"
	if inquiry.ProjectID != nil && *inquiry.ProjectID > 0 {
		// 查询项目表获取项目名称
		type Project struct {
			ID          uint   `gorm:"column:id"`
			ProjectName string `gorm:"column:project_name"`
		}
		var project Project
		if err := db.Table("projects").Select("id, project_name").Where("id = ?", *inquiry.ProjectID).First(&project).Error; err == nil {
			projectName = project.ProjectName
		} else {
			fmt.Printf("获取项目名称失败: ID=%d, 错误=%v\n", *inquiry.ProjectID, err)
		}
	}

	// 构建通用通知参数
	params := notifier.GeneralApprovalNotifyParams{
		FlowType:      notifier.FlowTypePurchaseInquiry,
		BusinessID:    input.InquiryID,
		BusinessNo:    input.InquiryNo,
		RequesterName: requesterName,
		BusinessType:  "采购询价",
		ProjectName:   projectName,
	}

	// 根据通知类型设置对应的事件类型和其他参数
	switch notifyType {
	case "submitted":
		params.EventType = notifier.EventTypeSubmitted
		params.ToStage = toStage

	case "stage_changed":
		params.EventType = notifier.EventTypeStageChange
		params.ToStage = toStage

	case "approved":
		params.EventType = notifier.EventTypeApproved
		params.Comments = comments

	case "rejected":
		params.EventType = notifier.EventTypeRejected
		params.Comments = comments

	case "rollback":
		params.EventType = notifier.EventTypeRollback
		// 转换阶段名称为友好显示名称
		fromDisplayName := fromStage
		toDisplayName := toStage

		switch fromStage {
		case "enterprise_review":
			fromDisplayName = "企业负责人审批"
		case "finance_review":
			fromDisplayName = "财务负责人审批"
		}

		switch toStage {
		case "enterprise_review":
			toDisplayName = "企业负责人审批"
		case "finance_review":
			toDisplayName = "财务负责人审批"
		}

		// 如果传入了显示名称，优先使用传入的
		if toStageDisplay != "" {
			toDisplayName = toStageDisplay
		}

		params.FromStage = fromDisplayName
		params.ToStage = toDisplayName
		params.Comments = comments

	default:
		return fmt.Errorf("未知的询价通知类型: %s", notifyType)
	}

	// 发送通用通知
	return purchaseNotifier.SendGeneralApprovalNotification(params)
}

// GetUserRealNameActivity 获取用户真实姓名的活动
func GetUserRealNameActivity(ctx context.Context, userID uint) (string, error) {
	// 连接数据库
	db, err := connectDB()
	if err != nil {
		return fmt.Sprintf("用户_%d", userID), fmt.Errorf("连接数据库失败: %w", err)
	}

	// 调用内部函数获取用户真实姓名
	return getUserRealName(db, userID), nil
}

// 连接数据库
func connectDB() (*gorm.DB, error) {
	// 使用GORM获取数据库连接
	db := database.GetDB()
	if db == nil {
		// 如果获取不到全局DB，尝试初始化
		cfg, err := configs.LoadConfig()
		if err != nil {
			return nil, fmt.Errorf("加载配置失败: %w", err)
		}

		db = database.InitDB(cfg)
		if db == nil {
			return nil, fmt.Errorf("初始化数据库失败")
		}
	}

	return db, nil
}

// getUserRealName 获取用户真实姓名
func getUserRealName(db *gorm.DB, userID uint) string {
	// 使用共享的工具函数
	return utils.GetUserRealName(db, userID)
}

// 全局飞书通知器实例（单例模式）
var (
	globalFeishuNotifier *notifier.FeishuNotifier
	notifierOnce         sync.Once
)

// 获取飞书通知器实例（单例模式，避免重复创建）
func getFeishuNotifier() (*notifier.FeishuNotifier, error) {
	var err error
	notifierOnce.Do(func() {
		// 加载配置
		cfg, configErr := configs.LoadConfig()
		if configErr != nil {
			err = fmt.Errorf("加载配置失败: %w", configErr)
			return
		}

		// 获取日志实例
		logger, logErr := zap.NewProduction()
		if logErr != nil {
			err = fmt.Errorf("创建日志实例失败: %w", logErr)
			return
		}

		// 创建飞书通知器 - 使用配置项（只创建一次）
		globalFeishuNotifier = notifier.NewFeishuNotifier(
			cfg.Feishu.WebhookURL,
			cfg.Feishu.Secret,
			cfg.Feishu.TicketDetailUrlTemplate,
			cfg.Feishu.RepairTicketDetailUrlTemplate,
			nil, // 采购模块不使用项目webhook映射
			logger,
		)
	})

	if err != nil {
		return nil, err
	}

	return globalFeishuNotifier, nil
}
