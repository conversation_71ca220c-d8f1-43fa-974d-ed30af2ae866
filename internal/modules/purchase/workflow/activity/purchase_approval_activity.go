package activity

import (
	"backend/internal/modules/purchase/constants"
	"context"
	"fmt"
	"time"

	"backend/configs"
	"backend/internal/common/utils/notifier"
	"backend/internal/infrastructure/database"
	"backend/internal/modules/purchase/model"

	"go.uber.org/zap"

	"gorm.io/gorm"
)

// PurchaseRequestWorkflowInput 采购申请工作流输入参数
type PurchaseRequestWorkflowInput struct {
	RequestID    uint   `json:"request_id"`
	RequestNo    string `json:"request_no"`
	RequesterID  uint   `json:"requester_id"`
	RequestType  string `json:"request_type"`
	UrgencyLevel string `json:"urgency_level"`
}

// NotifyCTOActivity CTO通知活动
func NotifyCTOActivity(ctx context.Context, input PurchaseRequestWorkflowInput) error {
	// 这里实现具体的通知逻辑，例如发送邮件、消息等
	// 这是一个示例实现，实际项目中需要根据需求来实现
	return nil
}

// WorkflowStageToDBStatus 将工作流阶段映射到数据库状态
func WorkflowStageToDBStatus(stage string) string {
	switch stage {
	case "project_manager_approval":
		return constants.StatusProjectManagerReview
	case "purchasing_manager_approval":
		return constants.StatusPurchaseManagerReview
	case "approved":
		return constants.StatusApproved
	case "rejected":
		return constants.StatusRejected
	default:
		return constants.StatusDraft
	}
}

// UpdateRequestStatusActivity 更新采购申请状态活动
func UpdateRequestStatusActivity(ctx context.Context, requestID uint, stage string, updatedBy uint, operatorName string, comments string, action string) error {
	// 连接数据库
	db, err := connectDB()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取当前采购申请
	var request model.PurchaseRequest
	if err := db.First(&request, requestID).Error; err != nil {
		return fmt.Errorf("获取采购申请失败: %w", err)
	}

	// 将工作流阶段转换为数据库状态
	status := WorkflowStageToDBStatus(stage)

	// 如果状态没有变化，不需要更新
	if request.Status == status {
		return nil
	}

	// 记录旧状态
	oldStatus := request.Status

	// 更新状态 - 明确指定要更新的字段
	if err := db.Model(&request).Select("status", "updated_by", "updated_at").Updates(map[string]interface{}{
		"status":     status,
		"updated_by": updatedBy,
		"updated_at": time.Now(),
	}).Error; err != nil {
		return fmt.Errorf("更新采购申请状态失败: %w", err)
	}

	// 如果没有传递操作人姓名，则查询获取
	if operatorName == "" {
		operatorName = getUserRealName(db, updatedBy)
	}

	// 如果没有传递action，根据状态推断
	if action == "" {
		switch status {
		case constants.StatusProjectManagerReview:
			action = constants.ActionSubmit
		case constants.StatusPurchaseManagerReview:
			action = constants.ActionApprove
		case constants.StatusApproved:
			action = constants.ActionApprove
		case constants.StatusRejected:
			action = constants.ActionReject
		default:
			action = constants.ActionUpdate
		}
	}

	// 记录状态变更历史
	history := model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseRequest,
		BusinessID:     requestID,
		PreviousStatus: oldStatus,
		NewStatus:      status,
		Action:         action,
		OperatorID:     updatedBy,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       comments,
		CreatedAt:      time.Now(),
	}

	if err := db.Create(&history).Error; err != nil {
		// 记录历史失败不影响主流程
		fmt.Printf("记录状态变更历史失败: %v\n", err)
	}

	return nil
}

// 连接数据库
func connectDB() (*gorm.DB, error) {
	// 使用GORM获取数据库连接
	db := database.GetDB()
	if db == nil {
		// 如果获取不到全局DB，尝试初始化
		cfg, err := configs.LoadConfig()
		if err != nil {
			return nil, fmt.Errorf("加载配置失败: %w", err)
		}

		db = database.InitDB(cfg)
		if db == nil {
			return nil, fmt.Errorf("初始化数据库失败")
		}
	}

	return db, nil
}

// getUserRealName 获取用户真实姓名
func getUserRealName(db *gorm.DB, userID uint) string {
	if userID == 0 {
		return "系统"
	}

	// 查询用户表获取真实姓名
	type User struct {
		ID       uint   `gorm:"column:id"`
		RealName string `gorm:"column:real_name"`
		Username string `gorm:"column:username"`
	}

	var user User
	if err := db.Table("users").Select("id, real_name, username").Where("id = ?", userID).First(&user).Error; err != nil {
		// 如果查询失败，返回默认值
		return fmt.Sprintf("用户_%d", userID)
	}

	// 优先返回真实姓名，如果为空则返回用户名
	if user.RealName != "" {
		return user.RealName
	}
	if user.Username != "" {
		return user.Username
	}
	return fmt.Sprintf("用户_%d", userID)
}

// SendNotificationActivity 发送通知活动
func SendNotificationActivity(ctx context.Context, userID uint, title string, content string) error {
	// 实现发送通知的逻辑，可以是邮件、短信、站内信等
	// 实际项目中需要集成具体的通知服务
	return nil
}

// 飞书通知相关活动

// SendFeishuPurchaseNotificationActivity 发送采购申请飞书通知活动
func SendFeishuPurchaseNotificationActivity(ctx context.Context, input PurchaseRequestWorkflowInput, notifyType string, stageName string, operatorName string, comments string, fromStage string, toStage string) error {
	// 连接数据库获取采购申请详情
	db, err := connectDB()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 获取飞书通知器
	baseNotifier, err := getFeishuNotifier()
	if err != nil {
		return fmt.Errorf("获取飞书通知器失败: %w", err)
	}

	// 创建采购专用通知器
	purchaseNotifier := notifier.NewPurchaseFeishuNotifier(
		baseNotifier,
		cfg.Feishu.PurchaseWebhookURL,
		cfg.Feishu.PurchaseSecret,
		cfg.Feishu.PurchaseUrlTemplates,
		cfg.Feishu.PurchaseEnabled,
	)

	// 设置数据库连接，用于查询审批历史
	purchaseNotifier.SetDB(db)

	// 获取采购申请详情，主要是为了获取项目信息
	var request model.PurchaseRequest
	if err := db.First(&request, input.RequestID).Error; err != nil {
		return fmt.Errorf("获取采购申请详情失败: %w", err)
	}

	// 获取申请人姓名（不再使用操作人姓名）
	requesterName := getUserRealName(db, request.CreatedBy)

	// 获取项目名称
	projectName := "无"
	if request.ProjectID != nil && *request.ProjectID > 0 {
		// 查询项目表获取项目名称
		type Project struct {
			ID          uint   `gorm:"column:id"`
			ProjectName string `gorm:"column:project_name"`
		}
		var project Project
		// 尝试从projects表获取项目名称
		if err := db.Table("projects").Select("id, project_name").Where("id = ?", *request.ProjectID).First(&project).Error; err == nil {
			projectName = project.ProjectName

		} else {
			fmt.Printf("获取项目名称失败: ID=%d, 错误=%v\n", *request.ProjectID, err)
		}
	} else {
		fmt.Printf("采购申请没有关联项目或项目ID为空\n")
	}

	// 构建通用通知参数
	params := notifier.GeneralApprovalNotifyParams{
		FlowType:      notifier.FlowTypePurchaseRequest,
		BusinessID:    input.RequestID,
		BusinessNo:    input.RequestNo,
		RequesterName: requesterName,
		BusinessType:  input.RequestType,
		ProjectName:   projectName,
	}

	// 根据通知类型设置对应的事件类型和其他参数
	switch notifyType {
	case "submitted":
		params.EventType = notifier.EventTypeSubmitted
		params.ToStage = stageName

	case "stage_changed":
		params.EventType = notifier.EventTypeStageChange
		params.ToStage = stageName

	case "approved":
		params.EventType = notifier.EventTypeApproved
		params.Comments = comments

	case "rejected":
		params.EventType = notifier.EventTypeRejected
		params.Comments = comments

	case "rollback":
		params.EventType = notifier.EventTypeRollback
		params.FromStage = fromStage
		params.ToStage = toStage
		params.Comments = comments

	default:
		return fmt.Errorf("未知的通知类型: %s", notifyType)
	}

	// 发送通用通知
	return purchaseNotifier.SendGeneralApprovalNotification(params)
}

// 获取飞书通知器实例
func getFeishuNotifier() (*notifier.FeishuNotifier, error) {
	// 加载配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	// 获取日志实例
	logger, err := zap.NewProduction()
	if err != nil {
		return nil, fmt.Errorf("创建日志实例失败: %w", err)
	}

	// 创建飞书通知器 - 使用配置项
	feishuNotifier := notifier.NewFeishuNotifier(
		cfg.Feishu.WebhookURL,
		cfg.Feishu.Secret,
		cfg.Feishu.TicketDetailUrlTemplate,
		cfg.Feishu.RepairTicketDetailUrlTemplate,
		nil, // 采购模块不使用项目webhook映射
		logger,
	)

	return feishuNotifier, nil
}
