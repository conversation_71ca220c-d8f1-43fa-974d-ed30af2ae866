package workflow

import (
	"time"
)

// PurchaseInquiryWorkflowState 采购询价工作流状态
type PurchaseInquiryWorkflowState struct {
	CurrentStage    string                  `json:"current_stage"`
	ApprovalHistory []InquiryApprovalSignal `json:"approval_history"`
	RollbackHistory []InquiryRollbackSignal `json:"rollback_history"`
	StartedAt       time.Time               `json:"started_at"`
	CompletedAt     *time.Time              `json:"completed_at"`
	Result          string                  `json:"result"` // approved, rejected, cancelled
	CanRollback     bool                    `json:"can_rollback"`
	StageHistory    []string                `json:"stage_history"` // 阶段历史，用于回退
}

// InquiryApprovalSignal 询价审批信号
type InquiryApprovalSignal struct {
	ApproverID   uint      `json:"approver_id"`
	Action       string    `json:"action"` // approve, reject, rollback
	Comments     string    `json:"comments"`
	Timestamp    time.Time `json:"timestamp"`
	RollbackTo   string    `json:"rollback_to,omitempty"`   // 回退到的阶段
	CurrentStage string    `json:"current_stage,omitempty"` // 当前阶段
}

// InquiryRollbackSignal 询价回退信号
type InquiryRollbackSignal struct {
	ApproverID   uint      `json:"approver_id"`
	RollbackTo   string    `json:"rollback_to"`   // 回退到的阶段
	CurrentStage string    `json:"current_stage"` // 当前阶段
	Comments     string    `json:"comments"`
	Timestamp    time.Time `json:"timestamp"`
}

// 询价工作流信号名称常量
const (
	SignalNameInquiryApproval = "inquiry_approval_signal"
	SignalNameInquiryRollback = "inquiry_rollback_signal"
)

// InquiryApprovalResult 询价审批结果
type InquiryApprovalResult struct {
	Action       string `json:"action"` // approve, reject, rollback
	ApproverID   uint   `json:"approver_id"`
	OperatorName string `json:"operator_name"`
	Comments     string `json:"comments"`
}

// 询价审批阶段
const (
	InquiryStageFinanceReview    = "finance_review"    // 财务负责人审批
	InquiryStageEnterpriseReview = "enterprise_review" // 企业负责人审批
	InquiryStageCompleted        = "completed"         // 流程完成
)
