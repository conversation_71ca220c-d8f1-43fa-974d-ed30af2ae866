package workflow

import (
	"time"

	"backend/internal/modules/purchase/constants"
)

// 发货管理工作流名称和信号名称
const (
	// ShipmentWorkflowName 发货管理工作流名称
	ShipmentWorkflowName = "ShipmentWorkflow"

	// ShipmentApprovalSignalName 发货审批信号名称
	ShipmentApprovalSignalName = "ShipmentApprovalSignal"

	// ShipmentRollbackSignalName 发货回退信号名称
	ShipmentRollbackSignalName = "ShipmentRollbackSignal"
)

// ShipmentWorkflowInput 发货管理工作流输入
type ShipmentWorkflowInput struct {
	ShipmentID        uint    `json:"shipment_id"`          // 发货记录ID
	ShipmentNo        string  `json:"shipment_no"`          // 发货通知单号
	RequesterID       uint    `json:"requester_id"`         // 申请人ID
	ContractID        uint    `json:"contract_id"`          // 合同ID
	SupplierID        uint    `json:"supplier_id"`          // 供应商ID
	TotalAmount       float64 `json:"total_amount"`         // 发货总金额
	CompanyID         uint    `json:"company_id"`           // 公司ID
	CompanyName       string  `json:"company_name"`         // 公司名称
	ProjectName       string  `json:"project_name"`         // 项目名称
	BusinessType      string  `json:"business_type"`        // 业务类型
	CompanyEntityType string  `json:"company_entity_type"`  // 公司实体类型
	RequestType       string  `json:"request_type"`         // 采购申请类型
}

// ShipmentApprovalSignal 发货审批信号
type ShipmentApprovalSignal struct {
	ShipmentID   uint      `json:"shipment_id"`   // 发货记录ID
	Action       string    `json:"action"`        // 操作类型：approve/reject
	ApproverID   uint      `json:"approver_id"`   // 审批人ID
	ApproverName string    `json:"approver_name"` // 审批人姓名
	Comments     string    `json:"comments"`      // 审批意见
	CurrentStage string    `json:"current_stage"` // 当前阶段
	ApprovedAt   time.Time `json:"approved_at"`   // 审批时间
}

// ShipmentRollbackSignal 发货回退信号
type ShipmentRollbackSignal struct {
	ShipmentID   uint      `json:"shipment_id"`    // 发货记录ID
	RollbackTo   string    `json:"rollback_to"`    // 回退到的状态
	ApproverID   uint      `json:"approver_id"`    // 操作人ID
	ApproverName string    `json:"approver_name"`  // 操作人姓名
	Comments     string    `json:"comments"`       // 回退原因
	RolledBackAt time.Time `json:"rolled_back_at"` // 回退时间
}

// ShipmentWorkflowState 发货管理工作流状态
type ShipmentWorkflowState struct {
	CurrentStage    string                   `json:"current_stage"`    // 当前阶段
	ApprovalHistory []ShipmentApprovalSignal `json:"approval_history"` // 审批历史
	RollbackHistory []ShipmentRollbackSignal `json:"rollback_history"` // 回退历史
	StartedAt       time.Time                `json:"started_at"`       // 开始时间
	CompletedAt     *time.Time               `json:"completed_at"`     // 完成时间
	Result          string                   `json:"result"`           // 结果：approved/rejected/cancelled
	CanRollback     bool                     `json:"can_rollback"`     // 是否可以回退
	StageHistory    []string                 `json:"stage_history"`    // 阶段历史
	RequestType     string                   `json:"request_type"`     // 采购申请类型
}

// ShipmentWorkflowResult 发货管理工作流结果
type ShipmentWorkflowResult struct {
	ShipmentID   uint      `json:"shipment_id"`    // 发货记录ID
	FinalStatus  string    `json:"final_status"`   // 最终状态
	Result       string    `json:"result"`         // 结果
	CompletedAt  time.Time `json:"completed_at"`   // 完成时间
	ErrorMessage string    `json:"error_message"`  // 错误信息（如果有）
}

// 发货管理工作流活动名称
const (
	// ActivityNameUpdateShipmentStatus 更新发货记录状态活动
	ActivityNameUpdateShipmentStatus = "UpdateShipmentStatusActivity"

	// ActivityNameSendShipmentNotification 发送发货通知活动
	ActivityNameSendShipmentNotification = "SendShipmentNotificationActivity"


)

// UpdateShipmentStatusInput 更新发货记录状态活动输入
type UpdateShipmentStatusInput struct {
	ShipmentID   uint   `json:"shipment_id"`   // 发货记录ID
	Status       string `json:"status"`        // 新状态
	OperatorID   uint   `json:"operator_id"`   // 操作人ID
	OperatorName string `json:"operator_name"` // 操作人姓名
	Action       string `json:"action"`        // 操作类型：approve, reject, rollback
	Comments     string `json:"comments"`      // 备注
}

// SendShipmentNotificationInput 发送发货通知活动输入
type SendShipmentNotificationInput struct {
	ShipmentID   uint     `json:"shipment_id"`   // 发货记录ID
	RecipientIDs []uint   `json:"recipient_ids"` // 接收人ID列表
	Stage        string   `json:"stage"`         // 当前阶段
	Action       string   `json:"action"`        // 动作
	Comments     string   `json:"comments"`      // 备注
}



// getNextShipmentStage 获取下一个发货阶段
func getNextShipmentStage(action, currentStage, requestType string) string {
	// 如果是拒绝操作，直接返回已取消状态
	if action == constants.ActionReject {
		return constants.ShipmentStageCancelled
	}

	// 根据当前阶段和操作类型确定下一个阶段
	switch currentStage {
	case constants.ShipmentStagePurchaseReview:
		return constants.ShipmentStageFinanceReview
	case constants.ShipmentStageFinanceReview:
		return constants.ShipmentStageWarehouseReview
	case constants.ShipmentStageWarehouseReview:
		return constants.ShipmentStageCompleted
	default:
		return currentStage
	}
}

// canShipmentRollback 判断当前发货阶段是否可以回退
func canShipmentRollback(stage string) bool {
	switch stage {
	case constants.ShipmentStageDraft:
		return false // 草稿状态不能回退
	case constants.ShipmentStageCompleted, constants.ShipmentStageCancelled:
		return false // 完成和取消状态不能回退
	default:
		return true
	}
}
