package workflow

import (
	"time"

	"backend/internal/modules/purchase/constants"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// ArrivalWorkflow 到货管理工作流
func ArrivalWorkflow(ctx workflow.Context, input ArrivalWorkflowInput) (*ArrivalWorkflowResult, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("开始到货管理工作流", "ArrivalID", input.ArrivalID, "ArrivalNo", input.ArrivalNo)

	// 初始化工作流状态
	state := &ArrivalWorkflowState{
		CurrentStage:    constants.ArrivalStagePurchaseReview,
		ApprovalHistory: []ArrivalApprovalSignal{},
		RollbackHistory: []ArrivalRollbackSignal{},
		StartedAt:       workflow.Now(ctx),
		Result:          "",
		CanRollback:     false,
		StageHistory:    []string{constants.ArrivalStagePurchaseReview},
		RequestType:     input.RequestType,
	}

	// 设置信号处理
	approvalSignal := workflow.GetSignalChannel(ctx, SignalNameArrivalApproval)
	rollbackSignal := workflow.GetSignalChannel(ctx, SignalNameArrivalRollback)

	// 获取采购申请类型（用于工作流分流）
	requestTypeOutput, err := executeGetArrivalRequestTypeActivity(ctx, GetArrivalRequestTypeInput{
		ArrivalID: input.ArrivalID,
	})
	if err != nil {
		logger.Error("获取采购申请类型失败", "error", err)
		// 使用输入中的默认值
	} else {
		state.RequestType = requestTypeOutput.RequestType
	}

	logger.Info("到货记录采购申请类型", "RequestType", state.RequestType)

	// 开始审批流程
	workflowCompleted := false
	for !workflowCompleted {
		logger.Info("当前审批阶段", "Stage", state.CurrentStage)

		// 发送通知
		err := executeSendArrivalNotificationActivity(ctx, SendArrivalNotificationInput{
			ArrivalID:    input.ArrivalID,
			RecipientIDs: []uint{}, // 审批人列表将在活动中根据阶段动态获取
			Stage:        state.CurrentStage,
			Action:       constants.ActionSubmit, // 使用常量替代中文
			Comments:     "请及时处理到货审批",
		})
		if err != nil {
			logger.Error("发送通知失败", "error", err)
			// 继续执行，不因通知失败而中断流程
		}

		// 等待审批信号
		selector := workflow.NewSelector(ctx)

		// 审批信号处理
		selector.AddReceive(approvalSignal, func(c workflow.ReceiveChannel, more bool) {
			var signal ArrivalApprovalSignal
			c.Receive(ctx, &signal)
			logger.Info("收到审批信号", "Action", signal.Action, "Stage", signal.CurrentStage)

			// 记录审批历史
			state.ApprovalHistory = append(state.ApprovalHistory, signal)

			// 更新状态
			err := executeUpdateArrivalStatusActivity(ctx, UpdateArrivalStatusInput{
				ArrivalID:    input.ArrivalID,
				Status:       getNextStage(signal.Action, state.CurrentStage, state.RequestType),
				OperatorID:   signal.ApproverID,
				OperatorName: "", // 将在活动中通过用户服务获取
				Action:       signal.Action,
				Comments:     signal.Comments,
			})
			if err != nil {
				logger.Error("更新到货记录状态失败", "error", err)
				return
			}

			// 处理审批结果
			switch signal.Action {
			case constants.ActionApprove:
				nextStage := getNextStage(signal.Action, state.CurrentStage, state.RequestType)
				if nextStage == constants.ArrivalStageCompleted {
					// 审批完成
					state.CurrentStage = nextStage
					state.Result = "approved"
					completedTime := workflow.Now(ctx)
					state.CompletedAt = &completedTime
					logger.Info("到货管理审批完成")

					// 发送审批完成通知
					err := executeSendArrivalNotificationActivity(ctx, SendArrivalNotificationInput{
						ArrivalID:    input.ArrivalID,
						RecipientIDs: []uint{}, // 审批人列表将在活动中根据阶段动态获取
						Stage:        nextStage,
						Action:       constants.ActionApprove, // 使用审批通过常量
						Comments:     signal.Comments,
					})
					if err != nil {
						logger.Error("发送审批完成通知失败", "error", err)
						// 继续执行，不因通知失败而中断流程
					}

					workflowCompleted = true
				} else {
					// 进入下一阶段
					state.CurrentStage = nextStage
					state.StageHistory = append(state.StageHistory, nextStage)
					state.CanRollback = true
				}
			case constants.ActionReject:
				// 审批被拒绝
				state.CurrentStage = constants.ArrivalStageCancelled
				state.Result = "rejected"
				completedTime := workflow.Now(ctx)
				state.CompletedAt = &completedTime
				logger.Info("到货管理审批被拒绝")

				// 发送审批拒绝通知
				err := executeSendArrivalNotificationActivity(ctx, SendArrivalNotificationInput{
					ArrivalID:    input.ArrivalID,
					RecipientIDs: []uint{}, // 审批人列表将在活动中根据阶段动态获取
					Stage:        constants.ArrivalStageCancelled,
					Action:       constants.ActionReject, // 使用拒绝常量
					Comments:     signal.Comments,
				})
				if err != nil {
					logger.Error("发送审批拒绝通知失败", "error", err)
					// 继续执行，不因通知失败而中断流程
				}

				workflowCompleted = true
			}
		})

		// 回退信号处理
		selector.AddReceive(rollbackSignal, func(c workflow.ReceiveChannel, more bool) {
			var signal ArrivalRollbackSignal
			c.Receive(ctx, &signal)
			logger.Info("收到回退信号", "RollbackTo", signal.RollbackTo)

			if !state.CanRollback {
				logger.Warn("当前状态不允许回退")
				return
			}

			// 记录回退历史
			state.RollbackHistory = append(state.RollbackHistory, signal)

			// 更新状态
			err := executeUpdateArrivalStatusActivity(ctx, UpdateArrivalStatusInput{
				ArrivalID:    input.ArrivalID,
				Status:       signal.RollbackTo,
				OperatorID:   signal.ApproverID,
				OperatorName: "", // 将在活动中通过用户服务获取
				Action:       constants.ActionRollback,
				Comments:     signal.Comments,
			})
			if err != nil {
				logger.Error("回退到货记录状态失败", "error", err)
				return
			}

			// 更新当前阶段
			state.CurrentStage = signal.RollbackTo
			state.StageHistory = append(state.StageHistory, signal.RollbackTo)
		})

		// 执行选择器
		selector.Select(ctx)
	}

	// 返回结果
	result := &ArrivalWorkflowResult{
		ArrivalID:   input.ArrivalID,
		FinalStatus: state.CurrentStage,
		Result:      state.Result,
		CompletedAt: workflow.Now(ctx),
	}

	logger.Info("到货管理工作流完成", "Result", result.Result, "FinalStatus", result.FinalStatus)
	return result, nil
}

// executeUpdateArrivalStatusActivity 执行更新到货记录状态活动
func executeUpdateArrivalStatusActivity(ctx workflow.Context, input UpdateArrivalStatusInput) error {
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 5,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	return workflow.ExecuteActivity(ctx, ActivityNameUpdateArrivalStatus, input).Get(ctx, nil)
}

// executeSendArrivalNotificationActivity 执行发送到货通知活动
func executeSendArrivalNotificationActivity(ctx workflow.Context, input SendArrivalNotificationInput) error {
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 2,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 2,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	return workflow.ExecuteActivity(ctx, ActivityNameSendArrivalNotification, input).Get(ctx, nil)
}

// executeGetArrivalRequestTypeActivity 执行获取到货记录的采购申请类型活动
func executeGetArrivalRequestTypeActivity(ctx workflow.Context, input GetArrivalRequestTypeInput) (*GetArrivalRequestTypeOutput, error) {
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 2,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts: 2,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	var result GetArrivalRequestTypeOutput
	err := workflow.ExecuteActivity(ctx, ActivityNameGetArrivalRequestType, input).Get(ctx, &result)
	return &result, err
}

// getNextStage 根据当前阶段和动作获取下一阶段
func getNextStage(action, currentStage, requestType string) string {
	if action == constants.ActionReject {
		return constants.ArrivalStageCancelled
	}

	switch currentStage {
	case constants.ArrivalStagePurchaseReview:
		// 根据采购申请类型分流
		switch requestType {
		case "服务器":
			return constants.ArrivalStageServerAdminReview
		case "网络设备":
			return constants.ArrivalStageNetworkAdminReview
		default:
			return constants.ArrivalStageAssetAdminReview // 其他类型到资产管理员审批
		}
	case constants.ArrivalStageServerAdminReview,
		constants.ArrivalStageNetworkAdminReview,
		constants.ArrivalStageAssetAdminReview:
		return constants.ArrivalStageCompleted
	default:
		return constants.ArrivalStageCompleted
	}
}
