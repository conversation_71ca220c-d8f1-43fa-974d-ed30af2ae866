package workflow

import (
	"fmt"
	"time"

	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/workflow/activity/inquiry"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// PurchaseInquiryApprovalWorkflow 采购询价审批工作流
func PurchaseInquiryApprovalWorkflow(ctx workflow.Context, input inquiry.PurchaseInquiryWorkflowInput) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("采购询价审批工作流开始", "inquiryID", input.InquiryID, "inquiryNo", input.InquiryNo)

	// 初始化工作流状态
	state := PurchaseInquiryWorkflowState{
		CurrentStage:    InquiryStageFinanceReview,
		ApprovalHistory: []InquiryApprovalSignal{},
		RollbackHistory: []InquiryRollbackSignal{},
		StartedAt:       workflow.Now(ctx),
		CanRollback:     false, // 第一个阶段不能回退
		StageHistory:    []string{InquiryStageFinanceReview},
	}

	// 注册状态查询处理器
	err := workflow.SetQueryHandler(ctx, "getInquiryWorkflowState", func() (PurchaseInquiryWorkflowState, error) {
		return state, nil
	})
	if err != nil {
		logger.Error("注册状态查询处理器失败", "error", err)
		return err
	}

	// 设置活动选项
	inquiryOptions := workflow.ActivityOptions{
		StartToCloseTimeout: 5 * time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Minute,
			MaximumAttempts:    5,
		},
	}
	inquiryCtx := workflow.WithActivityOptions(ctx, inquiryOptions)

	// 发送工作流开始通知 - 待审批通知
	err = workflow.ExecuteActivity(inquiryCtx, inquiry.SendFeishuInquiryNotificationActivity,
		input, "submitted", "财务负责人", "", "", "", "").Get(ctx, nil)
	if err != nil {
		logger.Error("发送询价工作流开始通知失败", "error", err)
		// 通知失败不影响流程继续
	}

	// 设置审批超时
	const approvalTimeout = 7 * 24 * time.Hour // 7天

	// 开始审批流程循环
	for {
		var err error

		switch state.CurrentStage {
		case InquiryStageFinanceReview:
			// 同步工作流状态到数据库
			err = workflow.ExecuteActivity(inquiryCtx, inquiry.UpdateInquiryStatusActivity,
				input.InquiryID, "finance_review", input.RequesterID, "", "进入财务负责人审批阶段", constants.ActionSubmit).Get(ctx, nil)
			if err != nil {
				logger.Error("更新数据库状态失败", "error", err, "stage", state.CurrentStage)
			}

			// 财务负责人审批阶段的通知在工作流开始时已发送，不再重复发送

			approvalResult, err := handleInquiryApprovalStageWithRollback(ctx, InquiryStageFinanceReview, approvalTimeout, &state, input)
			if err != nil {
				logger.Error("财务负责人审批阶段出错", "error", err)
				return err
			}

			switch approvalResult.Action {
			case constants.ActionApprove:
				moveToNextInquiryStage(ctx, &state, InquiryStageEnterpriseReview)

				// 同步工作流状态到数据库 - 使用审批信号中的信息
				err = workflow.ExecuteActivity(inquiryCtx, inquiry.UpdateInquiryStatusActivity,
					input.InquiryID, "enterprise_review", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", InquiryStageEnterpriseReview)
				}

				// 发送企业负责人审批阶段通知
				err = workflow.ExecuteActivity(inquiryCtx, inquiry.SendFeishuInquiryNotificationActivity,
					input, "submitted", "企业负责人", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送阶段变更通知失败", "error", err)
					// 通知失败不影响流程继续
				}
			case constants.ActionReject:
				// 拒绝申请
				state.CurrentStage = InquiryStageCompleted
				state.Result = "rejected"
				now := workflow.Now(ctx)
				state.CompletedAt = &now

				// 同步工作流状态到数据库
				err = workflow.ExecuteActivity(inquiryCtx, inquiry.UpdateInquiryStatusActivity,
					input.InquiryID, "rejected", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionReject).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err)
				}

				// 发送询价被拒绝通知
				err = workflow.ExecuteActivity(inquiryCtx, inquiry.SendFeishuInquiryNotificationActivity,
					input, "rejected", "", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送询价拒绝通知失败", "error", err)
				}

				logger.Info("采购询价被拒绝", "inquiryID", input.InquiryID, "reason", approvalResult.Comments)
				return nil
			default:
				logger.Error("未知的审批动作", "action", approvalResult.Action)
				return fmt.Errorf("未知的审批动作: %s", approvalResult.Action)
			}

		case InquiryStageEnterpriseReview:
			approvalResult, err := handleInquiryApprovalStageWithRollback(ctx, InquiryStageEnterpriseReview, approvalTimeout, &state, input)
			if err != nil {
				logger.Error("企业负责人审批阶段出错", "error", err)
				return err
			}

			switch approvalResult.Action {
			case constants.ActionApprove:
				// 审批完成
				moveToNextInquiryStage(ctx, &state, InquiryStageCompleted)
				state.Result = "approved"
				now := workflow.Now(ctx)
				state.CompletedAt = &now

				// 同步工作流状态到数据库
				err = workflow.ExecuteActivity(inquiryCtx, inquiry.UpdateInquiryStatusActivity,
					input.InquiryID, "approved", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err)
				}

				// 发送询价审批完成通知 - 此处是最终审批通过，正确发送approved通知
				err = workflow.ExecuteActivity(inquiryCtx, inquiry.SendFeishuInquiryNotificationActivity,
					input, "approved", "", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送询价审批完成通知失败", "error", err)
				}

				logger.Info("采购询价审批完成", "inquiryID", input.InquiryID)
				return nil

			case constants.ActionReject:
				// 拒绝申请
				state.CurrentStage = InquiryStageCompleted
				state.Result = "rejected"
				now := workflow.Now(ctx)
				state.CompletedAt = &now

				// 同步工作流状态到数据库
				err = workflow.ExecuteActivity(inquiryCtx, inquiry.UpdateInquiryStatusActivity,
					input.InquiryID, "rejected", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionReject).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err)
				}

				// 发送询价被拒绝通知
				err = workflow.ExecuteActivity(inquiryCtx, inquiry.SendFeishuInquiryNotificationActivity,
					input, "rejected", "", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送询价拒绝通知失败", "error", err)
				}

				logger.Info("采购询价被拒绝", "inquiryID", input.InquiryID, "reason", approvalResult.Comments)
				return nil

			case constants.ActionRollback:
				// 处理回退情况 - 已经在handleInquiryApprovalStageWithRollback中进行了状态更新
				logger.Info("采购询价已回退", "inquiryID", input.InquiryID, "to", state.CurrentStage)

				// 发送回退通知
				err = workflow.ExecuteActivity(inquiryCtx, inquiry.SendFeishuInquiryNotificationActivity,
					input, "rollback", state.CurrentStage, "", approvalResult.Comments, "enterprise_review", "财务负责人审批").Get(ctx, nil)
				if err != nil {
					logger.Error("发送询价回退通知失败", "error", err)
					// 通知失败不影响流程继续
				}

				// 继续循环，将在下一次迭代中处理回退后的阶段
				continue

			default:
				logger.Error("未知的审批动作", "action", approvalResult.Action)
				return fmt.Errorf("未知的审批动作: %s", approvalResult.Action)
			}

		case InquiryStageCompleted:
			logger.Info("采购询价审批流程已完成", "inquiryID", input.InquiryID, "result", state.Result)
			return nil

		default:
			logger.Error("未知的审批阶段", "stage", state.CurrentStage)
			return fmt.Errorf("未知的审批阶段: %s", state.CurrentStage)
		}
	}
}

// handleInquiryApprovalStageWithRollback 处理询价审批阶段（支持回退）
func handleInquiryApprovalStageWithRollback(ctx workflow.Context, stage string, timeout time.Duration, state *PurchaseInquiryWorkflowState, input inquiry.PurchaseInquiryWorkflowInput) (*InquiryApprovalResult, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("开始处理询价审批阶段", "stage", stage)

	// 创建审批信号通道
	approvalChan := workflow.GetSignalChannel(ctx, SignalNameInquiryApproval)
	rollbackChan := workflow.GetSignalChannel(ctx, SignalNameInquiryRollback)

	// 创建超时计时器
	timerCancellation := workflow.NewTimer(ctx, timeout)

	// 选择器，用于等待信号或超时
	selector := workflow.NewSelector(ctx)
	var approvalSignal InquiryApprovalSignal
	var rollbackSignal InquiryRollbackSignal
	var timerFired bool
	var signalType = "" // 明确初始化

	// 添加审批信号处理
	selector.AddReceive(approvalChan, func(c workflow.ReceiveChannel, more bool) {
		c.Receive(ctx, &approvalSignal)
		signalType = constants.ActionApprove
		logger.Info("收到询价审批信号", "approverID", approvalSignal.ApproverID, "action", approvalSignal.Action)
	})

	// 添加回退信号处理
	selector.AddReceive(rollbackChan, func(c workflow.ReceiveChannel, more bool) {
		c.Receive(ctx, &rollbackSignal)
		signalType = constants.ActionRollback
		logger.Info("收到询价回退信号", "approverID", rollbackSignal.ApproverID, "rollbackTo", rollbackSignal.RollbackTo)
	})

	// 添加超时处理
	selector.AddFuture(timerCancellation, func(f workflow.Future) {
		timerFired = true
		signalType = constants.ActionRollback
		logger.Info("询价审批超时", "stage", stage)
	})

	// 等待信号或超时
	selector.Select(ctx)

	logger.Info("处理信号完成", "signalType", signalType, "timerFired", timerFired)

	// 如果超时，返回错误
	if timerFired {
		return nil, fmt.Errorf("询价审批阶段 %s 超时", stage)
	}

	// 处理审批信号
	if signalType == constants.ActionApprove {
		// 记录审批历史
		approvalSignal.CurrentStage = stage
		state.ApprovalHistory = append(state.ApprovalHistory, approvalSignal)

		// 获取用户真实姓名
		operatorName := GetUserRealName(ctx, approvalSignal.ApproverID)

		// 标准化动作，确保与常量匹配
		action := approvalSignal.Action
		switch action {
		case constants.ActionApprove:
			action = constants.ActionApprove
		case constants.ActionReject:
			action = constants.ActionReject
		}

		// 返回审批结果
		return &InquiryApprovalResult{
			Action:       action,
			ApproverID:   approvalSignal.ApproverID,
			OperatorName: operatorName,
			Comments:     approvalSignal.Comments,
		}, nil
	}

	// 处理回退信号
	if signalType == constants.ActionRollback {
		// 检查是否可以回退
		if !canInquiryRollbackToStage(state, rollbackSignal.RollbackTo) {
			logger.Error("无法回退到指定阶段", "currentStage", stage, "rollbackTo", rollbackSignal.RollbackTo)
			return nil, fmt.Errorf("无法回退到指定阶段: %s", rollbackSignal.RollbackTo)
		}

		// 记录回退历史
		state.RollbackHistory = append(state.RollbackHistory, rollbackSignal)

		// 执行回退
		rollbackToInquiryStage(ctx, state, rollbackSignal.RollbackTo)

		// 获取用户真实姓名
		operatorName := GetUserRealName(ctx, rollbackSignal.ApproverID)

		// 设置回退的活动选项
		rollbackOptions := workflow.ActivityOptions{
			StartToCloseTimeout: 5 * time.Minute,
		}
		rollbackCtx := workflow.WithActivityOptions(ctx, rollbackOptions)

		// 同步回退状态到数据库
		err := workflow.ExecuteActivity(rollbackCtx, inquiry.UpdateInquiryStatusActivity,
			input.InquiryID, rollbackSignal.RollbackTo, rollbackSignal.ApproverID, operatorName, rollbackSignal.Comments, constants.ActionRollback).Get(ctx, nil)
		if err != nil {
			logger.Error("更新回退状态失败", "error", err)
			// 数据库更新失败不影响工作流状态更新
		}

		// 返回回退结果
		return &InquiryApprovalResult{
			Action:       constants.ActionRollback,
			ApproverID:   rollbackSignal.ApproverID,
			OperatorName: operatorName,
			Comments:     rollbackSignal.Comments,
		}, nil
	}

	// 如果没有收到任何有效信号，返回错误
	return nil, fmt.Errorf("询价审批阶段处理异常: 未收到有效信号, signalType=%s, timerFired=%v", signalType, timerFired)
}

// moveToNextInquiryStage 移动到下一个询价审批阶段
func moveToNextInquiryStage(ctx workflow.Context, state *PurchaseInquiryWorkflowState, nextStage string) {
	logger := workflow.GetLogger(ctx)
	logger.Info("询价审批阶段转换", "from", state.CurrentStage, "to", nextStage)

	state.CurrentStage = nextStage
	state.StageHistory = append(state.StageHistory, nextStage)

	// 根据阶段设置是否可以回退
	switch nextStage {
	case InquiryStageFinanceReview:
		state.CanRollback = false // 第一阶段不能回退
	case InquiryStageEnterpriseReview:
		state.CanRollback = true // 可以回退到财务审批
	case InquiryStageCompleted:
		state.CanRollback = false // 已完成不能回退
	}
}

// canInquiryRollbackToStage 检查是否可以回退到指定阶段
func canInquiryRollbackToStage(state *PurchaseInquiryWorkflowState, targetStage string) bool {
	// 第一个阶段不能回退
	if state.CurrentStage == InquiryStageFinanceReview {
		return false
	}

	// 检查目标阶段是否在历史中
	for _, stage := range state.StageHistory {
		if stage == targetStage {
			return true
		}
	}

	return false
}

// rollbackToInquiryStage 回退到指定阶段
func rollbackToInquiryStage(ctx workflow.Context, state *PurchaseInquiryWorkflowState, targetStage string) {
	logger := workflow.GetLogger(ctx)
	logger.Info("询价回退到指定阶段", "from", state.CurrentStage, "to", targetStage)

	// 更新当前阶段
	state.CurrentStage = targetStage

	// 更新回退权限
	state.CanRollback = targetStage != InquiryStageFinanceReview

	// 清理目标阶段之后的历史记录
	newStageHistory := []string{}
	for _, stage := range state.StageHistory {
		newStageHistory = append(newStageHistory, stage)
		if stage == targetStage {
			break
		}
	}
	state.StageHistory = newStageHistory
}
