package workflow

import (
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/workflow/activity/payment"
	"fmt"
	"time"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// PaymentRequestWorkflow 付款申请工作流
func PaymentRequestWorkflow(ctx workflow.Context, input PaymentRequestWorkflowInput) error {
	// 设置工作流选项
	options := workflow.ActivityOptions{
		StartToCloseTimeout: 10 * time.Second,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Minute,
			MaximumAttempts:    3,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, options)

	// 创建日志记录器
	logger := workflow.GetLogger(ctx)
	logger.Info("付款申请工作流开始", "paymentID", input.PaymentID, "requesterID", input.RequesterID)

	// 初始化工作流状态
	state := PaymentRequestWorkflowState{
		CurrentStage:      constants.PaymentStagePurchaseSupplyChainReview, // 从采购与供应链负责人审批开始
		CompanyEntityType: input.CompanyEntityType,                         // 公司主体类型
		ApprovalHistory:   []PaymentApprovalSignal{},
		RollbackHistory:   []PaymentRollbackSignal{},
		StartedAt:         workflow.Now(ctx),
		CanRollback:       false, // 第一个阶段不能回退
		StageHistory:      []string{constants.PaymentStagePurchaseSupplyChainReview},
	}

	// 创建审批上下文，可以被取消
	paymentCtx, cancel := workflow.WithCancel(ctx)
	defer cancel()

	// 注册审批信号将在处理函数中完成

	// 转换为活动需要的输入类型
	activityInput := payment.PaymentRequestWorkflowInput{
		PaymentID:    input.PaymentID,
		PaymentNo:    input.PaymentNo,
		RequesterID:  input.RequesterID,
		ContractID:   input.ContractID,
		TotalAmount:  input.TotalAmount,
		CompanyID:    input.CompanyID,
		CompanyName:  input.CompanyName,
		ProjectName:  input.ProjectName,
		BusinessType: input.BusinessType,
	}

	// 发送工作流开始通知 - 待审批通知
	err := workflow.ExecuteActivity(paymentCtx, payment.SendFeishuPaymentNotificationActivity,
		activityInput, "submitted", constants.GetPaymentStageDisplayName(state.CurrentStage), "", "", "", "").Get(ctx, nil)
	if err != nil {
		logger.Error("发送付款申请工作流开始通知失败", "error", err)
		// 通知失败不影响流程继续
	}

	// 开始审批流程
	for {
		// 根据当前阶段设置超时时间
		approvalTimeout := GetPaymentStageTimeout(state.CurrentStage)

		// 同步工作流状态到数据库
		err := workflow.ExecuteActivity(paymentCtx, "UpdatePaymentStatusActivity",
			input.PaymentID, state.CurrentStage, input.RequesterID, "", "进入"+constants.GetPaymentStageDisplayName(state.CurrentStage)+"阶段", constants.ActionSubmit).Get(ctx, nil)
		if err != nil {
			logger.Error("更新数据库状态失败", "error", err, "stage", state.CurrentStage)
		}

		// 如果当前阶段是已完成，则结束工作流
		if state.CurrentStage == constants.PaymentStageCompleted ||
			state.CurrentStage == constants.PaymentStageRejected ||
			state.CurrentStage == constants.PaymentStageCancelled {
			now := workflow.Now(ctx)
			state.CompletedAt = &now
			logger.Info("付款申请工作流完成", "paymentID", input.PaymentID, "finalStage", state.CurrentStage)
			return nil
		}



		// 处理当前阶段的审批
		approvalResult, err := handlePaymentApprovalStageWithRollback(ctx, state.CurrentStage, approvalTimeout, &state, activityInput)
		if err != nil {
			logger.Error("处理审批阶段失败", "error", err, "stage", state.CurrentStage)
			// 超时或其他错误，工作流暂停，等待人工干预
			return err
		}

		// 根据审批结果更新状态
		switch approvalResult.Action {
		case constants.ActionApprove:
			// 移动到下一个阶段
			nextStage := GetNextPaymentStage(state.CurrentStage, state.CompanyEntityType)
			state.CurrentStage = nextStage
			state.StageHistory = append(state.StageHistory, nextStage)
			state.CanRollback = true

			// 同步工作流状态到数据库
			err = workflow.ExecuteActivity(paymentCtx, "UpdatePaymentStatusActivity",
				input.PaymentID, nextStage, approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
			if err != nil {
				logger.Error("更新数据库状态失败", "error", err, "stage", nextStage)
			}

			// 根据下一阶段决定发送什么类型的通知
			if nextStage == constants.PaymentStageCompleted {
				// 流程完成，发送审批通过通知
				err = workflow.ExecuteActivity(paymentCtx, payment.SendFeishuPaymentNotificationActivity,
					activityInput, "approved", nextStage, approvalResult.OperatorName, approvalResult.Comments,
					state.StageHistory[len(state.StageHistory)-2], "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送审批完成通知失败", "error", err)
					// 通知失败不影响流程继续
				}
			} else {
				// 进入下一阶段，发送待审批通知
				err = workflow.ExecuteActivity(paymentCtx, payment.SendFeishuPaymentNotificationActivity,
					activityInput, "submitted", constants.GetPaymentStageDisplayName(nextStage), "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送阶段变更通知失败", "error", err)
					// 通知失败不影响流程继续
				}
			}

		case constants.ActionReject:
			// 更新状态为已拒绝
			state.CurrentStage = constants.PaymentStageRejected
			state.StageHistory = append(state.StageHistory, constants.PaymentStageRejected)
			state.CanRollback = false

			// 同步工作流状态到数据库
			err = workflow.ExecuteActivity(paymentCtx, "UpdatePaymentStatusActivity",
				input.PaymentID, constants.PaymentStageRejected, approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionReject).Get(ctx, nil)
			if err != nil {
				logger.Error("更新数据库状态失败", "error", err, "stage", constants.PaymentStageRejected)
			}

			// 发送拒绝的飞书通知
			err = workflow.ExecuteActivity(paymentCtx, payment.SendFeishuPaymentNotificationActivity,
				activityInput, "rejected", constants.PaymentStageRejected, approvalResult.OperatorName, approvalResult.Comments,
				state.CurrentStage, "").Get(ctx, nil)
			if err != nil {
				logger.Error("发送拒绝飞书通知失败", "error", err)
				// 通知失败不影响流程继续
			}
		}
	}
}

// handlePaymentApprovalStageWithRollback 处理付款申请审批阶段，支持回退
func handlePaymentApprovalStageWithRollback(
	ctx workflow.Context,
	stage string,
	timeout time.Duration,
	state *PaymentRequestWorkflowState,
	input payment.PaymentRequestWorkflowInput,
) (*PaymentApprovalResult, error) {
	// 创建日志记录器
	logger := workflow.GetLogger(ctx)

	// 创建选择器
	selector := workflow.NewSelector(ctx)

	// 创建审批和回退信号通道
	approvalChan := workflow.GetSignalChannel(ctx, SignalNamePaymentApproval)
	rollbackChan := workflow.GetSignalChannel(ctx, SignalNamePaymentRollback)

	// 创建超时计时器
	timer := workflow.NewTimer(ctx, timeout)

	// 创建信号变量
	var approvalSignal PaymentApprovalSignal
	var rollbackSignal PaymentRollbackSignal
	var signalType string

	// 添加审批信号处理
	selector.AddReceive(approvalChan, func(c workflow.ReceiveChannel, more bool) {
		c.Receive(ctx, &approvalSignal)
		signalType = "approval" // 标记为审批信号类型
		logger.Info("收到付款申请审批信号", "approverID", approvalSignal.ApproverID, "action", approvalSignal.Action)
	})

	// 添加回退信号处理
	selector.AddReceive(rollbackChan, func(c workflow.ReceiveChannel, more bool) {
		c.Receive(ctx, &rollbackSignal)
		signalType = "rollback" // 标记为回退信号类型
		logger.Info("收到付款申请回退信号", "approverID", rollbackSignal.ApproverID, "rollbackTo", rollbackSignal.RollbackTo)
	})

	// 添加超时处理
	selector.AddFuture(timer, func(f workflow.Future) {
		signalType = "timeout" // 标记为超时类型
		logger.Info("付款申请审批超时", "stage", stage)
	})

	// 等待信号或超时
	selector.Select(ctx)

	// 处理审批信号
	if signalType == "approval" {
		// 记录审批历史
		approvalSignal.CurrentStage = stage
		state.ApprovalHistory = append(state.ApprovalHistory, approvalSignal)

		// 获取用户真实姓名
		operatorName := GetUserRealName(ctx, approvalSignal.ApproverID)

		// 标准化动作，确保与常量匹配
		action := approvalSignal.Action
		switch action {
		case constants.ActionApprove:
			action = constants.ActionApprove
		case constants.ActionReject:
			action = constants.ActionReject
		default:
			// 如果动作不匹配，记录错误并返回
			logger.Error("审批信号中包含未知动作", "action", action, "stage", stage)
			return nil, fmt.Errorf("未知的审批动作: %s", action)
		}

		// 返回审批结果
		return &PaymentApprovalResult{
			Action:       action,
			ApproverID:   approvalSignal.ApproverID,
			OperatorName: operatorName,
			Comments:     approvalSignal.Comments,
		}, nil
	}

	// 处理回退信号
	if signalType == "rollback" {
		// 检查是否可以回退
		if !state.CanRollback {
			logger.Error("当前阶段不允许回退", "stage", stage)
			return nil, fmt.Errorf("当前阶段不允许回退: %s", stage)
		}

		// 验证回退目标阶段
		if !IsValidPaymentRollbackStage(rollbackSignal.RollbackTo) {
			logger.Error("无效的回退目标阶段", "rollbackTo", rollbackSignal.RollbackTo)
			return nil, fmt.Errorf("无效的回退目标阶段: %s", rollbackSignal.RollbackTo)
		}

		// 记录回退历史
		state.RollbackHistory = append(state.RollbackHistory, rollbackSignal)

		// 更新当前阶段为回退目标阶段
		state.CurrentStage = rollbackSignal.RollbackTo
		state.StageHistory = append(state.StageHistory, rollbackSignal.RollbackTo)

		// 获取用户真实姓名
		operatorName := getUserRealName(ctx, rollbackSignal.ApproverID)

		// 同步工作流状态到数据库
		paymentCtx := workflow.WithActivityOptions(ctx, workflow.ActivityOptions{
			StartToCloseTimeout: 30 * time.Second,
		})
		err := workflow.ExecuteActivity(paymentCtx, "UpdatePaymentStatusActivity",
			input.PaymentID, rollbackSignal.RollbackTo, rollbackSignal.ApproverID, operatorName, rollbackSignal.Comments, constants.ActionRollback).Get(ctx, nil)
		if err != nil {
			logger.Error("更新数据库状态失败", "error", err, "stage", rollbackSignal.RollbackTo)
		}

		// 发送回退的飞书通知
		err = workflow.ExecuteActivity(paymentCtx, payment.SendFeishuPaymentNotificationActivity,
			input, "rollback", rollbackSignal.RollbackTo, operatorName, rollbackSignal.Comments,
			stage, "").Get(ctx, nil)
		if err != nil {
			logger.Error("发送回退飞书通知失败", "error", err)
			// 通知失败不影响流程继续
		}

		// 返回回退结果
		return &PaymentApprovalResult{
			Action:       constants.ActionRollback,
			ApproverID:   rollbackSignal.ApproverID,
			OperatorName: operatorName,
			Comments:     rollbackSignal.Comments,
		}, nil
	}

	// 处理超时 - 取消自动审批，返回错误
	if signalType == "timeout" {
		logger.Error("付款申请审批超时", "stage", stage)
		return nil, fmt.Errorf("付款申请审批阶段 %s 超时", stage)
	}

	return nil, fmt.Errorf("未知的信号类型: %s", signalType)
}

// getUserRealName 获取用户真实姓名
func getUserRealName(ctx workflow.Context, userID uint) string {
	if userID == 0 {
		return "系统"
	}

	// 通过Activity获取用户真实姓名
	activityCtx := workflow.WithActivityOptions(ctx, workflow.ActivityOptions{
		StartToCloseTimeout: 10 * time.Second,
	})

	var realName string
	err := workflow.ExecuteActivity(activityCtx, "GetUserRealNameActivity", userID).Get(ctx, &realName)
	if err != nil {
		return fmt.Sprintf("用户%d", userID)
	}

	if realName == "" {
		return fmt.Sprintf("用户%d", userID)
	}

	return realName
}
