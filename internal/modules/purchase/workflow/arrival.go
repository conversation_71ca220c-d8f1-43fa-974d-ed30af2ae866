package workflow

import (
	"backend/internal/modules/purchase/constants"
	"time"
)

// 到货管理工作流常量
const (
	// ArrivalTaskQueue 到货管理工作流任务队列
	ArrivalTaskQueue = constants.TaskQueueArrival

	// ArrivalWorkflowName 到货管理工作流名称
	ArrivalWorkflowName = "ArrivalWorkflow"

	// SignalNameArrivalApproval 到货管理审批信号
	SignalNameArrivalApproval = "arrival_approval_signal"

	// SignalNameArrivalRollback 到货管理回退信号
	SignalNameArrivalRollback = "arrival_rollback_signal"
)

// ArrivalWorkflowInput 到货管理工作流输入
type ArrivalWorkflowInput struct {
	ArrivalID         uint    `json:"arrival_id"`          // 到货记录ID
	ArrivalNo         string  `json:"arrival_no"`          // 到货通知单号
	RequesterID       uint    `json:"requester_id"`        // 申请人ID
	ContractID        uint    `json:"contract_id"`         // 合同ID
	SupplierID        uint    `json:"supplier_id"`         // 供应商ID
	TotalAmount       float64 `json:"total_amount"`        // 到货总金额
	CompanyID         uint    `json:"company_id"`          // 公司ID
	CompanyName       string  `json:"company_name"`        // 公司名称
	ProjectName       string  `json:"project_name"`        // 项目名称
	BusinessType      string  `json:"business_type"`       // 业务类型
	CompanyEntityType string  `json:"company_entity_type"` // 公司主体类型
	RequestType       string  `json:"request_type"`        // 采购申请类型（用于工作流分流）
}

// ArrivalApprovalSignal 到货管理审批信号
type ArrivalApprovalSignal struct {
	ApproverID   uint      `json:"approver_id"`   // 审批人ID
	Action       string    `json:"action"`        // 动作：approve, reject
	Comments     string    `json:"comments"`      // 审批意见
	CurrentStage string    `json:"current_stage"` // 当前阶段
	Timestamp    time.Time `json:"timestamp"`     // 时间戳
}

// ArrivalRollbackSignal 到货管理回退信号
type ArrivalRollbackSignal struct {
	ApproverID   uint      `json:"approver_id"`   // 审批人ID
	RollbackTo   string    `json:"rollback_to"`   // 回退到的阶段
	Comments     string    `json:"comments"`      // 回退意见
	CurrentStage string    `json:"current_stage"` // 当前阶段
	Timestamp    time.Time `json:"timestamp"`     // 时间戳
}

// ArrivalWorkflowState 到货管理工作流状态
type ArrivalWorkflowState struct {
	CurrentStage    string                  `json:"current_stage"`    // 当前阶段
	ApprovalHistory []ArrivalApprovalSignal `json:"approval_history"` // 审批历史
	RollbackHistory []ArrivalRollbackSignal `json:"rollback_history"` // 回退历史
	StartedAt       time.Time               `json:"started_at"`       // 开始时间
	CompletedAt     *time.Time              `json:"completed_at"`     // 完成时间
	Result          string                  `json:"result"`           // 结果：approved, rejected, cancelled
	CanRollback     bool                    `json:"can_rollback"`     // 是否可以回退
	StageHistory    []string                `json:"stage_history"`    // 阶段历史，用于回退
	RequestType     string                  `json:"request_type"`     // 采购申请类型
}

// ArrivalWorkflowResult 到货管理工作流结果
type ArrivalWorkflowResult struct {
	ArrivalID    uint      `json:"arrival_id"`    // 到货记录ID
	FinalStatus  string    `json:"final_status"`  // 最终状态
	Result       string    `json:"result"`        // 结果
	CompletedAt  time.Time `json:"completed_at"`  // 完成时间
	ErrorMessage string    `json:"error_message"` // 错误信息（如果有）
}

// 到货管理工作流活动名称
const (
	// ActivityNameUpdateArrivalStatus 更新到货记录状态活动
	ActivityNameUpdateArrivalStatus = "UpdateArrivalStatusActivity"

	// ActivityNameSendArrivalNotification 发送到货通知活动
	ActivityNameSendArrivalNotification = "SendFeishuArrivalNotificationActivity"

	// ActivityNameGetArrivalRequestType 获取到货记录的采购申请类型活动
	ActivityNameGetArrivalRequestType = "GetArrivalRequestTypeActivity"
)

// UpdateArrivalStatusInput 更新到货记录状态活动输入
type UpdateArrivalStatusInput struct {
	ArrivalID    uint   `json:"arrival_id"`    // 到货记录ID
	Status       string `json:"status"`        // 新状态
	OperatorID   uint   `json:"operator_id"`   // 操作人ID
	OperatorName string `json:"operator_name"` // 操作人姓名
	Action       string `json:"action"`        // 操作类型：approve, reject, rollback
	Comments     string `json:"comments"`      // 备注
}

// SendArrivalNotificationInput 发送到货通知活动输入
type SendArrivalNotificationInput struct {
	ArrivalID    uint     `json:"arrival_id"`    // 到货记录ID
	RecipientIDs []uint   `json:"recipient_ids"` // 接收人ID列表
	Stage        string   `json:"stage"`         // 当前阶段
	Action       string   `json:"action"`        // 动作
	Comments     string   `json:"comments"`      // 备注
}

// GetArrivalRequestTypeInput 获取到货记录的采购申请类型活动输入
type GetArrivalRequestTypeInput struct {
	ArrivalID uint `json:"arrival_id"` // 到货记录ID
}

// GetArrivalRequestTypeOutput 获取到货记录的采购申请类型活动输出
type GetArrivalRequestTypeOutput struct {
	RequestType string `json:"request_type"` // 采购申请类型
	Error       string `json:"error"`        // 错误信息
}
