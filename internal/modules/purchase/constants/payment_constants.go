package constants

import "time"

// 付款申请状态常量
const (
	// PaymentStageDraft 草稿状态
	PaymentStageDraft = "draft"

	// PaymentStagePurchaseSupplyChainReview 采购与供应链负责人审批阶段
	PaymentStagePurchaseSupplyChainReview = "purchase_review"

	// PaymentStageFinanceManagerReview 财务负责人审批阶段
	PaymentStageFinanceManagerReview = "finance_manager_review"

	// PaymentStageEnterpriseManagerReview 企业负责人审批阶段
	PaymentStageEnterpriseManagerReview = "enterprise_manager_review"

	// PaymentStageCyberReview 赛博付款申请复核
	PaymentStageCyberReview = "cyber_payment_review"

	// PaymentStageOtherEntityReview 其他主体付款申请复核
	PaymentStageOtherEntityReview = "other_entity_payment_review"

	// PaymentStageFundManagerReview 资金负责人审批阶段
	PaymentStageFundManagerReview = "fund_manager_review"

	// PaymentStageCyberHandler 赛博付款经办（第二次公司主体判断后）
	PaymentStageCyberHandler = "cyber_payment_handler"

	// PaymentStageOtherEntityHandler 其他主体付款经办（第二次公司主体判断后）
	PaymentStageOtherEntityHandler = "other_entity_payment_handler"

	// PaymentStageCyberReview2 赛博复核2（赛博经办后的第一个复核）
	PaymentStageCyberReview2 = "cyber_review_2"

	// PaymentStageFinalReview 最终复核（所有主体的最后阶段）
	PaymentStageFinalReview = "final_review"

	// PaymentStageCompleted 已完成
	PaymentStageCompleted = "completed"

	// PaymentStageRejected 已拒绝
	PaymentStageRejected = "rejected"

	// PaymentStageCancelled 已取消
	PaymentStageCancelled = "cancelled"
)

// 公司主体类型常量
const (
	// CompanyEntityTypeCyber 赛博主体
	CompanyEntityTypeCyber = "cyber"

	// CompanyEntityTypeOther 其他主体
	CompanyEntityTypeOther = "other"
)

// 付款申请状态显示名称映射
var paymentStageNames = map[string]string{
	PaymentStageDraft:                     "草稿",
	PaymentStagePurchaseSupplyChainReview: "采购与供应链负责人审批",
	PaymentStageFinanceManagerReview:      "财务负责人审批",
	PaymentStageEnterpriseManagerReview:   "企业负责人审批",
	PaymentStageCyberReview:               "赛博付款申请复核",
	PaymentStageOtherEntityReview:         "其他主体付款申请复核",
	PaymentStageFundManagerReview:         "资金负责人审批",
	PaymentStageCyberHandler:              "赛博付款经办",
	PaymentStageOtherEntityHandler:        "其他主体付款经办",
	PaymentStageCyberReview2:              "赛博复核2",
	PaymentStageFinalReview:               "最终复核",
	PaymentStageCompleted:                 "已完成",
	PaymentStageRejected:                  "已拒绝",
	PaymentStageCancelled:                 "已取消",
}

// GetPaymentStageDisplayName 获取付款申请状态显示名称
func GetPaymentStageDisplayName(stage string) string {
	if displayName, exists := paymentStageNames[stage]; exists {
		return displayName
	}
	return stage
}

// GetPaymentStageList 获取付款申请有效状态列表
func GetPaymentStageList() []string {
	return []string{
		PaymentStagePurchaseSupplyChainReview,
		PaymentStageFinanceManagerReview,
		PaymentStageEnterpriseManagerReview,
		PaymentStageCyberReview,
		PaymentStageOtherEntityReview,
		PaymentStageFundManagerReview,
		PaymentStageCyberHandler,
		PaymentStageOtherEntityHandler,
		PaymentStageCyberReview2,
		PaymentStageFinalReview,
	}
}

// DetermineCompanyEntityType 根据公司名称判断公司主体类型
func DetermineCompanyEntityType(companyName string) string {
	// 根据公司名称判断是否为赛博主体
	// 这里可以根据实际业务规则进行调整
	if companyName == "" {
		return CompanyEntityTypeOther
	}

	// 赛博主体公司名称列表
	cyberCompanies := []string{
		"华为",
		"华为技术有限公司",
		"华为科技",
		"华为网络",
		"华为信息",
		"华为系统",
		"华为数据",
		"华为软件",
		"华为智能",
		"华为云",
		"华为安全",
		"华为通信",
		"华为电子",
		"华为互联",
		"华为创新",
		"赛博",
		"赛博科技",
		"赛博网络",
		"赛博信息",
		"赛博技术",
		"赛博系统",
		"赛博数据",
		"赛博软件",
		"赛博智能",
		"赛博云",
		"赛博安全",
		"赛博通信",
		"赛博电子",
		"赛博互联",
		"赛博创新",
	}

	// 判断公司名称是否在赛博主体列表中
	for _, cyberCompany := range cyberCompanies {
		if companyName == cyberCompany {
			return CompanyEntityTypeCyber
		}
	}

	// 默认为其他主体
	return CompanyEntityTypeOther
}

// GetPaymentStageTimeout 获取付款申请阶段超时时间
func GetPaymentStageTimeout(stage string) time.Duration {
	// 根据不同阶段设置不同的超时时间
	switch stage {
	case PaymentStagePurchaseSupplyChainReview:
		return 24 * time.Hour // 采购与供应链审批：24小时
	case PaymentStageFinanceManagerReview:
		return 48 * time.Hour // 财务负责人审批：48小时
	case PaymentStageEnterpriseManagerReview:
		return 48 * time.Hour // 企业负责人审批：48小时
	case PaymentStageCyberReview:
		return 24 * time.Hour // 赛博付款申请复核：24小时
	case PaymentStageOtherEntityReview:
		return 24 * time.Hour // 其他主体付款申请复核：24小时
	case PaymentStageFundManagerReview:
		return 48 * time.Hour // 资金负责人审批：48小时
	case PaymentStageCyberHandler:
		return 24 * time.Hour // 赛博付款经办：24小时
	case PaymentStageOtherEntityHandler:
		return 24 * time.Hour // 其他主体付款经办：24小时
	case PaymentStageCyberReview2:
		return 24 * time.Hour // 赛博复核2：24小时
	case PaymentStageFinalReview:
		return 24 * time.Hour // 最终复核：24小时
	default:
		return 24 * time.Hour // 默认：24小时
	}
}

// IsValidPaymentRollbackStage 检查是否为有效的回退目标阶段
func IsValidPaymentRollbackStage(stage string) bool {
	// 有效的回退目标阶段（不包括草稿阶段）
	validStages := []string{
		PaymentStagePurchaseSupplyChainReview,
		PaymentStageFinanceManagerReview,
		PaymentStageEnterpriseManagerReview,
		PaymentStageCyberReview,
		PaymentStageOtherEntityReview,
		PaymentStageFundManagerReview,
		PaymentStageCyberHandler,
		PaymentStageOtherEntityHandler,
		PaymentStageCyberReview2,
		PaymentStageFinalReview,
	}

	for _, validStage := range validStages {
		if stage == validStage {
			return true
		}
	}
	return false
}

// CanPaymentStageRollback 检查付款申请状态是否可以回退
func CanPaymentStageRollback(currentStage string) bool {
	// 草稿、完成状态不能回退
	if currentStage == PaymentStageDraft ||
		currentStage == PaymentStageCompleted ||
		currentStage == PaymentStageRejected ||
		currentStage == PaymentStageCancelled {
		return false
	}

	// 采购负责人阶段不允许回退（第一个审批阶段）
	if currentStage == PaymentStagePurchaseSupplyChainReview {
		return false
	}

	return true
}
