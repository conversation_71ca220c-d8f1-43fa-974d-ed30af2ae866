package constants

// 采购申请状态常量
const (
	// StatusDraft 草稿状态
	StatusDraft = "draft"

	// StatusProjectManagerReview 项目经理审批中
	StatusProjectManagerReview = "project_manager_review"

	// StatusPurchaseManagerReview 采购负责人审批中
	StatusPurchaseManagerReview = "purchase_manager_review"

	// StatusApproved 已批准
	StatusApproved = "approved"

	// StatusRejected 已拒绝
	StatusRejected = "rejected"

	// StatusCancelled 已取消
	StatusCancelled = "cancelled"
)

// 采购询比价状态常量
const (
	// InquiryStatusDraft 询价草稿
	InquiryStatusDraft = "draft"

	// InquiryStatusFinanceReview 财务负责人审批中
	InquiryStatusFinanceReview = "finance_review"

	// InquiryStatusEnterpriseReview 企业负责人审批中
	InquiryStatusEnterpriseReview = "enterprise_review"

	// InquiryStatusApproved 已批准
	InquiryStatusApproved = "approved"

	// InquiryStatusRejected 已拒绝
	InquiryStatusRejected = "rejected"

	// InquiryStatusCancelled 已取消
	InquiryStatusCancelled = "cancelled"

	// InquiryStatusCompleted 已完成
	InquiryStatusCompleted = "completed"

	// InquiryStatusQuoted 已报价
	InquiryStatusQuoted = "quoted"
)

// 采购合同状态常量
const (
	// ContractStatusDraft 合同草稿
	ContractStatusDraft = "contract_draft"

	// ContractStatusLegalReview 法务审核中
	ContractStatusLegalReview = "contract_legal_review"

	// ContractStatusFinanceReview 财务审核中
	ContractStatusFinanceReview = "contract_finance_review"

	// ContractStatusSigned 已签署
	ContractStatusSigned = "contract_signed"

	// ContractStatusExecuting 执行中
	ContractStatusExecuting = "contract_executing"

	// ContractStatusCompleted 已完成
	ContractStatusCompleted = "contract_completed"

	// ContractStatusTerminated 已终止
	ContractStatusTerminated = "contract_terminated"
)

// 采购订单状态常量
const (
	// OrderStatusPending 待确认
	OrderStatusPending = "order_pending"

	// OrderStatusConfirmed 已确认
	OrderStatusConfirmed = "order_confirmed"

	// OrderStatusProducing 生产中
	OrderStatusProducing = "order_producing"

	// OrderStatusShipping 配送中
	OrderStatusShipping = "order_shipping"

	// OrderStatusDelivered 已交付
	OrderStatusDelivered = "order_delivered"

	// OrderStatusCompleted 已完成
	OrderStatusCompleted = "order_completed"

	// OrderStatusCancelled 已取消
	OrderStatusCancelled = "order_cancelled"
)

// GetStatusDisplayName 获取状态显示名称
func GetStatusDisplayName(status string) string {
	statusMap := map[string]string{
		// 采购申请状态
		StatusDraft:                 "草稿",
		StatusProjectManagerReview:  "项目经理审批中",
		StatusPurchaseManagerReview: "采购经理审批中",
		StatusApproved:              "已批准",
		StatusRejected:              "已拒绝",
		StatusCancelled:             "已取消",

		// 采购合同状态
		ContractStatusDraft:         "合同草稿",
		ContractStatusLegalReview:   "法务审核中",
		ContractStatusFinanceReview: "财务审核中",
		ContractStatusSigned:        "已签署",
		ContractStatusExecuting:     "执行中",
		ContractStatusCompleted:     "合同已完成",
		ContractStatusTerminated:    "已终止",

		// 采购订单状态
		OrderStatusPending:   "待确认",
		OrderStatusConfirmed: "已确认",
		OrderStatusProducing: "生产中",
		OrderStatusShipping:  "配送中",
		OrderStatusDelivered: "已交付",
		OrderStatusCompleted: "订单已完成",
		OrderStatusCancelled: "订单已取消",
	}

	if displayName, exists := statusMap[status]; exists {
		return displayName
	}
	return status
}

// GetPurchaseRequestStatuses 获取所有采购申请状态
func GetPurchaseRequestStatuses() []string {
	return []string{
		StatusDraft,
		StatusProjectManagerReview,
		StatusPurchaseManagerReview,
		StatusApproved,
		StatusRejected,
		StatusCancelled,
	}
}

// GetContractStatuses 获取所有采购合同状态
func GetContractStatuses() []string {
	return []string{
		ContractStatusDraft,
		ContractStatusLegalReview,
		ContractStatusFinanceReview,
		ContractStatusSigned,
		ContractStatusExecuting,
		ContractStatusCompleted,
		ContractStatusTerminated,
	}
}

// GetOrderStatuses 获取所有采购订单状态
func GetOrderStatuses() []string {
	return []string{
		OrderStatusPending,
		OrderStatusConfirmed,
		OrderStatusProducing,
		OrderStatusShipping,
		OrderStatusDelivered,
		OrderStatusCompleted,
		OrderStatusCancelled,
	}
}

// IsValidPurchaseRequestStatus 验证采购申请状态是否有效
func IsValidPurchaseRequestStatus(status string) bool {
	validStatuses := GetPurchaseRequestStatuses()
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// IsValidContractStatus 验证采购合同状态是否有效
func IsValidContractStatus(status string) bool {
	validStatuses := GetContractStatuses()
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// IsValidOrderStatus 验证采购订单状态是否有效
func IsValidOrderStatus(status string) bool {
	validStatuses := GetOrderStatuses()
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}
