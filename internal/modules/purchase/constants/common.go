package constants

// 工作流队列名称常量
const (
	// TaskQueuePurchaseRequest 采购申请工作流队列
	TaskQueuePurchaseRequest = "PURCHASE_REQUEST_TASK_QUEUE"

	// TaskQueuePurchaseInquiry 采购询价工作流队列
	TaskQueuePurchaseInquiry = "PURCHASE_INQUIRY_TASK_QUEUE"

	// TaskQueuePurchaseContract 采购合同工作流队列
	TaskQueuePurchaseContract = "PURCHASE_CONTRACT_TASK_QUEUE"

	// TaskQueuePaymentRequest 付款申请工作流队列
	TaskQueuePaymentRequest = "PAYMENT_REQUEST_TASK_QUEUE"

	// TaskQueueArrival 到货管理工作流队列
	TaskQueueArrival = "ARRIVAL_TASK_QUEUE"

	// TaskQueueShipment 发货管理工作流队列
	TaskQueueShipment = "SHIPMENT_TASK_QUEUE"
)

// 紧急程度常量
const (
	// UrgencyLevelLow 低
	UrgencyLevelLow = "low"

	// UrgencyLevelMedium 中
	UrgencyLevelMedium = "medium"

	// UrgencyLevelHigh 高
	UrgencyLevelHigh = "high"

	// UrgencyLevelUrgent 紧急
	UrgencyLevelUrgent = "urgent"

	// UrgencyLevelCritical 极紧急
	UrgencyLevelCritical = "critical"
)

// 申请类型常量
const (
	// RequestTypeOfficeSupplies 办公用品
	RequestTypeOfficeSupplies = "office_supplies"

	// RequestTypeEquipment 设备采购
	RequestTypeEquipment = "equipment"

	// RequestTypeSoftware 软件采购
	RequestTypeSoftware = "software"

	// RequestTypeService 服务采购
	RequestTypeService = "service"

	// RequestTypeMaintenance 维保采购
	RequestTypeMaintenance = "maintenance"

	// RequestTypeConsultancy 咨询服务
	RequestTypeConsultancy = "consultancy"

	// RequestTypeTraining 培训服务
	RequestTypeTraining = "training"

	// RequestTypeMarketing 营销推广
	RequestTypeMarketing = "marketing"
)

// 供应商类型常量
const (
	// SupplierTypeInternal 内部供应商
	SupplierTypeInternal = "internal"

	// SupplierTypeExternal 外部供应商
	SupplierTypeExternal = "external"

	// SupplierTypePreferred 优选供应商
	SupplierTypePreferred = "preferred"

	// SupplierTypeStrategic 战略供应商
	SupplierTypeStrategic = "strategic"

	// SupplierTypeGeneral 一般供应商
	SupplierTypeGeneral = "general"
)

// 供应商状态常量
const (
	// SupplierStatusActive 活跃
	SupplierStatusActive = "active"

	// SupplierStatusInactive 非活跃
	SupplierStatusInactive = "inactive"

	// SupplierStatusSuspended 暂停
	SupplierStatusSuspended = "suspended"

	// SupplierStatusBlacklisted 黑名单
	SupplierStatusBlacklisted = "blacklisted"
)

// 货币类型常量
const (
	// CurrencyCNY 人民币
	CurrencyCNY = "CNY"

	// CurrencyUSD 美元
	CurrencyUSD = "USD"

	// CurrencyEUR 欧元
	CurrencyEUR = "EUR"

	// CurrencyJPY 日元
	CurrencyJPY = "JPY"

	// CurrencyGBP 英镑
	CurrencyGBP = "GBP"
)

// 分页默认值
const (
	// DefaultPageSize 默认分页大小
	DefaultPageSize = 20

	// MaxPageSize 最大分页大小
	MaxPageSize = 100

	// MinPageSize 最小分页大小
	MinPageSize = 1

	// DefaultPage 默认页码
	DefaultPage = 1
)

// GetUrgencyLevelDisplayName 获取紧急程度显示名称
func GetUrgencyLevelDisplayName(level string) string {
	levelMap := map[string]string{
		UrgencyLevelLow:      "低",
		UrgencyLevelMedium:   "中",
		UrgencyLevelHigh:     "高",
		UrgencyLevelUrgent:   "紧急",
		UrgencyLevelCritical: "极紧急",
	}

	if displayName, exists := levelMap[level]; exists {
		return displayName
	}
	return level
}

// GetRequestTypeDisplayName 获取申请类型显示名称
func GetRequestTypeDisplayName(requestType string) string {
	typeMap := map[string]string{
		RequestTypeOfficeSupplies: "办公用品",
		RequestTypeEquipment:      "设备采购",
		RequestTypeSoftware:       "软件采购",
		RequestTypeService:        "服务采购",
		RequestTypeMaintenance:    "维保采购",
		RequestTypeConsultancy:    "咨询服务",
		RequestTypeTraining:       "培训服务",
		RequestTypeMarketing:      "营销推广",
	}

	if displayName, exists := typeMap[requestType]; exists {
		return displayName
	}
	return requestType
}

// GetSupplierTypeDisplayName 获取供应商类型显示名称
func GetSupplierTypeDisplayName(supplierType string) string {
	typeMap := map[string]string{
		SupplierTypeInternal:  "内部供应商",
		SupplierTypeExternal:  "外部供应商",
		SupplierTypePreferred: "优选供应商",
		SupplierTypeStrategic: "战略供应商",
		SupplierTypeGeneral:   "一般供应商",
	}

	if displayName, exists := typeMap[supplierType]; exists {
		return displayName
	}
	return supplierType
}

// GetSupplierStatusDisplayName 获取供应商状态显示名称
func GetSupplierStatusDisplayName(status string) string {
	statusMap := map[string]string{
		SupplierStatusActive:      "活跃",
		SupplierStatusInactive:    "非活跃",
		SupplierStatusSuspended:   "暂停",
		SupplierStatusBlacklisted: "黑名单",
	}

	if displayName, exists := statusMap[status]; exists {
		return displayName
	}
	return status
}

// GetAllUrgencyLevels 获取所有紧急程度
func GetAllUrgencyLevels() []string {
	return []string{
		UrgencyLevelLow,
		UrgencyLevelMedium,
		UrgencyLevelHigh,
		UrgencyLevelUrgent,
		UrgencyLevelCritical,
	}
}

// GetAllRequestTypes 获取所有申请类型
func GetAllRequestTypes() []string {
	return []string{
		RequestTypeOfficeSupplies,
		RequestTypeEquipment,
		RequestTypeSoftware,
		RequestTypeService,
		RequestTypeMaintenance,
		RequestTypeConsultancy,
		RequestTypeTraining,
		RequestTypeMarketing,
	}
}

// GetAllSupplierTypes 获取所有供应商类型
func GetAllSupplierTypes() []string {
	return []string{
		SupplierTypeInternal,
		SupplierTypeExternal,
		SupplierTypePreferred,
		SupplierTypeStrategic,
		SupplierTypeGeneral,
	}
}

// GetAllSupplierStatuses 获取所有供应商状态
func GetAllSupplierStatuses() []string {
	return []string{
		SupplierStatusActive,
		SupplierStatusInactive,
		SupplierStatusSuspended,
		SupplierStatusBlacklisted,
	}
}

// GetAllCurrencies 获取所有货币类型
func GetAllCurrencies() []string {
	return []string{
		CurrencyCNY,
		CurrencyUSD,
		CurrencyEUR,
		CurrencyJPY,
		CurrencyGBP,
	}
}
