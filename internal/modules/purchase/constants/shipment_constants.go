package constants

// 发货管理状态常量
const (
	// ShipmentStageDraft 草稿状态
	ShipmentStageDraft = "draft"

	// ShipmentStagePurchaseReview 采购负责人审批阶段
	ShipmentStagePurchaseReview = "purchase_review"

	// ShipmentStageFinanceReview 财务负责人审批阶段
	ShipmentStageFinanceReview = "finance_review"

	// ShipmentStageWarehouseReview 仓库管理员审批阶段
	ShipmentStageWarehouseReview = "warehouse_review"

	// ShipmentStageCompleted 完成阶段
	ShipmentStageCompleted = "completed"

	// ShipmentStageCancelled 已取消
	ShipmentStageCancelled = "cancelled"
)

// GetShipmentStageDisplayName 获取发货管理状态显示名称
func GetShipmentStageDisplayName(stage string) string {
	stageMap := map[string]string{
		ShipmentStageDraft:           "草稿",
		ShipmentStagePurchaseReview:  "采购负责人审批中",
		ShipmentStageFinanceReview:   "财务负责人审批中",
		ShipmentStageWarehouseReview: "仓库管理员审批中",
		ShipmentStageCompleted:       "已完成",
		ShipmentStageCancelled:       "已取消",
	}

	if displayName, exists := stageMap[stage]; exists {
		return displayName
	}
	return stage
}

// GetAllShipmentStages 获取所有发货管理状态
func GetAllShipmentStages() []string {
	return []string{
		ShipmentStageDraft,
		ShipmentStagePurchaseReview,
		ShipmentStageFinanceReview,
		ShipmentStageWarehouseReview,
		ShipmentStageCompleted,
		ShipmentStageCancelled,
	}
}

// IsValidShipmentStage 验证发货管理状态是否有效
func IsValidShipmentStage(stage string) bool {
	validStages := GetAllShipmentStages()
	for _, validStage := range validStages {
		if stage == validStage {
			return true
		}
	}
	return false
}

// GetNextShipmentStage 获取下一个发货管理状态
func GetNextShipmentStage(currentStage string, action string) string {
	if action == ActionReject {
		return ShipmentStageCancelled
	}

	switch currentStage {
	case ShipmentStageDraft:
		return ShipmentStagePurchaseReview
	case ShipmentStagePurchaseReview:
		return ShipmentStageFinanceReview
	case ShipmentStageFinanceReview:
		return ShipmentStageWarehouseReview
	case ShipmentStageWarehouseReview:
		return ShipmentStageCompleted
	default:
		return currentStage
	}
}

// CanRollbackFromStage 判断是否可以从当前阶段回退
func CanRollbackFromStage(stage string) bool {
	switch stage {
	case ShipmentStageDraft:
		return false // 草稿状态不能回退
	case ShipmentStageCompleted, ShipmentStageCancelled:
		return false // 完成和取消状态不能回退
	default:
		return true
	}
}

// GetPreviousShipmentStage 获取上一个发货管理状态
func GetPreviousShipmentStage(currentStage string) string {
	switch currentStage {
	case ShipmentStageFinanceReview:
		return ShipmentStagePurchaseReview
	case ShipmentStageWarehouseReview:
		return ShipmentStageFinanceReview
	case ShipmentStageCompleted:
		return ShipmentStageWarehouseReview
	default:
		return ShipmentStageDraft
	}
}



// 工作流结果常量
const (
	WorkflowResultApproved  = "approved"
	WorkflowResultRejected  = "rejected"
	WorkflowResultCancelled = "cancelled"
)
