package constants

// 操作动作常量
const (
	// ActionCreate 创建
	ActionCreate = "create"

	// ActionSubmit 提交
	ActionSubmit = "submit"

	// ActionApprove 批准
	ActionApprove = "approve"

	// ActionReject 拒绝
	ActionReject = "reject"

	// ActionCancel 取消
	ActionCancel = "cancel"

	// ActionRollback 回退
	ActionRollback = "rollback"

	// ActionUpdate 更新
	ActionUpdate = "update"

	// ActionDelete 删除
	ActionDelete = "delete"

	// ActionSign 签署
	ActionSign = "sign"

	// ActionExecute 执行
	ActionExecute = "execute"

	// ActionComplete 完成
	ActionComplete = "complete"

	// ActionTerminate 终止
	ActionTerminate = "terminate"

	// ActionSuspend 暂停
	ActionSuspend = "suspend"

	// ActionResume 恢复
	ActionResume = "resume"
)

// 采购申请特有动作
const (
	// ActionPurchaseRequestEdit 编辑采购申请
	ActionPurchaseRequestEdit = "purchase_request_edit"

	// ActionPurchaseRequestSubmit 提交采购申请
	ActionPurchaseRequestSubmit = "purchase_request_submit"

	// ActionPurchaseRequestApprove 批准采购申请
	ActionPurchaseRequestApprove = "purchase_request_approve"

	// ActionPurchaseRequestReject 拒绝采购申请
	ActionPurchaseRequestReject = "purchase_request_reject"
)

// 采购询比价特有动作
const (
	// ActionInquiryCreate 创建采购询价
	ActionInquiryCreate = "inquiry_create"

	// ActionInquirySubmit 提交采购询价
	ActionInquirySubmit = "inquiry_submit"

	// ActionInquiryApprove 批准采购询价
	ActionInquiryApprove = "inquiry_approve"

	// ActionInquiryReject 拒绝采购询价
	ActionInquiryReject = "inquiry_reject"

	// ActionInquiryCancel 取消采购询价
	ActionInquiryCancel = "inquiry_cancel"

	// ActionInquiryRollback 回退采购询价
	ActionInquiryRollback = "inquiry_rollback"

	// ActionInquiryQuote 供应商报价
	ActionInquiryQuote = "inquiry_quote"
)

// 采购合同特有动作
const (
	// ActionContractDraft 起草合同
	ActionContractDraft = "contract_draft"

	// ActionContractReview 审核合同
	ActionContractReview = "contract_review"

	// ActionContractSign 签署合同
	ActionContractSign = "contract_sign"

	// ActionContractExecute 执行合同
	ActionContractExecute = "contract_execute"

	// ActionContractAmend 修改合同
	ActionContractAmend = "contract_amend"
)

// 采购订单特有动作
const (
	// ActionOrderConfirm 确认订单
	ActionOrderConfirm = "order_confirm"

	// ActionOrderProduce 开始生产
	ActionOrderProduce = "order_produce"

	// ActionOrderShip 发货
	ActionOrderShip = "order_ship"

	// ActionOrderDeliver 交付
	ActionOrderDeliver = "order_deliver"

	// ActionOrderReceive 收货
	ActionOrderReceive = "order_receive"
)

// GetActionDisplayName 获取动作显示名称
func GetActionDisplayName(action string) string {
	actionMap := map[string]string{
		// 通用动作
		ActionCreate:    "创建",
		ActionSubmit:    "提交",
		ActionApprove:   "批准",
		ActionReject:    "拒绝",
		ActionCancel:    "取消",
		ActionRollback:  "回退",
		ActionUpdate:    "更新",
		ActionDelete:    "删除",
		ActionSign:      "签署",
		ActionExecute:   "执行",
		ActionComplete:  "完成",
		ActionTerminate: "终止",
		ActionSuspend:   "暂停",
		ActionResume:    "恢复",

		// 采购申请动作
		ActionPurchaseRequestEdit:    "编辑采购申请",
		ActionPurchaseRequestSubmit:  "提交采购申请",
		ActionPurchaseRequestApprove: "批准采购申请",
		ActionPurchaseRequestReject:  "拒绝采购申请",

		// 采购询比价动作
		ActionInquiryCreate:   "创建采购询价",
		ActionInquirySubmit:   "提交采购询价",
		ActionInquiryApprove:  "批准采购询价",
		ActionInquiryReject:   "拒绝采购询价",
		ActionInquiryCancel:   "取消采购询价",
		ActionInquiryRollback: "回退采购询价",
		ActionInquiryQuote:    "供应商报价",

		// 采购合同动作
		ActionContractDraft:   "起草合同",
		ActionContractReview:  "审核合同",
		ActionContractSign:    "签署合同",
		ActionContractExecute: "执行合同",
		ActionContractAmend:   "修改合同",

		// 采购订单动作
		ActionOrderConfirm: "确认订单",
		ActionOrderProduce: "开始生产",
		ActionOrderShip:    "发货",
		ActionOrderDeliver: "交付",
		ActionOrderReceive: "收货",
	}

	if displayName, exists := actionMap[action]; exists {
		return displayName
	}
	return action
}

// GetCommonActions 获取通用操作动作
func GetCommonActions() []string {
	return []string{
		ActionCreate,
		ActionSubmit,
		ActionApprove,
		ActionReject,
		ActionCancel,
		ActionRollback,
		ActionUpdate,
		ActionDelete,
		ActionSign,
		ActionExecute,
		ActionComplete,
		ActionTerminate,
		ActionSuspend,
		ActionResume,
	}
}

// GetPurchaseRequestActions 获取采购申请相关动作
func GetPurchaseRequestActions() []string {
	return []string{
		ActionCreate,
		ActionSubmit,
		ActionApprove,
		ActionReject,
		ActionCancel,
		ActionUpdate,
		ActionDelete,
		ActionPurchaseRequestEdit,
		ActionPurchaseRequestSubmit,
		ActionPurchaseRequestApprove,
		ActionPurchaseRequestReject,
	}
}

// GetInquiryActions 获取采购询比价相关动作
func GetInquiryActions() []string {
	return []string{
		ActionInquiryCreate,
		ActionInquirySubmit,
		ActionInquiryApprove,
		ActionInquiryReject,
		ActionInquiryCancel,
		ActionInquiryRollback,
		ActionInquiryQuote,
		// 添加通用动作
		ActionCreate,
		ActionSubmit,
		ActionApprove,
		ActionReject,
		ActionCancel,
		ActionRollback,
		ActionUpdate,
	}
}

// GetContractActions 获取采购合同相关动作
func GetContractActions() []string {
	return []string{
		ActionCreate,
		ActionSubmit,
		ActionApprove,
		ActionReject,
		ActionCancel,
		ActionSign,
		ActionExecute,
		ActionComplete,
		ActionTerminate,
		ActionContractDraft,
		ActionContractReview,
		ActionContractSign,
		ActionContractExecute,
		ActionContractAmend,
	}
}

// GetOrderActions 获取采购订单相关动作
func GetOrderActions() []string {
	return []string{
		ActionCreate,
		ActionSubmit,
		ActionApprove,
		ActionCancel,
		ActionComplete,
		ActionOrderConfirm,
		ActionOrderProduce,
		ActionOrderShip,
		ActionOrderDeliver,
		ActionOrderReceive,
	}
}

// IsValidAction 验证动作是否有效
func IsValidAction(action string) bool {
	allActions := append(GetCommonActions(), GetPurchaseRequestActions()...)
	//allActions = append(allActions, GetInquiryActions()...)
	allActions = append(allActions, GetContractActions()...)
	allActions = append(allActions, GetOrderActions()...)

	// 去重
	actionSet := make(map[string]bool)
	for _, act := range allActions {
		actionSet[act] = true
	}

	// 直接添加询价必需的通用操作
	actionSet[ActionApprove] = true
	actionSet[ActionReject] = true
	actionSet[ActionRollback] = true

	return actionSet[action]
}

// IsValidPurchaseRequestAction 验证采购申请动作是否有效
func IsValidPurchaseRequestAction(action string) bool {
	validActions := GetPurchaseRequestActions()
	for _, validAction := range validActions {
		if action == validAction {
			return true
		}
	}
	return false
}

// IsValidInquiryAction 验证采购询比价动作是否有效
func IsValidInquiryAction(action string) bool {
	validActions := GetInquiryActions()
	for _, validAction := range validActions {
		if action == validAction {
			return true
		}
	}
	return false
}

// IsValidContractAction 验证采购合同动作是否有效
func IsValidContractAction(action string) bool {
	validActions := GetContractActions()
	for _, validAction := range validActions {
		if action == validAction {
			return true
		}
	}
	return false
}

// IsValidOrderAction 验证采购订单动作是否有效
func IsValidOrderAction(action string) bool {
	validActions := GetOrderActions()
	for _, validAction := range validActions {
		if action == validAction {
			return true
		}
	}
	return false
}
