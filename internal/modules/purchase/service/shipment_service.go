package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"backend/internal/common/utils"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"backend/internal/modules/purchase/workflow"
	userService "backend/internal/modules/user/service"

	"go.temporal.io/sdk/client"
	"gorm.io/gorm"
)

// 错误定义
var (
	ErrShipmentNotFound         = errors.New("发货记录不存在")
	ErrShipmentContractNotFound = errors.New("合同不存在")
	ErrInvalidShipmentNo        = errors.New("无效的发货通知单号")
	ErrShipmentNoExists         = errors.New("发货通知单号已存在")
	ErrInvalidShipmentStatus    = errors.New("无效的状态")
	ErrCannotDelete             = errors.New("当前状态不允许删除")
	ErrCannotUpdate             = errors.New("当前状态不允许更新")
	ErrInsufficientStock        = errors.New("发货数量超过可发货数量")
)

// ShipmentService 发货记录服务接口
type ShipmentService interface {
	// Create 创建发货记录
	Create(ctx context.Context, createDTO *dto.CreateShipmentDTO) (*model.Shipment, error)

	// GetByID 根据ID获取发货记录
	GetByID(ctx context.Context, id uint) (*dto.ShipmentDetailDTO, error)

	// GetByShipmentNo 根据发货通知单号获取发货记录
	GetByShipmentNo(ctx context.Context, shipmentNo string) (*dto.ShipmentDetailDTO, error)

	// Update 更新发货记录
	Update(ctx context.Context, id uint, updateDTO *dto.UpdateShipmentDTO, updatedBy uint) error

	// Delete 删除发货记录
	Delete(ctx context.Context, id uint, deletedBy uint) error

	// List 获取发货记录列表
	List(ctx context.Context, query *dto.ShipmentQueryDTO) ([]*dto.ShipmentListDTO, int64, error)

	// Submit 提交发货记录
	Submit(ctx context.Context, id uint, submittedBy uint) error

	// Cancel 取消发货记录
	Cancel(ctx context.Context, id uint, cancelledBy uint) error

	// Approve 审批发货记录
	Approve(ctx context.Context, approvalDTO *dto.ShipmentApprovalDTO, approverID uint) error

	// Rollback 回退发货记录
	Rollback(ctx context.Context, rollbackDTO *dto.ShipmentRollbackDTO, approverID uint) error

	// GetByContractID 根据合同ID获取发货记录列表
	GetByContractID(ctx context.Context, contractID uint) ([]*dto.ShipmentListDTO, error)

	// GetShipmentHistory 获取发货记录历史
	GetShipmentHistory(ctx context.Context, shipmentID uint) ([]*model.PurchaseApprovalHistory, error)

	// GetStatistics 获取发货统计信息
	GetStatistics(ctx context.Context) (*dto.ShipmentStatisticsDTO, error)
}

// shipmentService 发货记录服务实现
type shipmentService struct {
	repo           repository.ShipmentRepository
	contractRepo   repository.PurchaseContractRepository
	supplierRepo   repository.SupplierRepository
	temporalClient client.Client
	userService    userService.IUserService
	userHelper     *UserHelper
}

// NewShipmentService 创建发货记录服务
func NewShipmentService(
	repo repository.ShipmentRepository,
	contractRepo repository.PurchaseContractRepository,
	supplierRepo repository.SupplierRepository,
	temporalClient client.Client,
	userService userService.IUserService,
) ShipmentService {
	return &shipmentService{
		repo:           repo,
		contractRepo:   contractRepo,
		supplierRepo:   supplierRepo,
		temporalClient: temporalClient,
		userService:    userService,
		userHelper:     NewUserHelper(userService),
	}
}

// Create 创建发货记录
func (s *shipmentService) Create(ctx context.Context, createDTO *dto.CreateShipmentDTO) (*model.Shipment, error) {
	// 验证合同是否存在并获取合同信息
	contract, err := s.contractRepo.GetByID(ctx, createDTO.ContractID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrShipmentContractNotFound
		}
		return nil, err
	}

	// 生成发货通知单号
	shipmentNo, err := utils.GenerateUniqueCode(ctx, func(ctx context.Context, code string) (bool, error) {
		return s.repo.IsShipmentNoExists(ctx, code)
	}, utils.CodeGeneratorOptions{
		Prefix:       "SHIP",
		DateFormat:   "20060102150405",
		RandomLength: 4,
		Delimiter:    "",
	})
	if err != nil {
		return nil, fmt.Errorf("生成发货通知单号失败: %w", err)
	}

	// 创建发货记录，直接进入审批阶段
	shipment := &model.Shipment{
		ShipmentNo:          shipmentNo,
		ContractID:          createDTO.ContractID,
		SupplierID:          contract.SupplierID,
		TrackingInfo:        createDTO.TrackingInfo,
		DeliveryAddress:     createDTO.DeliveryAddress,
		ExpectedArrivalDate: createDTO.ExpectedArrivalDate,
		ShipmentNotice:      createDTO.ShipmentNotice,
		Remark:              createDTO.Remark,
		Status:              constants.ShipmentStagePurchaseReview,
		CreatedBy:           &createDTO.CreatedBy,
		CreatedAt:           time.Now(),
	}

	// 创建发货明细
	for _, itemDTO := range createDTO.Items {
		// 验证合同明细是否存在
		contractItem, err := s.contractRepo.GetContractItemByID(ctx, itemDTO.ContractItemID)
		if err != nil {
			return nil, fmt.Errorf("合同明细不存在: %w", err)
		}

		// 计算该合同明细的历史发货统计（不包括本次发货）
		shippedStats, err := s.calculateContractItemShippedStats(ctx, itemDTO.ContractItemID)
		if err != nil {
			return nil, fmt.Errorf("计算合同明细 %d 的发货统计失败: %w", itemDTO.ContractItemID, err)
		}

		// 验证发货数量是否超过可发货数量
		if itemDTO.CurrentShipmentQuantity > contractItem.ContractQuantity {
			return nil, fmt.Errorf("发货数量不能超过合同数量")
		}

		// 计算未发货数量和金额
		unshippedQuantity := contractItem.ContractQuantity - shippedStats.TotalQuantity
		unshippedAmount := contractItem.ContractAmount - shippedStats.TotalAmount

		// 序列化SN号
		var serialNumbersJSON string
		if len(itemDTO.SerialNumbers) > 0 {
			serialNumbersBytes, err := json.Marshal(itemDTO.SerialNumbers)
			if err != nil {
				return nil, fmt.Errorf("序列化SN号失败: %w", err)
			}
			serialNumbersJSON = string(serialNumbersBytes)
		}

		item := model.ShipmentItem{
			ContractItemID:          itemDTO.ContractItemID,
			ShippedQuantity:         shippedStats.TotalQuantity,      // 已发货数量（不包括本次）
			ShippedAmount:           shippedStats.TotalAmount,        // 已发货金额（不包括本次）
			UnshippedQuantity:       unshippedQuantity,               // 未发货数量
			UnshippedAmount:         unshippedAmount,                 // 未发货金额
			CurrentShipmentQuantity: itemDTO.CurrentShipmentQuantity, // 本次发货数量
			CurrentShipmentAmount:   itemDTO.CurrentShipmentAmount,   // 本次发货金额
			SerialNumbers:           serialNumbersJSON,
			Remark:                  itemDTO.Remark,
			CreatedAt:               time.Now(),
		}

		shipment.Items = append(shipment.Items, item)
	}

	// 计算总数量和总金额
	shipment.CalculateTotals()

	// 检查是否完全发货
	isComplete, err := s.checkIfContractFullyShipped(ctx, createDTO.ContractID, shipment.Items)
	if err != nil {
		return nil, fmt.Errorf("检查合同是否完全发货失败: %w", err)
	}
	shipment.IsComplete = isComplete

	// 保存发货记录
	if err := s.repo.Create(ctx, shipment); err != nil {
		return nil, fmt.Errorf("创建发货记录失败: %w", err)
	}

	// 直接使用创建人ID（controller层已从JWT获取）
	operatorID := createDTO.CreatedBy

	// 创建初始历史记录
	if err := s.createInitialHistory(ctx, shipment, operatorID); err != nil {
		log.Printf("创建发货记录历史失败: %v", err)
		// 历史记录创建失败不影响发货记录创建，只记录日志
	}

	// 启动工作流
	if err := s.startShipmentWorkflow(ctx, shipment, operatorID); err != nil {
		log.Printf("启动发货管理工作流失败: %v", err)
		// 工作流启动失败不影响发货记录创建，只记录日志
	}

	return shipment, nil
}

// GetByID 根据ID获取发货记录
func (s *shipmentService) GetByID(ctx context.Context, id uint) (*dto.ShipmentDetailDTO, error) {
	shipment, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrShipmentNotFound
		}
		return nil, err
	}

	return s.convertToDetailDTO(ctx, shipment)
}

// GetByShipmentNo 根据发货通知单号获取发货记录
func (s *shipmentService) GetByShipmentNo(ctx context.Context, shipmentNo string) (*dto.ShipmentDetailDTO, error) {
	shipment, err := s.repo.GetByShipmentNo(ctx, shipmentNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrShipmentNotFound
		}
		return nil, err
	}

	return s.convertToDetailDTO(ctx, shipment)
}

// Update 更新发货记录
func (s *shipmentService) Update(ctx context.Context, id uint, updateDTO *dto.UpdateShipmentDTO, updatedBy uint) error {
	// 获取现有发货记录
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrShipmentNotFound
		}
		return err
	}

	// 发货记录创建后直接进入审批流程，不可更新
	return errors.New("发货记录已进入审批流程，不可更新")
}

// Delete 删除发货记录
func (s *shipmentService) Delete(ctx context.Context, id uint, deletedBy uint) error {
	// 检查发货记录是否存在
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrShipmentNotFound
		}
		return err
	}

	// 发货记录创建后直接进入审批流程，不可删除
	return errors.New("发货记录已进入审批流程，不可删除")
}

// List 获取发货记录列表
func (s *shipmentService) List(ctx context.Context, query *dto.ShipmentQueryDTO) ([]*dto.ShipmentListDTO, int64, error) {
	shipments, total, err := s.repo.List(ctx, query)
	if err != nil {
		return nil, 0, err
	}

	var result []*dto.ShipmentListDTO
	for _, shipment := range shipments {
		listDTO, err := s.convertToListDTO(ctx, shipment)
		if err != nil {
			log.Printf("转换发货记录列表项失败: %v", err)
			continue
		}
		result = append(result, listDTO)
	}

	return result, total, nil
}

// Submit 提交发货记录（已废弃：发货记录创建后直接进入审批流程）
func (s *shipmentService) Submit(ctx context.Context, id uint, submittedBy uint) error {
	// 获取发货记录
	shipment, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrShipmentNotFound
		}
		return err
	}

	// 检查发货记录状态
	if shipment.Status == constants.ShipmentStageCompleted {
		return errors.New("发货记录已完成，无需提交")
	}
	if shipment.Status == constants.ShipmentStageCancelled {
		return errors.New("发货记录已取消，无法提交")
	}

	// 发货记录创建后已自动进入审批流程，此方法仅用于兼容性
	log.Printf("发货记录 %d 已在审批流程中，状态: %s", id, shipment.Status)
	return nil
}

// Cancel 取消发货记录
func (s *shipmentService) Cancel(ctx context.Context, id uint, cancelledBy uint) error {
	// 获取发货记录
	shipment, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrShipmentNotFound
		}
		return err
	}

	// 已完成或已取消的发货记录不能取消
	if shipment.Status == constants.ShipmentStageCompleted || shipment.Status == constants.ShipmentStageCancelled {
		return errors.New("已完成或已取消的发货记录不能取消")
	}

	// 更新状态为已取消
	return s.repo.UpdateStatus(ctx, id, constants.ShipmentStageCancelled, cancelledBy)
}

// Approve 审批发货记录
func (s *shipmentService) Approve(ctx context.Context, approvalDTO *dto.ShipmentApprovalDTO, approverID uint) error {
	// 发送审批信号到工作流
	if s.temporalClient != nil {
		workflowID := fmt.Sprintf("shipment_workflow_%d", approvalDTO.ShipmentID)

		signal := workflow.ShipmentApprovalSignal{
			ShipmentID: approvalDTO.ShipmentID,
			Action:     approvalDTO.Action,
			ApproverID: approverID,
			Comments:   approvalDTO.Comments,
			ApprovedAt: time.Now(),
		}

		err := s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.ShipmentApprovalSignalName, signal)
		if err != nil {
			return fmt.Errorf("发送审批信号失败: %w", err)
		}
	}

	return nil
}

// Rollback 回退发货记录
func (s *shipmentService) Rollback(ctx context.Context, rollbackDTO *dto.ShipmentRollbackDTO, approverID uint) error {
	// 发送回退信号到工作流
	if s.temporalClient != nil {
		workflowID := fmt.Sprintf("shipment_workflow_%d", rollbackDTO.ShipmentID)

		signal := workflow.ShipmentRollbackSignal{
			ShipmentID:   rollbackDTO.ShipmentID,
			RollbackTo:   rollbackDTO.RollbackTo,
			ApproverID:   approverID,
			Comments:     rollbackDTO.Comments,
			RolledBackAt: time.Now(),
		}

		err := s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.ShipmentRollbackSignalName, signal)
		if err != nil {
			return fmt.Errorf("发送回退信号失败: %w", err)
		}
	}

	return nil
}

// GetByContractID 根据合同ID获取发货记录列表
func (s *shipmentService) GetByContractID(ctx context.Context, contractID uint) ([]*dto.ShipmentListDTO, error) {
	shipments, err := s.repo.GetByContractID(ctx, contractID)
	if err != nil {
		return nil, err
	}

	var result []*dto.ShipmentListDTO
	for _, shipment := range shipments {
		listDTO, err := s.convertToListDTO(ctx, shipment)
		if err != nil {
			log.Printf("转换发货记录列表项失败: %v", err)
			continue
		}
		result = append(result, listDTO)
	}

	return result, nil
}

// GetShipmentHistory 获取发货记录历史
func (s *shipmentService) GetShipmentHistory(ctx context.Context, shipmentID uint) ([]*model.PurchaseApprovalHistory, error) {
	return s.repo.GetStatusHistory(ctx, shipmentID)
}

// GetStatistics 获取发货统计信息
func (s *shipmentService) GetStatistics(ctx context.Context) (*dto.ShipmentStatisticsDTO, error) {
	return s.repo.GetStatistics(ctx)
}

// convertToListDTO 转换为列表DTO
func (s *shipmentService) convertToListDTO(ctx context.Context, shipment *model.Shipment) (*dto.ShipmentListDTO, error) {
	listDTO := &dto.ShipmentListDTO{
		ID:                  shipment.ID,
		ShipmentNo:          shipment.ShipmentNo,
		ContractID:          shipment.ContractID,
		SupplierID:          shipment.SupplierID,
		TrackingInfo:        shipment.TrackingInfo,
		DeliveryAddress:     shipment.DeliveryAddress,
		ExpectedArrivalDate: shipment.ExpectedArrivalDate,
		ShipmentNotice:      shipment.ShipmentNotice,
		TotalQuantity:       shipment.TotalQuantity,
		TotalAmount:         shipment.TotalAmount,
		IsComplete:          shipment.IsComplete,
		Status:              shipment.Status,
		CreatedBy:           shipment.CreatedBy,
		CreatedAt:           shipment.CreatedAt,
	}

	// 设置合同编号和我方公司名称
	if shipment.Contract != nil {
		listDTO.ContractNo = shipment.Contract.ContractNo
		// 获取我方公司名称
		if shipment.Contract.OurCompany != nil {
			listDTO.OurCompanyName = shipment.Contract.OurCompany.CompanyName
		}
	}

	// 设置供应商名称
	if shipment.Supplier != nil {
		listDTO.SupplierName = shipment.Supplier.SupplierName
	}

	// 设置创建人姓名
	if shipment.CreatedBy != nil {
		listDTO.CreatedByName = s.userHelper.GetUserRealName(ctx, *shipment.CreatedBy)
	}

	// 转换发货明细
	for _, item := range shipment.Items {
		itemDTO := &dto.ShipmentItemDetailDTO{
			ID:                      item.ID,
			ShipmentID:              item.ShipmentID,
			ContractItemID:          item.ContractItemID,
			ShippedQuantity:         item.ShippedQuantity,
			ShippedAmount:           item.ShippedAmount,
			UnshippedQuantity:       item.UnshippedQuantity,
			UnshippedAmount:         item.UnshippedAmount,
			CurrentShipmentQuantity: item.CurrentShipmentQuantity,
			CurrentShipmentAmount:   item.CurrentShipmentAmount,
			Remark:                  item.Remark,
			CreatedAt:               item.CreatedAt,
			UpdatedAt:               item.UpdatedAt,
		}

		// 填充合同相关信息（列表接口只需要基本的合同信息）
		if item.ContractItem != nil {
			itemDTO.ContractQuantity = item.ContractItem.ContractQuantity
			itemDTO.ContractPrice = item.ContractItem.ContractPrice
			itemDTO.ContractAmount = item.ContractItem.ContractAmount
		}

		// 获取付款信息
		if item.ContractItemID != 0 {
			paidQuantity, paidAmount, unpaidQuantity, unpaidAmount, err := s.repo.GetContractItemPaymentInfo(ctx, item.ContractItemID)
			if err != nil {
				log.Printf("获取合同明细付款信息失败: contractItemID=%d, error=%v", item.ContractItemID, err)
				// 设置默认值，不中断流程
				itemDTO.PaidQuantity = 0
				itemDTO.PaidAmount = 0
				itemDTO.UnpaidQuantity = 0
				itemDTO.UnpaidAmount = 0
			} else {
				itemDTO.PaidQuantity = paidQuantity
				itemDTO.PaidAmount = paidAmount
				itemDTO.UnpaidQuantity = unpaidQuantity
				itemDTO.UnpaidAmount = unpaidAmount
			}
		}

		// 反序列化SN号
		if item.SerialNumbers != "" {
			var serialNumbers []string
			if err := json.Unmarshal([]byte(item.SerialNumbers), &serialNumbers); err == nil {
				itemDTO.SerialNumbers = serialNumbers
			}
		}

		listDTO.Items = append(listDTO.Items, itemDTO)
	}

	return listDTO, nil
}

// convertToDetailDTO 转换为详情DTO
func (s *shipmentService) convertToDetailDTO(ctx context.Context, shipment *model.Shipment) (*dto.ShipmentDetailDTO, error) {
	detailDTO := &dto.ShipmentDetailDTO{
		ID:                  shipment.ID,
		ShipmentNo:          shipment.ShipmentNo,
		ContractID:          shipment.ContractID,
		SupplierID:          shipment.SupplierID,
		TrackingInfo:        shipment.TrackingInfo,
		DeliveryAddress:     shipment.DeliveryAddress,
		ExpectedArrivalDate: shipment.ExpectedArrivalDate,
		ShipmentNotice:      shipment.ShipmentNotice,
		TotalQuantity:       shipment.TotalQuantity,
		TotalAmount:         shipment.TotalAmount,
		IsComplete:          shipment.IsComplete,
		Remark:              shipment.Remark,
		Status:              shipment.Status,
		CreatedBy:           shipment.CreatedBy,
		CreatedAt:           shipment.CreatedAt,
		UpdatedBy:           shipment.UpdatedBy,
		UpdatedAt:           shipment.UpdatedAt,
	}

	// 设置合同编号和我方公司名称
	if shipment.Contract != nil {
		detailDTO.ContractNo = shipment.Contract.ContractNo
		// 获取我方公司名称
		if shipment.Contract.OurCompany != nil {
			detailDTO.OurCompanyName = shipment.Contract.OurCompany.CompanyName
		}
	}

	// 设置供应商名称
	if shipment.Supplier != nil {
		detailDTO.SupplierName = shipment.Supplier.SupplierName
	}

	// 设置创建人姓名
	if shipment.CreatedBy != nil {
		detailDTO.CreatedByName = s.userHelper.GetUserRealName(ctx, *shipment.CreatedBy)
	}

	// 设置更新人姓名
	if shipment.UpdatedBy != nil {
		detailDTO.UpdatedByName = s.userHelper.GetUserRealName(ctx, *shipment.UpdatedBy)
	}

	// 转换发货明细
	for _, item := range shipment.Items {
		itemDTO := dto.ShipmentItemDetailDTO{
			ID:                      item.ID,
			ShipmentID:              item.ShipmentID,
			ContractItemID:          item.ContractItemID,
			ShippedQuantity:         item.ShippedQuantity,
			ShippedAmount:           item.ShippedAmount,
			UnshippedQuantity:       item.UnshippedQuantity,
			UnshippedAmount:         item.UnshippedAmount,
			CurrentShipmentQuantity: item.CurrentShipmentQuantity,
			CurrentShipmentAmount:   item.CurrentShipmentAmount,
			Remark:                  item.Remark,
			CreatedAt:               item.CreatedAt,
			UpdatedAt:               item.UpdatedAt,
		}

		// 反序列化SN号
		if item.SerialNumbers != "" {
			var serialNumbers []string
			if err := json.Unmarshal([]byte(item.SerialNumbers), &serialNumbers); err == nil {
				itemDTO.SerialNumbers = serialNumbers
			}
		}

		// 填充物料信息（从合同明细获取）
		if item.ContractItem != nil {
			itemDTO.MaterialType = item.ContractItem.MaterialType
			itemDTO.Model = item.ContractItem.Model
			itemDTO.Brand = item.ContractItem.Brand
			itemDTO.PN = item.ContractItem.PN
			itemDTO.Spec = item.ContractItem.Spec
			itemDTO.Unit = item.ContractItem.Unit
			itemDTO.ContractQuantity = item.ContractItem.ContractQuantity
			itemDTO.ContractPrice = item.ContractItem.ContractPrice
			itemDTO.ContractAmount = item.ContractItem.ContractAmount
		}

		// 获取付款信息
		if item.ContractItemID != 0 {
			paidQuantity, paidAmount, unpaidQuantity, unpaidAmount, err := s.repo.GetContractItemPaymentInfo(ctx, item.ContractItemID)
			if err != nil {
				log.Printf("获取合同明细付款信息失败: contractItemID=%d, error=%v", item.ContractItemID, err)
				// 设置默认值，不中断流程
				itemDTO.PaidQuantity = 0
				itemDTO.PaidAmount = 0
				itemDTO.UnpaidQuantity = 0
				itemDTO.UnpaidAmount = 0
			} else {
				itemDTO.PaidQuantity = paidQuantity
				itemDTO.PaidAmount = paidAmount
				itemDTO.UnpaidQuantity = unpaidQuantity
				itemDTO.UnpaidAmount = unpaidAmount
			}
		}

		detailDTO.Items = append(detailDTO.Items, itemDTO)
	}

	return detailDTO, nil
}

// startShipmentWorkflow 启动发货管理工作流
func (s *shipmentService) startShipmentWorkflow(ctx context.Context, shipment *model.Shipment, requesterID uint) error {
	if s.temporalClient == nil {
		return fmt.Errorf("Temporal客户端未初始化")
	}

	// 获取合同信息以获取项目和公司信息
	contract, err := s.contractRepo.GetByID(ctx, shipment.ContractID)
	if err != nil {
		return fmt.Errorf("获取合同信息失败: %w", err)
	}

	// 获取项目名称
	projectName := ""
	if contract.Inquiry != nil && contract.Inquiry.Request != nil {
		projectName = contract.Inquiry.Request.ProjectName
	}

	// 获取公司信息（从合同中获取）
	companyID := contract.OurCompanyID
	companyName := ""
	companyEntityType := ""
	if contract.OurCompany != nil {
		companyName = contract.OurCompany.CompanyName
		companyEntityType = "internal" // 默认为内部公司
	}

	// 获取申请类型
	requestType := "other"
	if contract.Inquiry != nil && contract.Inquiry.Request != nil {
		requestType = contract.Inquiry.Request.RequestType
	}

	// 构建工作流输入
	workflowOptions := client.StartWorkflowOptions{
		ID:        fmt.Sprintf("shipment_workflow_%d", shipment.ID),
		TaskQueue: constants.TaskQueueShipment,
	}

	workflowInput := workflow.ShipmentWorkflowInput{
		ShipmentID:        shipment.ID,
		ShipmentNo:        shipment.ShipmentNo,
		RequesterID:       requesterID,
		ContractID:        shipment.ContractID,
		SupplierID:        shipment.SupplierID,
		TotalAmount:       shipment.TotalAmount,
		CompanyID:         companyID,
		CompanyName:       companyName,
		ProjectName:       projectName,
		BusinessType:      "发货管理",
		CompanyEntityType: companyEntityType,
		RequestType:       requestType,
	}

	_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.ShipmentWorkflowName, workflowInput)
	if err != nil {
		return fmt.Errorf("启动发货管理工作流失败: %w", err)
	}

	log.Printf("发货管理工作流启动成功: ShipmentID=%d, WorkflowID=%s", shipment.ID, workflowOptions.ID)
	return nil
}

// createInitialHistory 创建初始历史记录
func (s *shipmentService) createInitialHistory(ctx context.Context, shipment *model.Shipment, operatorID uint) error {
	// 获取操作人姓名
	operatorName := s.userHelper.GetUserRealName(ctx, operatorID)

	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypeShipment,
		BusinessID:     shipment.ID,
		PreviousStatus: "",
		NewStatus:      shipment.Status,
		Action:         constants.ActionCreate,
		OperatorID:     operatorID,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "创建发货记录",
		CreatedAt:      time.Now(),
	}

	return s.repo.CreateStatusHistory(ctx, history)
}

// ShippedStats 发货统计信息
type ShippedStats struct {
	TotalQuantity int     // 总发货数量
	TotalAmount   float64 // 总发货金额
}

// calculateContractItemShippedStats 计算合同明细的历史发货统计（不包括本次发货）
func (s *shipmentService) calculateContractItemShippedStats(ctx context.Context, contractItemID uint) (*ShippedStats, error) {
	// 查询该合同明细的所有历史发货记录（只查询已完成的发货记录）
	shipments, err := s.repo.GetShipmentsByContractItemID(ctx, contractItemID)
	if err != nil {
		return nil, err
	}

	stats := &ShippedStats{
		TotalQuantity: 0,
		TotalAmount:   0.0,
	}

	// 累计所有历史发货数量和金额（只统计已完成的发货记录）
	for _, shipment := range shipments {
		// 只统计已完成的发货记录
		if shipment.Status == constants.ShipmentStageCompleted {
			for _, item := range shipment.Items {
				if item.ContractItemID == contractItemID {
					stats.TotalQuantity += item.CurrentShipmentQuantity
					stats.TotalAmount += item.CurrentShipmentAmount
				}
			}
		}
	}

	return stats, nil
}

// checkIfContractFullyShipped 检查合同是否完全发货（包括本次发货）
func (s *shipmentService) checkIfContractFullyShipped(ctx context.Context, contractID uint, currentItems []model.ShipmentItem) (bool, error) {
	// 获取合同的所有明细
	contractItems, err := s.contractRepo.GetContractItemsByContractID(ctx, contractID)
	if err != nil {
		return false, err
	}

	// 为每个合同明细检查是否完全发货
	for _, contractItem := range contractItems {
		// 计算该明细的历史发货统计
		shippedStats, err := s.calculateContractItemShippedStats(ctx, contractItem.ID)
		if err != nil {
			return false, err
		}

		// 加上本次发货数量
		currentShipmentQuantity := 0
		for _, currentItem := range currentItems {
			if currentItem.ContractItemID == contractItem.ID {
				currentShipmentQuantity += currentItem.CurrentShipmentQuantity
			}
		}

		// 检查总发货数量是否达到合同要求
		totalShippedQuantity := shippedStats.TotalQuantity + currentShipmentQuantity
		if totalShippedQuantity < contractItem.ContractQuantity {
			return false, nil // 还有明细未完全发货
		}
	}

	return true, nil // 所有明细都已完全发货
}
