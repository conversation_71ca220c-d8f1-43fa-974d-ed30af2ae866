package service

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"backend/internal/modules/purchase/workflow"
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	userService "backend/internal/modules/user/service"

	"go.temporal.io/sdk/client"
	"gorm.io/gorm"
)

// 定义错误
var (
	// ErrPaymentRequestNotFound 付款申请不存在
	ErrPaymentRequestNotFound = errors.New("付款申请不存在")

	// ErrInvalidPaymentRequestData 无效的付款申请数据
	ErrInvalidPaymentRequestData = errors.New("无效的付款申请数据")

	// ErrPaymentRequestSubmitNotAllowed 付款申请提交不被允许
	ErrPaymentRequestSubmitNotAllowed = errors.New("只有草稿状态的付款申请才能提交")

	// ErrInvalidPaymentRequestStatus 无效的付款申请状态
	ErrInvalidPaymentRequestStatus = errors.New("无效的付款申请状态")

	// ErrDuplicatePaymentNo 付款申请单号重复
	ErrDuplicatePaymentNo = errors.New("付款申请单号已存在")

	// ErrInsufficientContractAmount 合同金额不足
	ErrInsufficientContractAmount = errors.New("付款金额超过合同剩余金额")
)

// PaymentRequestService 付款申请服务接口
type PaymentRequestService interface {
	// Create 创建付款申请
	Create(ctx context.Context, createDTO *dto.CreatePaymentRequestDTO) (*model.PaymentRequest, error)

	// GetByID 根据ID获取付款申请
	GetByID(ctx context.Context, id uint) (*dto.PaymentRequestDetailDTO, error)

	// GetByPaymentNo 根据付款申请单号获取付款申请
	GetByPaymentNo(ctx context.Context, paymentNo string) (*dto.PaymentRequestDetailDTO, error)

	// Update 更新付款申请
	Update(ctx context.Context, id uint, updateDTO *dto.UpdatePaymentRequestDTO) error

	// Delete 删除付款申请
	Delete(ctx context.Context, id uint, deletedBy uint) error

	// List 获取付款申请列表
	List(ctx context.Context, query *dto.PaymentRequestQueryDTO) ([]*dto.PaymentRequestListDTO, int64, error)

	// Submit 提交付款申请
	Submit(ctx context.Context, id uint, submittedBy uint) error

	// Cancel 取消付款申请
	Cancel(ctx context.Context, id uint, cancelledBy uint) error

	// Approve 审批付款申请
	Approve(ctx context.Context, approvalDTO *dto.PaymentRequestApprovalDTO, approverID uint, approverName string) error

	// Rollback 回退付款申请
	Rollback(ctx context.Context, rollbackDTO *dto.PaymentRequestRollbackDTO, operatorID uint, operatorName string) error

	// GetByContractID 根据合同ID获取付款申请列表
	GetByContractID(ctx context.Context, contractID uint) ([]*model.PaymentRequest, error)

	// ValidatePaymentAmount 验证付款金额
	ValidatePaymentAmount(ctx context.Context, contractID uint, paymentAmount float64) error

	// GetPaymentRequestHistory 获取付款申请历史记录
	GetPaymentRequestHistory(ctx context.Context, paymentID uint) ([]*dto.PaymentRequestHistoryDTO, error)
}

// paymentRequestService 付款申请服务实现
type paymentRequestService struct {
	repo           repository.PaymentRequestRepository
	contractRepo   repository.PurchaseContractRepository
	supplierRepo   repository.SupplierRepository
	companyService CompanyService
	temporalClient client.Client
	userHelper     *UserHelper
}

// NewPaymentRequestService 创建付款申请服务实例
func NewPaymentRequestService(
	repo repository.PaymentRequestRepository,
	contractRepo repository.PurchaseContractRepository,
	supplierRepo repository.SupplierRepository,
	companyService CompanyService,
	temporalClient client.Client,
	userService userService.IUserService,
) PaymentRequestService {
	userHelper := NewUserHelper(userService)
	return &paymentRequestService{
		repo:           repo,
		contractRepo:   contractRepo,
		supplierRepo:   supplierRepo,
		companyService: companyService,
		temporalClient: temporalClient,
		userHelper:     userHelper,
	}
}

// Create 创建付款申请
func (s *paymentRequestService) Create(ctx context.Context, createDTO *dto.CreatePaymentRequestDTO) (*model.PaymentRequest, error) {
	// 验证合同是否存在并获取合同信息
	contract, err := s.contractRepo.GetByID(ctx, createDTO.ContractID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("关联的合同不存在")
		}
		return nil, err
	}

	// 验证付款金额
	if err := s.ValidatePaymentAmount(ctx, createDTO.ContractID, createDTO.CurrentPaymentAmount); err != nil {
		return nil, err
	}

	// 生成付款申请单号
	paymentNo, err := utils.GenerateUniqueCode(ctx, func(ctx context.Context, code string) (bool, error) {
		return s.repo.IsPaymentNoExists(ctx, code)
	}, utils.CodeGeneratorOptions{
		Prefix:       "PAY",
		DateFormat:   "20060102150405",
		RandomLength: 4,
		Delimiter:    "",
	})
	if err != nil {
		return nil, fmt.Errorf("生成付款申请单号失败: %w", err)
	}

	// 检查付款申请单号是否重复
	exists, err := s.repo.IsPaymentNoExists(ctx, paymentNo)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, ErrDuplicatePaymentNo
	}

	// 计算付款相关金额
	contractTotal, paidAmount, unpaidAmount, cumulativeAfterPayment, remainingAfterPayment, err := s.calculatePaymentAmounts(ctx, createDTO.ContractID, createDTO.CurrentPaymentAmount)
	if err != nil {
		return nil, fmt.Errorf("计算付款金额失败: %w", err)
	}

	// 从合同中获取供应商ID和公司ID
	supplierID := contract.SupplierID
	companyID := contract.OurCompanyID

	// 获取供应商银行信息
	supplier, err := s.supplierRepo.GetByID(ctx, supplierID)
	if err != nil {
		return nil, fmt.Errorf("获取供应商信息失败: %w", err)
	}

	// 创建付款申请
	paymentRequest := &model.PaymentRequest{
		PaymentNo:                   paymentNo,
		ContractID:                  createDTO.ContractID,
		SupplierID:                  supplierID,
		CompanyID:                   companyID,
		PaymentType:                 createDTO.PaymentType,
		ContractTotalAmount:         contractTotal,
		PaidAmount:                  paidAmount,
		UnpaidAmount:                unpaidAmount,
		CurrentPaymentAmount:        createDTO.CurrentPaymentAmount,
		PaymentReason:               createDTO.PaymentReason,
		CumulativePaidAfterPayment:  cumulativeAfterPayment,
		RemainingUnpaidAfterPayment: remainingAfterPayment,
		BankName:                    supplier.BankName,
		BankAccount:                 supplier.BankAccount,
		Remark:                      createDTO.Remark,
		Status:                      constants.PaymentStagePurchaseSupplyChainReview, // 直接进入采购与供应链负责人审批阶段
		CreatedBy:                   &createDTO.CreatedBy,
		CreatedAt:                   time.Now(),
	}

	// 创建付款申请明细
	for _, itemDTO := range createDTO.Items {
		// 计算明细项的付款相关金额
		paidQuantity, paidAmount, unpaidQuantity, unpaidAmount, err := s.calculateItemPaymentAmounts(ctx, itemDTO.ContractItemID, itemDTO.CurrentPaymentQuantity, itemDTO.CurrentPaymentAmount)
		if err != nil {
			return nil, fmt.Errorf("计算明细项付款金额失败: %w", err)
		}

		item := model.PaymentRequestItem{
			ContractItemID:         itemDTO.ContractItemID,
			PaidQuantity:           paidQuantity,
			PaidAmount:             paidAmount,
			UnpaidQuantity:         unpaidQuantity,
			UnpaidAmount:           unpaidAmount,
			CurrentPaymentQuantity: itemDTO.CurrentPaymentQuantity,
			CurrentPaymentAmount:   itemDTO.CurrentPaymentAmount,
			Remark:                 itemDTO.Remark,
			CreatedAt:              time.Now(),
		}
		paymentRequest.Items = append(paymentRequest.Items, item)
	}

	// 保存到数据库
	if err := s.repo.Create(ctx, paymentRequest); err != nil {
		return nil, err
	}

	// 记录创建历史
	operatorName := s.userHelper.GetUserRealName(ctx, createDTO.CreatedBy)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePaymentRequest,
		BusinessID:     paymentRequest.ID,
		PreviousStatus: "",
		NewStatus:      constants.PaymentStagePurchaseSupplyChainReview,
		Action:         constants.ActionCreate,
		OperatorID:     createDTO.CreatedBy,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "创建付款申请",
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		// 仅记录错误，不影响主流程
		log.Printf("记录付款申请状态历史失败: %v", err)
	}

	// 启动工作流
	if s.temporalClient != nil {
		workflowOptions := client.StartWorkflowOptions{
			ID:        fmt.Sprintf("payment_request_%d", paymentRequest.ID),
			TaskQueue: workflow.PaymentRequestTaskQueue,
		}

		// 重新加载付款申请以获取关联的公司信息
		paymentRequestWithCompany, err := s.repo.GetByID(ctx, paymentRequest.ID)
		if err != nil {
			log.Printf("获取付款申请公司信息失败: %v", err)
			// 使用默认值继续
			paymentRequestWithCompany = paymentRequest
		}

		// 根据公司信息判断公司类型
		companyEntityType := constants.CompanyEntityTypeOther
		companyName := ""
		if paymentRequestWithCompany.Company != nil {
			companyEntityType = constants.DetermineCompanyEntityType(paymentRequestWithCompany.Company.CompanyName)
			companyName = paymentRequestWithCompany.Company.CompanyName
		}

		// 获取项目名称
		projectName := ""
		if contract != nil {
			projectName = contract.ProjectName
		}

		workflowInput := workflow.PaymentRequestWorkflowInput{
			PaymentID:         paymentRequest.ID,
			PaymentNo:         paymentRequest.PaymentNo,
			RequesterID:       createDTO.CreatedBy,
			ContractID:        paymentRequest.ContractID,
			TotalAmount:       paymentRequest.CurrentPaymentAmount,
			CompanyID:         paymentRequest.CompanyID,
			CompanyName:       companyName,
			ProjectName:       projectName,
			BusinessType:      "付款申请",
			CompanyEntityType: companyEntityType,
		}

		_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.PaymentRequestWorkflowName, workflowInput)
		if err != nil {
			log.Printf("启动付款申请工作流失败: %v", err)
			// 工作流启动失败不影响付款申请创建
		}
	}

	return paymentRequest, nil
}

// GetByID 根据ID获取付款申请
func (s *paymentRequestService) GetByID(ctx context.Context, id uint) (*dto.PaymentRequestDetailDTO, error) {
	paymentRequest, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPaymentRequestNotFound
		}
		return nil, err
	}

	// 构建详情DTO
	detailDTO := &dto.PaymentRequestDetailDTO{
		PaymentRequest: paymentRequest,
	}

	// 填充附加信息
	if paymentRequest.Contract != nil {
		detailDTO.ContractNo = paymentRequest.Contract.ContractNo
		// 获取我方公司名称
		if paymentRequest.Contract.OurCompany != nil {
			detailDTO.OurCompanyName = paymentRequest.Contract.OurCompany.CompanyName
		}
	}
	if paymentRequest.Supplier != nil {
		detailDTO.SupplierName = paymentRequest.Supplier.SupplierName
	}
	if paymentRequest.CreatedBy != nil {
		detailDTO.CreatorName = s.userHelper.GetUserRealName(ctx, *paymentRequest.CreatedBy)
	}
	if paymentRequest.UpdatedBy != nil {
		detailDTO.UpdaterName = s.userHelper.GetUserRealName(ctx, *paymentRequest.UpdatedBy)
	}

	return detailDTO, nil
}

// GetByPaymentNo 根据付款申请单号获取付款申请
func (s *paymentRequestService) GetByPaymentNo(ctx context.Context, paymentNo string) (*dto.PaymentRequestDetailDTO, error) {
	paymentRequest, err := s.repo.GetByPaymentNo(ctx, paymentNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPaymentRequestNotFound
		}
		return nil, err
	}

	// 构建详情DTO
	detailDTO := &dto.PaymentRequestDetailDTO{
		PaymentRequest: paymentRequest,
	}

	// 填充附加信息
	if paymentRequest.Contract != nil {
		detailDTO.ContractNo = paymentRequest.Contract.ContractNo
	}
	if paymentRequest.Supplier != nil {
		detailDTO.SupplierName = paymentRequest.Supplier.SupplierName
	}
	if paymentRequest.CreatedBy != nil {
		detailDTO.CreatorName = s.userHelper.GetUserRealName(ctx, *paymentRequest.CreatedBy)
	}
	if paymentRequest.UpdatedBy != nil {
		detailDTO.UpdaterName = s.userHelper.GetUserRealName(ctx, *paymentRequest.UpdatedBy)
	}

	return detailDTO, nil
}

// Update 更新付款申请
func (s *paymentRequestService) Update(ctx context.Context, id uint, updateDTO *dto.UpdatePaymentRequestDTO) error {
	// 获取现有付款申请
	paymentRequest, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrPaymentRequestNotFound
		}
		return err
	}

	// 只有草稿状态的付款申请才能更新
	if paymentRequest.Status != constants.PaymentStageDraft {
		return errors.New("只有草稿状态的付款申请才能更新")
	}

	// 验证付款金额
	if err := s.ValidatePaymentAmount(ctx, paymentRequest.ContractID, updateDTO.CurrentPaymentAmount); err != nil {
		return err
	}

	// 计算付款相关金额
	contractTotal, paidAmount, unpaidAmount, cumulativeAfterPayment, remainingAfterPayment, err := s.calculatePaymentAmounts(ctx, paymentRequest.ContractID, updateDTO.CurrentPaymentAmount)
	if err != nil {
		return fmt.Errorf("计算付款金额失败: %w", err)
	}

	// 获取供应商银行信息
	supplier, err := s.supplierRepo.GetByID(ctx, paymentRequest.SupplierID)
	if err != nil {
		return fmt.Errorf("获取供应商信息失败: %w", err)
	}

	// 更新基本信息
	now := time.Now()
	paymentRequest.PaymentType = updateDTO.PaymentType
	// 注意：CompanyID 和 SupplierID 不允许修改，它们来自合同信息
	paymentRequest.ContractTotalAmount = contractTotal
	paymentRequest.PaidAmount = paidAmount
	paymentRequest.UnpaidAmount = unpaidAmount
	paymentRequest.CurrentPaymentAmount = updateDTO.CurrentPaymentAmount
	paymentRequest.PaymentReason = updateDTO.PaymentReason
	paymentRequest.CumulativePaidAfterPayment = cumulativeAfterPayment
	paymentRequest.RemainingUnpaidAfterPayment = remainingAfterPayment
	paymentRequest.BankName = supplier.BankName
	paymentRequest.BankAccount = supplier.BankAccount
	paymentRequest.Remark = updateDTO.Remark
	paymentRequest.UpdatedBy = &updateDTO.UpdatedBy
	paymentRequest.UpdatedAt = &now

	// 更新明细项
	paymentRequest.Items = nil // 清空现有明细
	for _, itemDTO := range updateDTO.Items {
		// 计算明细项的付款相关金额
		paidQuantity, paidAmount, unpaidQuantity, unpaidAmount, err := s.calculateItemPaymentAmounts(ctx, itemDTO.ContractItemID, itemDTO.CurrentPaymentQuantity, itemDTO.CurrentPaymentAmount)
		if err != nil {
			return fmt.Errorf("计算明细项付款金额失败: %w", err)
		}

		item := model.PaymentRequestItem{
			ID:                     itemDTO.ID,
			ContractItemID:         itemDTO.ContractItemID,
			PaidQuantity:           paidQuantity,
			PaidAmount:             paidAmount,
			UnpaidQuantity:         unpaidQuantity,
			UnpaidAmount:           unpaidAmount,
			CurrentPaymentQuantity: itemDTO.CurrentPaymentQuantity,
			CurrentPaymentAmount:   itemDTO.CurrentPaymentAmount,
			Remark:                 itemDTO.Remark,
			UpdatedAt:              &now,
		}
		if item.ID == 0 {
			item.CreatedAt = now
		}
		paymentRequest.Items = append(paymentRequest.Items, item)
	}

	// 保存更新
	if err := s.repo.Update(ctx, paymentRequest); err != nil {
		return err
	}

	// 注意：审批历史记录由现有系统处理，这里不需要创建

	return nil
}

// Delete 删除付款申请
func (s *paymentRequestService) Delete(ctx context.Context, id uint, deletedBy uint) error {
	// 获取付款申请
	paymentRequest, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrPaymentRequestNotFound
		}
		return err
	}

	// 只有草稿状态的付款申请才能删除
	if paymentRequest.Status != constants.PaymentStageDraft {
		return errors.New("只有草稿状态的付款申请才能删除")
	}

	// 删除付款申请
	if err := s.repo.Delete(ctx, id); err != nil {
		return err
	}

	// 注意：审批历史记录由现有系统处理，这里不需要创建

	return nil
}

// List 获取付款申请列表
func (s *paymentRequestService) List(ctx context.Context, query *dto.PaymentRequestQueryDTO) ([]*dto.PaymentRequestListDTO, int64, error) {
	paymentRequests, total, err := s.repo.List(ctx, query)
	if err != nil {
		return nil, 0, err
	}

	var listDTOs []*dto.PaymentRequestListDTO
	for _, paymentRequest := range paymentRequests {
		listDTO := &dto.PaymentRequestListDTO{
			ID:                   paymentRequest.ID,
			PaymentNo:            paymentRequest.PaymentNo,
			ContractID:           paymentRequest.ContractID,
			SupplierID:           paymentRequest.SupplierID,
			PaymentType:          paymentRequest.PaymentType,
			ContractTotalAmount:  paymentRequest.ContractTotalAmount,
			CurrentPaymentAmount: paymentRequest.CurrentPaymentAmount,
			Status:               paymentRequest.Status,
			CreatedBy:            paymentRequest.CreatedBy,
			CreatedAt:            paymentRequest.CreatedAt,
		}

		// 填充附加信息
		if paymentRequest.Contract != nil {
			listDTO.ContractNo = paymentRequest.Contract.ContractNo
			// 获取我方公司名称
			if paymentRequest.Contract.OurCompany != nil {
				listDTO.OurCompanyName = paymentRequest.Contract.OurCompany.CompanyName
			}
		}
		if paymentRequest.Supplier != nil {
			listDTO.SupplierName = paymentRequest.Supplier.SupplierName
			listDTO.BankName = paymentRequest.Supplier.BankName
			listDTO.BankAccount = paymentRequest.Supplier.BankAccount
		}
		if paymentRequest.CreatedBy != nil {
			listDTO.CreatorName = s.userHelper.GetUserRealName(ctx, *paymentRequest.CreatedBy)
		}

		// 填充付款明细信息
		var itemDTOs []*dto.PaymentRequestItemDetailDTO
		for _, item := range paymentRequest.Items {
			itemDTO := &dto.PaymentRequestItemDetailDTO{
				PaymentRequestItem: &item,
			}

			// 如果有合同明细信息，填充合同明细编号（使用ID作为编号）
			if item.ContractItem != nil {
				itemDTO.ContractItemNo = fmt.Sprintf("CI-%d", item.ContractItem.ID)
			}

			itemDTOs = append(itemDTOs, itemDTO)
		}
		listDTO.Items = itemDTOs

		listDTOs = append(listDTOs, listDTO)
	}

	return listDTOs, total, nil
}

// calculatePaymentAmounts 计算付款相关金额
func (s *paymentRequestService) calculatePaymentAmounts(ctx context.Context, contractID uint, currentPaymentAmount float64) (contractTotal, paidAmount, unpaidAmount, cumulativeAfterPayment, remainingAfterPayment float64, err error) {
	// 获取合同信息
	contract, err := s.contractRepo.GetByID(ctx, contractID)
	if err != nil {
		return 0, 0, 0, 0, 0, err
	}
	contractTotal = contract.TotalAmount

	// 查询该合同的所有已完成付款申请
	completedPayments, err := s.repo.GetByContractID(ctx, contractID)
	if err != nil {
		return 0, 0, 0, 0, 0, err
	}

	// 计算已付款金额（只计算已完成的付款申请）
	paidAmount = 0
	for _, payment := range completedPayments {
		if payment.Status == constants.PaymentStageCompleted {
			paidAmount += payment.CurrentPaymentAmount
		}
	}

	// 计算其他金额
	unpaidAmount = contractTotal - paidAmount
	cumulativeAfterPayment = paidAmount + currentPaymentAmount
	remainingAfterPayment = contractTotal - cumulativeAfterPayment

	return contractTotal, paidAmount, unpaidAmount, cumulativeAfterPayment, remainingAfterPayment, nil
}

// calculateItemPaymentAmounts 计算明细项的付款相关金额
func (s *paymentRequestService) calculateItemPaymentAmounts(ctx context.Context, contractItemID uint, currentPaymentQuantity int, currentPaymentAmount float64) (paidQuantity int, paidAmount float64, unpaidQuantity int, unpaidAmount float64, err error) {
	// 获取合同明细信息
	contractItem, err := s.contractRepo.GetItemByID(ctx, contractItemID)
	if err != nil {
		return 0, 0, 0, 0, err
	}

	// 查询该合同明细的所有已完成付款申请明细
	completedPaymentItems, err := s.repo.GetItemsByContractItemID(ctx, contractItemID)
	if err != nil {
		return 0, 0, 0, 0, err
	}

	// 计算已付款数量和金额（只计算已完成的付款申请）
	paidQuantity = 0
	paidAmount = 0
	for _, item := range completedPaymentItems {
		// 需要检查付款申请的状态
		if item.PaymentRequest != nil && item.PaymentRequest.Status == constants.PaymentStageCompleted {
			paidQuantity += item.CurrentPaymentQuantity
			paidAmount += item.CurrentPaymentAmount
		}
	}

	// 计算未付款数量和金额
	unpaidQuantity = contractItem.ContractQuantity - paidQuantity
	unpaidAmount = contractItem.ContractAmount - paidAmount

	return paidQuantity, paidAmount, unpaidQuantity, unpaidAmount, nil
}

// GetPaymentRequestHistory 获取付款申请历史记录
func (s *paymentRequestService) GetPaymentRequestHistory(ctx context.Context, paymentID uint) ([]*dto.PaymentRequestHistoryDTO, error) {
	// 检查付款申请是否存在
	_, err := s.repo.GetByID(ctx, paymentID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPaymentRequestNotFound
		}
		return nil, fmt.Errorf("检查付款申请是否存在失败: %w", err)
	}

	// 从仓库获取历史记录
	histories, err := s.repo.GetPaymentRequestHistory(ctx, paymentID)
	if err != nil {
		return nil, fmt.Errorf("获取付款申请历史记录失败: %w", err)
	}

	// 转换为DTO
	var historyDTOs []*dto.PaymentRequestHistoryDTO
	for _, history := range histories {
		historyDTO := &dto.PaymentRequestHistoryDTO{
			ID:             history.ID,
			BusinessType:   history.BusinessType,
			BusinessID:     history.BusinessID,
			PreviousStatus: history.PreviousStatus,
			NewStatus:      history.NewStatus,
			Action:         history.Action,
			OperatorID:     history.OperatorID,
			OperatorName:   history.OperatorName,
			OperationTime:  history.OperationTime,
			Comments:       history.Comments,
			CreatedAt:      history.CreatedAt,
		}

		// 设置状态显示名称
		historyDTO.PreviousStatusDisplay = constants.GetPaymentStageDisplayName(history.PreviousStatus)
		historyDTO.NewStatusDisplay = constants.GetPaymentStageDisplayName(history.NewStatus)

		// 设置操作动作显示名称
		historyDTO.ActionDisplay = getActionDisplayName(history.Action)

		historyDTOs = append(historyDTOs, historyDTO)
	}

	return historyDTOs, nil
}

// getActionDisplayName 获取操作动作显示名称
func getActionDisplayName(action string) string {
	actionMap := map[string]string{
		constants.ActionSubmit:   "提交",
		constants.ActionApprove:  "审批通过",
		constants.ActionReject:   "审批拒绝",
		constants.ActionRollback: "回退",
		constants.ActionCancel:   "取消",
		constants.ActionDelete:   "删除",
		constants.ActionUpdate:   "更新",
	}

	if displayName, exists := actionMap[action]; exists {
		return displayName
	}
	return action
}

// Approve 审批付款申请
func (s *paymentRequestService) Approve(ctx context.Context, approvalDTO *dto.PaymentRequestApprovalDTO, approverID uint, approverName string) error {
	// 获取付款申请记录
	paymentRequest, err := s.repo.GetByID(ctx, approvalDTO.PaymentID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrPaymentRequestNotFound
		}
		return err
	}

	// 直接使用传入的approverID（controller层已从JWT获取）
	userID := approverID

	// 构造审批信号数据
	approvalSignal := workflow.PaymentApprovalSignal{
		ApproverID:   userID,
		Action:       approvalDTO.Action,
		Comments:     approvalDTO.Comments,
		CurrentStage: paymentRequest.Status,
		Timestamp:    time.Now(),
	}

	// 发送审批信号到工作流
	workflowID := fmt.Sprintf("payment_request_%d", approvalDTO.PaymentID)
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNamePaymentApproval, approvalSignal)
	if err != nil {
		return fmt.Errorf("发送审批信号失败: %w", err)
	}

	return nil
}

// ValidatePaymentAmount 验证付款金额
func (s *paymentRequestService) ValidatePaymentAmount(ctx context.Context, contractID uint, paymentAmount float64) error {
	// 获取合同信息
	contract, err := s.contractRepo.GetByID(ctx, contractID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("关联的合同不存在")
		}
		return err
	}

	// 获取该合同已有的付款申请总金额
	existingPayments, err := s.repo.GetByContractID(ctx, contractID)
	if err != nil {
		return err
	}

	var totalPaidAmount float64
	for _, payment := range existingPayments {
		// 只计算已审批通过的付款申请
		if payment.Status == constants.PaymentStageCompleted {
			totalPaidAmount += payment.CurrentPaymentAmount
		}
	}

	// 检查付款金额是否超过合同剩余金额
	remainingAmount := contract.TotalAmount - totalPaidAmount
	if paymentAmount > remainingAmount {
		return fmt.Errorf("付款金额 %.2f 超过合同剩余金额 %.2f", paymentAmount, remainingAmount)
	}

	return nil
}

// Rollback 回退付款申请
func (s *paymentRequestService) Rollback(ctx context.Context, rollbackDTO *dto.PaymentRequestRollbackDTO, operatorID uint, operatorName string) error {
	// 获取付款申请记录
	paymentRequest, err := s.repo.GetByID(ctx, rollbackDTO.PaymentID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrPaymentRequestNotFound
		}
		return err
	}

	// 直接使用传入的operatorID（controller层已从JWT获取）
	userID := operatorID

	// 构造回退信号数据
	rollbackSignal := workflow.PaymentRollbackSignal{
		ApproverID:   userID,
		RollbackTo:   rollbackDTO.RollbackTo,
		Comments:     rollbackDTO.Comments,
		CurrentStage: paymentRequest.Status,
		Timestamp:    time.Now(),
	}

	// 发送回退信号到工作流
	workflowID := fmt.Sprintf("payment_request_%d", rollbackDTO.PaymentID)
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNamePaymentRollback, rollbackSignal)
	if err != nil {
		return fmt.Errorf("发送回退信号失败: %w", err)
	}

	return nil
}

// Cancel 取消付款申请
func (s *paymentRequestService) Cancel(ctx context.Context, id uint, cancelledBy uint) error {
	// 获取付款申请记录
	paymentRequest, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrPaymentRequestNotFound
		}
		return err
	}

	// 检查是否可以取消
	if paymentRequest.Status == constants.PaymentStageCompleted || paymentRequest.Status == constants.PaymentStageCancelled {
		return errors.New("当前状态的付款申请不能取消")
	}

	// 直接使用传入的cancelledBy（controller层已从JWT获取）
	userID := cancelledBy

	// 构造取消信号数据（使用审批信号结构，action为cancel）
	cancelSignal := workflow.PaymentApprovalSignal{
		ApproverID:   userID,
		Action:       constants.ActionCancel,
		Comments:     "取消付款申请",
		CurrentStage: paymentRequest.Status,
		Timestamp:    time.Now(),
	}

	// 发送取消信号到工作流（使用审批信号通道）
	workflowID := fmt.Sprintf("payment_request_%d", id)
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNamePaymentApproval, cancelSignal)
	if err != nil {
		return fmt.Errorf("发送取消信号失败: %w", err)
	}

	return nil
}

// GetByContractID 根据合同ID获取付款申请列表
func (s *paymentRequestService) GetByContractID(ctx context.Context, contractID uint) ([]*model.PaymentRequest, error) {
	return s.repo.GetByContractID(ctx, contractID)
}

// Submit 提交付款申请
func (s *paymentRequestService) Submit(ctx context.Context, id uint, submittedBy uint) error {
	// 获取付款申请记录
	paymentRequest, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrPaymentRequestNotFound
		}
		return err
	}

	// 检查是否可以提交
	if paymentRequest.Status != constants.PaymentStageDraft {
		return ErrPaymentRequestSubmitNotAllowed
	}

	// 直接使用传入的submittedBy（controller层已从JWT获取）
	userID := submittedBy

	// 获取合同信息用于工作流
	contract, err := s.contractRepo.GetByID(ctx, paymentRequest.ContractID)
	if err != nil {
		return fmt.Errorf("获取合同信息失败: %w", err)
	}

	// 获取公司信息（使用默认值）
	companyName := "默认公司"
	companyEntityType := "other" // 默认为其他主体类型

	// 启动付款申请工作流
	workflowID := fmt.Sprintf("payment_request_%d", id)
	workflowInput := workflow.PaymentRequestWorkflowInput{
		PaymentID:         id,
		PaymentNo:         paymentRequest.PaymentNo,
		RequesterID:       userID,
		ContractID:        paymentRequest.ContractID,
		TotalAmount:       paymentRequest.CurrentPaymentAmount,
		CompanyID:         paymentRequest.CompanyID,
		CompanyName:       companyName,
		ProjectName:       contract.ProjectName,
		BusinessType:      "付款申请",
		CompanyEntityType: companyEntityType,
	}

	workflowOptions := client.StartWorkflowOptions{
		ID:        workflowID,
		TaskQueue: constants.TaskQueuePaymentRequest,
	}

	_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.PaymentRequestWorkflowName, workflowInput)
	if err != nil {
		return fmt.Errorf("启动付款申请工作流失败: %w", err)
	}

	return nil
}
