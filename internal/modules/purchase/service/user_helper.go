package service

import (
	"context"
	"fmt"

	"backend/internal/common/utils"
	"backend/internal/infrastructure/database"
	userService "backend/internal/modules/user/service"
)

// UserHelper 用户辅助服务
type UserHelper struct {
	userService userService.IUserService
}

// NewUserHelper 创建用户辅助服务
func NewUserHelper(userService userService.IUserService) *UserHelper {
	return &UserHelper{
		userService: userService,
	}
}

// GetUserRealName 获取用户真实姓名
func (h *UserHelper) GetUserRealName(ctx context.Context, userID uint) string {
	if userID == 0 {
		return "系统"
	}

	// 1. 优先从当前上下文中获取用户姓名（当前登录用户）
	ctxUserID, err := utils.GetUserID(ctx)
	if err == nil && ctxUserID == userID {
		// 如果上下文中的用户ID与请求的用户ID匹配，则直接从上下文获取用户名
		userName, err := utils.GetUserName(ctx)
		if err == nil && userName != "" {
			return userName
		}
	}

	// 2. 如果上下文中没有或不匹配，尝试通过用户服务获取
	userInfo, err := h.userService.GetUserInfo(ctx, userID)
	if err == nil && userInfo.RealName != "" {
		return userInfo.RealName
	} else if err == nil && userInfo.UserName != "" {
		return userInfo.UserName
	}

	// 3. 如果用户服务获取失败，则通过直接数据库查询作为备份
	db := database.GetDB()
	if db != nil {
		return utils.GetUserRealName(db, userID)
	}

	// 4. 最后的兜底方案
	return fmt.Sprintf("用户_%d", userID)
}
