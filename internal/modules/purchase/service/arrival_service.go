package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"backend/internal/common/utils"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"backend/internal/modules/purchase/workflow"
	userService "backend/internal/modules/user/service"

	"go.temporal.io/sdk/client"
	"gorm.io/gorm"
)

// 错误定义
var (
	ErrArrivalNotFound      = errors.New("到货记录不存在")
	ErrArrivalNoExists      = errors.New("到货通知单号已存在")
	ErrInvalidArrivalStatus = errors.New("无效的到货记录状态")
	ErrContractNotFound     = errors.New("关联的合同不存在")
)

// ArrivalService 到货记录服务接口
type ArrivalService interface {
	// Create 创建到货记录
	Create(ctx context.Context, createDTO *dto.CreateArrivalDTO) (*model.Arrival, error)

	// GetByID 根据ID获取到货记录
	GetByID(ctx context.Context, id uint) (*dto.ArrivalDetailDTO, error)

	// GetByArrivalNo 根据到货通知单号获取到货记录
	GetByArrivalNo(ctx context.Context, arrivalNo string) (*dto.ArrivalDetailDTO, error)

	// Update 更新到货记录
	Update(ctx context.Context, id uint, updateDTO *dto.UpdateArrivalDTO) error

	// Delete 删除到货记录
	Delete(ctx context.Context, id uint, deletedBy uint) error

	// List 获取到货记录列表
	List(ctx context.Context, query *dto.ArrivalQueryDTO) ([]*dto.ArrivalListDTO, int64, error)

	// Submit 提交到货记录
	Submit(ctx context.Context, id uint, submittedBy uint) error

	// Cancel 取消到货记录
	Cancel(ctx context.Context, id uint, cancelledBy uint) error

	// Approve 审批到货记录
	Approve(ctx context.Context, approvalDTO *dto.ArrivalApprovalDTO, approverID uint, approverName string) error

	// Rollback 回退到货记录
	Rollback(ctx context.Context, rollbackDTO *dto.ArrivalRollbackDTO, operatorID uint, operatorName string) error

	// GetByContractID 根据合同ID获取到货记录列表
	GetByContractID(ctx context.Context, contractID uint) ([]*model.Arrival, error)

	// GetArrivalHistory 获取到货记录历史记录
	GetArrivalHistory(ctx context.Context, arrivalID uint) ([]*dto.ArrivalHistoryDTO, error)

	// GetStatistics 获取到货统计信息
	GetStatistics(ctx context.Context) (*dto.ArrivalStatisticsDTO, error)
}

// arrivalService 到货记录服务实现
type arrivalService struct {
	repo           repository.ArrivalRepository
	contractRepo   repository.PurchaseContractRepository
	supplierRepo   repository.SupplierRepository
	companyService CompanyService
	temporalClient client.Client
	userHelper     *UserHelper
}

// NewArrivalService 创建到货记录服务实例
func NewArrivalService(
	repo repository.ArrivalRepository,
	contractRepo repository.PurchaseContractRepository,
	supplierRepo repository.SupplierRepository,
	companyService CompanyService,
	temporalClient client.Client,
	userService userService.IUserService,
) ArrivalService {
	return &arrivalService{
		repo:           repo,
		contractRepo:   contractRepo,
		supplierRepo:   supplierRepo,
		companyService: companyService,
		temporalClient: temporalClient,
		userHelper:     NewUserHelper(userService),
	}
}

// Create 创建到货记录
func (s *arrivalService) Create(ctx context.Context, createDTO *dto.CreateArrivalDTO) (*model.Arrival, error) {
	// 验证合同是否存在并获取合同信息
	contract, err := s.contractRepo.GetByID(ctx, createDTO.ContractID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrContractNotFound
		}
		return nil, err
	}

	// 生成到货通知单号
	arrivalNo, err := utils.GenerateUniqueCode(ctx, func(ctx context.Context, code string) (bool, error) {
		return s.repo.IsArrivalNoExists(ctx, code)
	}, utils.CodeGeneratorOptions{
		Prefix:       "ARR",
		DateFormat:   "20060102150405",
		RandomLength: 4,
		Delimiter:    "",
	})
	if err != nil {
		return nil, fmt.Errorf("生成到货通知单号失败: %w", err)
	}

	// 创建到货记录，直接进入采购负责人审批阶段
	arrival := &model.Arrival{
		ArrivalNo:      arrivalNo,
		ContractID:     createDTO.ContractID,
		SupplierID:     contract.SupplierID,
		MayArriveAt:    createDTO.MayArriveAt,
		TrackingInfo:   createDTO.TrackingInfo,
		ArrivalAddress: createDTO.ArrivalAddress,
		ArrivalNotice:  createDTO.ArrivalNotice,
		Remark:         createDTO.Remark,
		Status:         constants.ArrivalStagePurchaseReview, // 直接进入采购负责人审批阶段
		CreatedBy:      &createDTO.CreatedBy,
		CreatedAt:      time.Now(),
	}

	// 创建到货明细并计算统计信息
	for _, itemDTO := range createDTO.Items {
		// 序列化SN号列表
		serialNumbersJSON := ""
		if len(itemDTO.SerialNumbers) > 0 {
			serialNumbersBytes, _ := json.Marshal(itemDTO.SerialNumbers)
			serialNumbersJSON = string(serialNumbersBytes)
		}

		// 计算该合同明细的历史到货统计（不包括本次到货）
		receivedStats, err := s.calculateContractItemReceivedStats(ctx, itemDTO.ContractItemID)
		if err != nil {
			return nil, fmt.Errorf("计算合同明细 %d 的到货统计失败: %w", itemDTO.ContractItemID, err)
		}

		// 获取合同明细信息
		contractItem, err := s.contractRepo.GetContractItemByID(ctx, itemDTO.ContractItemID)
		if err != nil {
			return nil, fmt.Errorf("获取合同明细 %d 失败: %w", itemDTO.ContractItemID, err)
		}

		// 计算未到货数量和金额
		unreceivedQuantity := contractItem.ContractQuantity - receivedStats.TotalQuantity
		unreceivedAmount := contractItem.ContractAmount - receivedStats.TotalAmount

		item := model.ArrivalItem{
			ContractItemID:         itemDTO.ContractItemID,
			ReceivedQuantity:       receivedStats.TotalQuantity,       // 已到货数量（不包括本次）
			ReceivedAmount:         receivedStats.TotalAmount,         // 已到货金额（不包括本次）
			UnreceivedQuantity:     unreceivedQuantity,                // 未到货数量
			UnreceivedAmount:       unreceivedAmount,                  // 未到货金额
			CurrentArrivalQuantity: itemDTO.CurrentArrivalQuantity,    // 本次到货数量
			CurrentArrivalAmount:   itemDTO.CurrentArrivalAmount,      // 本次到货金额
			ExpectedArrivalDate:    itemDTO.ExpectedArrivalDate,
			SerialNumbers:          serialNumbersJSON,
			Remark:                 itemDTO.Remark,
		}
		arrival.Items = append(arrival.Items, item)
	}

	// 计算总数量和总金额
	arrival.CalculateTotals()

	// 检查是否完全到货
	isComplete, err := s.checkIfContractFullyArrived(ctx, createDTO.ContractID, arrival.Items)
	if err != nil {
		return nil, fmt.Errorf("检查合同是否完全到货失败: %w", err)
	}
	arrival.IsComplete = isComplete

	// 保存到数据库
	if err := s.repo.Create(ctx, arrival); err != nil {
		return nil, err
	}

	// 直接使用创建人ID（controller层已从JWT获取）
	operatorID := createDTO.CreatedBy

	// 记录创建历史
	operatorName := s.userHelper.GetUserRealName(ctx, operatorID)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypeArrival,
		BusinessID:     arrival.ID,
		PreviousStatus: "",
		NewStatus:      constants.ArrivalStagePurchaseReview,
		Action:         constants.ActionCreate,
		OperatorID:     operatorID,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "创建到货记录",
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		// 仅记录错误，不影响主流程
		log.Printf("记录到货记录状态历史失败: %v", err)
	}

	// 启动工作流
	if s.temporalClient != nil {
		workflowOptions := client.StartWorkflowOptions{
			ID:        fmt.Sprintf("arrival_%d", arrival.ID),
			TaskQueue: workflow.ArrivalTaskQueue,
		}

		// 获取采购申请类型
		requestType, err := s.repo.GetRequestTypeByArrivalID(ctx, arrival.ID)
		if err != nil {
			log.Printf("获取采购申请类型失败: %v", err)
			requestType = "other" // 默认为其他类型
		}

		// 根据合同信息获取公司和项目信息
		companyEntityType := constants.CompanyEntityTypeOther
		companyName := ""
		companyID := uint(0)
		projectName := ""

		// 获取合同的完整信息（包含公司和项目）
		contractWithDetails, err := s.contractRepo.GetByIDWithDetails(ctx, arrival.ContractID)
		if err != nil {
			log.Printf("获取合同详细信息失败: %v", err)
		} else {
			// 获取公司信息
			if contractWithDetails.OurCompany != nil {
				companyID = contractWithDetails.OurCompany.ID
				companyName = contractWithDetails.OurCompany.CompanyName
				companyEntityType = constants.DetermineCompanyEntityType(contractWithDetails.OurCompany.CompanyName)
			}

			// 获取项目信息
			if contractWithDetails.Project != nil {
				projectName = contractWithDetails.Project.ProjectName
			}
		}

		workflowInput := workflow.ArrivalWorkflowInput{
			ArrivalID:         arrival.ID,
			ArrivalNo:         arrival.ArrivalNo,
			RequesterID:       operatorID, // 使用JWT上下文中的用户ID
			ContractID:        arrival.ContractID,
			SupplierID:        arrival.SupplierID,
			TotalAmount:       arrival.TotalAmount,
			CompanyID:         companyID,
			CompanyName:       companyName,
			ProjectName:       projectName,
			BusinessType:      "到货管理",
			CompanyEntityType: companyEntityType,
			RequestType:       requestType,
		}

		_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.ArrivalWorkflowName, workflowInput)
		if err != nil {
			log.Printf("启动到货管理工作流失败: %v", err)
			// 工作流启动失败不影响到货记录创建
		}
	}

	return arrival, nil
}

// GetByID 根据ID获取到货记录
func (s *arrivalService) GetByID(ctx context.Context, id uint) (*dto.ArrivalDetailDTO, error) {
	arrival, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrArrivalNotFound
		}
		return nil, err
	}

	// 转换为简化的DTO
	detailDTO := &dto.ArrivalDetailDTO{
		ID:             arrival.ID,
		ArrivalNo:      arrival.ArrivalNo,
		ContractID:     arrival.ContractID,
		SupplierID:     arrival.SupplierID,
		MayArriveAt:    arrival.MayArriveAt,
		TrackingInfo:   arrival.TrackingInfo,
		ArrivalAddress: arrival.ArrivalAddress,
		ArrivalNotice:  arrival.ArrivalNotice,
		TotalQuantity:  arrival.TotalQuantity,
		TotalAmount:    arrival.TotalAmount,
		IsComplete:     arrival.IsComplete,
		Remark:         arrival.Remark,
		Status:         arrival.Status,
		CreatedBy:      arrival.CreatedBy,
		CreatedAt:      arrival.CreatedAt,
		UpdatedBy:      arrival.UpdatedBy,
		UpdatedAt:      arrival.UpdatedAt,
	}

	// 转换到货明细
	for _, item := range arrival.Items {
		itemDTO := dto.ArrivalItemDetailDTO{
			ArrivalItem:  &item,
			ContractItem: item.ContractItem, // 包含合同明细信息
		}

		// 获取付款信息
		if item.ContractItemID != 0 {
			paidQuantity, paidAmount, unpaidQuantity, unpaidAmount, err := s.repo.GetContractItemPaymentInfo(ctx, item.ContractItemID)
			if err != nil {
				log.Printf("获取合同明细付款信息失败: contractItemID=%d, error=%v", item.ContractItemID, err)
				// 设置默认值，不中断流程
				itemDTO.PaidQuantity = 0
				itemDTO.PaidAmount = 0
				itemDTO.UnpaidQuantity = 0
				itemDTO.UnpaidAmount = 0
			} else {
				itemDTO.PaidQuantity = paidQuantity
				itemDTO.PaidAmount = paidAmount
				itemDTO.UnpaidQuantity = unpaidQuantity
				itemDTO.UnpaidAmount = unpaidAmount
			}
		}

		detailDTO.Items = append(detailDTO.Items, itemDTO)
	}

	// 填充附加信息（从JOIN查询中获取）
	// 注意：这里需要修改仓库层的查询来获取这些信息
	detailDTO.ContractNo = s.getContractNo(ctx, arrival.ContractID)
	detailDTO.SupplierName = s.getSupplierName(ctx, arrival.SupplierID)
	detailDTO.OurCompanyName = s.getOurCompanyName(ctx, arrival.ContractID)

	if arrival.CreatedBy != nil {
		detailDTO.CreatorName = s.userHelper.GetUserRealName(ctx, *arrival.CreatedBy)
	}
	if arrival.UpdatedBy != nil {
		detailDTO.UpdaterName = s.userHelper.GetUserRealName(ctx, *arrival.UpdatedBy)
	}

	return detailDTO, nil
}

// GetByArrivalNo 根据到货通知单号获取到货记录
func (s *arrivalService) GetByArrivalNo(ctx context.Context, arrivalNo string) (*dto.ArrivalDetailDTO, error) {
	arrival, err := s.repo.GetByArrivalNo(ctx, arrivalNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrArrivalNotFound
		}
		return nil, err
	}

	// 转换为简化的DTO
	detailDTO := &dto.ArrivalDetailDTO{
		ID:             arrival.ID,
		ArrivalNo:      arrival.ArrivalNo,
		ContractID:     arrival.ContractID,
		SupplierID:     arrival.SupplierID,
		MayArriveAt:    arrival.MayArriveAt,
		TrackingInfo:   arrival.TrackingInfo,
		ArrivalAddress: arrival.ArrivalAddress,
		ArrivalNotice:  arrival.ArrivalNotice,
		TotalQuantity:  arrival.TotalQuantity,
		TotalAmount:    arrival.TotalAmount,
		IsComplete:     arrival.IsComplete,
		Remark:         arrival.Remark,
		Status:         arrival.Status,
		CreatedBy:      arrival.CreatedBy,
		CreatedAt:      arrival.CreatedAt,
		UpdatedBy:      arrival.UpdatedBy,
		UpdatedAt:      arrival.UpdatedAt,
	}

	// 转换到货明细
	for _, item := range arrival.Items {
		itemDTO := dto.ArrivalItemDetailDTO{
			ArrivalItem:  &item,
			ContractItem: item.ContractItem, // 包含合同明细信息
		}

		// 获取付款信息
		if item.ContractItemID != 0 {
			paidQuantity, paidAmount, unpaidQuantity, unpaidAmount, err := s.repo.GetContractItemPaymentInfo(ctx, item.ContractItemID)
			if err != nil {
				log.Printf("获取合同明细付款信息失败: contractItemID=%d, error=%v", item.ContractItemID, err)
				// 设置默认值，不中断流程
				itemDTO.PaidQuantity = 0
				itemDTO.PaidAmount = 0
				itemDTO.UnpaidQuantity = 0
				itemDTO.UnpaidAmount = 0
			} else {
				itemDTO.PaidQuantity = paidQuantity
				itemDTO.PaidAmount = paidAmount
				itemDTO.UnpaidQuantity = unpaidQuantity
				itemDTO.UnpaidAmount = unpaidAmount
			}
		}

		detailDTO.Items = append(detailDTO.Items, itemDTO)
	}

	// 填充附加信息
	detailDTO.ContractNo = s.getContractNo(ctx, arrival.ContractID)
	detailDTO.SupplierName = s.getSupplierName(ctx, arrival.SupplierID)

	if arrival.CreatedBy != nil {
		detailDTO.CreatorName = s.userHelper.GetUserRealName(ctx, *arrival.CreatedBy)
	}
	if arrival.UpdatedBy != nil {
		detailDTO.UpdaterName = s.userHelper.GetUserRealName(ctx, *arrival.UpdatedBy)
	}

	return detailDTO, nil
}

// Update 更新到货记录
func (s *arrivalService) Update(ctx context.Context, id uint, updateDTO *dto.UpdateArrivalDTO) error {
	// 获取到货记录
	arrival, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrArrivalNotFound
		}
		return err
	}

	// 只有采购负责人审批阶段的到货记录才能更新
	if arrival.Status != constants.ArrivalStagePurchaseReview {
		return errors.New("只有采购负责人审批阶段的到货记录才能更新")
	}

	// 更新基本信息
	arrival.MayArriveAt = updateDTO.MayArriveAt
	arrival.TrackingInfo = updateDTO.TrackingInfo
	arrival.ArrivalAddress = updateDTO.ArrivalAddress
	arrival.ArrivalNotice = updateDTO.ArrivalNotice
	arrival.Remark = updateDTO.Remark
	arrival.UpdatedBy = &updateDTO.UpdatedBy
	now := time.Now()
	arrival.UpdatedAt = &now

	// 更新明细项
	// 这里简化处理，实际应该支持增删改明细项
	if len(updateDTO.Items) > 0 {
		arrival.Items = []model.ArrivalItem{}
		for _, itemDTO := range updateDTO.Items {
			// 序列化SN号列表
			serialNumbersJSON := ""
			if len(itemDTO.SerialNumbers) > 0 {
				serialNumbersBytes, _ := json.Marshal(itemDTO.SerialNumbers)
				serialNumbersJSON = string(serialNumbersBytes)
			}

			item := model.ArrivalItem{
				ID:                     itemDTO.ID,
				ArrivalID:              id,
				ContractItemID:         itemDTO.ContractItemID,
				CurrentArrivalQuantity: itemDTO.CurrentArrivalQuantity,
				CurrentArrivalAmount:   itemDTO.CurrentArrivalAmount,
				ExpectedArrivalDate:    itemDTO.ExpectedArrivalDate,
				SerialNumbers:          serialNumbersJSON,
				Remark:                 itemDTO.Remark,
			}
			arrival.Items = append(arrival.Items, item)
		}
	}

	// 重新计算总数量和总金额
	arrival.CalculateTotals()

	// 保存更新
	return s.repo.Update(ctx, arrival)
}

// Delete 删除到货记录
func (s *arrivalService) Delete(ctx context.Context, id uint, deletedBy uint) error {
	// 获取到货记录
	arrival, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrArrivalNotFound
		}
		return err
	}

	// 只有采购负责人审批阶段的到货记录才能删除
	if arrival.Status != constants.ArrivalStagePurchaseReview {
		return errors.New("只有采购负责人审批阶段的到货记录才能删除")
	}

	// 删除到货记录
	return s.repo.Delete(ctx, id)
}

// List 获取到货记录列表
func (s *arrivalService) List(ctx context.Context, query *dto.ArrivalQueryDTO) ([]*dto.ArrivalListDTO, int64, error) {
	arrivals, total, err := s.repo.List(ctx, query)
	if err != nil {
		return nil, 0, err
	}

	var listDTOs []*dto.ArrivalListDTO
	for _, arrival := range arrivals {
		listDTO := &dto.ArrivalListDTO{
			ID:             arrival.ID,
			ArrivalNo:      arrival.ArrivalNo,
			ContractID:     arrival.ContractID,
			SupplierID:     arrival.SupplierID,
			MayArriveAt:    arrival.MayArriveAt,
			TrackingInfo:   arrival.TrackingInfo,   // 物流信息
			ArrivalAddress: arrival.ArrivalAddress, // 到货地址
			ArrivalNotice:  arrival.ArrivalNotice,  // 到货通知
			TotalQuantity:  arrival.TotalQuantity,
			TotalAmount:    arrival.TotalAmount,
			IsComplete:     arrival.IsComplete,
			Status:         arrival.Status,
			CreatedBy:      arrival.CreatedBy,
			CreatedAt:      arrival.CreatedAt,
		}

		// 填充附加信息
		listDTO.ContractNo = s.getContractNo(ctx, arrival.ContractID)
		listDTO.SupplierName = s.getSupplierName(ctx, arrival.SupplierID)
		listDTO.OurCompanyName = s.getOurCompanyName(ctx, arrival.ContractID)
		if arrival.CreatedBy != nil {
			listDTO.CreatorName = s.userHelper.GetUserRealName(ctx, *arrival.CreatedBy)
		}

		// 填充到货明细信息
		var itemDTOs []*dto.ArrivalItemDetailDTO
		for _, item := range arrival.Items {
			itemDTO := &dto.ArrivalItemDetailDTO{
				ArrivalItem:  &item,
				ContractItem: item.ContractItem, // 包含合同明细信息
			}

			// 获取付款信息
			if item.ContractItemID != 0 {
				paidQuantity, paidAmount, unpaidQuantity, unpaidAmount, err := s.repo.GetContractItemPaymentInfo(ctx, item.ContractItemID)
				if err != nil {
					log.Printf("获取合同明细付款信息失败: contractItemID=%d, error=%v", item.ContractItemID, err)
					// 设置默认值，不中断流程
					itemDTO.PaidQuantity = 0
					itemDTO.PaidAmount = 0
					itemDTO.UnpaidQuantity = 0
					itemDTO.UnpaidAmount = 0
				} else {
					itemDTO.PaidQuantity = paidQuantity
					itemDTO.PaidAmount = paidAmount
					itemDTO.UnpaidQuantity = unpaidQuantity
					itemDTO.UnpaidAmount = unpaidAmount
				}
			}

			// 如果有合同明细信息，填充合同明细编号
			if item.ContractItem != nil {
				itemDTO.ContractItemNo = fmt.Sprintf("CI-%d", item.ContractItem.ID)
				itemDTO.MaterialType = item.ContractItem.MaterialType
				itemDTO.Model = item.ContractItem.Model
				itemDTO.Brand = item.ContractItem.Brand
				itemDTO.PN = item.ContractItem.PN
				itemDTO.Spec = item.ContractItem.Spec
				itemDTO.Unit = item.ContractItem.Unit
			}

			// 解析SN号列表
			if item.SerialNumbers != "" {
				var serialNumbers []string
				if err := json.Unmarshal([]byte(item.SerialNumbers), &serialNumbers); err == nil {
					itemDTO.SerialNumberList = serialNumbers
				}
			}

			itemDTOs = append(itemDTOs, itemDTO)
		}
		listDTO.Items = itemDTOs

		listDTOs = append(listDTOs, listDTO)
	}

	return listDTOs, total, nil
}

// Submit 提交到货记录
// 注意：创建到货记录后直接进入审批状态，不再需要单独的提交步骤
func (s *arrivalService) Submit(ctx context.Context, id uint, submittedBy uint) error {
	// 获取到货记录
	arrival, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrArrivalNotFound
		}
		return err
	}

	// 检查当前状态
	if arrival.Status == constants.ArrivalStagePurchaseReview {
		return errors.New("到货记录已在审批中，无需重复提交")
	}

	return errors.New("到货记录创建后直接进入审批状态，无需单独提交")
}

// Cancel 取消到货记录
func (s *arrivalService) Cancel(ctx context.Context, id uint, cancelledBy uint) error {
	// 获取到货记录
	arrival, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrArrivalNotFound
		}
		return err
	}

	// 检查是否可以取消
	if !arrival.IsCancellable() {
		return errors.New("当前状态的到货记录不能取消")
	}

	// 更新状态为已取消
	if err := s.repo.UpdateStatus(ctx, id, constants.ArrivalStageCancelled, cancelledBy); err != nil {
		return err
	}

	// 创建状态历史记录
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypeArrival,
		BusinessID:     id,
		Action:         constants.ActionCancel,
		PreviousStatus: arrival.Status,
		NewStatus:      constants.ArrivalStageCancelled,
		Comments:       "取消到货记录",
		OperatorID:     cancelledBy,
		OperatorName:   s.userHelper.GetUserRealName(ctx, cancelledBy),
		OperationTime:  time.Now(),
		CreatedAt:      time.Now(),
	}

	return s.repo.CreateStatusHistory(ctx, history)
}

// Approve 审批到货记录
func (s *arrivalService) Approve(ctx context.Context, approvalDTO *dto.ArrivalApprovalDTO, approverID uint, approverName string) error {
	// 获取到货记录
	arrival, err := s.repo.GetByID(ctx, approvalDTO.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrArrivalNotFound
		}
		return err
	}

	// 直接使用传入的approverID（controller层已从JWT获取）
	userID := approverID

	// 构造审批信号数据
	approvalSignal := workflow.ArrivalApprovalSignal{
		ApproverID:   userID,
		Action:       approvalDTO.Action,
		Comments:     approvalDTO.Comments,
		CurrentStage: arrival.Status, // 使用到货记录的当前状态
		Timestamp:    time.Now(),
	}

	// 发送审批信号到工作流
	workflowID := fmt.Sprintf("arrival_%d", approvalDTO.ID)
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNameArrivalApproval, approvalSignal)
	if err != nil {
		return fmt.Errorf("发送审批信号失败: %w", err)
	}

	return nil
}

// Rollback 回退到货记录
func (s *arrivalService) Rollback(ctx context.Context, rollbackDTO *dto.ArrivalRollbackDTO, operatorID uint, operatorName string) error {
	// 获取到货记录
	arrival, err := s.repo.GetByID(ctx, rollbackDTO.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrArrivalNotFound
		}
		return err
	}

	// 直接使用传入的operatorID（controller层已从JWT获取）
	userID := operatorID

	// 确定回退的目标状态
	var targetStatus string
	switch arrival.Status {
	case constants.ArrivalStagePurchaseReview:
		targetStatus = constants.ArrivalStageDraft
	case constants.ArrivalStageServerAdminReview,
		constants.ArrivalStageNetworkAdminReview,
		constants.ArrivalStageAssetAdminReview:
		targetStatus = constants.ArrivalStagePurchaseReview
	default:
		return errors.New("无效的回退状态")
	}

	// 构造回退信号数据
	rollbackSignal := workflow.ArrivalRollbackSignal{
		ApproverID:   userID,
		RollbackTo:   targetStatus,
		Comments:     rollbackDTO.Comments,
		CurrentStage: arrival.Status,
		Timestamp:    time.Now(),
	}

	// 发送回退信号到工作流
	workflowID := fmt.Sprintf("arrival_%d", rollbackDTO.ID)
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNameArrivalRollback, rollbackSignal)
	if err != nil {
		return fmt.Errorf("发送回退信号失败: %w", err)
	}

	return nil
}

// GetByContractID 根据合同ID获取到货记录列表
func (s *arrivalService) GetByContractID(ctx context.Context, contractID uint) ([]*model.Arrival, error) {
	return s.repo.GetByContractID(ctx, contractID)
}

// GetArrivalHistory 获取到货记录历史记录
func (s *arrivalService) GetArrivalHistory(ctx context.Context, arrivalID uint) ([]*dto.ArrivalHistoryDTO, error) {
	// 检查到货记录是否存在
	_, err := s.repo.GetByID(ctx, arrivalID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrArrivalNotFound
		}
		return nil, fmt.Errorf("检查到货记录是否存在失败: %w", err)
	}

	// 从仓库获取历史记录
	histories, err := s.repo.GetArrivalHistory(ctx, arrivalID)
	if err != nil {
		return nil, fmt.Errorf("获取到货记录历史记录失败: %w", err)
	}

	// 转换为DTO
	var historyDTOs []*dto.ArrivalHistoryDTO
	for _, history := range histories {
		historyDTO := &dto.ArrivalHistoryDTO{
			ID:           history.ID,
			ArrivalID:    history.BusinessID,
			Action:       history.Action,
			FromStatus:   history.PreviousStatus,
			ToStatus:     history.NewStatus,
			Comments:     history.Comments,
			ApproverID:   history.OperatorID,
			ApproverName: history.OperatorName,
			CreatedAt:    history.CreatedAt,
		}
		historyDTOs = append(historyDTOs, historyDTO)
	}

	return historyDTOs, nil
}

// GetStatistics 获取到货统计信息
func (s *arrivalService) GetStatistics(ctx context.Context) (*dto.ArrivalStatisticsDTO, error) {
	return s.repo.GetStatistics(ctx)
}

// getContractNo 获取合同编号
func (s *arrivalService) getContractNo(ctx context.Context, contractID uint) string {
	contract, err := s.contractRepo.GetByID(ctx, contractID)
	if err != nil {
		return ""
	}
	return contract.ContractNo
}

// getSupplierName 获取供应商名称
func (s *arrivalService) getSupplierName(ctx context.Context, supplierID uint) string {
	supplier, err := s.supplierRepo.GetByID(ctx, supplierID)
	if err != nil {
		return ""
	}
	return supplier.SupplierName
}

// getOurCompanyName 获取我方公司名称
func (s *arrivalService) getOurCompanyName(ctx context.Context, contractID uint) string {
	contract, err := s.contractRepo.GetByID(ctx, contractID)
	if err != nil {
		return ""
	}

	// 获取我方公司信息
	if contract.OurCompanyID > 0 {
		company, err := s.companyService.GetCompanyByID(ctx, contract.OurCompanyID)
		if err == nil && company != nil {
			return company.CompanyName
		}
	}

	return ""
}

// ReceivedStats 到货统计信息
type ReceivedStats struct {
	TotalQuantity int     // 总到货数量
	TotalAmount   float64 // 总到货金额
}

// calculateContractItemReceivedStats 计算合同明细的历史到货统计（不包括本次到货）
func (s *arrivalService) calculateContractItemReceivedStats(ctx context.Context, contractItemID uint) (*ReceivedStats, error) {
	// 查询该合同明细的所有历史到货记录
	arrivals, err := s.repo.GetArrivalsByContractItemID(ctx, contractItemID)
	if err != nil {
		return nil, err
	}

	stats := &ReceivedStats{
		TotalQuantity: 0,
		TotalAmount:   0.0,
	}

	// 累计所有历史到货数量和金额
	for _, arrival := range arrivals {
		for _, item := range arrival.Items {
			if item.ContractItemID == contractItemID {
				stats.TotalQuantity += item.CurrentArrivalQuantity
				stats.TotalAmount += item.CurrentArrivalAmount
			}
		}
	}

	return stats, nil
}

// checkIfContractFullyArrived 检查合同是否完全到货（包括本次到货）
func (s *arrivalService) checkIfContractFullyArrived(ctx context.Context, contractID uint, currentItems []model.ArrivalItem) (bool, error) {
	// 获取合同的所有明细
	contractItems, err := s.contractRepo.GetContractItemsByContractID(ctx, contractID)
	if err != nil {
		return false, err
	}

	// 为每个合同明细检查是否完全到货
	for _, contractItem := range contractItems {
		// 计算该明细的历史到货统计
		receivedStats, err := s.calculateContractItemReceivedStats(ctx, contractItem.ID)
		if err != nil {
			return false, err
		}

		// 加上本次到货数量
		currentArrivalQuantity := 0
		for _, currentItem := range currentItems {
			if currentItem.ContractItemID == contractItem.ID {
				currentArrivalQuantity += currentItem.CurrentArrivalQuantity
			}
		}

		// 检查总到货数量是否达到合同要求
		totalArrivedQuantity := receivedStats.TotalQuantity + currentArrivalQuantity
		if totalArrivedQuantity < contractItem.ContractQuantity {
			return false, nil // 还有明细未完全到货
		}
	}

	return true, nil // 所有明细都已完全到货
}
