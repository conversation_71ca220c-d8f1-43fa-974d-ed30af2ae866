package repository

import (
	"context"
	"encoding/json"
	"fmt"

	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"

	"gorm.io/gorm"
)

// ShipmentRepository 发货记录仓库接口
type ShipmentRepository interface {
	// Create 创建发货记录
	Create(ctx context.Context, shipment *model.Shipment) error

	// GetByID 根据ID获取发货记录
	GetByID(ctx context.Context, id uint) (*model.Shipment, error)

	// GetByShipmentNo 根据发货通知单号获取发货记录
	GetByShipmentNo(ctx context.Context, shipmentNo string) (*model.Shipment, error)

	// Update 更新发货记录
	Update(ctx context.Context, shipment *model.Shipment) error

	// Delete 删除发货记录
	Delete(ctx context.Context, id uint) error

	// List 获取发货记录列表
	List(ctx context.Context, query *dto.ShipmentQueryDTO) ([]*model.Shipment, int64, error)

	// GetByContractID 根据合同ID获取发货记录列表
	GetByContractID(ctx context.Context, contractID uint) ([]*model.Shipment, error)

	// IsShipmentNoExists 检查发货通知单号是否存在
	IsShipmentNoExists(ctx context.Context, shipmentNo string) (bool, error)

	// UpdateStatus 更新发货记录状态
	UpdateStatus(ctx context.Context, id uint, status string, operatorID uint) error

	// CreateStatusHistory 创建状态历史记录
	CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error

	// GetStatusHistory 获取状态历史记录
	GetStatusHistory(ctx context.Context, shipmentID uint) ([]*model.PurchaseApprovalHistory, error)

	// GetStatistics 获取发货统计信息
	GetStatistics(ctx context.Context) (*dto.ShipmentStatisticsDTO, error)

	// GetShipmentsByContractItemID 根据合同明细ID获取发货记录列表
	GetShipmentsByContractItemID(ctx context.Context, contractItemID uint) ([]*model.Shipment, error)

	// GetContractItemPaymentInfo 获取合同明细的付款信息
	GetContractItemPaymentInfo(ctx context.Context, contractItemID uint) (paidQuantity float64, paidAmount float64, unpaidQuantity float64, unpaidAmount float64, err error)
}

// shipmentRepository 发货记录仓库实现
type shipmentRepository struct {
	db *gorm.DB
}

// NewShipmentRepository 创建发货记录仓库
func NewShipmentRepository(db *gorm.DB) ShipmentRepository {
	return &shipmentRepository{
		db: db,
	}
}

// Create 创建发货记录
func (r *shipmentRepository) Create(ctx context.Context, shipment *model.Shipment) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建发货记录
		if err := tx.Create(shipment).Error; err != nil {
			return fmt.Errorf("创建发货记录失败: %w", err)
		}

		// 处理发货明细中的序列号
		for i := range shipment.Items {
			if len(shipment.Items[i].SerialNumbers) > 0 {
				serialNumbersJSON, err := json.Marshal(shipment.Items[i].SerialNumbers)
				if err != nil {
					return fmt.Errorf("序列化SN号失败: %w", err)
				}
				shipment.Items[i].SerialNumbers = string(serialNumbersJSON)
			}
		}

		return nil
	})
}

// GetByID 根据ID获取发货记录
func (r *shipmentRepository) GetByID(ctx context.Context, id uint) (*model.Shipment, error) {
	var shipment model.Shipment
	err := r.db.WithContext(ctx).
		Preload("Contract").
		Preload("Contract.OurCompany").
		Preload("Supplier").
		Preload("Items").
		Preload("Items.ContractItem").
		First(&shipment, id).Error

	if err != nil {
		return nil, err
	}

	// 注意：序列号以JSON字符串形式存储在数据库中，在需要时可以通过JSON反序列化获取

	return &shipment, nil
}

// GetByShipmentNo 根据发货通知单号获取发货记录
func (r *shipmentRepository) GetByShipmentNo(ctx context.Context, shipmentNo string) (*model.Shipment, error) {
	var shipment model.Shipment
	err := r.db.WithContext(ctx).
		Preload("Contract").
		Preload("Supplier").
		Preload("Items").
		Preload("Items.ContractItem").
		Where("shipment_no = ?", shipmentNo).
		First(&shipment).Error

	return &shipment, err
}

// Update 更新发货记录
func (r *shipmentRepository) Update(ctx context.Context, shipment *model.Shipment) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新发货记录基本信息
		if err := tx.Model(shipment).Updates(shipment).Error; err != nil {
			return fmt.Errorf("更新发货记录失败: %w", err)
		}

		// 删除原有的发货明细
		if err := tx.Where("shipment_id = ?", shipment.ID).Delete(&model.ShipmentItem{}).Error; err != nil {
			return fmt.Errorf("删除原有发货明细失败: %w", err)
		}

		// 创建新的发货明细
		for i := range shipment.Items {
			shipment.Items[i].ShipmentID = shipment.ID
			if len(shipment.Items[i].SerialNumbers) > 0 {
				serialNumbersJSON, err := json.Marshal(shipment.Items[i].SerialNumbers)
				if err != nil {
					return fmt.Errorf("序列化SN号失败: %w", err)
				}
				shipment.Items[i].SerialNumbers = string(serialNumbersJSON)
			}
		}

		if len(shipment.Items) > 0 {
			if err := tx.Create(&shipment.Items).Error; err != nil {
				return fmt.Errorf("创建发货明细失败: %w", err)
			}
		}

		return nil
	})
}

// Delete 删除发货记录
func (r *shipmentRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除发货明细
		if err := tx.Where("shipment_id = ?", id).Delete(&model.ShipmentItem{}).Error; err != nil {
			return fmt.Errorf("删除发货明细失败: %w", err)
		}

		// 删除发货记录
		if err := tx.Delete(&model.Shipment{}, id).Error; err != nil {
			return fmt.Errorf("删除发货记录失败: %w", err)
		}

		return nil
	})
}

// List 获取发货记录列表
func (r *shipmentRepository) List(ctx context.Context, query *dto.ShipmentQueryDTO) ([]*model.Shipment, int64, error) {
	var shipments []*model.Shipment
	var total int64

	// 构建查询条件
	db := r.db.WithContext(ctx).Model(&model.Shipment{})

	// 添加关联查询
	db = db.Preload("Contract").Preload("Contract.OurCompany").Preload("Supplier").Preload("Items").Preload("Items.ContractItem")

	// 添加过滤条件
	if query.ShipmentNo != "" {
		db = db.Where("shipment_no LIKE ?", "%"+query.ShipmentNo+"%")
	}

	if query.ContractID != nil {
		db = db.Where("contract_id = ?", *query.ContractID)
	}

	if query.ContractNo != "" {
		db = db.Joins("JOIN purchase_contracts ON shipments.contract_id = purchase_contracts.id").
			Where("purchase_contracts.contract_no LIKE ?", "%"+query.ContractNo+"%")
	}

	if query.SupplierID != nil {
		db = db.Where("supplier_id = ?", *query.SupplierID)
	}

	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}

	if query.IsComplete != nil {
		db = db.Where("is_complete = ?", *query.IsComplete)
	}

	if query.CreatedBy != nil {
		db = db.Where("created_by = ?", *query.CreatedBy)
	}

	if query.StartDate != nil {
		db = db.Where("created_at >= ?", *query.StartDate)
	}

	if query.EndDate != nil {
		db = db.Where("created_at <= ?", *query.EndDate)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取发货记录总数失败: %w", err)
	}

	// 分页查询
	if query.Page > 0 && query.PageSize > 0 {
		offset := (query.Page - 1) * query.PageSize
		db = db.Offset(offset).Limit(query.PageSize)
	}

	// 排序
	db = db.Order("created_at DESC")

	// 执行查询
	if err := db.Find(&shipments).Error; err != nil {
		return nil, 0, fmt.Errorf("获取发货记录列表失败: %w", err)
	}

	return shipments, total, nil
}

// GetByContractID 根据合同ID获取发货记录列表
func (r *shipmentRepository) GetByContractID(ctx context.Context, contractID uint) ([]*model.Shipment, error) {
	var shipments []*model.Shipment
	err := r.db.WithContext(ctx).
		Preload("Contract").
		Preload("Supplier").
		Preload("Items").
		Where("contract_id = ?", contractID).
		Order("created_at DESC").
		Find(&shipments).Error

	return shipments, err
}

// IsShipmentNoExists 检查发货通知单号是否存在
func (r *shipmentRepository) IsShipmentNoExists(ctx context.Context, shipmentNo string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.Shipment{}).Where("shipment_no = ?", shipmentNo).Count(&count).Error
	return count > 0, err
}

// UpdateStatus 更新发货记录状态
func (r *shipmentRepository) UpdateStatus(ctx context.Context, id uint, status string, operatorID uint) error {
	return r.db.WithContext(ctx).Model(&model.Shipment{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_by": operatorID,
		}).Error
}

// CreateStatusHistory 创建状态历史记录
func (r *shipmentRepository) CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// GetStatusHistory 获取状态历史记录
func (r *shipmentRepository) GetStatusHistory(ctx context.Context, shipmentID uint) ([]*model.PurchaseApprovalHistory, error) {
	var histories []*model.PurchaseApprovalHistory
	err := r.db.WithContext(ctx).
		Where("business_type = ? AND business_id = ?", "shipment", shipmentID).
		Order("operation_time DESC").
		Find(&histories).Error

	return histories, err
}

// GetStatistics 获取发货统计信息
func (r *shipmentRepository) GetStatistics(ctx context.Context) (*dto.ShipmentStatisticsDTO, error) {
	var stats dto.ShipmentStatisticsDTO

	// 获取总数
	if err := r.db.WithContext(ctx).Model(&model.Shipment{}).Count(&stats.TotalCount).Error; err != nil {
		return nil, fmt.Errorf("获取发货记录总数失败: %w", err)
	}

	// 按状态统计
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := r.db.WithContext(ctx).Model(&model.Shipment{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("获取状态统计失败: %w", err)
	}

	for _, sc := range statusCounts {
		switch sc.Status {
		case constants.ShipmentStageDraft:
			stats.DraftCount = sc.Count
		case constants.ShipmentStageCompleted:
			stats.CompletedCount = sc.Count
		case constants.ShipmentStageCancelled:
			stats.CancelledCount = sc.Count
		default:
			stats.PendingCount += sc.Count
		}
	}

	// 获取金额统计
	if err := r.db.WithContext(ctx).Model(&model.Shipment{}).
		Select("SUM(total_amount) as total_amount").
		Row().Scan(&stats.TotalAmount); err != nil {
		return nil, fmt.Errorf("获取总金额失败: %w", err)
	}

	if err := r.db.WithContext(ctx).Model(&model.Shipment{}).
		Where("status = ?", constants.ShipmentStageCompleted).
		Select("SUM(total_amount) as completed_amount").
		Row().Scan(&stats.CompletedAmount); err != nil {
		return nil, fmt.Errorf("获取已完成金额失败: %w", err)
	}

	return &stats, nil
}

// GetShipmentsByContractItemID 根据合同明细ID获取发货记录列表
func (r *shipmentRepository) GetShipmentsByContractItemID(ctx context.Context, contractItemID uint) ([]*model.Shipment, error) {
	var shipments []*model.Shipment

	err := r.db.WithContext(ctx).
		Preload("Items", "contract_item_id = ?", contractItemID).
		Joins("JOIN shipment_items ON shipments.id = shipment_items.shipment_id").
		Where("shipment_items.contract_item_id = ?", contractItemID).
		Group("shipments.id").
		Find(&shipments).Error

	if err != nil {
		return nil, fmt.Errorf("根据合同明细ID获取发货记录失败: %w", err)
	}

	return shipments, nil
}

// GetContractItemPaymentInfo 获取合同明细的付款信息
func (r *shipmentRepository) GetContractItemPaymentInfo(ctx context.Context, contractItemID uint) (paidQuantity float64, paidAmount float64, unpaidQuantity float64, unpaidAmount float64, err error) {
	// 首先获取合同明细的基本信息（总数量和总金额）
	var contractItem struct {
		ContractQuantity int     `json:"contract_quantity"`
		ContractAmount   float64 `json:"contract_amount"`
	}

	err = r.db.WithContext(ctx).
		Table("purchase_contract_items").
		Select("contract_quantity, contract_amount").
		Where("id = ?", contractItemID).
		Scan(&contractItem).Error

	if err != nil {
		return 0, 0, 0, 0, err
	}

	// 查询该合同明细在所有已完成付款申请中的累计已付款数量和金额
	var result struct {
		TotalPaidQuantity float64 `json:"total_paid_quantity"`
		TotalPaidAmount   float64 `json:"total_paid_amount"`
	}

	err = r.db.WithContext(ctx).
		Table("payment_request_items pri").
		Select("COALESCE(SUM(pri.current_payment_quantity), 0) as total_paid_quantity, COALESCE(SUM(pri.current_payment_amount), 0) as total_paid_amount").
		Joins("LEFT JOIN payment_requests pr ON pri.payment_id = pr.id").
		Where("pri.contract_item_id = ? AND pr.status = 'completed' AND pr.deleted_at IS NULL", contractItemID).
		Scan(&result).Error

	if err != nil {
		return 0, 0, 0, 0, err
	}

	// 计算各项数值
	paidQuantity = result.TotalPaidQuantity
	paidAmount = result.TotalPaidAmount
	unpaidQuantity = float64(contractItem.ContractQuantity) - result.TotalPaidQuantity
	unpaidAmount = contractItem.ContractAmount - result.TotalPaidAmount

	// 防止负数
	if unpaidQuantity < 0 {
		unpaidQuantity = 0
	}
	if unpaidAmount < 0 {
		unpaidAmount = 0
	}

	return paidQuantity, paidAmount, unpaidQuantity, unpaidAmount, nil
}
