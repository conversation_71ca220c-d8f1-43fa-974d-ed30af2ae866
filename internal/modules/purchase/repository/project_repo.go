package repository

import (
	"backend/internal/modules/purchase/model"
	"context"

	"gorm.io/gorm"
)

// ProjectRepository 项目信息仓库接口
type ProjectRepository interface {
	// GetByID 根据ID获取项目信息
	GetByID(ctx context.Context, id uint) (*model.Project, error)

	// GetByProjectCode 根据项目编号获取项目信息
	GetByProjectCode(ctx context.Context, projectCode string) (*model.Project, error)

	// List 获取项目信息列表
	List(ctx context.Context, filter model.ProjectFilter, pagination model.PaginationOptions) ([]*model.Project, int64, error)

	// Create 创建项目信息
	Create(ctx context.Context, project *model.Project) error

	// Update 更新项目信息
	Update(ctx context.Context, project *model.Project) error

	// Delete 删除项目信息
	Delete(ctx context.Context, id uint, deletedBy uint) error

	// GetDB 获取数据库实例
	GetDB() *gorm.DB
}

// projectRepository 项目信息仓库实现
type projectRepository struct {
	db *gorm.DB
}

// NewProjectRepository 创建项目信息仓库实例
func NewProjectRepository(db *gorm.DB) ProjectRepository {
	return &projectRepository{db: db}
}

// GetByID 根据ID获取项目信息
func (r *projectRepository) GetByID(ctx context.Context, id uint) (*model.Project, error) {
	var project model.Project
	if err := r.db.WithContext(ctx).First(&project, id).Error; err != nil {
		return nil, err
	}
	return &project, nil
}

// GetByProjectCode 根据项目编号获取项目信息
func (r *projectRepository) GetByProjectCode(ctx context.Context, projectCode string) (*model.Project, error) {
	var project model.Project
	if err := r.db.WithContext(ctx).Where("project_code = ?", projectCode).First(&project).Error; err != nil {
		return nil, err
	}
	return &project, nil
}

// List 获取项目信息列表
func (r *projectRepository) List(ctx context.Context, filter model.ProjectFilter, pagination model.PaginationOptions) ([]*model.Project, int64, error) {
	var projects []*model.Project
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Project{})

	// 应用过滤条件
	if filter.ProjectCode != "" {
		query = query.Where("project_code LIKE ?", "%"+filter.ProjectCode+"%")
	}
	if filter.ProjectName != "" {
		query = query.Where("project_name LIKE ?", "%"+filter.ProjectName+"%")
	}
	if filter.CustomerName != "" {
		query = query.Where("customer_name LIKE ?", "%"+filter.CustomerName+"%")
	}
	if filter.ProjectManager != "" {
		query = query.Where("project_manager LIKE ?", "%"+filter.ProjectManager+"%")
	}
	if filter.ContactPerson != "" {
		query = query.Where("contact_person LIKE ?", "%"+filter.ContactPerson+"%")
	}
	if filter.ProjectStatus != nil {
		query = query.Where("project_status = ?", *filter.ProjectStatus)
	}
	if filter.StartDateBegin != "" {
		query = query.Where("start_date >= ?", filter.StartDateBegin)
	}
	if filter.StartDateEnd != "" {
		query = query.Where("start_date <= ?", filter.StartDateEnd)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用分页
	if pagination.Page > 0 && pagination.PageSize > 0 {
		offset := (pagination.Page - 1) * pagination.PageSize
		query = query.Offset(offset).Limit(pagination.PageSize)
	}

	// 排序
	query = query.Order("created_at DESC")

	// 执行查询
	if err := query.Find(&projects).Error; err != nil {
		return nil, 0, err
	}

	return projects, total, nil
}

// Create 创建项目信息
func (r *projectRepository) Create(ctx context.Context, project *model.Project) error {
	return r.db.WithContext(ctx).Create(project).Error
}

// Update 更新项目信息
func (r *projectRepository) Update(ctx context.Context, project *model.Project) error {
	return r.db.WithContext(ctx).Save(project).Error
}

// Delete 删除项目信息
func (r *projectRepository) Delete(ctx context.Context, id uint, deletedBy uint) error {
	// 先更新操作人ID到updated_by字段
	if err := r.db.WithContext(ctx).Model(&model.Project{}).
		Where("id = ?", id).
		Update("updated_by", deletedBy).Error; err != nil {
		return err
	}

	// 然后执行软删除
	return r.db.WithContext(ctx).Delete(&model.Project{}, id).Error
}

// GetDB 获取数据库实例
func (r *projectRepository) GetDB() *gorm.DB {
	return r.db
}
