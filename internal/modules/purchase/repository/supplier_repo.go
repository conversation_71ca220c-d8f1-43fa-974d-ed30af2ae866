package repository

import (
	"backend/internal/modules/purchase/model"
	"context"

	"gorm.io/gorm"
)

// SupplierRepository 供应商信息仓库接口
type SupplierRepository interface {
	// GetByID 根据ID获取供应商信息
	GetByID(ctx context.Context, id uint) (*model.Supplier, error)

	// GetBySupplierCode 根据供应商编码获取供应商信息
	GetBySupplierCode(ctx context.Context, supplierCode string) (*model.Supplier, error)

	// List 获取供应商信息列表
	List(ctx context.Context, filter model.SupplierFilter, pagination model.PaginationOptions) ([]*model.Supplier, int64, error)

	// Create 创建供应商信息
	Create(ctx context.Context, supplier *model.Supplier) error

	// Update 更新供应商信息
	Update(ctx context.Context, supplier *model.Supplier) error

	// Delete 删除供应商信息
	Delete(ctx context.Context, id uint, deletedBy uint) error

	// GetDB 获取数据库实例
	GetDB() *gorm.DB
}

// supplierRepository 供应商信息仓库实现
type supplierRepository struct {
	db *gorm.DB
}

// NewSupplierRepository 创建供应商信息仓库实例
func NewSupplierRepository(db *gorm.DB) SupplierRepository {
	return &supplierRepository{db: db}
}

// GetByID 根据ID获取供应商信息
func (r *supplierRepository) GetByID(ctx context.Context, id uint) (*model.Supplier, error) {
	var supplier model.Supplier
	if err := r.db.WithContext(ctx).First(&supplier, id).Error; err != nil {
		return nil, err
	}
	return &supplier, nil
}

// GetBySupplierCode 根据供应商编码获取供应商信息
func (r *supplierRepository) GetBySupplierCode(ctx context.Context, supplierCode string) (*model.Supplier, error) {
	var supplier model.Supplier
	if err := r.db.WithContext(ctx).Where("supplier_code = ?", supplierCode).First(&supplier).Error; err != nil {
		return nil, err
	}
	return &supplier, nil
}

// List 获取供应商信息列表
func (r *supplierRepository) List(ctx context.Context, filter model.SupplierFilter, pagination model.PaginationOptions) ([]*model.Supplier, int64, error) {
	var suppliers []*model.Supplier
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Supplier{})

	// 应用过滤条件
	if filter.SupplierCode != "" {
		query = query.Where("supplier_code LIKE ?", "%"+filter.SupplierCode+"%")
	}
	if filter.SupplierName != "" {
		query = query.Where("supplier_name LIKE ?", "%"+filter.SupplierName+"%")
	}
	if filter.ShortName != "" {
		query = query.Where("short_name LIKE ?", "%"+filter.ShortName+"%")
	}
	if filter.ContactPerson != "" {
		query = query.Where("contact_person LIKE ?", "%"+filter.ContactPerson+"%")
	}
	if filter.ContactPhone != "" {
		query = query.Where("contact_phone LIKE ?", "%"+filter.ContactPhone+"%")
	}
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}
	if filter.ContractDateBegin != "" {
		query = query.Where("contract_start_date >= ?", filter.ContractDateBegin)
	}
	if filter.ContractDateEnd != "" {
		query = query.Where("contract_end_date <= ?", filter.ContractDateEnd)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用分页
	if pagination.Page > 0 && pagination.PageSize > 0 {
		offset := (pagination.Page - 1) * pagination.PageSize
		query = query.Offset(offset).Limit(pagination.PageSize)
	}

	// 排序
	query = query.Order("created_at DESC")

	// 执行查询
	if err := query.Find(&suppliers).Error; err != nil {
		return nil, 0, err
	}

	return suppliers, total, nil
}

// Create 创建供应商信息
func (r *supplierRepository) Create(ctx context.Context, supplier *model.Supplier) error {
	return r.db.WithContext(ctx).Create(supplier).Error
}

// Update 更新供应商信息
func (r *supplierRepository) Update(ctx context.Context, supplier *model.Supplier) error {
	return r.db.WithContext(ctx).Save(supplier).Error
}

// Delete 删除供应商信息
func (r *supplierRepository) Delete(ctx context.Context, id uint, deletedBy uint) error {
	// 先更新操作人ID到updated_by字段
	if err := r.db.WithContext(ctx).Model(&model.Supplier{}).
		Where("id = ?", id).
		Update("updated_by", deletedBy).Error; err != nil {
		return err
	}

	// 然后执行软删除
	return r.db.WithContext(ctx).Delete(&model.Supplier{}, id).Error
}

// GetDB 获取数据库实例
func (r *supplierRepository) GetDB() *gorm.DB {
	return r.db
}
