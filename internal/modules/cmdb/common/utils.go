package common

import "backend/internal/common/constants"

// MapMaterialTypeToAssetType 映射物料类型到资产类型
var MapMaterialTypeToAssetType = map[string]string{
	"GPU服务器": constants.AssetTypeGPUServer,
	"服务器":    constants.AssetTypeServer,
	"存储设备":   constants.AssetTypeStorage,
	"交换机":    constants.AssetTypeSwitch,
	"负载均衡":   constants.AssetTypeLoadbalancer,
	"防火墙":    constants.AssetTypeFirewall,
	"路由器":    constants.AssetTypeRouter,
	"其他":     constants.AssetTypeOther,
}
