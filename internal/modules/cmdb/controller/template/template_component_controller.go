package template

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	modelTemplate "backend/internal/modules/cmdb/model/template"
	serviceTemplate "backend/internal/modules/cmdb/service/template"
	"backend/response"
)

// TemplateComponentController 模板组件控制器
type TemplateComponentController struct {
	service serviceTemplate.TemplateComponentService
}

// NewTemplateComponentController 创建模板组件控制器
func NewTemplateComponentController(service serviceTemplate.TemplateComponentService) *TemplateComponentController {
	return &TemplateComponentController{service: service}
}

// ListByTemplateID 获取模板组件列表
// @Summary 获取模板组件列表
// @Description 根据模板ID获取组件列表
// @Tags CMDB-模板组件
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/machine-templates/{id}/components [get]
func (c *TemplateComponentController) ListByTemplateID(ctx *gin.Context) {
	templateID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的模板ID: "+err.Error())
		return
	}

	components, err := c.service.ListByTemplateID(ctx, uint(templateID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取模板组件列表失败: "+err.Error())
		return
	}

	response.Success(ctx, components, "获取模板组件列表成功")
}

// Create 创建模板组件
// @Summary 创建模板组件
// @Description 创建模板组件
// @Tags CMDB-模板组件
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Param component body modelTemplate.TemplateComponent true "组件信息"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/machine-templates/{id}/components [post]
func (c *TemplateComponentController) Create(ctx *gin.Context) {
	templateID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的模板ID: "+err.Error())
		return
	}

	var component modelTemplate.TemplateComponent
	if err := ctx.ShouldBindJSON(&component); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求参数: "+err.Error())
		return
	}

	component.TemplateID = uint(templateID)

	createdComponent, err := c.service.Create(ctx, &component)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建模板组件失败: "+err.Error())
		return
	}

	response.Success(ctx, createdComponent, "创建模板组件成功")
}

// Update 更新模板组件
// @Summary 更新模板组件
// @Description 更新模板组件信息
// @Tags CMDB-模板组件
// @Accept json
// @Produce json
// @Param id path int true "组件ID"
// @Param component body modelTemplate.TemplateComponent true "组件信息"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/template-components/{id} [put]
func (c *TemplateComponentController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID: "+err.Error())
		return
	}

	var component modelTemplate.TemplateComponent
	if err := ctx.ShouldBindJSON(&component); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求参数: "+err.Error())
		return
	}

	component.ID = uint(id)

	updatedComponent, err := c.service.Update(ctx, &component)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新模板组件失败: "+err.Error())
		return
	}

	response.Success(ctx, updatedComponent, "更新模板组件成功")
}

// Delete 删除模板组件
// @Summary 删除模板组件
// @Description 根据ID删除模板组件
// @Tags CMDB-模板组件
// @Accept json
// @Produce json
// @Param id path int true "组件ID"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/template-components/{id} [delete]
func (c *TemplateComponentController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID: "+err.Error())
		return
	}

	if err := c.service.Delete(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除模板组件失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除模板组件成功")
}
