package template

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	modelTemplate "backend/internal/modules/cmdb/model/template"
	serviceTemplate "backend/internal/modules/cmdb/service/template"
	"backend/response"
)

// MachineTemplateController 套餐模板控制器
type MachineTemplateController struct {
	service serviceTemplate.MachineTemplateService
}

// NewMachineTemplateController 创建套餐模板控制器
func NewMachineTemplateController(service serviceTemplate.MachineTemplateService) *MachineTemplateController {
	return &MachineTemplateController{service: service}
}

// List 查询套餐模板列表
// @Summary 查询套餐模板列表
// @Description 分页查询套餐模板列表
// @Tags CMDB-套餐模板
// @Accept json
// @Produce json
// @Param page query int false "页码, 默认1"
// @Param pageSize query int false "每页数量, 默认10"
// @Param query query string false "查询关键字"
// @Param category query string false "模板类别"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/machine-templates [get]
func (c *MachineTemplateController) List(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.Query("query")
	category := ctx.Query("category")

	templates, total, err := c.service.List(ctx, page, pageSize, query, category)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "查询套餐模板列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  templates,
		"total": total,
	}, "查询套餐模板列表成功")
}

// Get 获取套餐模板详情
// @Summary 获取套餐模板详情
// @Description 根据ID获取套餐模板详情
// @Tags CMDB-套餐模板
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/machine-templates/{id} [get]
func (c *MachineTemplateController) Get(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID: "+err.Error())
		return
	}

	template, err := c.service.GetByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取套餐模板详情失败: "+err.Error())
		return
	}

	response.Success(ctx, template, "获取套餐模板详情成功")
}

// Create 创建套餐模板
// @Summary 创建套餐模板
// @Description 创建新的套餐模板
// @Tags CMDB-套餐模板
// @Accept json
// @Produce json
// @Param template body modelTemplate.MachineTemplate true "套餐模板信息"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/machine-templates [post]
func (c *MachineTemplateController) Create(ctx *gin.Context) {
	var tmpl modelTemplate.MachineTemplate
	if err := ctx.ShouldBindJSON(&tmpl); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求参数: "+err.Error())
		return
	}

	createdTemplate, err := c.service.Create(ctx, &tmpl)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建套餐模板失败: "+err.Error())
		return
	}

	response.Success(ctx, createdTemplate, "创建套餐模板成功")
}

// Update 更新套餐模板
// @Summary 更新套餐模板
// @Description 更新套餐模板信息
// @Tags CMDB-套餐模板
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Param template body modelTemplate.MachineTemplate true "套餐模板信息"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/machine-templates/{id} [put]
func (c *MachineTemplateController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID: "+err.Error())
		return
	}

	var tmpl modelTemplate.MachineTemplate
	if err := ctx.ShouldBindJSON(&tmpl); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求参数: "+err.Error())
		return
	}

	tmpl.ID = uint(id)

	updatedTemplate, err := c.service.Update(ctx, &tmpl)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新套餐模板失败: "+err.Error())
		return
	}

	response.Success(ctx, updatedTemplate, "更新套餐模板成功")
}

// Delete 删除套餐模板
// @Summary 删除套餐模板
// @Description 根据ID删除套餐模板
// @Tags CMDB-套餐模板
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/machine-templates/{id} [delete]
func (c *MachineTemplateController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID: "+err.Error())
		return
	}

	if err := c.service.Delete(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除套餐模板失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除套餐模板成功")
}
