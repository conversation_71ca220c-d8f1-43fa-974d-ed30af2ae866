package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	ser "backend/internal/modules/cmdb/service/asset"
	"backend/response"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// StatusChangeController 资产状态变更控制器
type StatusChangeController struct {
	service ser.StatusChangeService
}

// NewStatusChangeController 创建资产状态变更控制器
func NewStatusChangeController(service ser.StatusChangeService) *StatusChangeController {
	return &StatusChangeController{service: service}
}

// GetAssetStatusHistory 获取资产状态变更历史
// @Summary 获取资产状态变更历史
// @Description 获取指定资产的状态变更历史记录
// @Tags 资产管理-状态变更
// @Accept json
// @Produce json
// @Param assetID query int true "资产ID"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/asset-status/history [get]
func (c *StatusChangeController) GetAssetStatusHistory(ctx *gin.Context) {
	var (
		startDate time.Time
		endDate   time.Time
	)
	assetID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的资产ID"+": "+err.Error())
		return
	}

	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	// 解析时间参数
	startDateStr := ctx.DefaultQuery("startTime", "")
	if startDateStr != "" {
		startDate, err = time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "无效的开始时间: "+err.Error())
			return
		}
	}
	endDateStr := ctx.DefaultQuery("endTime", "")
	if endDateStr != "" {
		endDate, err = time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "无效的结束时间: "+err.Error())
			return
		}
	}
	operatorID, err := strconv.ParseUint(ctx.DefaultQuery("operatorID", "0"), 10, 64)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的操作人ID: "+err.Error())
		return
	}
	// 构建查询参数
	param := asset.ListParams{
		Page:       page,
		PageSize:   pageSize,
		StartDate:  startDate,
		EndDate:    endDate,
		OperatorID: uint(operatorID),
	}

	logs, total, err := c.service.GetAssetStatusHistory(ctx, uint(assetID), param)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取状态变更历史失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  logs,
		"total": total,
	}, "获取状态变更历史成功")
}

// ChangeAssetStatus 变更资产状态
// @Summary 变更资产状态
// @Description 手动变更资产状态
// @Tags 资产管理-状态变更
// @Accept json
// @Produce json
// @Param request body ChangeAssetStatusRequest true "变更请求"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/asset-status/change-asset-status [post]
func (c *StatusChangeController) ChangeAssetStatus(ctx *gin.Context) {
	var req ChangeAssetStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT获取操作人信息
	operatorID := uint(1)   // 这里应该从JWT中获取
	operatorName := "系统管理员" // 这里应该从JWT中获取

	if err := c.service.ChangeAssetStatus(ctx, req.AssetID, req.NewStatus, req.Reason, operatorID, operatorName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "变更资产状态失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "变更资产状态成功")
}

// ChangeBizStatus 变更业务状态
// @Summary 变更业务状态
// @Description 手动变更资源业务状态
// @Tags 资产管理-状态变更
// @Accept json
// @Produce json
// @Param request body ChangeBizStatusRequest true "变更请求"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/asset-status/change-biz-status [post]
func (c *StatusChangeController) ChangeBizStatus(ctx *gin.Context) {
	var req ChangeBizStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT获取操作人信息
	operatorID := uint(1)   // 这里应该从JWT中获取
	operatorName := "系统管理员" // 这里应该从JWT中获取

	if err := c.service.ChangeBizStatus(ctx, req.ResourceID, req.NewStatus, req.Reason, operatorID, operatorName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "变更业务状态失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "变更业务状态成功")
}

// ProcessAssetStorage 处理资产入库
// @Summary 处理资产入库
// @Description 处理资产入库流程
// @Tags 资产管理-状态变更
// @Accept json
// @Produce json
// @Param request body ProcessAssetStorageRequest true "入库请求"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/asset-status/process-storage [post]
func (c *StatusChangeController) ProcessAssetStorage(ctx *gin.Context) {
	var req ProcessAssetStorageRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT获取操作人信息
	operatorID := uint(1)   // 这里应该从JWT中获取
	operatorName := "系统管理员" // 这里应该从JWT中获取

	if err := c.service.ProcessAssetStorage(ctx, req.AssetID, operatorID, operatorName, req.ApproverID, req.ApproverName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "处理资产入库失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "处理资产入库成功")
}

// ProcessAssetOutbound 处理资产出库
// @Summary 处理资产出库
// @Description 处理资产出库流程
// @Tags 资产管理-状态变更
// @Accept json
// @Produce json
// @Param request body ProcessAssetOutboundRequest true "出库请求"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/asset-status/process-outbound [post]
func (c *StatusChangeController) ProcessAssetOutbound(ctx *gin.Context) {
	var req ProcessAssetOutboundRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT获取操作人信息
	operatorID := uint(1)   // 这里应该从JWT中获取
	operatorName := "系统管理员" // 这里应该从JWT中获取

	if err := c.service.ProcessAssetOutbound(ctx, req.AssetID, operatorID, operatorName, req.ApproverID, req.ApproverName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "处理资产出库失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "处理资产出库成功")
}

// ProcessAssetRacking 处理资产上架
// @Summary 处理资产上架
// @Description 处理资产上架流程
// @Tags 资产管理-状态变更
// @Accept json
// @Produce json
// @Param request body ProcessAssetRackingRequest true "上架请求"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/asset-status/process-rack [post]
func (c *StatusChangeController) ProcessAssetRacking(ctx *gin.Context) {
	var req ProcessAssetRackingRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT获取操作人信息
	operatorID := uint(1)   // 这里应该从JWT中获取
	operatorName := "系统管理员" // 这里应该从JWT中获取

	if err := c.service.ProcessAssetRacking(ctx, req.AssetID, req.CabinetID, req.RackPosition, operatorID, operatorName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "处理资产上架失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "处理资产上架成功")
}

// ProcessAssetDelivery 处理资产交付
// @Summary 处理资产交付
// @Description 处理资产交付流程
// @Tags 资产管理-状态变更
// @Accept json
// @Produce json
// @Param request body ProcessAssetDeliveryRequest true "交付请求"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/asset-status/process-delivery [post]
func (c *StatusChangeController) ProcessAssetDelivery(ctx *gin.Context) {
	var req ProcessAssetDeliveryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT获取操作人信息
	operatorID := uint(1)   // 这里应该从JWT中获取
	operatorName := "系统管理员" // 这里应该从JWT中获取

	if err := c.service.ProcessAssetDelivery(ctx, req.AssetID, req.Project, operatorID, operatorName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "处理资产交付失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "处理资产交付成功")
}

// ProcessAssetMaintenance 处理资产维修
// @Summary 处理资产维修
// @Description 处理资产维修流程
// @Tags 资产管理-状态变更
// @Accept json
// @Produce json
// @Param request body ProcessAssetMaintenanceRequest true "维修请求"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/asset-status/process-maintenance [post]
func (c *StatusChangeController) ProcessAssetMaintenance(ctx *gin.Context) {
	var req ProcessAssetMaintenanceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT获取操作人信息
	operatorID := uint(1)   // 这里应该从JWT中获取
	operatorName := "系统管理员" // 这里应该从JWT中获取

	if err := c.service.ProcessAssetMaintenance(ctx, req.AssetID, req.Reason, operatorID, operatorName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "处理资产维修失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "处理资产维修成功")
}

// ProcessAssetScrap 处理资产报废
// @Summary 处理资产报废
// @Description 处理资产报废流程
// @Tags 资产管理-状态变更
// @Accept json
// @Produce json
// @Param request body ProcessAssetScrapRequest true "报废请求"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/asset-status/process-scrap [post]
func (c *StatusChangeController) ProcessAssetScrap(ctx *gin.Context) {
	var req ProcessAssetScrapRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 从JWT获取操作人信息
	operatorID := uint(1)   // 这里应该从JWT中获取
	operatorName := "系统管理员" // 这里应该从JWT中获取

	if err := c.service.ProcessAssetScrap(ctx, req.AssetID, req.Reason, operatorID, operatorName, req.ApproverID, req.ApproverName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "处理资产报废失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "处理资产报废成功")
}

// 请求结构体定义
type ChangeAssetStatusRequest struct {
	AssetID   uint   `json:"assetID" binding:"required"`
	NewStatus string `json:"newStatus" binding:"required"`
	Reason    string `json:"reason" binding:"required"`
}

type ChangeBizStatusRequest struct {
	ResourceID uint   `json:"resourceID" binding:"required"`
	NewStatus  string `json:"newStatus" binding:"required"`
	Reason     string `json:"reason" binding:"required"`
}

type ProcessAssetStorageRequest struct {
	AssetID      uint   `json:"assetID" binding:"required"`
	ApproverID   uint   `json:"approverID" binding:"required"`
	ApproverName string `json:"approverName" binding:"required"`
}

type ProcessAssetOutboundRequest struct {
	AssetID      uint   `json:"assetID" binding:"required"`
	ApproverID   uint   `json:"approverID" binding:"required"`
	ApproverName string `json:"approverName" binding:"required"`
}

type ProcessAssetRackingRequest struct {
	AssetID      uint `json:"assetID" binding:"required"`
	CabinetID    uint `json:"cabinetID" binding:"required"`
	RackPosition int  `json:"rackPosition" binding:"required"`
}

type ProcessAssetDeliveryRequest struct {
	AssetID uint   `json:"assetID" binding:"required"`
	Project string `json:"project" binding:"required"`
}

type ProcessAssetMaintenanceRequest struct {
	AssetID uint   `json:"assetID" binding:"required"`
	Reason  string `json:"reason" binding:"required"`
}

type ProcessAssetScrapRequest struct {
	AssetID      uint   `json:"assetID" binding:"required"`
	Reason       string `json:"reason" binding:"required"`
	ApproverID   uint   `json:"approverID" binding:"required"`
	ApproverName string `json:"approverName" binding:"required"`
}
