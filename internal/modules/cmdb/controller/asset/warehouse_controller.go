package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	assetSvc "backend/internal/modules/cmdb/service/asset"
	"backend/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// WarehouseController 仓库控制器
type WarehouseController struct {
	service assetSvc.WarehouseService
}

// NewWarehouseController 创建仓库控制器
func NewWarehouseController(service assetSvc.WarehouseService) *WarehouseController {
	return &WarehouseController{service: service}
}

// RegisterRoutes 注册路由
func (c *WarehouseController) RegisterRoutes(router *gin.RouterGroup) {
	warehouseRouter := router.Group("/warehouses")
	{
		// 基本CRUD操作
		warehouseRouter.POST("", c.CreateWarehouse)
		warehouseRouter.PUT("/:id", c.UpdateWarehouse)
		warehouseRouter.DELETE("/:id", c.DeleteWarehouse)
		warehouseRouter.GET("/:id", c.GetWarehouseByID)
		warehouseRouter.GET("/code/:code", c.GetWarehouseByCode)
		warehouseRouter.GET("", c.ListWarehouses)

		// 获取统计信息
		warehouseRouter.GET("/:id/stats", c.GetWarehouseStats)

		// 获取仓库内容
		warehouseRouter.GET("/:id/spares", c.GetWarehouseSpares)
		warehouseRouter.GET("/:id/inventory", c.GetWarehouseInventory)

		// 获取仓库类型
		warehouseRouter.GET("/types", c.GetWarehouseTypes)
	}
}

// CreateWarehouse 创建仓库
// @Summary 创建仓库
// @Description 创建新的仓库
// @Tags 资产管理-仓库
// @Accept json
// @Produce json
// @Param warehouse body asset.Warehouse true "仓库信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/warehouses [post]
func (c *WarehouseController) CreateWarehouse(ctx *gin.Context) {
	var warehouse asset.Warehouse
	if err := ctx.ShouldBindJSON(&warehouse); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.CreateWarehouse(ctx, &warehouse); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建仓库失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": warehouse.ID}, "创建仓库成功")
}

// UpdateWarehouse 更新仓库
// @Summary 更新仓库
// @Description 更新仓库信息
// @Tags 资产管理-仓库
// @Accept json
// @Produce json
// @Param id path int true "仓库ID"
// @Param warehouse body asset.Warehouse true "仓库信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/warehouses/{id} [put]
func (c *WarehouseController) UpdateWarehouse(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var warehouse asset.Warehouse
	if err := ctx.ShouldBindJSON(&warehouse); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	warehouse.ID = uint(id)
	if err := c.service.UpdateWarehouse(ctx, &warehouse); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新仓库失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新仓库成功")
}

// DeleteWarehouse 删除仓库
// @Summary 删除仓库
// @Description 删除指定ID的仓库
// @Tags 资产管理-仓库
// @Accept json
// @Produce json
// @Param id path int true "仓库ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/warehouses/{id} [delete]
func (c *WarehouseController) DeleteWarehouse(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteWarehouse(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除仓库失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除仓库成功")
}

// GetWarehouseByID 根据ID获取仓库
// @Summary 获取仓库详情
// @Description 根据ID获取仓库详情
// @Tags 资产管理-仓库
// @Accept json
// @Produce json
// @Param id path int true "仓库ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/warehouses/{id} [get]
func (c *WarehouseController) GetWarehouseByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	warehouse, err := c.service.GetWarehouseByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取仓库失败: "+err.Error())
		return
	}

	response.Success(ctx, warehouse, "获取仓库成功")
}

// GetWarehouseByCode 根据编码获取仓库
// @Summary 根据编码获取仓库
// @Description 根据仓库编码获取仓库详情
// @Tags 资产管理-仓库
// @Accept json
// @Produce json
// @Param code path string true "仓库编码"
// @Success 200 {object} response.ResponseStruct{data=asset.Warehouse}
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/warehouses/code/{code} [get]
func (c *WarehouseController) GetWarehouseByCode(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		response.Fail(ctx, http.StatusBadRequest, "仓库编码不能为空")
		return
	}

	warehouse, err := c.service.GetWarehouseByCode(ctx, code)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取仓库失败: "+err.Error())
		return
	}

	response.Success(ctx, warehouse, "获取仓库成功")
}

// ListWarehouses 获取仓库列表
// @Summary 获取仓库列表
// @Description 分页获取仓库列表，支持按类型和关键词筛选
// @Tags 资产管理-仓库
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Param type query string false "仓库类型"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/warehouses [get]
func (c *WarehouseController) ListWarehouses(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	query := ctx.DefaultQuery("query", "")
	warehouseType := ctx.DefaultQuery("type", "")

	warehouses, total, err := c.service.ListWarehouses(ctx, page, pageSize, query, warehouseType)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取仓库列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  warehouses,
		"total": total,
	}, "获取仓库列表成功")
}

// GetWarehouseStats 获取仓库统计信息
// @Summary 获取仓库统计信息
// @Description 获取指定仓库的统计信息，包括产品数量、总库存、可用库存等
// @Tags 资产管理-仓库
// @Accept json
// @Produce json
// @Param id path int true "仓库ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/warehouses/{id}/stats [get]
func (c *WarehouseController) GetWarehouseStats(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	stats, err := c.service.GetWarehouseWithStats(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取仓库统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取仓库统计信息成功")
}

// GetWarehouseSpares 获取仓库内备件
// @Summary 获取仓库内备件
// @Description 获取指定仓库内的备件列表
// @Tags 资产管理-仓库
// @Accept json
// @Produce json
// @Param id path int true "仓库ID"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/warehouses/{id}/spares [get]
func (c *WarehouseController) GetWarehouseSpares(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	spares, total, err := c.service.GetWarehouseSpares(ctx, uint(id), page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取仓库备件失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  spares,
		"total": total,
	}, "获取仓库备件成功")
}

// GetWarehouseInventory 获取仓库内库存
// @Summary 获取仓库内库存
// @Description 获取指定仓库内的库存明细
// @Tags 资产管理-仓库
// @Accept json
// @Produce json
// @Param id path int true "仓库ID"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/warehouses/{id}/inventory [get]
func (c *WarehouseController) GetWarehouseInventory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	inventory, total, err := c.service.GetWarehouseInventory(ctx, uint(id), page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取仓库库存失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  inventory,
		"total": total,
	}, "获取仓库库存成功")
}

// GetWarehouseTypes 获取所有仓库类型
// @Summary 获取所有仓库类型
// @Description 获取系统中所有的仓库类型
// @Tags 资产管理-仓库
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseStruct{data=[]string}
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/warehouses/types [get]
func (c *WarehouseController) GetWarehouseTypes(ctx *gin.Context) {
	types := c.service.GetWarehouseTypes(ctx)
	response.Success(ctx, types, "获取仓库类型成功")
}
