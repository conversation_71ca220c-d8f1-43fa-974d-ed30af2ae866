package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	ser "backend/internal/modules/cmdb/service/asset"
	"backend/response"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// ResourceController 资源控制器
type ResourceController struct {
	service ser.ResourceService
}

// NewResourceController 创建资源控制器
func NewResourceController(service ser.ResourceService) *ResourceController {
	return &ResourceController{service: service}
}

// Create 创建资源
// @Summary 创建资源
// @Description 创建新的资源
// @Tags 资产管理-资源
// @Accept json
// @Produce json
// @Param resource body asset.Resource true "资源信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/resources [post]
func (c *ResourceController) Create(ctx *gin.Context) {
	var resource asset.Resource
	if err := ctx.ShouldBindJSON(&resource); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.CreateResource(ctx, &resource); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建资源失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": resource.ID}, "创建资源成功")
}

// Update 更新资源
// @Summary 更新资源
// @Description 更新资源信息
// @Tags 资产管理-资源
// @Accept json
// @Produce json
// @Param id path int true "资源ID"
// @Param resource body asset.Resource true "资源信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/resources/{id} [put]
func (c *ResourceController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var resource asset.Resource
	if err := ctx.ShouldBindJSON(&resource); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	resource.ID = uint(id)
	if err := c.service.UpdateResource(ctx, &resource); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新资源失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新资源成功")
}

// Delete 删除资源
// @Summary 删除资源
// @Description 删除指定ID的资源
// @Tags 资产管理-资源
// @Accept json
// @Produce json
// @Param id path int true "资源ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/resources/{id} [delete]
func (c *ResourceController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteResource(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除资源失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除资源成功")
}

// GetByID 根据ID获取资源
// @Summary 获取资源详情
// @Description 根据ID获取资源详情
// @Tags 资产管理-资源
// @Accept json
// @Produce json
// @Param id path int true "资源ID"
// @Success 200 {object} response.ResponseStruct{data=asset.Resource}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/resources/{id} [get]
func (c *ResourceController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	resource, err := c.service.GetResourceByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取资源失败: "+err.Error())
		return
	}

	response.Success(ctx, resource, "获取资源成功")
}

// GetByAssetID 根据资产ID获取资源
// @Summary 根据资产ID获取资源
// @Description 根据资产ID获取资源详情
// @Tags 资产管理-资源
// @Accept json
// @Produce json
// @Param assetID query int true "资产ID"
// @Success 200 {object} response.ResponseStruct{data=asset.Resource}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/resources/by-asset [get]
func (c *ResourceController) GetByAssetID(ctx *gin.Context) {
	assetID, err := strconv.ParseUint(ctx.Query("assetID"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的资产ID")
		return
	}

	resource, err := c.service.GetResourceByAssetID(ctx, uint(assetID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取资源失败: "+err.Error())
		return
	}

	response.Success(ctx, resource, "获取资源成功")
}

// GetBySN 根据SN获取资源
// @Summary 根据SN获取资源
// @Description 根据设备SN获取资源详情
// @Tags 资产管理-资源
// @Accept json
// @Produce json
// @Param sn query string true "设备SN"
// @Success 200 {object} response.ResponseStruct{data=asset.Resource}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/resources/by-sn [get]
func (c *ResourceController) GetBySN(ctx *gin.Context) {
	sn := ctx.Query("sn")
	if sn == "" {
		response.Fail(ctx, http.StatusBadRequest, "SN不能为空")
		return
	}

	resource, err := c.service.GetResourceBySN(ctx, sn)
	if err != nil {
		if strings.Contains(err.Error(), "不存在") || strings.Contains(err.Error(), "not found") {
			response.Success(ctx, nil, "查询完成")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取资源失败: "+err.Error())
		return
	}

	response.Success(ctx, resource, "获取资源成功")
}

// List 获取资源列表
// @Summary 获取资源列表
// @Description 分页获取资源列表
// @Tags 资产管理-资源
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Param bizStatus query string false "业务状态(online:在线,offline:离线)"
// @Param resStatus query string false "资源状态(allocated:已分配,unallocated:未分配)"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/resources [get]
func (c *ResourceController) List(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("query", "")
	bizStatus := ctx.DefaultQuery("bizStatus", "")
	resStatus := ctx.DefaultQuery("resStatus", "")

	resources, total, err := c.service.ListResources(ctx, page, pageSize, query, bizStatus, resStatus)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取资源列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  resources,
		"total": total,
	}, "获取资源列表成功")
}

// GetAllProjects 获取所有项目列表
// @Summary 获取所有项目列表
// @Description 获取系统中所有唯一的项目名称，供前端下拉框使用
// @Tags 资产管理-资源
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseStruct{data=[]string}
// @Failure 500 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/resources/projects [get]
func (c *ResourceController) GetAllProjects(ctx *gin.Context) {
	projects, err := c.service.GetAllProjects(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取项目列表失败: "+err.Error())
		return
	}

	response.Success(ctx, projects, "获取项目列表成功")
}

// GetAllClusters 获取所有集群列表
// @Summary 获取所有集群列表
// @Description 获取系统中所有唯一的集群名称，供前端下拉框使用
// @Tags 资产管理-资源
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseStruct{data=[]string}
// @Failure 500 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/resources/clusters [get]
func (c *ResourceController) GetAllClusters(ctx *gin.Context) {
	clusters, err := c.service.GetAllClusters(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取集群列表失败: "+err.Error())
		return
	}

	response.Success(ctx, clusters, "获取集群列表成功")
}

// GetAvailableBackups 获取可用的备机列表
// @Summary 获取可用备机列表
// @Description 根据项目、集群、硬件状态和业务状态获取符合条件的备机列表
// @Tags 资产管理-资源
// @Accept json
// @Produce json
// @Param project query string true "项目名称"
// @Param cluster query string false "集群名称"
// @Param hardwareStatus query string false "硬件状态，默认为normal"
// @Param bizStatus query string false "业务状态，默认为maintaining"
// @Success 200 {object} response.ResponseStruct{data=[]asset.Resource}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/resources/available-backups [get]
func (c *ResourceController) GetAvailableBackups(ctx *gin.Context) {
	project := ctx.Query("project")
	cluster := ctx.Query("cluster")
	hardwareStatus := ctx.DefaultQuery("hardwareStatus", "normal")
	bizStatus := ctx.DefaultQuery("bizStatus", "maintaining")

	if project == "" {
		response.Fail(ctx, http.StatusBadRequest, "项目名称不能为空")
		return
	}

	// 获取符合条件的备机列表
	resources, err := c.service.GetAvailableBackups(ctx, project, cluster, hardwareStatus, bizStatus)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取可用备机列表失败: "+err.Error())
		return
	}

	response.Success(ctx, resources, "获取可用备机列表成功")
}
