package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	ser "backend/internal/modules/cmdb/service/asset"
	"backend/response"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// DeviceController 资产设备控制器
type DeviceController struct {
	service ser.DeviceService
}

// NewDeviceController 创建资产设备控制器
func NewDeviceController(service ser.DeviceService) *DeviceController {
	return &DeviceController{service: service}
}

// Create 创建资产设备
// @Summary 创建资产设备
// @Description 创建新的资产设备
// @Tags 资产管理-设备
// @Accept json
// @Produce json
// @Param device body asset.Device true "资产设备信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/devices [post]
func (c *DeviceController) Create(ctx *gin.Context) {
	var device asset.Device
	if err := ctx.ShouldBindJSON(&device); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.Create(ctx, &device); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建资产设备失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": device.ID}, "创建资产设备成功")
}

// Update 更新资产设备
// @Summary 更新资产设备
// @Description 更新资产设备信息
// @Tags 资产管理-设备
// @Accept json
// @Produce json
// @Param id path int true "资产设备ID"
// @Param device body asset.Device true "资产设备信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/devices/{id} [put]
func (c *DeviceController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var device asset.Device
	if err := ctx.ShouldBindJSON(&device); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	device.ID = uint(id)
	if err := c.service.Update(ctx, &device); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新资产设备失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新资产设备成功")
}

// Delete 删除资产设备
// @Summary 删除资产设备
// @Description 删除指定ID的资产设备
// @Tags 资产管理-设备
// @Accept json
// @Produce json
// @Param id path int true "资产设备ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/devices/{id} [delete]
func (c *DeviceController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.Delete(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除资产设备失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除资产设备成功")
}

// GetByID 根据ID获取资产设备
// @Summary 获取资产设备详情
// @Description 根据ID获取资产设备详情
// @Tags 资产管理-设备
// @Accept json
// @Produce json
// @Param id path int true "资产设备ID"
// @Success 200 {object} response.ResponseStruct{data=asset.Device}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/devices/{id} [get]
func (c *DeviceController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	device, err := c.service.GetByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取资产设备失败: "+err.Error())
		return
	}

	response.Success(ctx, device, "获取资产设备成功")
}

// GetBySN 根据SN获取资产设备
// @Summary 根据SN获取资产设备
// @Description 根据SN获取资产设备详情，支持多个SN（用逗号分隔）
// @Tags 资产管理-设备
// @Accept json
// @Produce json
// @Param sn query string true "设备SN，多个SN用逗号分隔"
// @Success 200 {object} response.ResponseStruct{data=[]asset.Device}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/devices/by-sn [get]
func (c *DeviceController) GetBySN(ctx *gin.Context) {
	sn := ctx.Query("sn")
	if sn == "" {
		response.Fail(ctx, http.StatusBadRequest, "SN不能为空")
		return
	}

	// 支持多个SN，用逗号分隔
	snList := strings.Split(sn, ",")
	var devices []asset.Device

	for _, s := range snList {
		s = strings.TrimSpace(s)
		if s == "" {
			continue
		}

		device, err := c.service.GetBySN(ctx, s)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "获取资产设备失败: SN="+s+", 错误: "+err.Error())
			return
		}
		devices = append(devices, *device)
	}

	response.Success(ctx, devices, "获取资产设备成功")
}

// GetIdleBySN 根据SN获取状态为闲置中的资产设备
func (c *DeviceController) GetIdleBySN(ctx *gin.Context) {
	sn := ctx.Query("sn")
	if sn == "" {
		response.Fail(ctx, http.StatusBadRequest, "SN不能为空")
		return
	}

	// 支持多个SN，用逗号分隔
	snList := strings.Split(sn, ",")
	var devices []asset.Device
	var notFoundSNs []string
	var nonIdleSNs []string

	for _, s := range snList {
		s = strings.TrimSpace(s)
		if s == "" {
			continue
		}

		// 先检查设备是否存在
		device, err := c.service.GetBySN(ctx, s)
		if err != nil {
			if strings.Contains(err.Error(), "资产设备不存在") {
				notFoundSNs = append(notFoundSNs, s)
				continue
			}
			response.Fail(ctx, http.StatusInternalServerError, "获取资产设备失败: SN="+s+", 错误: "+err.Error())
			return
		}

		// 设备存在，但检查状态是否为idle
		if device.AssetStatus != "idle" {
			nonIdleSNs = append(nonIdleSNs, s)
			continue
		}

		devices = append(devices, *device)
	}

	// 构建详细的响应信息
	if len(devices) == 0 {
		errMsg := "未找到符合条件的闲置设备"
		if len(notFoundSNs) > 0 {
			errMsg += "，不存在的SN: " + strings.Join(notFoundSNs, ", ")
		}
		if len(nonIdleSNs) > 0 {
			errMsg += "，非闲置状态的SN: " + strings.Join(nonIdleSNs, ", ")
		}
		response.Fail(ctx, http.StatusNotFound, errMsg)
		return
	}

	// 成功找到至少一个设备
	successMsg := "获取闲置中的资产设备成功"
	if len(notFoundSNs) > 0 || len(nonIdleSNs) > 0 {
		successMsg += "，但部分SN存在问题"
		if len(notFoundSNs) > 0 {
			successMsg += "，不存在的SN: " + strings.Join(notFoundSNs, ", ")
		}
		if len(nonIdleSNs) > 0 {
			successMsg += "，非闲置状态的SN: " + strings.Join(nonIdleSNs, ", ")
		}
	}

	response.Success(ctx, devices, successMsg)
}

func (c *DeviceController) IsExist(ctx *gin.Context) {
	sn := ctx.Query("sn")
	if sn == "" {
		response.Fail(ctx, http.StatusBadRequest, "SN不能为空")
		return
	}

	snList := strings.Split(sn, "\n")
	for _, sn := range snList {
		_, err := c.service.GetBySN(ctx, sn)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "获取资产设备"+sn+"失败: "+err.Error())
			return
		}
	}

	response.Success(ctx, true, "获取资产设备成功")
}

// List 获取资产设备列表
// @Summary 获取资产设备列表
// @Description 分页获取资产设备列表
// @Tags 资产管理-设备
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Param assetType query string false "资产类型(server:服务器,network:网络设备,storage:存储设备)"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/devices [get]
func (c *DeviceController) List(ctx *gin.Context) {
	var req struct {
		Page     int    `form:"page" binding:"required,min=1"`
		PageSize int    `form:"pageSize" binding:"required,min=1,max=100"`
		Query    string `form:"query"`
	}

	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	page := req.Page
	pageSize := req.PageSize
	query := req.Query
	assetStatus := ctx.DefaultQuery("assetStatus", "")
	assetType := ctx.DefaultQuery("assetType", "")

	devices, total, err := c.service.List(ctx, page, pageSize, query, assetStatus, assetType)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取资产设备列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  devices,
		"total": total,
	}, "获取资产设备列表成功")
}

func (c *DeviceController) GetDeviceAmount(ctx *gin.Context) {
	var req []asset.DeviceAmountReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	res, err := c.service.GetDeviceAmount(ctx, req)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取资产设备数量失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"amounts": res}, "获取资产设备数量成功")
}
