package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	assetSvc "backend/internal/modules/cmdb/service/asset"
	"backend/response"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// SpareController 备件控制器
type SpareController struct {
	service assetSvc.SpareService
}

// NewSpareController 创建备件控制器
func NewSpareController(service assetSvc.SpareService) *SpareController {
	return &SpareController{service: service}
}

// RegisterRoutes 注册路由
func (c *SpareController) RegisterRoutes(router *gin.RouterGroup) {
	spareRouter := router.Group("/spares")
	{
		// 基本CRUD操作
		spareRouter.POST("", c.CreateSpare)
		spareRouter.PUT("/:id", c.UpdateSpare)
		spareRouter.DELETE("/:id", c.DeleteSpare)
		spareRouter.GET("/:id", c.GetSpareByID)
		spareRouter.GET("", c.ListSpares)

		// 获取详情
		spareRouter.GET("/:id/details", c.GetSpareWithDetails)
		spareRouter.POST("/sn", c.GetSparesBySNs)

		// 状态管理
		spareRouter.POST("/:id/status", c.ChangeSpareStatus)

		// 关联设备
		spareRouter.POST("/:id/assign", c.AssignSpareToDevice)
		spareRouter.POST("/:id/remove", c.RemoveSpareFromDevice)

		// 库存相关
		spareRouter.POST("/:id/transfer", c.TransferWarehouse)
		spareRouter.GET("/by-warehouse/:warehouseID", c.ListSparesByWarehouse)
		spareRouter.GET("/by-product/:productID", c.ListSparesByProduct)

		// 统计信息
		spareRouter.GET("/statistics", c.GetSpareStatistics)

		// 服务器组件相关
		spareRouter.GET("/by-component-type", c.ListSparesByComponentType)
		spareRouter.GET("/product/:productID/details", c.ListSparesByProductID)
	}
}

// CreateSpare 创建备件
// @Summary 创建备件
// @Description 创建新的备件记录
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param spare body asset.AssetSpare true "备件信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares [post]
func (c *SpareController) CreateSpare(ctx *gin.Context) {
	var spare asset.AssetSpare
	if err := ctx.ShouldBindJSON(&spare); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.CreateSpare(ctx, &spare); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建备件失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": spare.ID}, "创建备件成功")
}

// UpdateSpare 更新备件
// @Summary 更新备件
// @Description 更新备件信息
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param id path int true "备件ID"
// @Param spare body asset.AssetSpare true "备件信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/{id} [put]
func (c *SpareController) UpdateSpare(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var spare asset.AssetSpare
	if err := ctx.ShouldBindJSON(&spare); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 添加调试日志
	userID, exists := ctx.Get("userID")
	userInfo, infoExists := ctx.Get("userInfo")
	fmt.Printf("Controller: 用户信息 userID存在=%v, userID=%v, userInfo存在=%v, userInfo=%+v\n",
		exists, userID, infoExists, userInfo)

	spare.ID = uint(id)
	if err := c.service.UpdateSpare(ctx, &spare); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新备件失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新备件成功")
}

// DeleteSpare 删除备件
// @Summary 删除备件
// @Description 删除指定ID的备件
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param id path int true "备件ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/{id} [delete]
func (c *SpareController) DeleteSpare(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteSpare(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除备件失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除备件成功")
}

// GetSpareByID 根据ID获取备件
// @Summary 获取备件详情
// @Description 根据ID获取备件详情
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param id path int true "备件ID"
// @Success 200 {object} response.ResponseStruct{data=asset.AssetSpare}
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/{id} [get]
func (c *SpareController) GetSpareByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	spare, err := c.service.GetSpareByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取备件失败: "+err.Error())
		return
	}

	response.Success(ctx, spare, "获取备件成功")
}

// ListSpares 获取备件列表
// @Summary 获取备件列表
// @Description 分页获取备件列表，支持多种精确查询条件
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param sn query string false "备件SN(精确匹配)"
// @Param pn query string false "备件PN号码(精确匹配)"
// @Param product_id query int false "产品ID(精确匹配)"
// @Param source_type query string false "来源类型(精确匹配)"
// @Param asset_status query string false "资产状态(精确匹配)"
// @Param hardware_status query string false "硬件状态(精确匹配)"
// @Param warehouse_id query int false "仓库ID(精确匹配)"
// @Param type query string false "备件类型(精确匹配)"
// @Param firmware_version query string false "固件版本(精确匹配)"
// @Param batch_number query string false "批次号(精确匹配)"
// @Param location query string false "存放位置(精确匹配)"
// @Param min_price query number false "最低价格"
// @Param max_price query number false "最高价格"
// @Param purchase_date_start query string false "购买日期开始(格式: 2023-01-01)"
// @Param purchase_date_end query string false "购买日期结束(格式: 2023-12-31)"
// @Param warranty_expire_start query string false "保修期开始(格式: 2023-01-01)"
// @Param warranty_expire_end query string false "保修期结束(格式: 2023-12-31)"
// @Param brand query string false "品牌(精确匹配)"
// @Param spec query string false "规格(精确匹配)"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares [get]
func (c *SpareController) ListSpares(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	// 构建查询参数
	params := make(map[string]interface{})

	// 获取所有查询参数
	if sn := ctx.Query("sn"); sn != "" {
		params["sn"] = sn

	}

	// 增加PN号码查询支持，同时支持pn和pn[]两种格式
	pnArray := ctx.QueryArray("pn[]")
	if len(pnArray) > 0 {
		params["pn"] = pnArray[0]
	} else if pn := ctx.Query("pn"); pn != "" {
		params["pn"] = pn
	}

	// 同时支持product_id和product_id[]两种格式
	productIdArray := ctx.QueryArray("product_id[]")
	if len(productIdArray) > 0 {
		params["product_id"] = productIdArray[0]
	} else if productId := ctx.Query("product_id"); productId != "" {
		params["product_id"] = productId
	}

	// 检查source_type和source_type[]两种参数格式
	sourceTypeArray := ctx.QueryArray("source_type[]")
	if len(sourceTypeArray) > 0 {
		params["source_type"] = sourceTypeArray[0]
	} else if sourceType := ctx.Query("source_type"); sourceType != "" {
		params["source_type"] = sourceType
	}

	// 检查asset_status和asset_status[]两种参数格式
	assetStatusArray := ctx.QueryArray("asset_status[]")
	if len(assetStatusArray) > 0 {
		params["asset_status"] = assetStatusArray[0]
	} else if assetStatus := ctx.Query("asset_status"); assetStatus != "" {
		params["asset_status"] = assetStatus
	}

	// 检查hardware_status和hardware_status[]两种参数格式
	hardwareStatusArray := ctx.QueryArray("hardware_status[]")
	if len(hardwareStatusArray) > 0 {
		params["hardware_status"] = hardwareStatusArray[0]
	} else if hardwareStatus := ctx.Query("hardware_status"); hardwareStatus != "" {
		params["hardware_status"] = hardwareStatus
	}

	if warehouseID, err := strconv.ParseUint(ctx.Query("warehouse_id"), 10, 32); err == nil && warehouseID > 0 {
		params["warehouse_id"] = uint(warehouseID)
	}

	// 检查type和type[]两种参数格式
	typeArray := ctx.QueryArray("type[]")
	if len(typeArray) > 0 {
		params["type"] = typeArray[0]
	} else if typeParam := ctx.Query("type"); typeParam != "" {
		params["type"] = typeParam
	}

	// 支持firmware_version和firmware_version[]两种格式
	firmwareVersionArray := ctx.QueryArray("firmware_version[]")
	if len(firmwareVersionArray) > 0 {
		params["firmware_version"] = firmwareVersionArray[0]
	} else if firmwareVersion := ctx.Query("firmware_version"); firmwareVersion != "" {
		params["firmware_version"] = firmwareVersion
	}

	if batchNumber := ctx.Query("batch_number"); batchNumber != "" {
		params["batch_number"] = batchNumber
	}

	if location := ctx.Query("location"); location != "" {
		params["location"] = location
	}

	if minPrice, err := strconv.ParseFloat(ctx.Query("min_price"), 64); err == nil {
		params["min_price"] = minPrice
	}

	if maxPrice, err := strconv.ParseFloat(ctx.Query("max_price"), 64); err == nil {
		params["max_price"] = maxPrice
	}

	if purchaseDateStart := ctx.Query("purchase_date_start"); purchaseDateStart != "" {
		params["purchase_date_start"] = purchaseDateStart
	}

	if purchaseDateEnd := ctx.Query("purchase_date_end"); purchaseDateEnd != "" {
		params["purchase_date_end"] = purchaseDateEnd
	}

	if warrantyExpireStart := ctx.Query("warranty_expire_start"); warrantyExpireStart != "" {
		params["warranty_expire_start"] = warrantyExpireStart
	}

	if warrantyExpireEnd := ctx.Query("warranty_expire_end"); warrantyExpireEnd != "" {
		params["warranty_expire_end"] = warrantyExpireEnd
	}

	// 检查sn和sn[]两种参数格式
	snArray := ctx.QueryArray("sn[]")
	if len(snArray) > 0 {
		params["sn"] = snArray[0]
	} else if sn := ctx.Query("sn"); sn != "" {
		params["sn"] = sn
	}

	// 同时支持brand和brand[]两种格式
	brandArray := ctx.QueryArray("brand[]")
	if len(brandArray) > 0 {
		params["brand"] = brandArray[0]
	} else if brand := ctx.Query("brand"); brand != "" {
		params["brand"] = brand
	}

	// 同时支持spec和spec[]两种格式
	specArray := ctx.QueryArray("spec[]")
	if len(specArray) > 0 {
		params["spec"] = specArray[0]
	} else if spec := ctx.Query("spec"); spec != "" {
		params["spec"] = spec
	}

	spares, total, err := c.service.ListSpares(ctx, page, pageSize, params)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取备件列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  spares,
		"total": total,
	}, "获取备件列表成功")
}

// GetSpareWithDetails 获取备件详情（包含关联信息）
// @Summary 获取备件详情（包含关联信息）
// @Description 获取指定ID的备件详细信息，包含产品、仓库、库存等关联信息
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param id path int true "备件ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/{id}/details [get]
func (c *SpareController) GetSpareWithDetails(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	spare, err := c.service.GetSpareWithDetails(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取备件详情失败: "+err.Error())
		return
	}

	response.Success(ctx, spare, "获取备件详情成功")
}

// ChangeSpareStatus 更改备件状态
// @Summary 更改备件状态
// @Description 更改备件的状态，例如变更为闲置、使用中、维修中等
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param id path int true "备件ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/{id}/status [post]
func (c *SpareController) ChangeSpareStatus(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
		Reason string `json:"reason" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.ChangeSpareStatus(ctx, uint(id), req.Status, req.Reason); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更改备件状态失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更改备件状态成功")
}

// AssignSpareToDevice 分配备件到设备
// @Summary 分配备件到设备
// @Description 将备件分配到指定设备
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param id path int true "备件ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/{id}/assign [post]
func (c *SpareController) AssignSpareToDevice(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var req struct {
		DeviceID uint   `json:"device_id" binding:"required"`
		Position string `json:"position" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.AssignSpareToDevice(ctx, uint(id), req.DeviceID, req.Position); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "分配备件失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "分配备件成功")
}

// RemoveSpareFromDevice 从设备移除备件
// @Summary 从设备移除备件
// @Description 将备件从当前安装的设备上移除
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param id path int true "备件ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/{id}/remove [post]
func (c *SpareController) RemoveSpareFromDevice(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.RemoveSpareFromDevice(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "移除备件失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "移除备件成功")
}

// TransferWarehouse 转移备件到其他仓库
// @Summary 转移备件到其他仓库
// @Description 将备件转移到指定仓库的指定位置
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param id path int true "备件ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/{id}/transfer [post]
func (c *SpareController) TransferWarehouse(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var req struct {
		WarehouseID uint   `json:"warehouse_id" binding:"required"`
		Location    string `json:"location" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.TransferWarehouse(ctx, uint(id), req.WarehouseID, req.Location); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "转移备件失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "转移备件成功")
}

// ListSparesByWarehouse 根据仓库ID获取备件列表
// @Summary 根据仓库ID获取备件列表
// @Description 获取指定仓库中的备件列表，支持分页
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param warehouseID path int true "仓库ID"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/by-warehouse/{warehouseID} [get]
func (c *SpareController) ListSparesByWarehouse(ctx *gin.Context) {
	warehouseID, err := strconv.ParseUint(ctx.Param("warehouseID"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的仓库ID")
		return
	}

	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	spares, total, err := c.service.ListSparesByWarehouse(ctx, uint(warehouseID), page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取备件列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  spares,
		"total": total,
	}, "获取备件列表成功")
}

// ListSparesByProduct 根据产品ID获取备件列表
// @Summary 根据产品ID获取备件列表
// @Description 获取指定产品的备件列表，支持分页
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param productID path int true "产品ID"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/by-product/{productID} [get]
func (c *SpareController) ListSparesByProduct(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("productID"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的产品ID")
		return
	}

	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	spares, total, err := c.service.ListSparesByProduct(ctx, uint(productID), page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取备件列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  spares,
		"total": total,
	}, "获取备件列表成功")
}

// GetSpareStatistics 获取备件统计信息
// @Summary 获取备件统计信息
// @Description 获取备件的统计信息，包括按类型、状态和仓库的统计
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param type query string false "备件类型"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/statistics [get]
func (c *SpareController) GetSpareStatistics(ctx *gin.Context) {
	spareType := ctx.Query("type")

	stats, err := c.service.GetSpareStatistics(ctx, spareType)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取备件统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取备件统计成功")
}

// ListSparesByComponentType 根据组件类型获取可用备件列表
// @Summary 根据组件类型获取可用备件列表
// @Description 获取指定组件类型的可用备件列表，包含详细信息
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param component_type query string true "组件类型"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/by-component-type [get]
func (c *SpareController) ListSparesByComponentType(ctx *gin.Context) {
	componentType := ctx.Query("component_type")
	if componentType == "" {
		response.Fail(ctx, http.StatusBadRequest, "组件类型不能为空")
		return
	}

	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	spares, total, err := c.service.ListSparesByComponentType(ctx, componentType, page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取备件列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  spares,
		"total": total,
	}, "获取备件列表成功")
}

// ListSparesByProductID 根据产品ID获取详细备件列表
// @Summary 根据产品ID获取详细备件列表
// @Description 获取指定产品的详细备件列表，包含产品、仓库、库存等关联信息
// @Tags 资产管理-备件
// @Accept json
// @Produce json
// @Param productID path int true "产品ID"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/spares/product/{productID}/details [get]
func (c *SpareController) ListSparesByProductID(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("productID"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的产品ID")
		return
	}

	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	spares, total, err := c.service.ListSparesByProductID(ctx, uint(productID), page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取备件列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  spares,
		"total": total,
	}, "获取备件列表成功")
}

type GetSparesBySNsDTO struct {
	SNs []string `json:"sns" binding:"required"`
}

// GetSparesBySNs 通过sn批量获取备件
func (c *SpareController) GetSparesBySNs(ctx *gin.Context) {
	var DTO GetSparesBySNsDTO
	if err := ctx.ShouldBindJSON(&DTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "绑定数据失败")
		return
	}
	if DTO.SNs == nil {
		response.Fail(ctx, http.StatusBadRequest, "查询的SN为空")
		return
	}
	spares, err := c.service.GetSpareBySNs(ctx, DTO.SNs)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "<UN "+err.Error())
	}
	response.Success(ctx, spares, "获取成功")

}
