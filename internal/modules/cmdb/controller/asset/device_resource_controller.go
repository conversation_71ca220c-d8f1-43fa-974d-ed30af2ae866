package asset

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"backend/internal/modules/cmdb/model/asset"
	ser "backend/internal/modules/cmdb/service/asset"
	"backend/response"
)

type DeviceResourceController struct {
	service ser.DeviceService
}

func NewDeviceResourceController(service ser.DeviceService) *DeviceResourceController {
	return &DeviceResourceController{service: service}
}

// List @Summary 获取服务器资源列表
// @Description 获取服务器资源列表，支持基于设备和资源属性的精确查询
// @Tags CMDB-服务器资源
// @Accept json
// @Produce json
// @Param page query int true "页码"
// @Param pageSize query int true "每页数量"
// @Param query query string false "搜索关键词(精确匹配SN/主机名)，支持以逗号、空格或换行符分隔的多个关键词"
// @Param brand query string false "品牌(精确匹配)"
// @Param model query string false "型号(精确匹配)"
// @Param assetStatus query string false "资产状态"
// @Param assetType query string false "资产类型"
// @Param bizStatus query string false "业务状态"
// @Param project query string false "项目名称(精确匹配)"
// @Param vpcIP query string false "VPC IP地址(精确匹配)"
// @Param bmcIP query string false "BMC IP地址(精确匹配)"
// @Param tenantIP query string false "租户IP地址(精确匹配)"
// @Param isBackup query bool false "是否备机"
// @Success 200 {object} response.ResponseStruct{data=response.PageResult{list=[]asset.Device}}
// @Router /cmdb/device-resources [get]
func (c *DeviceResourceController) List(ctx *gin.Context) {
	var req struct {
		Page        int    `form:"page" binding:"required,min=1"`
		PageSize    int    `form:"pageSize" binding:"required,min=1,max=99999"`
		Query       string `form:"query"`
		Brand       string `form:"brand"`
		Model       string `form:"model"`
		AssetStatus string `form:"assetStatus"`
		AssetType   string `form:"assetType"`
		BizStatus   string `form:"bizStatus"`
		Project     string `form:"project"`
		VpcIP       string `form:"vpcIP"`
		BmcIP       string `form:"bmcIP"`
		TenantIP    string `form:"tenantIP"`
		IsBackup    *bool  `form:"isBackup"`
		Cluster     string `form:"cluster"`
	}

	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 处理query参数，支持逗号、空格、换行符分隔的多个查询关键词
	query := req.Query
	if query != "" {
		// 将换行符替换为空格
		query = strings.NewReplacer("\n", " ", "\r", " ").Replace(query)
		// 将逗号替换为空格
		query = strings.ReplaceAll(query, ",", " ")
		// 替换连续的多个空格为单个空格
		for strings.Contains(query, "  ") {
			query = strings.ReplaceAll(query, "  ", " ")
		}
		query = strings.TrimSpace(query)
	}

	// 调用服务层查询数据
	devices, total, err := c.service.ListDeviceResourcesExtended(
		ctx,
		req.Page,
		req.PageSize,
		query,
		"", // hostname参数置空，因为已合并到query中
		req.Brand,
		req.Model,
		req.AssetStatus,
		req.AssetType,
		req.BizStatus,
		req.Project,
		req.Cluster,
		req.VpcIP,
		req.BmcIP,
		req.TenantIP,
		req.IsBackup,
	)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取服务器资源列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"total": total,
		"list":  devices,
	}, "获取服务器资源列表成功")
}

// @Summary 获取服务器资源详情
// @Description 根据ID获取服务器资源详情
// @Tags CMDB-服务器资源
// @Accept json
// @Produce json
// @Param id path int true "服务器ID"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/device-resources/{id} [get]
func (c *DeviceResourceController) GetByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 0)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	device, err := c.service.GetByIDWithResource(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取服务器资源失败: "+err.Error())
		return
	}

	response.Success(ctx, device, "获取服务器资源成功")
}

// @Summary 创建服务器资源
// @Description 创建新的服务器资源记录
// @Tags CMDB-服务器资源
// @Accept json
// @Produce json
// @Param data body asset.Device true "服务器资源数据"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/device-resources [post]
func (c *DeviceResourceController) Create(ctx *gin.Context) {
	var device asset.Device
	if err := ctx.ShouldBindJSON(&device); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	createdDevice, err := c.service.CreateWithResource(ctx, &device)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建服务器资源失败: "+err.Error())
		return
	}

	response.Success(ctx, createdDevice, "创建服务器资源成功")
}

// @Summary 更新服务器资源
// @Description 更新现有的服务器资源记录
// @Tags CMDB-服务器资源
// @Accept json
// @Produce json
// @Param id path int true "服务器ID"
// @Param data body asset.Device true "服务器资源数据"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/device-resources/{id} [put]
func (c *DeviceResourceController) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 0)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var device asset.Device
	if err := ctx.ShouldBindJSON(&device); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	device.ID = uint(id)
	updatedDevice, err := c.service.UpdateWithResource(ctx, &device)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新服务器资源失败: "+err.Error())
		return
	}

	response.Success(ctx, updatedDevice, "更新服务器资源成功")
}

// @Summary 删除服务器资源
// @Description 删除服务器资源记录
// @Tags CMDB-服务器资源
// @Accept json
// @Produce json
// @Param id path int true "服务器ID"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/device-resources/{id} [delete]
func (c *DeviceResourceController) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 0)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteWithResource(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除服务器资源失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除服务器资源成功")
}

// GetDeviceTemplate 获取设备关联的套餐模板
// @Summary 获取设备关联的套餐模板
// @Description 根据设备ID获取关联的套餐模板信息
// @Tags CMDB-服务器资源
// @Accept json
// @Produce json
// @Param id path int true "设备ID"
// @Param with_components query bool false "是否包含组件信息"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/devices/{id}/machine-template [get]
func (c *DeviceResourceController) GetDeviceTemplate(ctx *gin.Context) {
	// 从:id参数获取设备ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的设备ID: "+err.Error())
		return
	}

	withComponents, err := strconv.ParseBool(ctx.DefaultQuery("with_components", "false"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的参数: "+err.Error())
		return
	}

	template, err := c.service.GetDeviceTemplate(ctx, uint(id), withComponents)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取设备套餐模板失败: "+err.Error())
		return
	}

	response.Success(ctx, template, "获取设备套餐模板成功")
}
