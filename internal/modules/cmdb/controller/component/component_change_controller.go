package component

import (
	"backend/internal/modules/cmdb/service/component"
	"backend/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ComponentChangeRequest 组件变更请求
type ComponentChangeRequest struct {
	SpareID    uint                          `json:"spareId" binding:"required"`
	ChangeInfo component.ComponentChangeInfo `json:"changeInfo" binding:"required"`
}

// ComponentInstallRequest 组件安装请求
type ComponentInstallRequest struct {
	ServerID    uint                           `json:"serverId" binding:"required"`
	ProductID   uint                           `json:"productId" binding:"required"`
	SpareID     uint                           `json:"spareId" binding:"required"`
	InstallInfo component.ComponentInstallInfo `json:"installInfo" binding:"required"`
}

// ComponentRemoveRequest 组件移除请求
type ComponentRemoveRequest struct {
	RemoveInfo component.ComponentRemoveInfo `json:"removeInfo" binding:"required"`
}

// ComponentChangeController 组件变更控制器
type ComponentChangeController struct {
	changeService component.ComponentChangeService
}

// NewComponentChangeController 创建组件变更控制器
func NewComponentChangeController(changeService component.ComponentChangeService) *ComponentChangeController {
	return &ComponentChangeController{
		changeService: changeService,
	}
}

// RegisterRoutes 注册路由
func (c *ComponentChangeController) RegisterRoutes(router *gin.RouterGroup) {
	componentGroup := router.Group("/components")

	// 获取组件变更历史
	componentGroup.GET("/:id/changes", c.GetComponentChangeHistory)
	// 获取服务器组件变更历史
	componentGroup.GET("/server/:serverId/changes", c.GetServerComponentChangeHistory)
	// 更换组件
	componentGroup.POST("/:id/replace", c.ReplaceComponent)
	// 安装组件
	componentGroup.POST("/install", c.InstallComponent)
	// 移除组件
	componentGroup.POST("/:id/remove", c.RemoveComponent)
}

// GetComponentChangeHistory 获取组件变更历史
func (c *ComponentChangeController) GetComponentChangeHistory(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Fail(ctx, 400, "无效的组件ID")
		return
	}

	changes, err := c.changeService.GetChangeHistory(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, changes, "获取组件变更历史成功")
}

// GetServerComponentChangeHistory 获取服务器组件变更历史
func (c *ComponentChangeController) GetServerComponentChangeHistory(ctx *gin.Context) {
	serverIDStr := ctx.Param("serverId")
	serverID, err := strconv.ParseUint(serverIDStr, 10, 64)
	if err != nil {
		response.Fail(ctx, 400, "无效的服务器ID")
		return
	}

	pageStr := ctx.DefaultQuery("page", "1")
	pageSizeStr := ctx.DefaultQuery("pageSize", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 10
	}

	changes, total, err := c.changeService.GetServerChangeHistory(ctx, uint(serverID), page, pageSize)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	// 构造分页响应
	data := gin.H{
		"list":      changes,
		"total":     total,
		"page":      page,
		"pageSize":  pageSize,
		"totalPage": (total + int64(pageSize) - 1) / int64(pageSize),
	}

	response.Success(ctx, data, "获取服务器组件变更历史成功")
}

// ReplaceComponent 更换组件
func (c *ComponentChangeController) ReplaceComponent(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Fail(ctx, 400, "无效的组件ID")
		return
	}

	var req ComponentChangeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, 400, "无效的请求参数")
		return
	}

	// 从JWT中获取操作人ID
	userID, exists := ctx.Get("userId")
	if exists {
		if userIDVal, ok := userID.(uint); ok {
			req.ChangeInfo.OperatorID = userIDVal
		}
	}

	newComponent, err := c.changeService.ReplaceComponent(ctx, uint(id), req.SpareID, &req.ChangeInfo)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, newComponent, "组件更换成功")
}

// InstallComponent 安装组件
func (c *ComponentChangeController) InstallComponent(ctx *gin.Context) {
	var req ComponentInstallRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, 400, "无效的请求参数")
		return
	}

	// 从JWT中获取操作人ID
	userID, exists := ctx.Get("userId")
	if exists {
		if userIDVal, ok := userID.(uint); ok {
			req.InstallInfo.OperatorID = userIDVal
		}
	}

	newComponent, err := c.changeService.InstallComponent(ctx, req.ServerID, req.ProductID, req.SpareID, &req.InstallInfo)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, newComponent, "组件安装成功")
}

// RemoveComponent 移除组件
func (c *ComponentChangeController) RemoveComponent(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Fail(ctx, 400, "无效的组件ID")
		return
	}

	var req ComponentRemoveRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, 400, "无效的请求参数")
		return
	}

	// 从JWT中获取操作人ID
	userID, exists := ctx.Get("userId")
	if exists {
		if userIDVal, ok := userID.(uint); ok {
			req.RemoveInfo.OperatorID = userIDVal
		}
	}

	if err := c.changeService.RemoveComponent(ctx, uint(id), &req.RemoveInfo); err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, gin.H{"message": "组件已成功移除"}, "组件移除成功")
}
