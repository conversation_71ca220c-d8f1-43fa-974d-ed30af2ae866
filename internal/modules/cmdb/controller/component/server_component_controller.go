package component

import (
	"backend/internal/modules/cmdb/model/component"
	ser "backend/internal/modules/cmdb/service/component"
	"backend/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ServerComponentController 服务器组件控制器
type ServerComponentController struct {
	service ser.ServerComponentService
}

// NewServerComponentController 创建服务器组件控制器
func NewServerComponentController(service ser.ServerComponentService) *ServerComponentController {
	return &ServerComponentController{service: service}
}

// Create 创建服务器组件
// @Summary 创建服务器组件
// @Description 创建新的服务器组件
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param component body component.ServerComponent true "服务器组件信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/server-components [post]
func (c *ServerComponentController) Create(ctx *gin.Context) {
	var component component.ServerComponent
	if err := ctx.ShouldBindJSON(&component); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.CreateServerComponent(ctx, &component); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建服务器组件失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": component.ID}, "创建服务器组件成功")
}

// Update 更新服务器组件
// @Summary 更新服务器组件
// @Description 更新服务器组件信息
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param id path int true "服务器组件ID"
// @Param component body component.ServerComponent true "服务器组件信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/server-components/{id} [put]
func (c *ServerComponentController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var component component.ServerComponent
	if err := ctx.ShouldBindJSON(&component); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	component.ID = uint(id)
	if err := c.service.UpdateServerComponent(ctx, &component); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新服务器组件失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新服务器组件成功")
}

// Delete 删除服务器组件
// @Summary 删除服务器组件
// @Description 删除指定ID的服务器组件
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param id path int true "服务器组件ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/server-components/{id} [delete]
func (c *ServerComponentController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteServerComponent(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除服务器组件失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除服务器组件成功")
}

// GetByID 根据ID获取服务器组件
// @Summary 获取服务器组件详情
// @Description 根据ID获取服务器组件详情
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param id path int true "服务器组件ID"
// @Success 200 {object} response.ResponseStruct{data=component.ServerComponent}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/server-components/{id} [get]
func (c *ServerComponentController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	component, err := c.service.GetServerComponentByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取服务器组件失败: "+err.Error())
		return
	}

	response.Success(ctx, component, "获取服务器组件成功")
}

// GetBySN 根据SN获取服务器组件
// @Summary 根据SN获取服务器组件
// @Description 根据SN获取服务器组件详情
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param sn query string true "组件SN"
// @Success 200 {object} response.ResponseStruct{data=component.ServerComponent}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/server-components/by-sn [get]
func (c *ServerComponentController) GetBySN(ctx *gin.Context) {
	sn := ctx.Query("sn")
	if sn == "" {
		response.Fail(ctx, http.StatusBadRequest, "SN不能为空")
		return
	}

	component, err := c.service.GetServerComponentBySN(ctx, sn)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取服务器组件失败: "+err.Error())
		return
	}

	response.Success(ctx, component, "获取服务器组件成功")
}

// ListByServerID 根据服务器ID获取组件列表
// @Summary 根据服务器ID获取组件列表
// @Description 根据服务器ID获取该服务器的所有组件
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param serverID query int true "服务器ID"
// @Success 200 {object} response.ResponseStruct{data=[]component.ServerComponent}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/server-components/by-server [get]
func (c *ServerComponentController) ListByServerID(ctx *gin.Context) {
	serverID, err := strconv.ParseUint(ctx.Query("serverID"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的服务器ID")
		return
	}

	components, err := c.service.ListServerComponentsByServerID(ctx, uint(serverID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取服务器组件列表失败: "+err.Error())
		return
	}

	response.Success(ctx, components, "获取服务器组件列表成功")
}

// List 获取服务器组件列表
// @Summary 获取服务器组件列表
// @Description 分页获取服务器组件列表
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Param componentType query string false "组件类型(CPU,GPU,Memory,Disk等)"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/server-components [get]
func (c *ServerComponentController) List(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("query", "")
	componentType := ctx.DefaultQuery("componentType", "")

	components, total, err := c.service.ListServerComponents(ctx, page, pageSize, query, componentType)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取服务器组件列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  components,
		"total": total,
	}, "获取服务器组件列表成功")
}

// GetComponentWithDetails 获取组件详情
// @Summary 获取组件详情
// @Description 获取指定ID的组件详细信息（包含关联数据）
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param id path int true "组件ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct "请求错误"
// @Failure 500 {object} response.ResponseStruct "内部错误"
// @Router /cmdb/server-components/{id}/details [get]
func (c *ServerComponentController) GetComponentWithDetails(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "ID参数错误")
		return
	}

	comp, err := c.service.GetComponentWithDetails(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取组件详情失败: "+err.Error())
		return
	}

	response.Success(ctx, comp, "获取组件详情成功")
}

// ListComponentsWithDetails 获取组件列表（包含关联信息）
// @Summary 获取组件列表（包含关联信息）
// @Description 获取组件列表，包含关联信息，支持分页和过滤
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Param componentType query string false "组件类型(CPU,GPU,Memory,Disk等)"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct "请求错误"
// @Failure 500 {object} response.ResponseStruct "内部错误"
// @Router /cmdb/server-components/details [get]
func (c *ServerComponentController) ListComponentsWithDetails(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("query", "")
	componentType := ctx.DefaultQuery("componentType", "")

	components, total, err := c.service.ListComponentsWithDetails(ctx, page, pageSize, query, componentType)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取组件列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  components,
		"total": total,
	}, "获取组件列表成功")
}

// GetComponentStatistics 获取组件统计信息
// @Summary 获取组件统计信息
// @Description 获取组件的统计信息，包括按类型、状态和服务器的统计
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param component_type query string false "组件类型"
// @Success 200 {object} response.ResponseStruct{data=component.ComponentStatistics} "成功"
// @Failure 500 {object} response.ResponseStruct "内部错误"
// @Router /cmdb/server-components/statistics [get]
func (c *ServerComponentController) GetComponentStatistics(ctx *gin.Context) {
	componentType := ctx.Query("component_type")

	stats, err := c.service.GetComponentStatistics(ctx, componentType)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取组件统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取组件统计成功")
}

// GetComponentSpares 获取组件相关的备件
// @Summary 获取组件相关的备件
// @Description 获取指定组件ID关联的备件列表
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param id path int true "组件ID"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct "成功"
// @Failure 400 {object} response.ResponseStruct "请求错误"
// @Failure 500 {object} response.ResponseStruct "内部错误"
// @Router /cmdb/server-components/{id}/spares [get]
func (c *ServerComponentController) GetComponentSpares(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的组件ID")
		return
	}

	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	spares, total, err := c.service.GetComponentSpares(ctx, uint(id), page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取组件备件失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  spares,
		"total": total,
	}, "获取组件备件成功")
}

// GetSparesByComponentType 根据组件类型获取可用备件
// @Summary 根据组件类型获取可用备件
// @Description 获取指定组件类型的可用备件列表
// @Tags 资产管理-服务器组件
// @Accept json
// @Produce json
// @Param component_type query string true "组件类型"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct "成功"
// @Failure 400 {object} response.ResponseStruct "请求错误"
// @Failure 500 {object} response.ResponseStruct "内部错误"
// @Router /cmdb/server-components/type-spares [get]
func (c *ServerComponentController) GetSparesByComponentType(ctx *gin.Context) {
	componentType := ctx.Query("component_type")
	if componentType == "" {
		response.Fail(ctx, http.StatusBadRequest, "组件类型不能为空")
		return
	}

	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	spares, total, err := c.service.GetSparesByComponentType(ctx, componentType, page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取组件类型备件失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  spares,
		"total": total,
	}, "获取组件类型备件成功")
}

// RegisterRoutes 注册路由
func (c *ServerComponentController) RegisterRoutes(router *gin.RouterGroup) {
	serverComponentsGroup := router.Group("/server-components")
	{
		// 基本CRUD操作
		serverComponentsGroup.POST("", c.Create)

		// 先注册具体路径的GET路由
		serverComponentsGroup.GET("/by-sn", c.GetBySN)
		serverComponentsGroup.GET("/by-server", c.ListByServerID)
		serverComponentsGroup.GET("/details", c.ListComponentsWithDetails)
		serverComponentsGroup.GET("/statistics", c.GetComponentStatistics)
		serverComponentsGroup.GET("/type-spares", c.GetSparesByComponentType)

		// 组件详情与备件
		serverComponentsGroup.GET("/:id/details", c.GetComponentWithDetails)
		serverComponentsGroup.GET("/:id/spares", c.GetComponentSpares)

		// 最后注册一般的参数化路由
		serverComponentsGroup.GET("", c.List)
		serverComponentsGroup.GET("/:id", c.GetByID)
		serverComponentsGroup.PUT("/:id", c.Update)
		serverComponentsGroup.DELETE("/:id", c.Delete)
	}
}
