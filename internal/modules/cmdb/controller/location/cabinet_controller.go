// controller/location/cabinet_controller.go
package location

import (
	"backend/internal/modules/cmdb/model/location"
	ser "backend/internal/modules/cmdb/service/location"
	"backend/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// CabinetController 机柜控制器
type CabinetController struct {
	service ser.CabinetService
}

// NewCabinetController 创建机柜控制器
func NewCabinetController(service ser.CabinetService) *CabinetController {
	return &CabinetController{service: service}
}

// Create 创建机柜
// @Summary 创建机柜
// @Description 创建新的机柜
// @Tags 位置管理-机柜
// @Accept json
// @Produce json
// @Param cabinet body location.Cabinet true "机柜信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/cabinets [post]
func (c *CabinetController) Create(ctx *gin.Context) {
	var cabinet location.Cabinet
	if err := ctx.ShouldBindJSON(&cabinet); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.CreateCabinet(ctx, &cabinet); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建机柜失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": cabinet.ID}, "创建机柜成功")
}

// Update 更新机柜
// @Summary 更新机柜
// @Description 更新机柜信息
// @Tags 位置管理-机柜
// @Accept json
// @Produce json
// @Param id path int true "机柜ID"
// @Param cabinet body location.Cabinet true "机柜信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/cabinets/{id} [put]
func (c *CabinetController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var cabinet location.Cabinet
	if err := ctx.ShouldBindJSON(&cabinet); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	cabinet.ID = uint(id)
	if err := c.service.UpdateCabinet(ctx, &cabinet); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新机柜失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新机柜成功")
}

// Delete 删除机柜
// @Summary 删除机柜
// @Description 删除指定ID的机柜
// @Tags 位置管理-机柜
// @Accept json
// @Produce json
// @Param id path int true "机柜ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/cabinets/{id} [delete]
func (c *CabinetController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteCabinet(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除机柜失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除机柜成功")
}

// GetByID 根据ID获取机柜
// @Summary 获取机柜详情
// @Description 根据ID获取机柜详情
// @Tags 位置管理-机柜
// @Accept json
// @Produce json
// @Param id path int true "机柜ID"
// @Success 200 {object} response.ResponseStruct{data=location.Cabinet}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/cabinets/{id} [get]
func (c *CabinetController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	cabinet, err := c.service.GetCabinetByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取机柜失败: "+err.Error())
		return
	}

	response.Success(ctx, cabinet, "获取机柜成功")
}

// GetByRoomID 根据房间ID获取机柜列表
// @Summary 获取房间下的机柜列表
// @Description 根据房间ID获取机柜列表
// @Tags 位置管理-机柜
// @Accept json
// @Produce json
// @Param id path int true "房间ID"
// @Success 200 {object} response.ResponseStruct{data=[]location.Cabinet}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/rooms/{id}/cabinets [get]
func (c *CabinetController) GetByRoomID(ctx *gin.Context) {
	roomID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的房间ID")
		return
	}

	cabinets, err := c.service.GetCabinetsByRoomID(ctx, uint(roomID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取机柜列表失败: "+err.Error())
		return
	}

	response.Success(ctx, cabinets, "获取机柜列表成功")
}

// List 获取机柜列表
// @Summary 获取机柜列表
// @Description 分页获取机柜列表
// @Tags 位置管理-机柜
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Param roomID query int false "房间ID，按房间筛选"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/cabinets [get]
func (c *CabinetController) List(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("query", "")

	// 获取房间ID参数
	var roomID uint
	if rID := ctx.Query("roomID"); rID != "" {
		id, err := strconv.ParseUint(rID, 10, 32)
		if err == nil {
			roomID = uint(id)
		}
	}

	cabinets, total, err := c.service.ListCabinets(ctx, page, pageSize, query, roomID)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取机柜列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  cabinets,
		"total": total,
	}, "获取机柜列表成功")
}
