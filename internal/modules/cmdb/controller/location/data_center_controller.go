package location

import (
	"backend/internal/modules/cmdb/model/location"
	ser "backend/internal/modules/cmdb/service/location"
	"backend/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// DataCenterController 机房控制器
type DataCenterController struct {
	service ser.DataCenterService
}

// NewDataCenterController 创建机房控制器
func NewDataCenterController(service ser.DataCenterService) *DataCenterController {
	return &DataCenterController{service: service}
}

// Create 创建机房
// @Summary 创建机房
// @Description 创建新的机房
// @Tags 位置管理-机房
// @Accept json
// @Produce json
// @Param dataCenter body location.DataCenter true "机房信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/datacenters [post]
func (c *DataCenterController) Create(ctx *gin.Context) {
	var dataCenter location.DataCenter
	if err := ctx.ShouldBindJSON(&dataCenter); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.CreateDataCenter(ctx, &dataCenter); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建机房失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": dataCenter.ID}, "创建机房成功")
}

// Update 更新机房
// @Summary 更新机房
// @Description 更新机房信息
// @Tags 位置管理-机房
// @Accept json
// @Produce json
// @Param id path int true "机房ID"
// @Param dataCenter body location.DataCenter true "机房信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/datacenters/{id} [put]
func (c *DataCenterController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var dataCenter location.DataCenter
	if err := ctx.ShouldBindJSON(&dataCenter); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	dataCenter.ID = uint(id)
	if err := c.service.UpdateDataCenter(ctx, &dataCenter); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新机房失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新机房成功")
}

// Delete 删除机房
// @Summary 删除机房
// @Description 删除指定ID的机房
// @Tags 位置管理-机房
// @Accept json
// @Produce json
// @Param id path int true "机房ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/datacenters/{id} [delete]
func (c *DataCenterController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteDataCenter(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除机房失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除机房成功")
}

// GetByID 根据ID获取机房
// @Summary 获取机房详情
// @Description 根据ID获取机房详情
// @Tags 位置管理-机房
// @Accept json
// @Produce json
// @Param id path int true "机房ID"
// @Success 200 {object} response.ResponseStruct{data=location.DataCenter}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/datacenters/{id} [get]
func (c *DataCenterController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	dataCenter, err := c.service.GetDataCenterByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取机房失败: "+err.Error())
		return
	}

	response.Success(ctx, dataCenter, "获取机房成功")
}

// GetByAZID 根据可用区ID获取机房列表
// @Summary 获取可用区下的机房列表
// @Description 根据可用区ID获取机房列表
// @Tags 位置管理-机房
// @Accept json
// @Produce json
// @Param id path int true "可用区ID"
// @Success 200 {object} response.ResponseStruct{data=[]location.DataCenter}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/azs/{id}/datacenters [get]
func (c *DataCenterController) GetByAZID(ctx *gin.Context) {
	azID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的可用区ID")
		return
	}

	dataCenters, err := c.service.GetDataCentersByAZID(ctx, uint(azID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取机房列表失败: "+err.Error())
		return
	}

	response.Success(ctx, dataCenters, "获取机房列表成功")
}

// List 获取机房列表
// @Summary 获取机房列表
// @Description 分页获取机房列表
// @Tags 位置管理-机房
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/datacenters [get]
func (c *DataCenterController) List(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("query", "")

	dataCenters, total, err := c.service.ListDataCenters(ctx, page, pageSize, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取机房列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  dataCenters,
		"total": total,
	}, "获取机房列表成功")
}
