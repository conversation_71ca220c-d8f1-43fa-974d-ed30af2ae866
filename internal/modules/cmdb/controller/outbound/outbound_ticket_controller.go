package outbound

import (
	cmdbOutbound "backend/internal/modules/cmdb/model/outbound"
	cmdbCommon "backend/internal/modules/cmdb/model/outbound/common"
	"backend/internal/modules/cmdb/service/outbound"
	cmdbWorkflow "backend/internal/modules/cmdb/workflow"
	"backend/internal/modules/ticket/common"
	"backend/response"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// OutboundTicketController 出库单控制器
type OutboundTicketController struct {
	service outbound.OutboundTicketService
}

// NewOutboundTicketController 创建出库单控制器
func NewOutboundTicketController(service outbound.OutboundTicketService) *OutboundTicketController {
	return &OutboundTicketController{service: service}
}

// RegisterRoutes 注册路由
func (c *OutboundTicketController) RegisterRoutes(router *gin.RouterGroup) {
	outboundTicketRouter := router.Group("/outbound-tickets")
	{
		outboundTicketRouter.POST("", c.CreateOutboundTicket)
		outboundTicketRouter.GET("/:id", c.GetOutboundTicket)
		outboundTicketRouter.PUT("/:id", c.UpdateOutboundTicket)
		outboundTicketRouter.PUT("/:id/close", c.CloseOutboundTicket)
		outboundTicketRouter.GET("", c.ListOutboundTickets)
		outboundTicketRouter.GET("/:id/history", c.GetOutboundTicketStatusHistory)
		outboundTicketRouter.PUT("/:id/transition", c.TransitionOutboundTicket)

		//	重构后的接口
		outboundTicketRouter.POST("/v2", c.CreateOutboundTicketV2) // 修改绑定的结构
		outboundTicketRouter.GET("/:id/info", c.GetTicketInfo)
		outboundTicketRouter.GET("/:id/details", c.GetTicketDetails)
		outboundTicketRouter.GET("/:id/details-list", c.GetTicketDetailsList)
		outboundTicketRouter.PUT("/details", c.UpdateOutboundTicketDetails)
		outboundTicketRouter.PUT("/:id/transition/v2", c.TransitionOutboundTicketV2)
		outboundTicketRouter.POST("/:id/start-workflow", c.StartWorkflow)                  // 新增启动工作流接口
		outboundTicketRouter.PUT("/details/import", c.UpdateOutboundTicketDetailsByImport) // 添加导入接口
	}
}

func (c *OutboundTicketController) CreateOutboundTicket(ctx *gin.Context) {
	var requestBody struct {
		OutboundTitle  string `json:"outbound_title" binding:"required"`
		OutboundType   string `json:"outbound_type" binding:"required"`
		OutboundReason string `json:"outbound_reason" binding:"required"`
		Spares         []struct {
			PN     string `json:"pn" binding:"required"`
			Number *int   `json:"number" binding:"required"`
		} `json:"spares" binding:"required"`
		RepairTicketId uint `json:"repair_ticket_id"`

		Project      string `json:"project"`
		OrderNumber  string `json:"order_number"`
		BuyerInfo    string `json:"buyer_info"`
		ShipmentTime string `json:"shipmentTime"`

		Machines        string `json:"machines"`
		DestWarehouseID uint   `json:"dest_warehouse_id"`
		DestLocation    string `json:"dest_location"`

		CreationTime string `json:"creationTime"`
		Reason       string `json:"reason"`
	}

	if err := ctx.ShouldBindJSON(&requestBody); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: 备件ID是必填字段: "+err.Error())
		return
	}

	// 获取当前用户信息
	currentUserID, currentUserName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败: "+err.Error())
		return
	}

	creationTime := time.Now()
	if requestBody.CreationTime != "" {
		parsedTime, err := time.Parse(time.RFC3339, requestBody.CreationTime)
		if err == nil {
			creationTime = parsedTime
		}
	}

	spares, err := json.Marshal(requestBody.Spares)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "序列化备件信息失败: "+err.Error())
		return
	}

	// 创建一个只包含必要信息的备件出库单对象
	outboundTicket := cmdbOutbound.SpareOutboundTicket{
		Project:        requestBody.Project,
		OutboundTitle:  requestBody.OutboundTitle,
		OutboundType:   requestBody.OutboundType,
		OutboundReason: requestBody.OutboundReason,
		Spares:         string(spares),
		CreationTime:   creationTime,
		ReporterID:     currentUserID,
		ReporterName:   currentUserName,
	}

	if requestBody.OutboundReason == cmdbWorkflow.Repair {
		outboundTicket.RepairTicketId = requestBody.RepairTicketId
	}

	if requestBody.OutboundReason == cmdbWorkflow.Sell {
		outboundTicket.OrderNumber = requestBody.OrderNumber
		outboundTicket.BuyerInfo = requestBody.BuyerInfo

		shipmentTime := time.Now()
		if requestBody.ShipmentTime != "" {
			parsedTime, err := time.Parse(time.RFC3339, requestBody.ShipmentTime)
			if err == nil {
				shipmentTime = parsedTime
			}
		}
		outboundTicket.ShipmentTime = &shipmentTime
	}

	if requestBody.OutboundReason == cmdbWorkflow.Replacement {
		outboundTicket.Machines = requestBody.Machines
	}

	if requestBody.OutboundReason == cmdbWorkflow.Allocate {
		outboundTicket.DestWarehouseID = requestBody.DestWarehouseID
		outboundTicket.DestLocation = requestBody.DestLocation
	}

	// 设置默认值
	//if faultTicket.FaultType == "" {
	//	faultTicket.FaultType = "hardware"
	//}
	//if faultTicket.Priority == "" {
	//	faultTicket.Priority = "medium"
	//}
	//if faultTicket.Source == "" {
	//	faultTicket.Source = "manual"
	//}

	if err := c.service.CreateOutboundTicket(ctx, &outboundTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建出库单失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": outboundTicket.ID, "ticket_no": outboundTicket.TicketNo}, "创建出库单成功")
}

type request struct {
	Project        string                      `json:"project" binding:"required"`
	OutboundType   string                      `json:"outbound_type" binding:"required"`
	OutboundReason string                      `json:"outbound_reason" binding:"required"`
	Info           []cmdbOutbound.OutboundInfo `json:"info" binding:"required"`
	OutboundTitle  string                      `json:"outbound_title" binding:"required"`
	RepairTicketId uint                        `json:"repair_ticket_id"`
	OrderNumber    string                      `json:"order_number"`
	BuyerInfo      string                      `json:"buyer_info"`
	ShipmentTime   string                      `json:"shipmentTime"`
	DeviceSN       string                      `json:"device_sn"`

	SourceWarehouseID uint   `json:"source_warehouse_id"`
	SourceLocation    string `json:"source_location"`
	DestWarehouseID   uint   `json:"dest_warehouse_id"`
	DestLocation      string `json:"dest_location"`

	TrackingInfo string `json:"tracking_info" gorm:"type:text;comment:物流跟踪信息"`
}

// CreateOutboundTicketV2 创建出库单
func (c *OutboundTicketController) CreateOutboundTicketV2(ctx *gin.Context) {
	var (
		req request
	)
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: : "+err.Error())
		return
	}

	userID, user, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败: "+err.Error())
		return
	}

	for i := 0; i < len(req.Info); i++ {
		req.Info[i].OutboundType = req.OutboundType
	}

	Ticket := cmdbOutbound.SpareOutboundTicket{
		Project:        req.Project,
		AssetType:      req.OutboundType,
		OutboundType:   req.OutboundType,
		OutboundReason: req.OutboundReason,
		Info:           req.Info,
		OutboundTitle:  req.OutboundTitle,
		CreationTime:   time.Now(),
		ReporterID:     userID,
		ReporterName:   user,
	}

	switch req.OutboundReason {
	case cmdbCommon.OutboundReasonReplacement: // 改配
		Ticket.Status = cmdbCommon.StatusWaitingEngineerApproval
		Ticket.Stage = cmdbCommon.StageEngineerApproval
		Ticket.SourceWarehouseID = req.SourceWarehouseID
		Ticket.SourceLocation = req.SourceLocation
	case cmdbCommon.OutboundReasonReturnRepair, cmdbWorkflow.OutboundReasonRack: // 返修、上架
		Ticket.Status = cmdbCommon.StatusWaitingAssetApproval
		Ticket.Stage = cmdbCommon.StageAssetApproval
		Ticket.SourceWarehouseID = req.SourceWarehouseID
		Ticket.SourceLocation = req.SourceLocation
	case cmdbCommon.OutboundReasonRepair: // 维修
		Ticket.Status = cmdbCommon.StatusWaitingEngineerApproval
		Ticket.Stage = cmdbCommon.StageEngineerApproval
		Ticket.RepairTicketId = req.RepairTicketId
		Ticket.SourceWarehouseID = req.SourceWarehouseID
		Ticket.SourceLocation = req.SourceLocation
		Ticket.DeviceSN = req.DeviceSN
	case cmdbCommon.OutboundReasonAllocate: // 调拨
		Ticket.Status = cmdbCommon.StatusWaitingAssetApproval
		Ticket.Stage = cmdbCommon.StageAssetApproval
		Ticket.SourceWarehouseID = req.SourceWarehouseID
		Ticket.SourceLocation = req.SourceLocation
		Ticket.DestWarehouseID = req.DestWarehouseID
		Ticket.DestLocation = req.DestLocation
	case cmdbCommon.OutboundReasonSell: // 售卖
		Ticket.Status = cmdbCommon.StatusWaitingBuyerApproval
		Ticket.Stage = cmdbCommon.StageBuyerApproval
		Ticket.SourceWarehouseID = req.SourceWarehouseID
		Ticket.SourceLocation = req.SourceLocation
		Ticket.OrderNumber = req.OrderNumber
		Ticket.BuyerInfo = req.BuyerInfo
	default:
		response.Fail(ctx, http.StatusBadRequest, "该接口暂不支持此入库原因:"+req.OutboundReason)
		return
	}

	err = c.service.CreateOutboundTicketV2(ctx, &Ticket)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, fmt.Errorf("创建失败：%v", err).Error())
		return
	}
	response.Success(ctx, Ticket.ID, "创建成功")
}

func (c *OutboundTicketController) GetOutboundTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	outboundTicket, err := c.service.GetOutboundTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取出库单失败: "+err.Error())
		return
	}

	response.Success(ctx, outboundTicket, "获取出库单成功")
}

func (c *OutboundTicketController) UpdateOutboundTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var outboundTicket cmdbOutbound.SpareOutboundTicket
	if err := ctx.ShouldBindJSON(&outboundTicket); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	outboundTicket.ID = uint(id)
	if err := c.service.UpdateOutboundTicket(ctx, &outboundTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新出库单失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新出库单成功")
}

// UpdateOutboundTicketDetails 更新出库单
func (c *OutboundTicketController) UpdateOutboundTicketDetails(ctx *gin.Context) {
	var outboundDetails []cmdbOutbound.OutboundDetail
	err := ctx.ShouldBindJSON(&outboundDetails)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "绑定数据失败："+err.Error())
		return
	}
	if err := c.service.UpdateOutboundTicketDetails(ctx, outboundDetails); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新出库单失败: "+err.Error())
		return
	}
	response.Success(ctx, nil, "更新出库单成功")
}

// UpdateOutboundTicketDetailsByImport 通过Excel文件导入更新出库单详情
func (c *OutboundTicketController) UpdateOutboundTicketDetailsByImport(ctx *gin.Context) {
	var importForm cmdbOutbound.OutboundDetailImport
	if err := ctx.ShouldBind(&importForm); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "绑定数据失败: "+err.Error())
		return
	}

	err := c.service.UpdateOutboundTicketDetailsByImport(ctx, &importForm)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "导入出库详情失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "导入成功")
}

func (c *OutboundTicketController) CloseOutboundTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 关闭出库单
	if err := c.service.CloseOutboundTicket(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "关闭出库单失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "关闭出库单成功")
}

func (c *OutboundTicketController) ListOutboundTickets(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页大小: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("query", "")
	stage := ctx.DefaultQuery("stage", "")
	// 出库单类型
	outboundType := ctx.DefaultQuery("outboundType", "")
	outboundReason := ctx.DefaultQuery("outboundReason", "")
	project := ctx.DefaultQuery("project", "")
	outboundTickets, total, err := c.service.ListOutboundTickets(ctx, page, pageSize, query, stage, outboundType, outboundReason, project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取出库单列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  outboundTickets,
		"total": total,
	}, "获取出库单列表成功")
}

func (c *OutboundTicketController) GetOutboundTicketStatusHistory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	history, err := c.service.GetOutboundTicketStatusHistory(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取状态历史失败: "+err.Error())
		return
	}

	response.Success(ctx, history, "获取状态历史成功")
}

func (c *OutboundTicketController) TransitionOutboundTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var transitionRequest struct {
		Stage    string                 `json:"stage" binding:"required"`  // 工作流阶段
		Status   string                 `json:"status" binding:"required"` // 新状态
		Comments string                 `json:"comments"`                  // 备注
		Data     map[string]interface{} `json:"data"`                      // 阶段相关数据
	}

	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取当前工单
	ticket, err := c.service.GetOutboundTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取出库单失败: "+err.Error())
		return
	}

	// 验证状态转换是否合法
	if !cmdbWorkflow.IsValidStatusTransition(ticket.Status, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", ticket.Status, transitionRequest.Status))
		return
	}

	// 验证阶段转换是否合法
	isValid, message := cmdbWorkflow.ValidateStageTransition(ticket.Status, transitionRequest.Stage)
	if !isValid {
		response.Fail(ctx, http.StatusBadRequest, "当前状态下不能触发此阶段: "+message)
		return
	}

	// 根据不同的阶段创建相应的业务记录
	switch transitionRequest.Stage {
	case cmdbWorkflow.StageCustomerApproval:
		// 创建客户审批记录
		customerApproval := &cmdbOutbound.OutboundApproval{
			TicketID:     uint(id),
			TicketNo:     ticket.TicketNo,
			Status:       "",
			ResponseTime: time.Now(),
			Comments:     transitionRequest.Comments,
			CustomerID:   operatorID,
			CustomerName: operatorName,
		}

		if transitionRequest.Data != nil {
			if status, ok := transitionRequest.Data["status"].(string); ok {
				customerApproval.Status = status
			} else {
				response.Fail(ctx, http.StatusBadRequest, "审批数据获取不到: "+err.Error())
				return
			}
		}

		if err := c.service.CreateApproval(ctx, customerApproval); err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "创建客户审批记录失败: "+err.Error())
			return
		}

		//case cmdbWorkflow.StageSecondApproval:
		//	approval, err := c.service.GetApprovalById(ctx, uint(id))
		//	if err != nil {
		//		fmt.Println(approval)
		//		response.Fail(ctx, http.StatusBadRequest, "没有第一次审批数据: "+err.Error())
		//		return
		//	}
		//
		//	if transitionRequest.Data != nil {
		//		if status, ok := transitionRequest.Data["status"].(string); ok {
		//			approval.Status = status
		//		} else {
		//			response.Fail(ctx, http.StatusBadRequest, "二次审批数据获取不到: "+err.Error())
		//			return
		//		}
		//
		//		prevName := approval.CustomerName
		//		approval.CustomerName = strings.Join([]string{prevName, operatorName}, ",")
		//
		//	}
		//
		//	if err := c.service.UpdateApproval(ctx, approval); err != nil {
		//		response.Fail(ctx, http.StatusInternalServerError, "更新审批记录失败: "+err.Error())
		//		return
		//	}

		//case "verification":
		//	// 创建验证记录
		//	verification := &model.Verification{
		//		TicketID:         uint(id),
		//		Success:          transitionRequest.Data["success"].(bool),
		//		VerificationTime: time.Now(),
		//		Comments:         transitionRequest.Comments,
		//		OperatorID:       operatorID,
		//		OperatorName:     operatorName,
		//	}
		//	if err := c.service.CreateVerification(ctx, verification); err != nil {
		//		response.Fail(ctx, http.StatusInternalServerError, "创建验证记录失败: "+err.Error())
		//		return
		//	}
	}

	// 先触发工作流，确保工作流可以正常工作
	err = c.service.TriggerWorkflowStage(
		ctx,
		uint(id),
		transitionRequest.Stage,
		operatorID,
		operatorName,
		transitionRequest.Comments,
		transitionRequest.Data,
	)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "触发工作流失败: "+err.Error())
		return
	}

	// 工作流触发成功后，再更新状态
	if err := c.service.UpdateOutboundTicketStatus(ctx, uint(id), transitionRequest.Status, operatorID, operatorName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新状态失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "状态转换成功")
}

func (c *OutboundTicketController) TransitionOutboundTicketV2(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}
	var transitionRequest struct {
		Status   string                 `json:"status" binding:"required"` // 状态
		Comments string                 `json:"comments"`                  // 备注
		Data     map[string]interface{} `json:"data"`                      // 阶段相关数据
	}

	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取当前工单
	ticket, err := c.service.GetOutboundTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取出库单失败: "+err.Error())
		return
	}

	// 验证状态转换是否合法
	if !cmdbWorkflow.IsValidStatusTransition(ticket.Status, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", ticket.Status, transitionRequest.Status))
		return
	}

	triggers := cmdbCommon.OutboundTrigger{
		Status:       transitionRequest.Status,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     transitionRequest.Comments,
		Data:         transitionRequest.Data,
	}
	// 触发工作流，确保工作流可以正常工作
	err = c.service.TriggerWorkflowStageV2(ctx, ticket, triggers)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "转换成功")
}

// StartWorkflow 启动出库单工作流
func (c *OutboundTicketController) StartWorkflow(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户失败: "+err.Error())
		return
	}

	// 启动工作流
	err = c.service.StartWorkflow(ctx, uint(id), userID, userName)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "启动工作流失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "工作流启动成功")
}

// 获取出库单 Info 信息
func (c *OutboundTicketController) GetTicketInfo(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	infoList, err := c.service.GetTicketInfoByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取出库单Info失败: "+err.Error())
		return
	}

	response.Success(ctx, infoList, "获取出库单Info成功")
}

// 获取出库单 Detail 信息
func (c *OutboundTicketController) GetTicketDetails(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}
	detailList, err := c.service.GetTicketDetailsByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取出库单Detail失败: "+err.Error())
		return
	}
	response.Success(ctx, gin.H{
		"list":  detailList,
		"total": len(detailList),
	}, "获取出库单Detail成功")
}

// GetTicketDetailsList 分页获取出库单详情列表
// @Summary 分页获取出库单详情列表
// @Description 根据出库单ID分页获取出库单详情列表
// @Tags 资产管理-出库
// @Accept json
// @Produce json
// @Param id path int true "出库单ID"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct{data=gin.H{list=[]outbound.OutboundDetail,total=int64}} "出库单详情列表"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 404 {object} response.ResponseStruct "出库单不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /cmdb/outbound-tickets/{id}/details-list [get]
func (c *OutboundTicketController) GetTicketDetailsList(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 获取分页参数
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码")
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	if err != nil || pageSize < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量")
		return
	}

	// 获取出库单详情列表（分页）
	details, total, err := c.service.GetTicketDetailsListByID(ctx, uint(id), page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取出库单详情列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  details,
		"total": total,
	}, "获取出库单详情列表成功")
}
