package cmdb

import (
	"backend/internal/middleware"
	assetController "backend/internal/modules/cmdb/controller/asset"
	componentController "backend/internal/modules/cmdb/controller/component"
	inventoryController "backend/internal/modules/cmdb/controller/inventory"
	productHandler "backend/internal/modules/cmdb/controller/product"
	assetRepo "backend/internal/modules/cmdb/repository/asset"
	componentRepo "backend/internal/modules/cmdb/repository/component"
	inventoryRepo "backend/internal/modules/cmdb/repository/inventory"
	productRepo "backend/internal/modules/cmdb/repository/product"
	templateRepo "backend/internal/modules/cmdb/repository/template"
	assetService "backend/internal/modules/cmdb/service/asset"
	componentService "backend/internal/modules/cmdb/service/component"
	inventoryService "backend/internal/modules/cmdb/service/inventory"
	productService "backend/internal/modules/cmdb/service/product"
	fileService "backend/internal/modules/file/service"
	importhandler "backend/internal/modules/import/handle"
	purchaseReposity "backend/internal/modules/purchase_old/repository"
	purchaseService "backend/internal/modules/purchase_old/service"
	serviceReposity "backend/internal/modules/server/repository"
	serviceService "backend/internal/modules/server/service"

	// 入库模块
	inboundController "backend/internal/modules/cmdb/controller/inbound"
	inboundReposity "backend/internal/modules/cmdb/repository/inbound"
	inboundService "backend/internal/modules/cmdb/service/inbound"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RegisterProductModule 注册产品模块
func RegisterProductModule(router *gin.RouterGroup, db *gorm.DB) {
	// 初始化仓库
	productRepository := productRepo.NewProductRepository(db)

	// 初始化服务
	productSvc := productService.NewProductService(productRepository)

	// 初始化处理器
	productHdl := productHandler.NewProductHandler(productSvc)

	// 注册路由
	// 创建需要认证的路由组
	apiGroup := router.Group("/cmdb")
	apiGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件
	productHdl.Register(apiGroup)
}

// RegisterComponentModule 注册组件模块
func RegisterComponentModule(router *gin.RouterGroup, db *gorm.DB) {
	// 初始化仓库
	serverComponentRepo := componentRepo.NewServerComponentRepository(db)
	inventoryRepository := inventoryRepo.NewInventoryRepository(db)
	spareRepository := assetRepo.NewSpareRepository(db)
	componentChangeLogRepo := componentRepo.NewComponentChangeLogRepository(db)
	deviceRepo := assetRepo.NewDeviceRepository(db)

	// 初始化库存服务，用于组件服务依赖
	inventorySvc := inventoryService.NewInventoryService(inventoryRepository, db)
	deviceSvc := assetService.NewDeviceService(deviceRepo)
	spareSvc := assetService.NewSpareService(spareRepository, inventorySvc, deviceSvc)

	// 初始化服务，注入库存服务和备件服务依赖
	serverComponentSvc := componentService.NewServerComponentService(serverComponentRepo, inventorySvc, spareSvc)
	componentChangeSvc := componentService.NewComponentChangeService(serverComponentRepo, componentChangeLogRepo, spareRepository)

	// 初始化控制器
	serverComponentCtrl := componentController.NewServerComponentController(serverComponentSvc)
	componentChangeCtrl := componentController.NewComponentChangeController(componentChangeSvc)

	// 注册路由
	// 创建需要认证的路由组
	apiGroup := router.Group("/cmdb")
	apiGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件
	serverComponentCtrl.RegisterRoutes(apiGroup)
	componentChangeCtrl.RegisterRoutes(apiGroup)
}

// RegisterInventoryModule 注册库存管理模块
func RegisterInventoryModule(router *gin.RouterGroup, db *gorm.DB) {
	// 初始化仓库
	inventoryRepository := inventoryRepo.NewInventoryRepository(db)

	// 初始化服务
	inventorySvc := inventoryService.NewInventoryService(inventoryRepository, db)

	// 初始化控制器
	inventoryCtrl := inventoryController.NewInventoryController(inventorySvc)

	// 注册路由
	// 创建需要认证的路由组
	apiGroup := router.Group("/cmdb")
	apiGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件
	inventoryCtrl.Register(apiGroup)
}

// RegisterSpareModule 注册备件模块
func RegisterSpareModule(router *gin.RouterGroup, db *gorm.DB) {
	// 初始化仓库
	spareRepository := assetRepo.NewSpareRepository(db)
	inventoryRepository := inventoryRepo.NewInventoryRepository(db)
	deviceRepo := assetRepo.NewDeviceRepository(db)

	// 初始化服务
	inventorySvc := inventoryService.NewInventoryService(inventoryRepository, db)
	deviceSvc := assetService.NewDeviceService(deviceRepo)
	spareSvc := assetService.NewSpareService(spareRepository, inventorySvc,deviceSvc)


	// 初始化控制器
	spareCtrl := assetController.NewSpareController(spareSvc)

	// 注册路由
	// 创建需要认证的路由组
	apiGroup := router.Group("/cmdb")
	apiGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件
	spareCtrl.RegisterRoutes(apiGroup)
}

// RegisterNetworkDeviceModule 注册网络设备模块
func RegisterNetworkDeviceModule(router *gin.RouterGroup, db *gorm.DB) {
	// 初始化仓库
	networkDeviceRepo := assetRepo.NewNetworkDeviceRepository(db)
	deviceRepo := assetRepo.NewDeviceRepository(db)
	machineTemplateRepo := templateRepo.NewMachineTemplateRepository(db)
	resourceRepo := assetRepo.NewResourceRepository(db)

	// 初始化服务
	networkDeviceSvc := assetService.NewNetworkDeviceService(networkDeviceRepo, deviceRepo, machineTemplateRepo, resourceRepo)

	// 初始化控制器
	networkDeviceCtrl := assetController.NewNetworkDeviceController(networkDeviceSvc)

	// 注册路由
	// 创建需要认证的路由组
	apiGroup := router.Group("/cmdb")
	apiGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件
	networkDeviceCtrl.RegisterRoutes(apiGroup)
}

// RegisterWarehouseModule 注册仓库模块
func RegisterWarehouseModule(router *gin.RouterGroup, db *gorm.DB) {
	// 初始化仓库层
	warehouseRepository := assetRepo.NewWarehouseRepository(db)

	// 初始化服务层
	warehouseSvc := assetService.NewWarehouseService(warehouseRepository)

	// 初始化控制器
	warehouseCtrl := assetController.NewWarehouseController(warehouseSvc)

	// 注册路由
	// 创建需要认证的路由组
	apiGroup := router.Group("/cmdb")
	apiGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件
	warehouseCtrl.RegisterRoutes(apiGroup)
}

// RegisterInboundModule 注册入库相关模块
func RegisterInboundModule(router *gin.RouterGroup, db *gorm.DB) {
	// 初始化仓储层
	inboundRepo := inboundReposity.NewInboundRepository(db)
	warehouseRepo := assetRepo.NewWarehouseRepository(db)
	productRepository := productRepo.NewProductRepository(db)
	purchaseRepo := purchaseReposity.NewPurchaseRepository(db)
	inventoryRepository := inventoryRepo.NewInventoryRepository(db)
	serviceRepo := serviceReposity.NewServerRepository(db)

	// 初始化服务层
	purchaseSvc := purchaseService.NewPurchaseService(purchaseRepo)
	serviceSvc := serviceService.NewServerService(serviceRepo)
	inventorySvc := inventoryService.NewInventoryService(inventoryRepository, db)
	inboundSvc := inboundService.InitInboundService(inboundRepo, purchaseSvc, inventorySvc, inventoryRepository, serviceSvc)
	spareHandler := importhandler.NewSpareHandler(db)
	productHdl := importhandler.NewProductHandler(db)
	fileSvc := fileService.NewFileService(db)
	inboundImportSvc := inboundService.NewInboundImportService(inboundRepo, spareHandler, productHdl, productRepository, warehouseRepo)
	// 初始化控制器
	inboundCtrl := inboundController.NewInboundController(inboundSvc, inboundImportSvc, fileSvc)
	// 注册路由
	// 创造需要认证的路由组
	apiGroup := router.Group("/cmdb")
	apiGroup.Use(middleware.AuthMiddleware()) //添加认真中间件
	inboundCtrl.RegisterRoutes(apiGroup)

}

//// AutoMigrate 自动迁移数据库
//func AutoMigrate(db *gorm.DB, models ...interface{}) error {
//	// 现在重新创建表
//	return db.AutoMigrate(models...)
//}
