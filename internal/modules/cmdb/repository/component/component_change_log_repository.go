package component

import (
	"context"

	"backend/internal/modules/cmdb/model/component"

	"gorm.io/gorm"
)

// ComponentChangeLogRepository 组件变更日志仓库接口
type ComponentChangeLogRepository interface {
	// Create 创建变更日志
	Create(ctx context.Context, log *component.ComponentChangeLog) (*component.ComponentChangeLog, error)
	// ListByServerID 查询服务器的变更日志
	ListByServerID(ctx context.Context, serverID uint, page, pageSize int) ([]*component.ComponentChangeLog, int64, error)
	// ListByComponentID 查询组件的变更日志
	ListByComponentID(ctx context.Context, componentID uint) ([]*component.ComponentChangeLog, error)
	// GetByID 根据ID获取变更日志
	GetByID(ctx context.Context, id uint) (*component.ComponentChangeLog, error)

	// 批量插入
	BatchInsert(ctx context.Context, logs []*component.ComponentChangeLog) error

	// GetDB 获取数据库实例，用于事务
	GetDB() *gorm.DB
	// WithTx 使用事务
	WithTx(tx *gorm.DB) ComponentChangeLogRepository
}

// componentChangeLogRepository 组件变更日志仓库实现
type componentChangeLogRepository struct {
	db *gorm.DB
}

// NewComponentChangeLogRepository 创建组件变更日志仓库
func NewComponentChangeLogRepository(db *gorm.DB) ComponentChangeLogRepository {
	return &componentChangeLogRepository{db: db}
}

// GetDB 获取数据库实例
func (r *componentChangeLogRepository) GetDB() *gorm.DB {
	return r.db
}

// WithTx 使用事务
func (r *componentChangeLogRepository) WithTx(tx *gorm.DB) ComponentChangeLogRepository {
	if tx == nil {
		return r
	}
	return &componentChangeLogRepository{db: tx}
}

// Create 创建变更日志
func (r *componentChangeLogRepository) Create(ctx context.Context, log *component.ComponentChangeLog) (*component.ComponentChangeLog, error) {
	if err := r.db.WithContext(ctx).Create(log).Error; err != nil {
		return nil, err
	}
	return log, nil
}

// ListByServerID 查询服务器的变更日志
func (r *componentChangeLogRepository) ListByServerID(ctx context.Context, serverID uint, page, pageSize int) ([]*component.ComponentChangeLog, int64, error) {
	var logs []*component.ComponentChangeLog
	var total int64

	query := r.db.WithContext(ctx).Model(&component.ComponentChangeLog{}).Where("server_id = ?", serverID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Preload("Spare").
		Order("change_time DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// ListByComponentID 查询组件的变更日志
func (r *componentChangeLogRepository) ListByComponentID(ctx context.Context, componentID uint) ([]*component.ComponentChangeLog, error) {
	var logs []*component.ComponentChangeLog

	if err := r.db.WithContext(ctx).
		Preload("Spare").
		Where("component_id = ? OR previous_component_id = ?", componentID, componentID).
		Order("change_time DESC").
		Find(&logs).Error; err != nil {
		return nil, err
	}

	return logs, nil
}

// GetByID 根据ID获取变更日志
func (r *componentChangeLogRepository) GetByID(ctx context.Context, id uint) (*component.ComponentChangeLog, error) {
	var log component.ComponentChangeLog
	if err := r.db.WithContext(ctx).Preload("Spare").First(&log, id).Error; err != nil {
		return nil, err
	}
	return &log, nil
}

func (r *componentChangeLogRepository) BatchInsert(ctx context.Context, logs []*component.ComponentChangeLog) error {
	if len(logs) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Create(&logs).Error
}
