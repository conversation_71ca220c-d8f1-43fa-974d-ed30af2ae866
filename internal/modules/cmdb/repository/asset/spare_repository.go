package asset

import (
	"backend/internal/common/utils"
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/product"
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// SpareRepository 备件仓库接口
type SpareRepository interface {
	// 基本CRUD操作
	Create(ctx context.Context, spare *asset.AssetSpare) error
	Update(ctx context.Context, spare *asset.AssetSpare) error
	UpdateSpares(ctx context.Context, spares []asset.AssetSpare) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*asset.AssetSpare, error)
	GetBySN(ctx context.Context, sn string) (*asset.AssetSpare, error)
	GetBySNs(ctx context.Context, SNs []string) ([]asset.AssetSpare, error)
	List(ctx context.Context, page, pageSize int, params map[string]interface{}) ([]*asset.AssetSpare, int64, error)

	// 特定业务接口
	ChangeStatus(ctx context.Context, id uint, status, reason string) error
	GetSpareWithDetails(ctx context.Context, id uint) (*asset.AssetSpareWithDetails, error)
	GetSpareStatistics(ctx context.Context, spareType string) (*asset.SpareStatistics, error)

	// 备件与设备关联/分离
	AssignToDevice(ctx context.Context, spareID, deviceID uint, position string) error
	RemoveFromDevice(ctx context.Context, spareID uint) error

	// 备件库存相关
	TransferWarehouse(ctx context.Context, spareID, warehouseID uint, newLocation string) error
	ListByWarehouse(ctx context.Context, warehouseID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error)
	ListByProduct(ctx context.Context, productID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error)

	// 事务支持
	GetDB() *gorm.DB
	WithTx(tx *gorm.DB) SpareRepository
}

// spareRepository 备件仓库实现
type spareRepository struct {
	db *gorm.DB
}

// NewSpareRepository 创建备件仓库
func NewSpareRepository(db *gorm.DB) SpareRepository {
	return &spareRepository{db: db}
}

// GetDB 获取数据库实例
func (r *spareRepository) GetDB() *gorm.DB {
	return r.db
}

// WithTx 使用事务
func (r *spareRepository) WithTx(tx *gorm.DB) SpareRepository {
	if tx == nil {
		return r
	}
	return &spareRepository{db: tx}
}

// Create 创建备件
func (r *spareRepository) Create(ctx context.Context, spare *asset.AssetSpare) error {
	return r.db.WithContext(ctx).Create(spare).Error
}

// Update 更新备件
func (r *spareRepository) Update(ctx context.Context, spare *asset.AssetSpare) error {
	var original asset.AssetSpare
	if err := r.db.WithContext(ctx).First(&original, spare.ID).Error; err != nil {
		return err
	}

	// 保留创建时间
	spare.CreatedAt = original.CreatedAt
	return r.db.WithContext(ctx).Save(spare).Error
}

// UpdateSpares 批量更新备件信息
func (r *spareRepository) UpdateSpares(ctx context.Context, spares []asset.AssetSpare) error {
	db := utils.GetDB(ctx, r.db)
	if db == r.db {
		db = r.db // 使用原始数据库连接
		return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			for _, spare := range spares {
				err := tx.Model(asset.AssetSpare{}).Where("id = ?", spare.ID).Updates(map[string]interface{}{
					"asset_status":     spare.AssetStatus,
					"source_type":      spare.SourceType,
					"related_asset_id": spare.RelatedAssetID,
					"hardware_status":  spare.HardwareStatus,
					"warehouse_id":     spare.WarehouseID,
					"related_asset_sn": spare.RelatedAssetSN,
					"location":         spare.Location,
				}).Error
				if err != nil {
					return fmt.Errorf("批量更新备件信息失败: %w", err)
				}
			}
			return nil
		})
	} else {
		for _, spare := range spares {
			err := db.Model(asset.AssetSpare{}).Where("id = ?", spare.ID).Updates(map[string]interface{}{
				"asset_status":     spare.AssetStatus,
				"source_type":      spare.SourceType,
				"related_asset_id": spare.RelatedAssetID,
				"hardware_status":  spare.HardwareStatus,
				"warehouse_id":     spare.WarehouseID,
				"related_asset_sn": spare.RelatedAssetSN,
				"location":         spare.Location,
			}).Error
			if err != nil {
				return fmt.Errorf("批量更新备件信息失败: %w", err)
			}
		}
		return nil
	}
}

// Delete 删除备件
func (r *spareRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&asset.AssetSpare{}, id).Error
}

// GetByID 根据ID获取备件
func (r *spareRepository) GetByID(ctx context.Context, id uint) (*asset.AssetSpare, error) {
	var spare asset.AssetSpare
	if err := r.db.WithContext(ctx).Preload("Product").Preload("Warehouse").First(&spare, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("备件不存在")
		}
		return nil, err
	}
	return &spare, nil
}

// GetBySN 根据SN获取备件
func (r *spareRepository) GetBySN(ctx context.Context, sn string) (*asset.AssetSpare, error) {
	var spare asset.AssetSpare
	if err := r.db.WithContext(ctx).Preload("Product").Preload("Warehouse").Where("sn = ?", sn).First(&spare).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("备件不存在")
		}
		return nil, err
	}
	return &spare, nil
}

// GetBySNs 通过批量SN获取备件数据
func (r *spareRepository) GetBySNs(ctx context.Context, SNs []string) ([]asset.AssetSpare, error) {
	var spares []asset.AssetSpare
	if err := r.db.WithContext(ctx).Model(&asset.AssetSpare{}).Where("sn IN ?", SNs).Preload("Product").Preload("Warehouse").Find(&spares).Error; err != nil {
		return nil, fmt.Errorf("通过SNs:%v获取备件信息失败：%v", SNs, err)
	}
	return spares, nil
}

// List 分页查询备件列表
func (r *spareRepository) List(ctx context.Context, page, pageSize int, params map[string]interface{}) ([]*asset.AssetSpare, int64, error) {
	var spares []*asset.AssetSpare
	var total int64

	db := r.db.WithContext(ctx).Model(&asset.AssetSpare{}).Preload("Product").Preload("Warehouse")

	// 标记是否已经JOIN了products表
	hasJoinedProducts := false

	// 应用查询参数
	for key, value := range params {
		switch key {
		case "sn":
			db = db.Where("asset_spares.sn = ?", value)
		case "pn":
			// 处理PN号码查询 - 可以通过产品表的PN字段关联查询
			// 方法1: 使用子查询
			db = db.Joins("JOIN products ON asset_spares.product_id = products.id").
				Where("products.pn = ?", value)
			hasJoinedProducts = true
		case "product_id":
			db = db.Where("asset_spares.product_id = ?", value)
		case "asset_status":
			db = db.Where("asset_spares.asset_status = ?", value)
		case "hardware_status":
			db = db.Where("asset_spares.hardware_status = ?", value)
		case "source_type":
			db = db.Where("asset_spares.source_type = ?", value)
		case "warehouse_id":
			db = db.Where("asset_spares.warehouse_id = ?", value)
		case "type":
			// 修改为查询产品表的material_type字段
			if !hasJoinedProducts {
				db = db.Joins("JOIN products ON asset_spares.product_id = products.id")
				hasJoinedProducts = true
			}
			db = db.Where("products.material_type = ?", value)
		case "related_asset_sn":
			db = db.Where("asset_spares.related_asset_sn = ?", value)
		case "firmware_version":
			db = db.Where("asset_spares.firmware_version = ?", value)
		case "batch_number":
			db = db.Where("asset_spares.batch_number = ?", value)
		case "location":
			db = db.Where("asset_spares.location = ?", value)
		case "min_price":
			db = db.Where("asset_spares.price >= ?", value)
		case "max_price":
			db = db.Where("asset_spares.price <= ?", value)
		case "purchase_date_start":
			db = db.Where("asset_spares.purchase_date >= ?", value)
		case "purchase_date_end":
			db = db.Where("asset_spares.purchase_date <= ?", value)
		case "warranty_expire_start":
			db = db.Where("asset_spares.warranty_expire >= ?", value)
		case "warranty_expire_end":
			db = db.Where("asset_spares.warranty_expire <= ?", value)
		case "brand":
			// 处理品牌查询，需要关联products表
			if !hasJoinedProducts {
				db = db.Joins("JOIN products ON asset_spares.product_id = products.id")
				hasJoinedProducts = true
			}
			db = db.Where("products.brand = ?", value)
		case "spec":
			// 处理规格查询，需要关联products表
			if !hasJoinedProducts {
				db = db.Joins("JOIN products ON asset_spares.product_id = products.id")
				hasJoinedProducts = true
			}
			db = db.Where("products.spec = ?", value)
		default:
			fmt.Printf("未知参数: %s = %v\n", key, value)
		}
	}

	// 统计总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 获取结果
	if err := db.Find(&spares).Error; err != nil {
		return nil, 0, err
	}

	return spares, total, nil
}

// ChangeStatus 更改备件状态
func (r *spareRepository) ChangeStatus(ctx context.Context, id uint, status, reason string) error {
	// 验证状态是否有效
	validStatuses := map[string]bool{
		"idle":      true,
		"in_use":    true,
		"repairing": true,
		"scrapped":  true,
	}

	if !validStatuses[status] {
		return errors.New("无效的状态值")
	}

	// 查找备件
	var spare asset.AssetSpare
	if err := r.db.WithContext(ctx).First(&spare, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("备件不存在")
		}
		return err
	}

	// 开始事务
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 更新状态
	if err := tx.Model(&asset.AssetSpare{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"asset_status": status,
			"updated_at":   time.Now(),
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// TODO: 如果需要记录状态变更历史，可以在这里添加相关代码
	// 例如：创建StatusChangeHistory记录，记录从spare.AssetStatus到status的变更

	// 提交事务
	return tx.Commit().Error
}

// GetSpareWithDetails 获取备件详情，包含关联信息
func (r *spareRepository) GetSpareWithDetails(ctx context.Context, id uint) (*asset.AssetSpareWithDetails, error) {
	// 获取基本信息
	spare, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 创建结果
	result := &asset.AssetSpareWithDetails{
		AssetSpare: *spare,
	}

	// 产品信息
	if spare.ProductID > 0 && spare.Product.ID > 0 {
		// 如果已经预加载了Product，就使用它
		result.ProductInfo = asset.ProductInfo{
			Name:            spare.Product.Model, // 使用型号作为名称
			Brand:           spare.Product.Brand,
			Model:           spare.Product.Model,
			Spec:            spare.Product.Spec,
			ProductCategory: spare.Product.ProductCategory,
		}
	} else if spare.ProductID > 0 {
		// 如果没有预加载，单独查询
		var prod product.Product
		if err := r.db.WithContext(ctx).First(&prod, spare.ProductID).Error; err == nil {
			result.ProductInfo = asset.ProductInfo{
				Name:            prod.Model,
				Brand:           prod.Brand,
				Model:           prod.Model,
				Spec:            prod.Spec,
				ProductCategory: prod.ProductCategory,
			}
		}
	}

	// 仓库信息
	if spare.WarehouseID > 0 && spare.Warehouse.ID > 0 {
		// 如果已经预加载了Warehouse，就使用它
		result.WarehouseInfo = asset.WarehouseInfo{
			Name:     spare.Warehouse.Name,
			Code:     spare.Warehouse.Code,
			Type:     spare.Warehouse.Type,
			Location: spare.Location, // 使用备件自身的位置信息
		}
	} else if spare.WarehouseID > 0 {
		// 如果没有预加载，单独查询
		var warehouse asset.Warehouse
		if err := r.db.WithContext(ctx).First(&warehouse, spare.WarehouseID).Error; err == nil {
			result.WarehouseInfo = asset.WarehouseInfo{
				Name:     warehouse.Name,
				Code:     warehouse.Code,
				Type:     warehouse.Type,
				Location: spare.Location,
			}
		}
	}

	// TODO: 如果需要获取库存明细信息，可以在这里添加查询

	return result, nil
}

// GetSpareStatistics 获取备件统计信息
func (r *spareRepository) GetSpareStatistics(ctx context.Context, spareType string) (*asset.SpareStatistics, error) {
	result := &asset.SpareStatistics{
		ByType:      make(map[string]int),
		ByStatus:    make(map[string]int),
		ByWarehouse: make(map[string]int),
	}

	// 构建查询基础
	query := r.db.WithContext(ctx).Model(&asset.AssetSpare{})
	if spareType != "" {
		query = query.Where("type = ?", spareType)
	}

	// 获取总数
	var totalCount int64
	query.Count(&totalCount)
	result.TotalSpares = int(totalCount)

	// 按类型统计
	var typeStats []struct {
		Type  string
		Count int
	}
	r.db.WithContext(ctx).Model(&asset.AssetSpare{}).
		Select("type, count(*) as count").
		Group("type").
		Find(&typeStats)

	for _, stat := range typeStats {
		result.ByType[stat.Type] = stat.Count
	}

	// 按状态统计
	var statusStats []struct {
		AssetStatus string
		Count       int
	}
	r.db.WithContext(ctx).Model(&asset.AssetSpare{}).
		Select("asset_status, count(*) as count").
		Group("asset_status").
		Find(&statusStats)

	for _, stat := range statusStats {
		result.ByStatus[stat.AssetStatus] = stat.Count
	}

	// 按仓库统计
	var warehouseStats []struct {
		WarehouseID uint
		Name        string
		Count       int
	}
	r.db.WithContext(ctx).Model(&asset.AssetSpare{}).
		Select("asset_spares.warehouse_id, warehouses.name, count(*) as count").
		Joins("left join warehouses on asset_spares.warehouse_id = warehouses.id").
		Where("asset_spares.warehouse_id > 0").
		Group("asset_spares.warehouse_id, warehouses.name").
		Find(&warehouseStats)

	for _, stat := range warehouseStats {
		result.ByWarehouse[stat.Name] = stat.Count
	}

	return result, nil
}

// AssignToDevice 分配备件到设备
func (r *spareRepository) AssignToDevice(ctx context.Context, spareID, deviceID uint, position string) error {
	// 验证备件
	var spare asset.AssetSpare
	if err := r.db.WithContext(ctx).First(&spare, spareID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("备件不存在")
		}
		return err
	}

	// 验证设备
	var device asset.Device
	if err := r.db.WithContext(ctx).First(&device, deviceID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("设备不存在")
		}
		return err
	}

	// 验证备件状态
	if spare.AssetStatus != "idle" {
		return fmt.Errorf("备件状态不是闲置状态，当前状态：%s", spare.AssetStatus)
	}

	// 开始事务
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 更新备件信息
	if err := tx.Model(&asset.AssetSpare{}).Where("id = ?", spareID).
		Updates(map[string]interface{}{
			"related_asset_id": deviceID,
			"related_asset_sn": device.SN,
			"asset_status":     "in_use",
			"location":         position,
			"updated_at":       time.Now(),
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// TODO: 添加设备-备件关联记录或日志
	// 例如：记录备件安装历史

	// 提交事务
	return tx.Commit().Error
}

// RemoveFromDevice 从设备移除备件
func (r *spareRepository) RemoveFromDevice(ctx context.Context, spareID uint) error {
	// 验证备件
	var spare asset.AssetSpare
	if err := r.db.WithContext(ctx).First(&spare, spareID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("备件不存在")
		}
		return err
	}

	// 验证备件当前是否安装在设备上
	if spare.RelatedAssetID == 0 || spare.AssetStatus != "in_use" {
		return errors.New("备件当前未安装在任何设备上")
	}

	// 开始事务
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 更新备件信息
	if err := tx.Model(&asset.AssetSpare{}).Where("id = ?", spareID).
		Updates(map[string]interface{}{
			"related_asset_id": 0,
			"related_asset_sn": "",
			"asset_status":     "idle",
			"location":         "",
			"updated_at":       time.Now(),
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// TODO: 添加备件移除记录或日志
	// 例如：记录备件拆卸历史

	// 提交事务
	return tx.Commit().Error
}

// TransferWarehouse 移动备件到其他仓库
func (r *spareRepository) TransferWarehouse(ctx context.Context, spareID, warehouseID uint, newLocation string) error {
	// 验证备件
	var spare asset.AssetSpare
	if err := r.db.WithContext(ctx).First(&spare, spareID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("备件不存在")
		}
		return err
	}

	// 验证仓库
	var warehouse asset.Warehouse
	if err := r.db.WithContext(ctx).First(&warehouse, warehouseID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("仓库不存在")
		}
		return err
	}

	// 验证备件状态 - 只有闲置状态的备件可以移动仓库
	if spare.AssetStatus != "idle" {
		return fmt.Errorf("只有闲置状态的备件可以移动仓库，当前状态：%s", spare.AssetStatus)
	}

	// 如果目标仓库和当前仓库相同，只更新位置
	if spare.WarehouseID == warehouseID {
		return r.db.WithContext(ctx).Model(&asset.AssetSpare{}).Where("id = ?", spareID).
			Updates(map[string]interface{}{
				"location":   newLocation,
				"updated_at": time.Now(),
			}).Error
	}

	// 开始事务
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 更新备件仓库和位置
	if err := tx.Model(&asset.AssetSpare{}).Where("id = ?", spareID).
		Updates(map[string]interface{}{
			"warehouse_id": warehouseID,
			"location":     newLocation,
			"updated_at":   time.Now(),
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// TODO: 添加仓库移动记录
	// 例如：记录备件转移历史

	// 提交事务
	return tx.Commit().Error
}

// ListByWarehouse 根据仓库ID获取备件列表
func (r *spareRepository) ListByWarehouse(ctx context.Context, warehouseID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error) {
	var spares []*asset.AssetSpare
	var total int64

	// 验证仓库
	var warehouse asset.Warehouse
	if err := r.db.WithContext(ctx).First(&warehouse, warehouseID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, 0, errors.New("仓库不存在")
		}
		return nil, 0, err
	}

	// 基础查询
	db := r.db.WithContext(ctx).Model(&asset.AssetSpare{}).
		Preload("Product").
		Where("warehouse_id = ?", warehouseID)

	// 统计总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 获取结果
	if err := db.Find(&spares).Error; err != nil {
		return nil, 0, err
	}

	return spares, total, nil
}

// ListByProduct 根据产品ID获取备件列表
func (r *spareRepository) ListByProduct(ctx context.Context, productID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error) {
	var spares []*asset.AssetSpare
	var total int64

	// 验证产品
	var product product.Product
	if err := r.db.WithContext(ctx).First(&product, productID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, 0, errors.New("产品不存在")
		}
		return nil, 0, err
	}

	// 基础查询
	db := r.db.WithContext(ctx).Model(&asset.AssetSpare{}).
		Preload("Warehouse").
		Where("product_id = ?", productID)

	// 统计总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 获取结果
	if err := db.Find(&spares).Error; err != nil {
		return nil, 0, err
	}

	return spares, total, nil
}
