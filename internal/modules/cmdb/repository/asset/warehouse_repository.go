package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/inventory"
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// WarehouseRepository 仓库管理接口
type WarehouseRepository interface {
	// 基本CRUD操作
	Create(ctx context.Context, warehouse *asset.Warehouse) error
	Update(ctx context.Context, warehouse *asset.Warehouse) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*asset.Warehouse, error)
	GetByCode(ctx context.Context, code string) (*asset.Warehouse, error)
	List(ctx context.Context, page, pageSize int, query, warehouseType string) ([]*asset.Warehouse, int64, error)
	GetByCodeOrName(ctx context.Context, info string) (*asset.Warehouse, error)
	// 仓库统计接口
	GetWarehouseWithStats(ctx context.Context, id uint) (*asset.WarehouseWithStats, error)

	// 仓库内容查询
	GetWarehouseSpares(ctx context.Context, warehouseID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error)
	GetWarehouseInventory(ctx context.Context, warehouseID uint, page, pageSize int) ([]*inventory.InventoryDetail, int64, error)
}

// warehouseRepository 仓库管理实现
type warehouseRepository struct {
	db *gorm.DB
}

// NewWarehouseRepository 创建仓库管理仓库
func NewWarehouseRepository(db *gorm.DB) WarehouseRepository {
	return &warehouseRepository{db: db}
}

// Create 创建仓库
func (r *warehouseRepository) Create(ctx context.Context, warehouse *asset.Warehouse) error {
	// 检查编码是否重复
	var count int64
	if err := r.db.WithContext(ctx).Model(&asset.Warehouse{}).Where("code = ?", warehouse.Code).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.New("仓库编码已存在")
	}

	return r.db.WithContext(ctx).Create(warehouse).Error
}

// Update 更新仓库
func (r *warehouseRepository) Update(ctx context.Context, warehouse *asset.Warehouse) error {
	var original asset.Warehouse
	if err := r.db.WithContext(ctx).First(&original, warehouse.ID).Error; err != nil {
		return err
	}
	fmt.Println(original)

	// 检查编码是否被其他仓库使用
	if warehouse.Code != original.Code {
		var count int64
		if err := r.db.WithContext(ctx).Model(&asset.Warehouse{}).Where("code = ? AND id != ?", warehouse.Code, warehouse.ID).Count(&count).Error; err != nil {
			return err
		}
		if count > 0 {
			return errors.New("仓库编码已被其他仓库使用")
		}
	}

	// 保留创建时间
	warehouse.CreatedAt = original.CreatedAt
	return r.db.WithContext(ctx).Save(warehouse).Error
}

// Delete 删除仓库
func (r *warehouseRepository) Delete(ctx context.Context, id uint) error {
	// 检查仓库是否存在库存物品
	var spareCount int64
	if err := r.db.WithContext(ctx).Model(&asset.AssetSpare{}).Where("warehouse_id = ?", id).Count(&spareCount).Error; err != nil {
		return err
	}
	if spareCount > 0 {
		return errors.New("仓库内仍有备件，无法删除")
	}

	var inventoryCount int64
	if err := r.db.WithContext(ctx).Model(&inventory.InventoryDetail{}).Where("warehouse_id = ?", id).Count(&inventoryCount).Error; err != nil {
		return err
	}
	if inventoryCount > 0 {
		return errors.New("仓库内仍有库存，无法删除")
	}

	return r.db.WithContext(ctx).Delete(&asset.Warehouse{}, id).Error
}

// GetByID 根据ID获取仓库
func (r *warehouseRepository) GetByID(ctx context.Context, id uint) (*asset.Warehouse, error) {
	var warehouse asset.Warehouse
	if err := r.db.WithContext(ctx).Preload("Room.DataCenter").First(&warehouse, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("仓库不存在")
		}
		return nil, err
	}
	return &warehouse, nil
}

// GetByCode 根据编码获取仓库
func (r *warehouseRepository) GetByCode(ctx context.Context, code string) (*asset.Warehouse, error) {
	var warehouse asset.Warehouse
	if err := r.db.WithContext(ctx).Preload("Room").Where("code = ?", code).First(&warehouse).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("仓库不存在")
		}
		return nil, err
	}
	return &warehouse, nil
}

// GetByCodeOrName 根据编码或名称获取仓库
func (r *warehouseRepository) GetByCodeOrName(ctx context.Context, info string) (*asset.Warehouse, error) {
	var warehouse asset.Warehouse
	if err := r.db.WithContext(ctx).Preload("Room").Where("code = ?", info).First(&warehouse).Error; err != nil {
		if err = r.db.WithContext(ctx).Preload("Room").Where("name = ?", info).First(&warehouse).Error; err != nil {
			return nil, err
		}
	}
	return &warehouse, nil
}

// List 分页查询仓库列表
func (r *warehouseRepository) List(ctx context.Context, page, pageSize int, query, warehouseType string) ([]*asset.Warehouse, int64, error) {
	var warehouses []*asset.Warehouse
	var total int64

	db := r.db.WithContext(ctx).Model(&asset.Warehouse{}).Preload("Room.DataCenter")

	// 查询条件
	if query != "" {
		db = db.Where("name LIKE ? OR code LIKE ? OR description LIKE ?", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if warehouseType != "" {
		db = db.Where("type = ?", warehouseType)
	}

	// 统计总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 获取结果
	if err := db.Find(&warehouses).Error; err != nil {
		return nil, 0, err
	}

	return warehouses, total, nil
}

// GetWarehouseWithStats 获取仓库及统计信息
func (r *warehouseRepository) GetWarehouseWithStats(ctx context.Context, id uint) (*asset.WarehouseWithStats, error) {
	// 查询仓库基本信息
	warehouse, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	result := &asset.WarehouseWithStats{
		Warehouse: *warehouse,
	}

	// 统计不同产品数量
	var productCount int64
	subQuery := r.db.WithContext(ctx).Model(&asset.AssetSpare{}).
		Where("warehouse_id = ?", id).
		Select("DISTINCT product_id")

	if err := r.db.WithContext(ctx).Table("(?) as t", subQuery).Count(&productCount).Error; err != nil {
		return nil, err
	}
	result.ProductCount = int(productCount)

	// 统计总库存项目数
	var totalItems int64
	if err := r.db.WithContext(ctx).Model(&asset.AssetSpare{}).
		Where("warehouse_id = ?", id).
		Count(&totalItems).Error; err != nil {
		return nil, err
	}
	result.TotalItems = int(totalItems)

	// 统计可用（闲置）备件数
	var availableItems int64
	if err := r.db.WithContext(ctx).Model(&asset.AssetSpare{}).
		Where("warehouse_id = ? AND asset_status = 'idle'", id).
		Count(&availableItems).Error; err != nil {
		return nil, err
	}
	result.AvailableItems = int(availableItems)

	return result, nil
}

// GetWarehouseSpares 获取仓库内所有备件
func (r *warehouseRepository) GetWarehouseSpares(ctx context.Context, warehouseID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error) {
	var spares []*asset.AssetSpare
	var total int64

	// 验证仓库是否存在
	_, err := r.GetByID(ctx, warehouseID)
	if err != nil {
		return nil, 0, err
	}

	// 基础查询
	db := r.db.WithContext(ctx).Model(&asset.AssetSpare{}).
		Preload("Product").
		Where("warehouse_id = ?", warehouseID)

	// 统计总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 获取结果
	if err := db.Find(&spares).Error; err != nil {
		return nil, 0, err
	}

	return spares, total, nil
}

// GetWarehouseInventory 获取仓库内库存明细
func (r *warehouseRepository) GetWarehouseInventory(ctx context.Context, warehouseID uint, page, pageSize int) ([]*inventory.InventoryDetail, int64, error) {
	var inventories []*inventory.InventoryDetail
	var total int64

	// 验证仓库是否存在
	_, err := r.GetByID(ctx, warehouseID)
	if err != nil {
		return nil, 0, err
	}

	// 基础查询
	db := r.db.WithContext(ctx).Model(&inventory.InventoryDetail{}).
		Preload("Product").
		Where("warehouse_id = ?", warehouseID)

	// 统计总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 获取结果
	if err := db.Find(&inventories).Error; err != nil {
		return nil, 0, err
	}

	return inventories, total, nil
}
