package inbound

import (
	"backend/internal/common/constants"
	assetModel "backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/inbound"
	ticketModel "backend/internal/modules/ticket/model"
	"context"
	"fmt"
	"math"
	"time"

	"gorm.io/gorm"
)

type Repository interface {
	//List(ctx context.Context, userId uint, query inbound.GetListQuery) ([]inbound.PartInboundTicket, int64, error)
	// 创建入库单（旧件入库）
	CreatePartInbound(ctx context.Context, req *inbound.PartInbound) (uint, error)
	UpdatePartInbound(ctx context.Context, id uint, req *inbound.PartInbound) error
	DeletePartInbound(ctx context.Context, id uint) error
	GetPartInboundByID(ctx context.Context, id uint) (*inbound.PartInbound, error)
	GenerateTicketNo() string
	buildUpdatePartFields(req *inbound.PartInbound) map[string]interface{}

	/*
		新购入库相关方法
	*/
	CreateNewInbound(ctx context.Context, req *inbound.NewInbound) error
	GetNewInboundByID(ctx context.Context, id uint) (*inbound.NewInbound, error)
	GetNewInboundByNo(ctx context.Context, newInboundNo string) (*inbound.NewInbound, *ticketModel.NewInboundTicket, error)

	buildUpdateNewFields(req *inbound.NewInbound) map[string]interface{}
	GetNewInboundInfoByID(ctx context.Context, id uint) (*[]inbound.NewInboundDetail, error)
	GetNewInboundSubmitterList(ctx context.Context) ([]inbound.SubmitterInfo, error)
	InitNewInboundDetails(ctx context.Context, details []inbound.NewInboundDetail) error
	// 批量创建新购入库信息
	CreateNewInboundInfo(ctx context.Context, infos []*inbound.NewInboundDetail) error
	CreateNewInboundWithInfo(ctx context.Context, inboundList *inbound.InboundList, newInbound *inbound.NewInbound, infos []inbound.NewInboundInfo) error
	// ImportToCMDB 导入CMDB，事务操作
	//ImportToCMDB(ctx context.Context, spare []assetModel.AssetSpare, history []inventory.StockHistory) error
	ImportToCMDB(ctx context.Context, spare []assetModel.AssetSpare) error
	// Update 方法
	UpdateNewInbound(ctx context.Context, id uint, req *inbound.NewInbound) error
	UpdateNewInboundDetail(ctx context.Context, details []inbound.NewInboundDetail) error
	UpdateInboundListStage(ctx context.Context, inboundNo, Stage string) error
	UpdateDismantledData(ctx context.Context, DismantledInbounds inbound.DismantledInbound) error
	UpdateDismantledDataV1(ctx context.Context, DismantledInbounds inbound.DismantledInbound) error

	/* 整机入库相关方法 */
	UpdateDeviceInboundDetailsByInput(ctx context.Context, details []inbound.DeviceInboundDetail) error

	// GetRepairedInboundByNo 根据工单号获取返修入库信息
	GetRepairedInboundByNo(ctx context.Context, inboundNo string) (*inbound.RepairInbound, *ticketModel.RepairPartInboundTicket, error)
	// GetDismantledInboundByNo 根据工单号获取拆机入库信息
	GetDismantledInboundByNo(ctx context.Context, inboundNo string) (*inbound.DismantledInbound, *ticketModel.DismantledPartInboundTicket, error)
	GetDeviceInboundByNo(ctx context.Context, inboundNo string) (*inbound.DeviceInbound, *ticketModel.DeviceInboundTicket, error)

	// List 获取入库单列表
	List(ctx context.Context, dto *inbound.ListDTO) (int64, []inbound.InboundList, error)

	// CreateDismantledInbound 创建拆机入库单
	CreateDismantledInbound(ctx context.Context, inboundList *inbound.InboundList, dismantledInbounds *inbound.DismantledInbound) error

	// CreateRepairInbound 创建维修入库单
	CreateRepairInbound(ctx context.Context, inboundList *inbound.InboundList, repairInbounds *inbound.RepairInbound) error

	// 进行数据校验
	ValidRepairPartInboundSN(ctx context.Context, repairsSNs, replaceSNs []string) error
	ValidNewPartInboundSN(ctx context.Context, SNs []string) error
	ValidDeviceInboundSN(ctx context.Context, SNs []string) error
	////工单相关方法
	//CreateTicket(ctx context.Context, req inbound.InboundTicket) error
	//UpdateTicket(ctx context.Context, id uint, req inbound.InboundTicket) error
	//DeleteTicket(ctx context.Context, id uint) error
	//GetTicketByID(ctx context.Context, id uint) (*inbound.InboundTicket, error)
	//// 历史记录相关方法
	//CreateHistory(ctx context.Context, req *inbound.InboundTicketHistory) error
}

type inboundRepository struct {
	db *gorm.DB
}

func NewInboundRepository(db *gorm.DB) Repository {
	return &inboundRepository{db: db}
}

// 获取我的工单
//func (i inboundRepository) List(ctx context.Context, userId uint, query inbound.GetListQuery) ([]inbound.InboundTicket, int64, error) {
//	var tickets []inbound.InboundTicket
//	var total int64
//
//	// 筛选条件：我发起的、我维修的、我审核的
//	db := i.db.WithContext(ctx).Model(&inbound.InboundTicket{}).Where("submitter_id = ? OR approver_id= ? OR Handler_id= ?", userId, userId, userId)
//
//	// 根据查询条件动态构建查询
//	if query.Role != "" {
//		db = db.Where("role = ?", query.Role)
//	}
//	if query.Status != "" {
//		db = db.Where("status = ?", query.Status)
//	}
//	if query.OrderCategory != "" {
//		db = db.Where("order_category = ?", query.OrderCategory)
//	}
//
//	// 查询总数
//	err := db.Count(&total).Error
//	if err != nil {
//		return nil, 0, err
//	}
//
//	// 分页查询
//	if query.Page > 0 && query.PageSize > 0 {
//		offset := (query.Page - 1) * query.PageSize
//		db = db.Offset(offset).Limit(query.PageSize)
//	}
//
//	// 执行查询
//	err = db.Preload("PartInbound").Find(&tickets).Error
//	if err != nil {
//		return nil, 0, err
//	}
//	return tickets, total, nil
//}

// 创建维修入库单
func (i inboundRepository) CreatePartInbound(ctx context.Context, req *inbound.PartInbound) (uint, error) {
	err := i.db.WithContext(ctx).Model(&inbound.PartInbound{}).Create(req).Error
	if err != nil {
		return 0, err
	}
	return req.ID, err
}

// 更新入库单
func (i *inboundRepository) UpdatePartInbound(ctx context.Context, id uint, req *inbound.PartInbound) error {
	updates := i.buildUpdatePartFields(req)
	return i.db.WithContext(ctx).Model(&inbound.PartInbound{}).Where("id = ?", id).Updates(updates).Error
}

// UpdateNewInbound 更新新购入库单
func (i inboundRepository) UpdateNewInbound(ctx context.Context, id uint, req *inbound.NewInbound) error {
	updates := i.buildUpdateNewFields(req)
	return i.db.WithContext(ctx).Model(&inbound.NewInbound{}).Where("id = ? ", id).Updates(updates).Error
}

// 删除入库单
func (i *inboundRepository) DeletePartInbound(ctx context.Context, id uint) error {
	return i.db.WithContext(ctx).Where("id = ?", id).Delete(&inbound.PartInbound{}).Error
}

// 获取入库单详情
func (i *inboundRepository) GetPartInboundByID(ctx context.Context, id uint) (*inbound.PartInbound, error) {
	var partInbound inbound.PartInbound
	if err := i.db.WithContext(ctx).Where("id = ?", id).First(&partInbound).Error; err != nil {
		return nil, err
	}
	return &partInbound, nil
}

// generateTicketNo 生成工单号
func (i *inboundRepository) GenerateTicketNo() string {
	// 使用时间戳和随机数生成工单号
	now := time.Now()
	return fmt.Sprintf("PT%s%d", now.Format("20060102"), now.UnixNano()%10000)
}

// buildUpdatePartFields 生成用于 GORM 更新的字段 map，只包含零字段
func (i *inboundRepository) buildUpdatePartFields(req *inbound.PartInbound) map[string]interface{} {
	updates := make(map[string]interface{})
	updates["warehouse_id"] = req.WarehouseID
	if req.RepairTicketID != 0 {
		updates["repair_ticket_id"] = req.RepairTicketID
	}
	if req.InspectionResult != "" {
		updates["inspection_result"] = req.InspectionResult
	}
	if req.FaultDescription != "" {
		updates["fault_description"] = req.FaultDescription
	}
	// 继续添加更多字段
	// if req.OtherField != "" || req.OtherField != 0 { updates["other_field"] = req.OtherField }

	return updates
}

// buildUpdatePartFields 生成用于 GORM 更新的字段 map，只包含零字段
func (i *inboundRepository) buildUpdateNewFields(req *inbound.NewInbound) map[string]interface{} {
	updates := make(map[string]interface{})
	if req.PurchaseOrderID != 0 {
		updates["purchase_order_id"] = req.PurchaseOrderID
	}
	if req.PurchaseOrderNo != "" {
		updates["purchase_order_no"] = req.PurchaseOrderNo
	}
	if req.SupplierName != "" {
		updates["supplier_name"] = req.SupplierName
	}
	if req.Amount != 0 {
		updates["quantity"] = req.Amount
	}
	updates["lock"] = req.Lock
	return updates
}

// CreateNewInbound 创建新购入库单
func (i *inboundRepository) CreateNewInbound(ctx context.Context, req *inbound.NewInbound) error {
	return i.db.WithContext(ctx).Create(req).Error
}

// CreateNewInboundInfo 批量创建新购入库信息
func (i *inboundRepository) CreateNewInboundInfo(ctx context.Context, infos []*inbound.NewInboundDetail) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, info := range infos {
			if err := tx.Create(info).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetNewInboundByID 通过ID获取入库单
func (i inboundRepository) GetNewInboundByID(ctx context.Context, id uint) (*inbound.NewInbound, error) {
	var NewInbound inbound.NewInbound
	err := i.db.WithContext(ctx).Where("id = ? ", id).Preload("NewInfo").Preload("NewInfo.Product").Preload("NewDetails").First(&NewInbound).Error
	return &NewInbound, err
}

func (i inboundRepository) GetNewInboundInfoByID(ctx context.Context, id uint) (*[]inbound.NewInboundDetail, error) {
	infoList := make([]inbound.NewInboundDetail, 0)
	err := i.db.WithContext(ctx).Model(&inbound.NewInboundDetail{}).Where("new_inbound_id = ?", id).Find(&infoList).Error
	return &infoList, err
}

// 获取新购入库提交者列表
func (i inboundRepository) GetNewInboundSubmitterList(ctx context.Context) ([]inbound.SubmitterInfo, error) {
	var SubmitterList []inbound.SubmitterInfo
	err := i.db.WithContext(ctx).Model(&inbound.NewInbound{}).Distinct("create_id").Select("created_by,create_id").Scan(&SubmitterList).Error
	if err != nil {
		return nil, err
	}
	return SubmitterList, err
}

// 创建新购入库单、详情单、粗略单
func (i *inboundRepository) CreateNewInboundWithInfo(ctx context.Context, inboundList *inbound.InboundList, newInbound *inbound.NewInbound, infos []inbound.NewInboundInfo) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 创建入库单列表记录
		if err := tx.Create(inboundList).Error; err != nil {
			return fmt.Errorf("创建入库单列表信息失败: %w", err)
		}

		// 2. 创建新购入库单
		if err := tx.Create(newInbound).Error; err != nil {
			return fmt.Errorf("创建新购入库单失败: %w", err)
		}

		// 3. 设置详情项关联ID并保存
		//for j := range infos {
		//	infos[j].ID = 0 // 保证自增
		//	infos[j].NewInboundID = newInbound.ID
		//}
		//
		//if len(infos) > 0 {
		//	if err := tx.Create(&infos).Error; err != nil {
		//		return fmt.Errorf("创建入库详情失败: %w", err)
		//	}
		//}

		// 4. 更新入库单数量
		var amount uint
		for _, info := range infos {
			amount += info.Amount
		}

		newInbound.Amount = amount
		if err := tx.Model(newInbound).Update("Amount", newInbound.Amount).Error; err != nil {
			return fmt.Errorf("更新入库单数量失败: %w", err)
		}

		// 初始化入库详情
		var newInboundDetails []inbound.NewInboundDetail
		for _, info := range infos {
			for i := uint(0); i < info.Amount; i++ {
				detail := inbound.NewInboundDetail{
					ProductID:    info.ProductID,
					NewInboundID: info.NewInboundID,
				}
				newInboundDetails = append(newInboundDetails, detail)
			}
		}
		if err := tx.Create(&newInboundDetails).Error; err != nil {
			return fmt.Errorf("初始化入库详情失败: %w", err)
		}

		return nil
	})
}

func (i inboundRepository) GetNewInboundByNo(ctx context.Context, newInboundNo string) (*inbound.NewInbound, *ticketModel.NewInboundTicket, error) {
	var NewInbound inbound.NewInbound
	var NewInboundTicket ticketModel.NewInboundTicket

	err := i.db.WithContext(ctx).Model(&inbound.NewInbound{}).Preload("NewDetails").Preload("NewDetails.Product").Preload("NewInfo").Preload("NewInfo.Product").Where("inbound_no = ?", newInboundNo).First(&NewInbound).Error
	if err != nil {
		return nil, nil, fmt.Errorf("获取新购入库单失败: %w", err)
	}
	err = i.db.WithContext(ctx).Model(&ticketModel.NewInboundTicket{}).Where("inbound_no = ?", newInboundNo).Preload("History", func(db *gorm.DB) *gorm.DB {
		return db.Order("created_at DESC")
	}).First(&NewInboundTicket).Error
	if err != nil {
		return nil, nil, fmt.Errorf("获取入库工单失败: %w", err)
	}
	return &NewInbound, &NewInboundTicket, nil
}

//func (i inboundRepository) ImportToCMDB(ctx context.Context, spares []assetModel.AssetSpare, histories []inventory.StockHistory) error {
//	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
//		if err := tx.Create(&spares).Error; err != nil {
//			return fmt.Errorf("创建资产备件表失败: %w", err)
//		}
//		if err := tx.Create(&histories).Error; err != nil {
//			return fmt.Errorf("创建库存历史失败: %w", err)
//		}
//		return nil
//	})
//}

func (i inboundRepository) ImportToCMDB(ctx context.Context, spares []assetModel.AssetSpare) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&spares).Error; err != nil {
			return fmt.Errorf("创建资产备件表失败: %w", err)
		}
		return nil
	})
}

func (i inboundRepository) InitNewInboundDetails(ctx context.Context, details []inbound.NewInboundDetail) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(details).Error; err != nil {
			return fmt.Errorf("创建入库详情失败：: %w", err)
		}
		return nil
	})
}

// GetRepairedInboundByNo 根据工单号获取返修入库信息
func (i *inboundRepository) CreateRepairInbound(ctx context.Context, inboundList *inbound.InboundList, repairInbound *inbound.RepairInbound) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 创建入库单列表记录
		if err := tx.Create(inboundList).Error; err != nil {
			return fmt.Errorf("创建入库单列表失败: %w", err)
		}

		// 2. 创建返修入库记录
		if err := tx.Create(repairInbound).Error; err != nil {
			return fmt.Errorf("创建返修配件入库单失败: %w", err)
		}

		//for _, detail := range repairInbound.RepairDetails {
		//	detail.RepairInboundID = repairInbound.ID
		//	detail.ID = 0 //形成自增组件
		//	if err := tx.Create(&detail).Error; err != nil {
		//		return fmt.Errorf("创建返修配件入库详情失败: %w", err)
		//	}
		//}

		return nil
	})
}

// GetRepairedInboundByNo 根据工单号获取返修入库信息
func (i *inboundRepository) GetRepairedInboundByNo(ctx context.Context, inboundNo string) (*inbound.RepairInbound, *ticketModel.RepairPartInboundTicket, error) {
	var (
		repairInbound      inbound.RepairInbound
		repairIboundTicket ticketModel.RepairPartInboundTicket
	)
	err := i.db.WithContext(ctx).Model(&inbound.RepairInbound{}).Where("inbound_no = ? ", inboundNo).Preload("RepairDetails").First(&repairInbound).Error
	if err != nil {
		return nil, nil, fmt.Errorf("获取返修入库信息失败: %w", err)
	}
	err = i.db.WithContext(ctx).Model(&ticketModel.RepairPartInboundTicket{}).Where("inbound_no = ? ", inboundNo).Preload("History", func(db *gorm.DB) *gorm.DB {
		return db.Order("created_at DESC")
	}).First(&repairIboundTicket).Error
	if err != nil {
		return nil, nil, fmt.Errorf("获取返修入库工单信息失败: %w", err)
	}
	return &repairInbound, &repairIboundTicket, nil
}

// GetDismantledInboundByNo 根据工单号获取拆机入库信息
func (i *inboundRepository) GetDismantledInboundByNo(ctx context.Context, inboundNo string) (*inbound.DismantledInbound, *ticketModel.DismantledPartInboundTicket, error) {
	var (
		dismantledInbound      inbound.DismantledInbound
		dismantledIboundTicket ticketModel.DismantledPartInboundTicket
	)
	err := i.db.WithContext(ctx).Model(&inbound.DismantledInbound{}).Where("inbound_no = ?", inboundNo).Preload("Details").Preload("Details.Product").Find(&dismantledInbound).Error
	if err != nil {
		return nil, nil, fmt.Errorf("获取 dismantledInbound 信息失败: %w", err)
	}
	if err := i.db.WithContext(ctx).Model(&ticketModel.DismantledPartInboundTicket{}).Where("inbound_no = ?", inboundNo).Preload("History", func(db *gorm.DB) *gorm.DB {
		return db.Order("created_at DESC")
	}).First(&dismantledIboundTicket).Error; err != nil {
		return nil, nil, fmt.Errorf("获取dismantledInboundTicket信息失败: %w", err)
	}
	return &dismantledInbound, &dismantledIboundTicket, nil
}

// List 获取入库单列表
func (i *inboundRepository) List(ctx context.Context, dto *inbound.ListDTO) (int64, []inbound.InboundList, error) {
	var total int64
	var list []inbound.InboundList

	// 构建查询
	query := i.db.WithContext(ctx).Model(&inbound.InboundList{})

	// 添加查询条件
	if dto.Project != "" {
		query = query.Where("project = ?", dto.Project)
	}
	if dto.InboundNo != "" {
		query = query.Where("inbound_no LIKE ?", "%"+dto.InboundNo+"%")
	}
	if dto.InboundType != "" {
		query = query.Where("inbound_type = ?", dto.InboundType)
	}
	if dto.InboundReason != "" {
		query = query.Where("inbound_reason = ?", dto.InboundReason)
	}
	if dto.CreateID != 0 {
		query = query.Where("creater_id = ?", dto.CreateID)
	}
	if dto.Stage != "" {
		query = query.Where("stage LIKE ?", "%"+dto.Stage+"%")
	}
	if dto.CreateBy != "" {
		query = query.Where("create_by LIKE ?", dto.CreateBy)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return 0, nil, fmt.Errorf("获取总数失败: %w", err)
	}

	// 分页查询
	offset := (dto.Page - 1) * dto.PageSize
	// 确保offset在int范围内
	if offset > math.MaxInt32 {
		return 0, nil, fmt.Errorf("页码过大，导致偏移量溢出")
	}
	// 确保pageSize在int范围内
	if dto.PageSize > math.MaxInt32 {
		return 0, nil, fmt.Errorf("每页数量过大，超出系统限制")
	}
	if err := query.Offset(int(offset)).Limit(int(dto.PageSize)).Order("created_at DESC").Find(&list).Error; err != nil {
		return 0, nil, fmt.Errorf("查询入库单列表失败: %w", err)
	}

	return total, list, nil
}

func (i inboundRepository) UpdateNewInboundDetail(ctx context.Context, details []inbound.NewInboundDetail) error {

	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, detail := range details {
			// 确保只更新需要的字段
			updateData := map[string]interface{}{
				"sn":             detail.SN,
				"warehouse_id":   detail.WarehouseID,
				"warehouse_name": detail.WarehouseName,
				// 其他需要更新的字段...
			}

			// 使用Model指定表，Updates指定更新字段
			result := tx.Model(&inbound.NewInboundDetail{}).
				Where("id = ?", detail.ID).
				Updates(updateData)

			if result.Error != nil {
				return fmt.Errorf("更新入库详情失败：%v", result.Error)
			}

			if result.RowsAffected == 0 {
				return fmt.Errorf("未找到ID为 %d 的入库详情", detail.ID)
			}
		}
		return nil
	})
}

func (i inboundRepository) UpdateInboundListStage(ctx context.Context, inboundNo, Stage string) error {
	update := map[string]interface{}{
		"stage": Stage,
	}
	err := i.db.WithContext(ctx).Model(&inbound.InboundList{}).Where("inbound_no = ?", inboundNo).Updates(update).Error
	if err != nil {
		return fmt.Errorf("更新InboundList的stage字段失败：: %w", err)
	}
	return nil
}

// CreateDismantledInbound 创建拆机入库单
func (i *inboundRepository) CreateDismantledInbound(ctx context.Context, inboundList *inbound.InboundList, dismantledInbounds *inbound.DismantledInbound) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		//var (
		//	productID uint
		//	SN        string
		//)
		// 1. 创建入库单列表记录
		if err := tx.Create(inboundList).Error; err != nil {
			return fmt.Errorf("创建入库单列表信息失败: %w", err)
		}

		// 2. 创建拆机入库单记录
		//for index, detail := range dismantledInbounds.Details {
		//	// 获取SN
		//	//err := i.db.WithContext(ctx).Model(&inbound.DismantledInbound{}).Where("inbound_no = ?", inboundList.InboundNo).Pluck("component_sn", &SN).Error
		//	//if err != nil {
		//	//	return fmt.Errorf("获取拆机入库信息失败: %w", err)
		//	//}
		//	// 获取Product.id
		//	err := i.db.WithContext(ctx).Model(&assetModel.AssetSpare{}).Where("sn = ?", detail.ComponentSN).Pluck("product_id", &productID).Error
		//	if err != nil {
		//		return fmt.Errorf("通过 %v 获取规格信息失败: %w", SN, err)
		//	}
		//	detail.ProductID = productID
		//	dismantledInbounds.InboundNo = inboundList.InboundNo
		//	if err := tx.Create(dismantledInbounds).Error; err != nil {
		//		return fmt.Errorf("创建拆机入库单失败: %w", err)
		//	}
		//}
		dismantledInbounds.InboundNo = inboundList.InboundNo
		err := tx.Create(&dismantledInbounds).Error
		if err != nil {
			return fmt.Errorf("创建拆机入库单失败: %w", err)
		}
		//for _, detail := range dismantledInbounds.Details {
		//	detail.DismantledInboundID = dismantledInbounds.ID
		//	detail.ID = 0
		//	err := tx.Create(&detail).Error
		//	if err != nil {
		//		return fmt.Errorf("创建拆机入库详情失败: %w", err)
		//	}
		//}

		return nil
	})
}

func (i inboundRepository) UpdateDismantledData(ctx context.Context, DismantledInbounds inbound.DismantledInbound) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, dismantledInbound := range DismantledInbounds.Details {
			var (
				assetSpare assetModel.AssetSpare
				//detail     inventory.InventoryDetail
			)
			updateData := map[string]interface{}{
				"hardware_status": dismantledInbound.ComponentState,
				"warehouse_id":    dismantledInbound.WarehouseID,
				"source_type":     inbound.SourceTypeDismantled,
			}
			if err := tx.Model(&assetModel.AssetSpare{}).Where("sn = ?", dismantledInbound.ComponentSN).Updates(updateData).Error; err != nil {
				return fmt.Errorf("更新asset失败：%w", err)
			}

			if err := tx.Model(&assetModel.AssetSpare{}).Where("sn = ?", dismantledInbound.ComponentSN).First(&assetSpare).Error; err != nil {
				return fmt.Errorf("获取assetSpare失败：%w", err)
			}

			// 获取detail
			//err := tx.Model(&inventory.InventoryDetail{}).Where("product_id = ? AND warehouse_id = ?", assetSpare.ProductID, assetSpare.WarehouseID).First(&detail).Error
			//if err != nil {
			//	return fmt.Errorf("获取detail失败：%w", err)
			//}
			//
			//history := inventory.StockHistory{
			//	ProductID:    assetSpare.ProductID,
			//	DetailID:     detail.ID,
			//	ChangeTime:   time.Now(),
			//	ChangeAmount: 1,
			//	OperatorID:   DismantledInbounds.CreateID,
			//	Operator:     DismantledInbounds.CreateBy,
			//	ChangeType:   inbound.SourceTypeDismantled,
			//}
			//err = tx.Model(&inventory.StockHistory{}).Create(&history).Error
			//if err != nil {
			//	return fmt.Errorf("创建StockHistory失败：%w", err)
			//}

		}
		return nil
	})
}

func (i inboundRepository) UpdateDismantledDataV1(ctx context.Context, DismantledInbounds inbound.DismantledInbound) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, dismantledInbound := range DismantledInbounds.Details {
			var (
				assetSpare assetModel.AssetSpare
				//detail     inventory.InventoryDetail
				AssetStatus string
			)
			//fmt.Println(dismantledInbound.ComponentState)
			//switch dismantledInbound.ComponentState {
			//case constants.HardwareStatusNormal:
			//	AssetStatus = constants.AssetStatusIdle
			//case constants.HardwareStatusFaulty:
			//	AssetStatus = constants.AssetStatusRepairing
			//default:
			//	AssetStatus = constants.AssetStatusInStock
			//}
			//if dismantledInbound.NeedReturn {
			//	AssetStatus = constants.AssetStatusOutStock
			//}
			AssetStatus = constants.AssetStatusIdle
			// 查找有没有对应数据
			err := tx.Model(&assetModel.AssetSpare{}).Where("sn = ?", dismantledInbound.ComponentSN).First(&assetSpare).Error
			if err != nil {
				if err == gorm.ErrRecordNotFound { //没有对应数据，创建一条

					asset := assetModel.AssetSpare{
						ProductID:      dismantledInbound.ProductID,
						SN:             dismantledInbound.ComponentSN,
						WarehouseID:    dismantledInbound.WarehouseID,
						AssetStatus:    AssetStatus,
						HardwareStatus: dismantledInbound.ComponentState,
						SourceType:     inbound.SourceTypeDismantled,
					}
					if err := tx.Create(&asset).Error; err != nil {
						return fmt.Errorf("创建assetSpare失败：%w", err)
					}
				} else {
					return fmt.Errorf("发生未知错误：%w", err)
				}
			} else { // 记录存在，更新指定字段
				updateData := map[string]interface{}{
					"asset_status":     AssetStatus,
					"hardware_status":  dismantledInbound.ComponentState,
					"warehouse_id":     dismantledInbound.WarehouseID,
					"source_type":      inbound.SourceTypeDismantled,
					"related_asset_id": 0,
					"related_asset_sn": "",
				}
				if err := tx.Model(&assetModel.AssetSpare{}).Where("sn = ?", dismantledInbound.ComponentSN).Updates(updateData).Error; err != nil {
					return fmt.Errorf("更新asset失败：%w", err)
				}
			}
			// 获取detail
			//err = tx.Model(&inventory.InventoryDetail{}).Where("product_id = ? AND warehouse_id = ?", dismantledInbound.ProductID, dismantledInbound.WarehouseID).First(&detail).Error
			//if err != nil {
			//	return fmt.Errorf("请联系管理员检查仓库与规格数据：%w", err)
			//}

			//history := inventory.StockHistory{
			//	ProductID:    assetSpare.ProductID,
			//	DetailID:     detail.ID,
			//	ChangeTime:   time.Now(),
			//	OldQuantity:  detail.CurrentStock,
			//	NewQuantity:  detail.CurrentStock + 1,
			//	ChangeAmount: 1,
			//	OperatorID:   DismantledInbounds.CreateID,
			//	Operator:     DismantledInbounds.CreateBy,
			//	WarehouseID:  dismantledInbound.WarehouseID,
			//	ChangeType:   inbound.SourceTypeDismantled,
			//}
			//err = tx.Model(&inventory.StockHistory{}).Create(&history).Error
			//if err != nil {
			//	return fmt.Errorf("创建StockHistory失败：%w", err)
			//}
		}
		return nil
	})
}

// ValidRepairPartInboundSN 验证返修入库单SN
// repairsSNs 维修的SN, replaceSNs 被换下来的SN
func (i *inboundRepository) ValidRepairPartInboundSN(ctx context.Context, repairsSNs, replaceSNs []string) error {
	var (
		existRepairSns     []string
		existReplaceSns    []string
		notExistRepairSNs  []string
		notExistReplaceSNs []string
	)
	if len(repairsSNs) != 0 {
		err := i.db.WithContext(ctx).Model(&assetModel.AssetSpare{}).Where("sn IN ?", repairsSNs).Pluck("sn", &existRepairSns).Error
		if err != nil {
			return fmt.Errorf("获取用于维修的SN失败：%w", err)
		}
		// 构建存在 / 不存在列表
		existsRepairMap := make(map[string]bool)
		for _, sn := range existRepairSns {
			existsRepairMap[sn] = true
		}
		for _, sn := range repairsSNs {
			if !existsRepairMap[sn] {
				notExistRepairSNs = append(notExistRepairSNs, sn)
			}
		}
	}

	if len(replaceSNs) != 0 {
		if err := i.db.WithContext(ctx).Model(&assetModel.AssetSpare{}).Where("sn IN ?", replaceSNs).Pluck("sn", &existReplaceSns).Error; err != nil {
			return fmt.Errorf("获取换新报废的SN失败：%w", err)
		}
		if len(existReplaceSns) == 0 {
			return fmt.Errorf("所有换新报废的SN都不存在，SN号：%v", repairsSNs)
		}

		existsReplaceMap := make(map[string]bool)
		for _, sn := range existReplaceSns {
			existsReplaceMap[sn] = true
		}
		for _, sn := range replaceSNs {
			if !existsReplaceMap[sn] {
				notExistReplaceSNs = append(notExistReplaceSNs, sn)
			}
		}

	}
	if len(notExistRepairSNs) != 0 || len(notExistReplaceSNs) != 0 {
		return fmt.Errorf("SN校验失败，不存在的维修SN：%v，不存在的换新入库旧SN：%v", notExistRepairSNs, notExistReplaceSNs)
	}
	return nil
}

// ValidNewPartInboundSN 验证新购配件入库SN
func (i *inboundRepository) ValidNewPartInboundSN(ctx context.Context, SNs []string) error {
	var (
		nums     int64
		existSNs []string
	)
	err := i.db.WithContext(ctx).Model(&assetModel.AssetSpare{}).Where("sn IN ?", SNs).Count(&nums).Pluck("sn", &existSNs).Error
	if err != nil {
		return fmt.Errorf("校验新购配件入库SN信息失败：%w", err)
	}
	if len(existSNs) > 0 {
		return fmt.Errorf("以下SN%v已存在", existSNs)
	}
	return nil
}

// ValidDeviceInboundSN 校验整机设备入库SN
func (i inboundRepository) ValidDeviceInboundSN(ctx context.Context, SNs []string) error {
	var (
		nums     int64
		existSNs []string
	)
	err := i.db.WithContext(ctx).Model(&assetModel.Device{}).Where("sn IN ?", SNs).Count(&nums).Pluck("sn", &existSNs).Error
	if err != nil {
		return fmt.Errorf("校验新购配件入库SN信息失败：%w", err)
	}
	if len(existSNs) > 0 {
		return fmt.Errorf("以下SN%v已存在", existSNs)
	}
	return nil
}

// GetDeviceInboundByNo 通过订单号获取整机设备入库信息
func (i inboundRepository) GetDeviceInboundByNo(ctx context.Context, inboundNo string) (*inbound.DeviceInbound, *ticketModel.DeviceInboundTicket, error) {
	var (
		deviceInbound *inbound.DeviceInbound
		ticket        *ticketModel.DeviceInboundTicket
	)
	err := i.db.WithContext(ctx).Model(&inbound.DeviceInbound{}).Where("inbound_no = ?", inboundNo).Preload("DeviceInfo").Preload("DeviceInfo.Template").Preload("DeviceDetails").Preload("DeviceDetails.Template").Preload("DeviceDetails.Warehouse").Preload("Warehouse").Take(&deviceInbound).Error
	if err != nil {
		return nil, nil, fmt.Errorf("查找deviceInbound失败：%v", err)
	}
	err = i.db.WithContext(ctx).Model(&ticketModel.DeviceInboundTicket{}).Preload("History").Where("inbound_no = ?", inboundNo).Take(&ticket).Error
	if err != nil {
		return nil, nil, fmt.Errorf("查找deviceInboundTicket失败：%v", err)
	}
	return deviceInbound, ticket, nil

}

func (i inboundRepository) UpdateDeviceInboundDetailsByInput(ctx context.Context, details []inbound.DeviceInboundDetail) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, detail := range details {
			// 更新设备入库详情
			updateData := map[string]interface{}{
				"sn":               detail.SN,
				"warehouse_id":     detail.WarehouseID,
				"warehouse_name":   detail.WarehouseName,
				"location":         detail.Location,
				"data_center_id":   detail.DataCenterID,
				"data_center_name": detail.DataCenterName,
				"room_id":          detail.RoomID,
				"room_name":        detail.RoomName,
			}

			// 使用Model指定表，Updates指定更新字段
			result := tx.Model(&inbound.DeviceInboundDetail{}).
				Where("id = ?", detail.ID).
				Updates(updateData)

			if result.Error != nil {
				return fmt.Errorf("更新入库详情失败：%v", result.Error)
			}

			if result.RowsAffected == 0 {
				return fmt.Errorf("未找到ID为 %d 的入库详情", detail.ID)
			}
		}

		return nil
	})
}
