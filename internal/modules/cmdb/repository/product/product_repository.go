package product

import (
	"context"
	"fmt"

	"backend/internal/modules/cmdb/model/product"

	"gorm.io/gorm"
)

// ProductFilter 产品查询过滤条件
type ProductFilter struct {
	PN              string
	MaterialType    string
	Brand           string
	Model           string
	Spec            string
	ProductCategory string
	Status          int8
}

// PaginationOptions 分页选项
type PaginationOptions struct {
	Page     int
	PageSize int
}

// ProductRepository 产品仓库接口
type ProductRepository interface {
	// GetByID 根据ID获取产品
	GetByID(ctx context.Context, id uint) (*product.Product, error)

	// List 获取产品列表
	List(ctx context.Context, filter ProductFilter, pagination PaginationOptions) ([]*product.Product, int64, error)
	ListAll(ctx context.Context) ([]*product.Product, error)

	// Create 创建产品
	Create(ctx context.Context, product *product.Product) error

	// Update 更新产品
	Update(ctx context.Context, product *product.Product) error

	// Delete 删除产品
	Delete(ctx context.Context, id uint) error

	// GetMaterialTypes 获取所有物料类型
	GetMaterialTypes(ctx context.Context) ([]string, error)

	// GetProductCategories 获取所有产品类别
	GetProductCategories(ctx context.Context) ([]string, error)

	// GetBrands 获取所有品牌
	GetBrands(ctx context.Context) ([]string, error)

	// GetSpecsByMaterialType 获取特定物料类型的规格列表
	GetSpecsByMaterialType(ctx context.Context, materialType string) ([]string, error)

	// GetAllSpecs 获取所有规格列表
	GetAllSpecs(ctx context.Context) ([]string, error)

	// ListByPNs 通过PN获取列表
	ListByPNs(ctx context.Context, pns []string) ([]*product.Product, error)

	// GetByPNAndBrand 根据PN和品牌查找产品
	GetByPNAndBrand(ctx context.Context, pn, brand string) (*product.Product, error)
}

// productRepository 产品仓库实现
type productRepository struct {
	db *gorm.DB
}

// NewProductRepository 创建产品仓库
func NewProductRepository(db *gorm.DB) ProductRepository {
	return &productRepository{db: db}
}

// GetByID 根据ID获取产品
func (r *productRepository) GetByID(ctx context.Context, id uint) (*product.Product, error) {
	var prod product.Product
	if err := r.db.WithContext(ctx).First(&prod, id).Error; err != nil {
		return nil, err
	}
	return &prod, nil
}

// List 获取产品列表
func (r *productRepository) List(ctx context.Context, filter ProductFilter, pagination PaginationOptions) ([]*product.Product, int64, error) {
	var products []*product.Product
	var total int64

	query := r.db.WithContext(ctx).Model(&product.Product{})

	// 应用过滤条件
	if filter.PN != "" {
		query = query.Where("pn LIKE ?", "%"+filter.PN+"%")
	}
	if filter.MaterialType != "" {
		query = query.Where("material_type = ?", filter.MaterialType)
	}
	if filter.Brand != "" {
		query = query.Where("brand LIKE ?", "%"+filter.Brand+"%")
	}
	if filter.Model != "" {
		query = query.Where("model LIKE ?", "%"+filter.Model+"%")
	}
	if filter.Spec != "" {
		query = query.Where("spec LIKE ?", "%"+filter.Spec+"%")
	}
	if filter.ProductCategory != "" {
		query = query.Where("product_category = ?", filter.ProductCategory)
	}
	if filter.Status != 0 {
		query = query.Where("status = ?", filter.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用分页
	if pagination.Page > 0 && pagination.PageSize > 0 {
		offset := (pagination.Page - 1) * pagination.PageSize
		query = query.Offset(offset).Limit(pagination.PageSize)
	}

	// 排序
	query = query.Order("id DESC")

	// 执行查询
	if err := query.Find(&products).Error; err != nil {
		return nil, 0, err
	}

	return products, total, nil
}

// ListAll 获取所有的规格数据
func (r *productRepository) ListAll(ctx context.Context) ([]*product.Product, error) {
	var products []*product.Product
	if err := r.db.WithContext(ctx).Find(&products).Error; err != nil {
		return nil, fmt.Errorf("获取规格列表失败：%v", err.Error())
	}
	if len(products) == 0 {
		return nil, fmt.Errorf("规格数据为空，请联系管理员处理")
	}
	return products, nil
}

// Create 创建产品
func (r *productRepository) Create(ctx context.Context, product *product.Product) error {
	return r.db.WithContext(ctx).Create(product).Error
}

// Update 更新产品
func (r *productRepository) Update(ctx context.Context, product *product.Product) error {
	return r.db.WithContext(ctx).Save(product).Error
}

// Delete 删除产品
func (r *productRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&product.Product{}, id).Error
}

// GetMaterialTypes 获取所有物料类型
func (r *productRepository) GetMaterialTypes(ctx context.Context) ([]string, error) {
	var types []string
	err := r.db.WithContext(ctx).Model(&product.Product{}).Distinct().Pluck("material_type", &types).Error
	return types, err
}

// GetProductCategories 获取所有产品类别
func (r *productRepository) GetProductCategories(ctx context.Context) ([]string, error) {
	var categories []string
	err := r.db.WithContext(ctx).Model(&product.Product{}).Distinct().Pluck("product_category", &categories).Error
	return categories, err
}

// GetBrands 获取所有品牌
func (r *productRepository) GetBrands(ctx context.Context) ([]string, error) {
	var brands []string
	err := r.db.WithContext(ctx).Model(&product.Product{}).Distinct().Pluck("brand", &brands).Error
	return brands, err
}

// GetSpecsByMaterialType 获取特定物料类型的规格列表
func (r *productRepository) GetSpecsByMaterialType(ctx context.Context, materialType string) ([]string, error) {
	var specs []string
	err := r.db.WithContext(ctx).Model(&product.Product{}).
		Where("material_type = ?", materialType).
		Distinct().
		Pluck("spec", &specs).Error
	return specs, err
}

// GetAllSpecs 获取所有规格列表
func (r *productRepository) GetAllSpecs(ctx context.Context) ([]string, error) {
	var specs []string
	err := r.db.WithContext(ctx).Model(&product.Product{}).Distinct().Pluck("spec", &specs).Error
	return specs, err
}

// ListByPNs 根据PN列表查询匹配的产品信息
func (r *productRepository) ListByPNs(ctx context.Context, pns []string) ([]*product.Product, error) {
	if len(pns) == 0 {
		return nil, nil
	}

	var products []*product.Product
	err := r.db.WithContext(ctx).
		Model(&product.Product{}).
		Where("pn IN (?)", pns).
		Find(&products).Error

	if err != nil {
		return nil, err
	}
	return products, nil
}

// GetByPNAndBrand 根据PN和品牌查找产品
func (r *productRepository) GetByPNAndBrand(ctx context.Context, pn, brand string) (*product.Product, error) {
	var prod product.Product
	err := r.db.WithContext(ctx).
		Where("pn = ? AND brand = ?", pn, brand).
		First(&prod).Error

	if err != nil {
		return nil, err
	}
	return &prod, nil
}
