package outbound

import (
	"backend/internal/common/constants"
	"backend/internal/common/utils"
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/outbound"
	"backend/internal/modules/cmdb/model/outbound/common"
	"context"
	"fmt"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// OutboundTicketRepository 出库单仓库接口
type OutboundTicketRepository interface {
	Create(ctx context.Context, ticket *outbound.SpareOutboundTicket) error
	CreateV2(ctx context.Context, ticket *outbound.SpareOutboundTicket, history *outbound.OutboundTicketStatusHistory) error
	GetByID(ctx context.Context, id uint) (*outbound.SpareOutboundTicket, error)
	GetByTicketNo(ctx context.Context, ticketNo string) (*outbound.SpareOutboundTicket, error)
	GetDetailsLen(ctx context.Context, ticketID uint) (uint, error)
	Update(ctx context.Context, ticket *outbound.SpareOutboundTicket) error
	UpdateDetails(ctx context.Context, details []outbound.OutboundDetail) error
	Delete(ctx context.Context, id uint) error
	List(ctx context.Context, page, pageSize int, query, stage, outboundType, ourboundReason, project string) ([]*outbound.SpareOutboundTicket, int64, error)
	CreateStatusHistory(ctx context.Context, history *outbound.OutboundTicketStatusHistory) error
	GetStatusHistory(ctx context.Context, ticketID uint) ([]*outbound.OutboundTicketStatusHistory, error)
	//UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error
	WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo OutboundTicketRepository) error) error
	// FindTicketsForRetry 查找需要重试的工单
	FindTicketsForRetry(ctx context.Context) ([]*outbound.SpareOutboundTicket, error)
	WithDB(ctx context.Context) *gorm.DB
	GetInfoByTicketID(ctx context.Context, ticketID uint) ([]outbound.OutboundInfo, error)
	GetDetailsByTicketID(ctx context.Context, ticketID uint) ([]outbound.OutboundDetail, error)
	GetDetailByID(ctx context.Context, detailID uint) (*outbound.OutboundDetail, error)

	/** 重构后  */
	GetDB() (*gorm.DB, error)
	CreateDeviceLifeCycleLog(ctx context.Context, ticket *outbound.SpareOutboundTicket, devices []asset.Device, oldAssetStatus string) error
}

// outboundTicketRepository 出库单仓库实现
type outboundTicketRepository struct {
	db *gorm.DB
}

// NewOutboundTicketRepository 创建出库单仓库
func NewOutboundTicketRepository(db *gorm.DB) OutboundTicketRepository {
	return &outboundTicketRepository{db: db}
}

// Create 创建出库单
func (r *outboundTicketRepository) Create(ctx context.Context, ticket *outbound.SpareOutboundTicket) error {
	// 使用事务进行创建操作，确保数据完整性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 使用明确的Create方法而不是Save，避免潜在的全表更新风险
		if err := tx.Create(ticket).Error; err != nil {
			return err
		}
		// 添加日志记录成功创建的工单
		fmt.Printf("成功创建工单: ID=%d, TicketNo=%s\n", ticket.ID, ticket.TicketNo)
		return nil
	})
}

// CreateV2 创建出库单
func (r *outboundTicketRepository) CreateV2(ctx context.Context, ticket *outbound.SpareOutboundTicket, history *outbound.OutboundTicketStatusHistory) error {
	// 使用事务进行创建操作，确保数据完整性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(ticket).Error; err != nil {
			return fmt.Errorf("创建出库单失败：%v", err.Error())
		}
		history.OutboundTicketID = ticket.ID
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("创建出库单历史失败：%v", err.Error())
		}

		return nil
	})
}

// GetByID 根据ID获取出库单
func (r *outboundTicketRepository) GetByID(ctx context.Context, id uint) (*outbound.SpareOutboundTicket, error) {
	var ticket outbound.SpareOutboundTicket
	err := r.db.WithContext(ctx).
		Preload("RepairTicket").
		First(&ticket, id).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

// GetByTicketNo 根据工单号获取出库单
func (r *outboundTicketRepository) GetByTicketNo(ctx context.Context, ticketNo string) (*outbound.SpareOutboundTicket, error) {
	var ticket outbound.SpareOutboundTicket
	err := r.db.WithContext(ctx).
		//Preload("Device").
		//Preload("Component").
		//Preload("Resource").
		Where("ticket_no = ?", ticketNo).
		First(&ticket).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

// Update 更新出库单
func (r *outboundTicketRepository) Update(ctx context.Context, ticket *outbound.SpareOutboundTicket) error {
	// 确保有ID，避免无条件更新
	if ticket.ID == 0 {
		return fmt.Errorf("无法更新出库单：ID不能为0")
	}

	// 先检查记录是否存在
	var count int64
	if err := r.db.WithContext(ctx).Model(&outbound.SpareOutboundTicket{}).Where("id = ?", ticket.ID).Count(&count).Error; err != nil {
		return fmt.Errorf("检查工单是否存在失败: %w", err)
	}
	if count == 0 {
		return fmt.Errorf("工单(ID=%d)不存在，无法更新", ticket.ID)
	}

	// 记录操作，便于调试
	fmt.Printf("更新工单(ID=%d): %+v\n", ticket.ID, ticket)

	// 使用事务进行更新操作，确保数据完整性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 对CurrentWaitingStage字段进行验证，确保不包含非法字符
		if ticket.CurrentWaitingStage == "reaitingTime" ||
			strings.Contains(ticket.CurrentWaitingStage, "Time:") ||
			strings.Contains(ticket.CurrentWaitingStage, ":") {

			// 记录警告
			fmt.Printf("警告: 检测到工单(ID=%d)的CurrentWaitingStage字段包含非法字符: %s\n",
				ticket.ID, ticket.CurrentWaitingStage)

			// 尝试清理字段值
			cleanedStage := ""

			// 处理特殊错误模式
			if ticket.CurrentWaitingStage == "reaitingTime" {
				cleanedStage = "repair_selection"
				fmt.Printf("修复工单(ID=%d)的CurrentWaitingStage: reaitingTime -> repair_selection\n", ticket.ID)
			} else if strings.Contains(ticket.CurrentWaitingStage, "Time:") {
				// 提取前缀部分
				parts := strings.Split(ticket.CurrentWaitingStage, "Time:")
				if len(parts) > 0 {
					potentialStage := parts[0]
					fmt.Printf("从Time:分隔提取的阶段名称: %s\n", potentialStage)
					cleanedStage = potentialStage
				}
			}

			// 如果清理失败，设置为空值
			if cleanedStage == "" {
				fmt.Printf("无法修复工单(ID=%d)的CurrentWaitingStage，已清空该字段\n", ticket.ID)
				ticket.CurrentWaitingStage = ""
			} else {
				ticket.CurrentWaitingStage = cleanedStage
				fmt.Printf("已修复工单(ID=%d)的CurrentWaitingStage: %s\n", ticket.ID, cleanedStage)
			}
		}

		// 使用明确的Where条件和Updates方法，避免潜在的全表更新风险
		// 不直接使用整个ticket对象，而是明确指定要更新的字段
		result := tx.Model(&outbound.SpareOutboundTicket{}).
			Where("id = ?", ticket.ID).
			Updates(map[string]interface{}{
				"status":               ticket.Status,
				"stage":                ticket.Stage,
				"remarks":              ticket.Remarks,
				"needs_workflow_retry": ticket.NeedsWorkflowRetry,
				// 添加工作流等待相关字段
				"waiting_manual_trigger": ticket.WaitingManualTrigger,
				"current_waiting_stage":  ticket.CurrentWaitingStage,
				"last_waiting_time":      ticket.LastWaitingTime,

				// 添加指针类型的时间字段，需要用gorm.Expr判断是否为nil
				"close_time":               ticket.CloseTime,
				"last_workflow_retry_time": ticket.LastWorkflowRetryTime,
			})

		if result.Error != nil {
			return fmt.Errorf("更新工单失败: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			fmt.Printf("警告: 工单(ID=%d)更新影响行数为0\n", ticket.ID)
		} else {
			fmt.Printf("工单(ID=%d)更新成功，影响行数: %d\n", ticket.ID, result.RowsAffected)
		}

		return nil
	})
}

func (r *outboundTicketRepository) UpdateDetails(ctx context.Context, details []outbound.OutboundDetail) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, detail := range details {
			if err := r.db.Model(&outbound.OutboundDetail{}).Where("id = ?", detail.ID).Updates(detail).Error; err != nil {
				return fmt.Errorf("更新失败: %w", err)
			}

		}
		return nil
	})
}

// Delete 删除出库单
func (r *outboundTicketRepository) Delete(ctx context.Context, id uint) error {
	if id == 0 {
		return fmt.Errorf("无法删除出库单：ID不能为0")
	}

	// 记录删除操作
	fmt.Printf("删除工单(ID=%d)\n", id)

	// 先检查记录是否存在
	var count int64
	if err := r.db.WithContext(ctx).Model(&outbound.SpareOutboundTicket{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("检查工单是否存在失败: %w", err)
	}
	if count == 0 {
		return fmt.Errorf("工单(ID=%d)不存在，无法删除", id)
	}

	// 使用事务进行删除操作
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 使用明确的删除条件，避免全表删除风险
		result := tx.Where("id = ?", id).Delete(&outbound.SpareOutboundTicket{})
		if result.Error != nil {
			return fmt.Errorf("删除工单失败: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			return fmt.Errorf("删除工单(ID=%d)失败: 影响行数为0", id)
		}

		fmt.Printf("成功删除工单(ID=%d)\n", id)
		return nil
	})
}

// List 获取出库单列表
func (r *outboundTicketRepository) List(ctx context.Context, page, pageSize int, query, stage, outboundType, outboundReason, project string) ([]*outbound.SpareOutboundTicket, int64, error) {
	var tickets []*outbound.SpareOutboundTicket
	var total int64

	db := r.db.WithContext(ctx).Model(&outbound.SpareOutboundTicket{})

	if query != "" {
		db = db.Where("ticket_no LIKE ?", "%"+query+"%")
	}

	if stage != "" {
		db = db.Where("stage = ?", stage)
	}

	if outboundType != "" {
		db = db.Where("outbound_type = ?", outboundType)
	}

	if outboundReason != "" {
		db = db.Where("outbound_reason = ?", outboundReason)
	}

	if project != "" {
		db = db.Where("project = ?", project)
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = db.
		Preload("RepairTicket").
		//Preload("Device").
		//Preload("Component").
		//Preload("Resource").
		Offset(offset).
		Limit(pageSize).
		Order("created_at DESC").
		Find(&tickets).Error
	if err != nil {
		return nil, 0, err
	}

	return tickets, total, nil
}

// CreateStatusHistory 创建报障单状态历史
func (r *outboundTicketRepository) CreateStatusHistory(ctx context.Context, history *outbound.OutboundTicketStatusHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// GetStatusHistory 获取报障单状态历史
func (r *outboundTicketRepository) GetStatusHistory(ctx context.Context, ticketID uint) ([]*outbound.OutboundTicketStatusHistory, error) {
	var histories []*outbound.OutboundTicketStatusHistory
	err := r.db.WithContext(ctx).
		Where("outbound_ticket_id = ?", ticketID).
		Order("operation_time DESC").
		Find(&histories).Error
	if err != nil {
		return nil, err
	}
	return histories, nil
}

// UpdateFields 安全地更新报障单的特定字段，避免全表更新风险
//func (r *outboundTicketRepository) UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error {
//	if id == 0 {
//		return errors.New("invalid ticket id: id cannot be zero")
//	}
//
//	// 记录更新操作
//	fmt.Printf("正在更新工单(ID=%d)的指定字段: %+v\n", id, fields)
//
//	// 如果字段中包含CurrentWaitingStage，验证其值
//	if stageValue, exists := fields["current_waiting_stage"]; exists {
//		if stage, ok := stageValue.(string); ok && stage != "" {
//			if stage == "reaitingTime" || strings.Contains(stage, "Time:") || strings.Contains(stage, ":") {
//				fmt.Printf("警告: 检测到UpdateFields尝试设置非法的CurrentWaitingStage值: %s\n", stage)
//
//				cleanedStage := ""
//
//				// 处理特殊错误模式
//				if stage == "reaitingTime" {
//					cleanedStage = "repair_selection"
//					fmt.Printf("修复UpdateFields的CurrentWaitingStage: reaitingTime -> repair_selection\n")
//				} else if strings.Contains(stage, "Time:") {
//					// 提取前缀部分
//					parts := strings.Split(stage, "Time:")
//					if len(parts) > 0 {
//						potentialStage := parts[0]
//						fmt.Printf("从Time:分隔提取的阶段名称: %s\n", potentialStage)
//						cleanedStage = potentialStage
//					}
//				}
//
//				// 如果清理失败，设置为空值
//				if cleanedStage == "" {
//					fmt.Printf("无法修复UpdateFields的CurrentWaitingStage，已清空该字段\n")
//					fields["current_waiting_stage"] = ""
//				} else {
//					fields["current_waiting_stage"] = cleanedStage
//					fmt.Printf("已修复UpdateFields的CurrentWaitingStage: %s\n", cleanedStage)
//				}
//			}
//		}
//	}
//
//	// 检查记录是否存在
//	var count int64
//	if err := r.db.WithContext(ctx).Model(&model.FaultTicket{}).Where("id = ?", id).Count(&count).Error; err != nil {
//		return fmt.Errorf("验证工单存在性失败: %w", err)
//	}
//	if count == 0 {
//		return fmt.Errorf("工单不存在(ID=%d)", id)
//	}
//
//	// 执行更新，明确指定WHERE条件
//	tx := r.db.WithContext(ctx).Begin()
//	if tx.Error != nil {
//		return fmt.Errorf("开始事务失败: %w", tx.Error)
//	}
//
//	defer func() {
//		if r := recover(); r != nil {
//			tx.Rollback()
//		}
//	}()
//
//	// 明确使用WHERE条件，只更新指定ID的记录
//	result := tx.Model(&model.FaultTicket{}).Where("id = ?", id).Updates(fields)
//	if result.Error != nil {
//		tx.Rollback()
//		return fmt.Errorf("更新字段失败: %w", result.Error)
//	}
//
//	if result.RowsAffected == 0 {
//		tx.Rollback()
//		return fmt.Errorf("更新失败: 没有记录被修改(ID=%d)", id)
//	}
//
//	// 提交事务
//	if err := tx.Commit().Error; err != nil {
//		tx.Rollback()
//		return fmt.Errorf("提交事务失败: %w", err)
//	}
//
//	fmt.Printf("成功更新工单(ID=%d)的指定字段\n", id)
//	return nil
//}

// WithTransaction 在事务中执行操作
func (r *outboundTicketRepository) WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo OutboundTicketRepository) error) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建新的仓库实例，使用事务对象
		txRepo := &outboundTicketRepository{db: tx}
		// 执行回调函数
		return fn(ctx, txRepo)
	})
}

// WithDB 获取DB
func (r *outboundTicketRepository) WithDB(ctx context.Context) *gorm.DB {
	return r.db
}

// FindTicketsForRetry 查找需要重试的工单
func (r *outboundTicketRepository) FindTicketsForRetry(ctx context.Context) ([]*outbound.SpareOutboundTicket, error) {
	var tickets []*outbound.SpareOutboundTicket
	err := r.db.WithContext(ctx).Where("needs_workflow_retry = ?", true).Find(&tickets).Error
	if err != nil {
		return nil, err
	}
	return tickets, nil
}

// 获取出库单 Info
func (r *outboundTicketRepository) GetInfoByTicketID(ctx context.Context, ticketID uint) ([]outbound.OutboundInfo, error) {
	var infos []outbound.OutboundInfo
	err := r.db.WithContext(ctx).Where("spare_outbound_ticket_id = ?", ticketID).Preload("Product").Find(&infos).Error
	if err != nil {
		return nil, err
	}
	return infos, nil
}

// 获取出库单 Detail
func (r *outboundTicketRepository) GetDetailsByTicketID(ctx context.Context, ticketID uint) ([]outbound.OutboundDetail, error) {
	var details []outbound.OutboundDetail
	err := r.db.WithContext(ctx).Where("spare_outbound_ticket_id = ?", ticketID).Preload("Product").Find(&details).Error
	if err != nil {
		return nil, err
	}
	return details, nil
}

// GetDetailsLen 获取出库数量
func (r *outboundTicketRepository) GetDetailsLen(ctx context.Context, ticketID uint) (uint, error) {
	var (
		amount int64
	)
	err := r.db.WithContext(ctx).Model(&outbound.OutboundDetail{}).Where("spare_outbound_ticket_id = ?", ticketID).Count(&amount).Error
	if err != nil {
		return 0, err
	}
	if amount < 0 {
		return 0, fmt.Errorf("数量不能为负数: %d", amount)
	}
	// 检查整数是否超出 uint 的最大范围
	if uint64(amount) > uint64(^uint(0)) {
		return 0, fmt.Errorf("数量超出范围: %d", amount)
	}
	return uint(amount), nil
}

func (r *outboundTicketRepository) GetDetailByID(ctx context.Context, detailID uint) (*outbound.OutboundDetail, error) {
	var detail outbound.OutboundDetail
	err := r.db.WithContext(ctx).Model(&outbound.OutboundDetail{}).Where("id = ?", detailID).First(&detail).Error

	return &detail, err
}
func (r *outboundTicketRepository) GetDB() (*gorm.DB, error) {
	if r.db == nil {
		return nil, fmt.Errorf("获取数据库示例失败")
	}
	return r.db, nil
}

func (r *outboundTicketRepository) CreateDeviceLifeCycleLog(ctx context.Context, ticket *outbound.SpareOutboundTicket, devices []asset.Device, oldAssetStatus string) error {
	var (
		assetStatusLogs []asset.StatusChangeLog
		changeReason    string
	)
	tx := utils.GetDB(ctx, r.db)
	if tx == r.db {
		return fmt.Errorf("创建生命周期数据时未在事务操作中")
	}
	switch ticket.OutboundReason {
	case common.OutboundReasonRack:
		changeReason = fmt.Sprintf("上架出库，项目：%s，出库位置：%s", ticket.Project, ticket.SourceLocation)
	case common.OutboundReasonAllocate:
		changeReason = fmt.Sprintf("调拨出库，项目：%s，出库位置：%s，目标位置：%s", ticket.Project, ticket.SourceLocation, ticket.DestLocation)
	}
	for _, device := range devices {
		assetStatusLog := asset.StatusChangeLog{
			AssetID:           device.ID,
			ChangeReason:      changeReason,
			OperatorID:        ticket.ReporterID,
			OperatorName:      ticket.ReporterName,
			OldAssetStatus:    oldAssetStatus,
			NewAssetStatus:    device.AssetStatus,
			OldBizStatus:      device.Resource.BizStatus,
			NewBizStatus:      device.Resource.BizStatus,
			NewHardwareStatus: device.HardwareStatus,
			TicketNo:          ticket.TicketNo,
			WorkflowID:        "device_outbound_" + strconv.FormatUint(uint64(ticket.ID), 10),
			Source:            constants.ChangeTypeOutbound,
		}
		assetStatusLogs = append(assetStatusLogs, assetStatusLog)
	}
	err := tx.Create(&assetStatusLogs).Error
	if err != nil {
		return fmt.Errorf("创建生命周期记录失败")
	}
	return nil
}
