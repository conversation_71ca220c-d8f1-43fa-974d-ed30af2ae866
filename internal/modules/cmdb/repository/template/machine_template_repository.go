package template

import (
	"context"

	"backend/internal/modules/cmdb/model/template"

	"gorm.io/gorm"
)

// MachineTemplateRepository 套餐模板仓库接口
type MachineTemplateRepository interface {
	// List 查询套餐模板列表
	List(ctx context.Context, page, pageSize int, query, category string) ([]*template.MachineTemplate, int64, error)
	// GetByID 根据ID获取套餐模板
	GetByID(ctx context.Context, id uint) (*template.MachineTemplate, error)
	// GetByName 根据名称获取套餐模板
	GetByName(ctx context.Context, name string) (*template.MachineTemplate, error)
	// Create 创建套餐模板
	Create(ctx context.Context, template *template.MachineTemplate) (*template.MachineTemplate, error)
	// Update 更新套餐模板
	Update(ctx context.Context, template *template.MachineTemplate) (*template.MachineTemplate, error)
	// Delete 删除套餐模板
	Delete(ctx context.Context, id uint) error
	// GetDB 获取数据库连接
	GetDB() *gorm.DB
}

// machineTemplateRepository 套餐模板仓库实现
type machineTemplateRepository struct {
	db *gorm.DB
}

// NewMachineTemplateRepository 创建套餐模板仓库
func NewMachineTemplateRepository(db *gorm.DB) MachineTemplateRepository {
	return &machineTemplateRepository{db: db}
}

// List 查询套餐模板列表
func (r *machineTemplateRepository) List(ctx context.Context, page, pageSize int, query, category string) ([]*template.MachineTemplate, int64, error) {
	var templates []*template.MachineTemplate
	var total int64

	db := r.db.WithContext(ctx)

	// 构建查询条件
	if query != "" {
		db = db.Where("template_name LIKE ?", "%"+query+"%")
	}

	if category != "" {
		db = db.Where("template_category = ?", category)
	}

	// 查询总数
	if err := db.Model(&template.MachineTemplate{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 查询数据
	if err := db.Preload("Components").Preload("Components.Product").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&templates).Error; err != nil {
		return nil, 0, err
	}

	return templates, total, nil
}

// GetByID 根据ID获取套餐模板
func (r *machineTemplateRepository) GetByID(ctx context.Context, id uint) (*template.MachineTemplate, error) {
	var temp template.MachineTemplate
	if err := r.db.WithContext(ctx).Preload("Components").Preload("Components.Product").
		First(&temp, id).Error; err != nil {
		return nil, err
	}
	return &temp, nil
}

// GetByName 根据名称获取套餐模板
func (r *machineTemplateRepository) GetByName(ctx context.Context, name string) (*template.MachineTemplate, error) {
	var temp template.MachineTemplate
	if err := r.db.WithContext(ctx).Where("template_name = ?", name).
		Preload("Components").Preload("Components.Product").
		First(&temp).Error; err != nil {
		return nil, err
	}
	return &temp, nil
}

// Create 创建套餐模板
func (r *machineTemplateRepository) Create(ctx context.Context, temp *template.MachineTemplate) (*template.MachineTemplate, error) {
	if err := r.db.WithContext(ctx).Create(temp).Error; err != nil {
		return nil, err
	}
	return temp, nil
}

// Update 更新套餐模板
func (r *machineTemplateRepository) Update(ctx context.Context, temp *template.MachineTemplate) (*template.MachineTemplate, error) {
	if err := r.db.WithContext(ctx).Model(temp).Updates(map[string]interface{}{
		"template_name":     temp.TemplateName,
		"cpu_model":         temp.CPUModel,
		"memory_capacity":   temp.MemoryCapacity,
		"gpu_model":         temp.GPUModel,
		"disk_type":         temp.DiskType,
		"component_list":    temp.ComponentList,
		"template_category": temp.TemplateCategory,
	}).Error; err != nil {
		return nil, err
	}
	return temp, nil
}

// Delete 删除套餐模板
func (r *machineTemplateRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&template.MachineTemplate{}, id).Error
}

// GetDB 获取数据库连接
func (r *machineTemplateRepository) GetDB() *gorm.DB {
	return r.db
}
