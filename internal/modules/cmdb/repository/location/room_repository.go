package location

import (
	"backend/internal/modules/cmdb/model/location" // 确保这个路径正确
	"context"
	"errors"

	"gorm.io/gorm"
)

// RoomRepository 房间仓库接口
type RoomRepository interface {
	Create(ctx context.Context, room *location.Room) error
	Update(ctx context.Context, room *location.Room) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*location.Room, error)
	GetByName(ctx context.Context, name string) (*location.Room, error)
	GetByDataCenterID(ctx context.Context, dataCenterID uint) ([]*location.Room, error)
	List(ctx context.Context, page, pageSize int, query string, dataCenterID uint) ([]*location.Room, int64, error)
}

// roomRepository 房间仓库实现
type roomRepository struct {
	db *gorm.DB
}

// NewRoomRepository 创建房间仓库
func NewRoomRepository(db *gorm.DB) RoomRepository {
	return &roomRepository{db: db}
}

// Create 创建房间
func (r *roomRepository) Create(ctx context.Context, room *location.Room) error {
	return r.db.WithContext(ctx).Create(room).Error
}

// Update 更新房间
func (r *roomRepository) Update(ctx context.Context, room *location.Room) error {
	var original location.Room
	if err := r.db.WithContext(ctx).First(&original, room.ID).Error; err != nil {
		return err
	}
	room.CreatedAt = original.CreatedAt
	return r.db.WithContext(ctx).Save(room).Error
}

// Delete 删除房间
func (r *roomRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&location.Room{}, id).Error
}

// GetByID 根据ID获取房间
func (r *roomRepository) GetByID(ctx context.Context, id uint) (*location.Room, error) {
	var room location.Room
	if err := r.db.WithContext(ctx).Preload("DataCenter.AZ.Region").First(&room, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("房间不存在")
		}
		return nil, err
	}
	return &room, nil
}

// GetByName 根据名称获取房间
func (r *roomRepository) GetByName(ctx context.Context, name string) (*location.Room, error) {
	var room location.Room
	if err := r.db.WithContext(ctx).Preload("DataCenter.AZ.Region").Where("name = ?", name).First(&room).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("房间不存在")
		}
		return nil, err
	}
	return &room, nil
}

// GetByDataCenterID 根据机房ID获取房间列表
func (r *roomRepository) GetByDataCenterID(ctx context.Context, dataCenterID uint) ([]*location.Room, error) {
	var rooms []*location.Room
	if err := r.db.WithContext(ctx).Where("data_center_id = ?", dataCenterID).Find(&rooms).Error; err != nil {
		return nil, err
	}
	return rooms, nil
}

// List 分页查询房间列表
func (r *roomRepository) List(ctx context.Context, page, pageSize int, query string, dataCenterID uint) ([]*location.Room, int64, error) {
	var rooms []*location.Room
	var total int64

	db := r.db.WithContext(ctx).Model(&location.Room{}).Preload("DataCenter.AZ.Region")

	if query != "" {
		db = db.Where("name LIKE ? OR floor LIKE ? OR description LIKE ?", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if dataCenterID > 0 {
		db = db.Where("data_center_id = ?", dataCenterID)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	if err := db.Find(&rooms).Error; err != nil {
		return nil, 0, err
	}

	return rooms, total, nil
}
