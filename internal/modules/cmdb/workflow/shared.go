package workflow

import (
	"time"
)

const (
	// 任务队列
	OutboundTicketTaskQueue = "OUTBOUND_TICKET_TASK_QUEUE"

	// 工作流ID前缀
	FaultTicketWorkflowIDPrefix  = "fault_ticket_"
	RepairTicketWorkflowIDPrefix = "repair_ticket_"

	// 活动超时设置
	ActivityStartToCloseTimeout = 7 * 24 * time.Hour
	ActivityHeartbeatTimeout    = 12 * time.Hour

	// 重试策略
	MaxAttempts        = 5
	InitialInterval    = 5 * time.Second
	BackoffCoefficient = 2.0
	MaximumInterval    = 2 * time.Hour

	// 定义信号名称常量
	WorkflowControlSignalName = "workflow_control_signal" // 工作流控制信号名称
)

// OutboundTicketWorkflowInput 出库单工作流输入
type OutboundTicketWorkflowInput struct {
	TicketID     uint   `json:"ticket_id"`
	ReporterID   uint   `json:"reporter_id"`
	ReporterName string `json:"reporter_name"`
	//DeviceID         *uint  `json:"device_id"` // 可能为空的设备ID
	//ComponentID      *uint  `json:"component_id"`
	//ResourceID       *uint  `json:"resource_id"`
	OutboundType   string `json:"outbound_type"`
	OutboundReason string `json:"outbound_reason"`
	OutboundIds    string `json:"outbound_ids"`
	//FaultDescription string `json:"fault_description"`
	//Symptom          string `json:"symptom"`
	//SlotPosition     string `json:"slot_position"`
	//Priority         string `json:"priority"`
	//Source           string `json:"source"`
	OperatorID   uint   `json:"operator_id"`   // 操作人ID
	OperatorName string `json:"operator_name"` // 操作人姓名
	Completed    bool   `json:"completed"`     // 是否已完成

	// 扩展字段 - 保存报障单的更多信息
	TicketNo string `json:"ticket_no"` // 工单号
	Status   string `json:"status"`    // 当前状态
	//Title              string `json:"title"`               // 故障标题
	//DeviceSN           string `json:"device_sn"`           // 设备序列号
	//ComponentSN        string `json:"component_sn"`        // 组件序列号
	//ComponentType      string `json:"component_type"`      // 组件类型
	//ResourceIdentifier string `json:"resource_identifier"` // 资源标识符
	RequireApproval bool `json:"require_approval"` // 是否需要客户审批
}

// DiagnosisResult 诊断结果
type DiagnosisResult struct {
	RepairType              string `json:"repair_type"`
	DiagnosisResult         string `json:"diagnosis_result"`
	RequireCustomerApproval bool   `json:"require_customer_approval"`
	ApprovalType            string `json:"approval_type"`
	EstimatedRepairTime     int    `json:"estimated_repair_time"` // 分钟
}

// CustomerApprovalResult 客户审批结果
type CustomerApprovalResult struct {
	Status        string    `json:"status"`
	ResponseTime  time.Time `json:"response_time"`
	Comments      string    `json:"comments"`
	ApproverCount int       `json:"approver_count"`
}

// VerificationResult 验证结果
type VerificationResult struct {
	Success          bool      `json:"success"`
	VerificationTime time.Time `json:"verification_time"`
	Comments         string    `json:"comments"`
}

// InventoryAvailabilityResult 库存可用性结果
type InventoryAvailabilityResult struct {
	Available   bool   `json:"available"`
	ProductID   uint   `json:"product_id"`
	Quantity    int    `json:"quantity"`
	ProductName string `json:"product_name"`
}

// RepairResult 维修结果
type RepairResult struct {
	RepairResult string `json:"repair_result"`
	Solution     string `json:"solution"`
	RepairSteps  string `json:"repair_steps"`
}

// RepairSelectionInput 维修方式选择输入
type RepairSelectionInput struct {
	TicketID                uint   `json:"ticket_id"`
	RepairType              string `json:"repair_type"`
	Diagnosis               string `json:"diagnosis"`
	Comments                string `json:"comments"`
	OperatorID              uint   `json:"operator_id"`
	OperatorName            string `json:"operator_name"`
	RequireCustomerApproval bool   `json:"require_customer_approval"`
}

// WorkflowControlSignal 工作流控制信号
type WorkflowControlSignal struct {
	Stage        string                 `json:"stage"`         // 要进入的阶段
	OperatorID   uint                   `json:"operator_id"`   // 操作人ID
	OperatorName string                 `json:"operator_name"` // 操作人姓名
	Comments     string                 `json:"comments"`      // 操作说明
	Data         map[string]interface{} `json:"data"`          // 可能需要的附加数据
}

type OutboundResult struct {
	Sns string `json:"sns"`
	//Errors  []error `json:"errors"`
	Success bool `json:"success"`
}

func (o OutboundResult) Error() string {
	return ""
}

// IsValidStatusTransition 检查状态转换是否合法
func IsValidStatusTransition(currentStatus, newStatus string) bool {
	// 定义状态转换映射
	validTransitions := map[string][]string{
		StatusWaitingApproval: {StatusOutbounding, StatusRejected, StatusEngineerApprovalPass, StatusEngineerApprovalFail, StatusAssetApprovalPass, StatusAssetApprovalFail},
		//StatusWaitingApproval: {StatusWaitingSecondApproval, StatusOutbounding},
		//StatusWaitingSecondApproval: {StatusOutbounding},
		//StatusOutbounding:      {StatusCompleteOutbound},
		StatusOutbounding: {StatusCompleted, StatusRejected},
		//StatusCompleteOutbound: {StatusCompleted},

		// 出库
		StatusWaitingEngineerApproval:  {StatusEngineerApprovalPass, StatusEngineerApprovalFail},   // 专业工程师
		StatusWaitingAssetApproval:     {StatusAssetApprovalPass, StatusAssetApprovalFail},         // 仓库管理员
		StatusWaitingReplaceOperate:    {StatusReplaceApprovalPass, StatusReplaceApprovalFail},     // 改配负责人
		StatusWaitingAssetDestApproval: {StatusAssetDestApprovalPass, StatusAssetDestApprovalFail}, // 收货库管
		StatusWaitingBuyerApproval:     {StatusBuyerApprovalPass, StatusBuyerApprovalFail},         // 买方
	}

	// 任何状态都可以转换为相同的状态（无变化）
	if currentStatus == newStatus {
		return true
	}

	// 检查目标状态是否在当前状态允许的转换列表中
	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}

	return false
}
