package workflow

import (
	"fmt"
)

// 状态转换规则
var ValidStatusTransitions = map[string][]string{
	//StatusWaitingAccept:       {StatusInvestigating, StatusCancelled},
	//StatusInvestigating:       {StatusWaitingApproval, StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing, StatusCompleted, StatusCancelled},
	//StatusWaitingApproval:     {StatusApprovedWaiting, StatusCancelled},
	//StatusApprovedWaiting:     {StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing, StatusCancelled},
	//StatusRepairing:           {StatusWaitingVerification, StatusCancelled},
	//StatusRestarting:          {StatusWaitingVerification, StatusCancelled},
	//StatusMigrating:           {StatusWaitingVerification, StatusCancelled},
	//StatusSoftwareFixing:      {StatusWaitingVerification, StatusCancelled},
	//StatusWaitingVerification: {StatusSummarizing, StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing, StatusCancelled},
	//StatusSummarizing:         {StatusCompleted, StatusCancelled},
}

// StatusTransitionError 状态转换错误
type StatusTransitionError struct {
	CurrentStatus string
	NewStatus     string
	Message       string
}

func (e *StatusTransitionError) Error() string {
	return fmt.Sprintf("无效的状态转换: 从 %s 到 %s - %s", e.CurrentStatus, e.NewStatus, e.Message)
}

// ValidateStageTransition 验证工作流阶段转换是否有效
func ValidateStageTransition(currentStatus, stage string) (bool, string) {
	validStages := make([]string, 0)
	switch currentStatus {
	case StatusWaitingApproval:
		validStages = []string{StageCustomerApproval, StageStartOutbound, StageRejected}
	//case StatusWaitingSecondApproval:
	//	validStages = []string{StageSecondApproval}
	case StatusOutbounding:
		validStages = []string{StageStartOutbound, StageRejected}
	case StatusRejected:
		validStages = []string{StageRejected}
	//case StatusCompleteOutbound:
	//	validStages = []string{StageCompleteOutbound}
	case StatusCompleted:
		validStages = []string{StageCompleteTicket}
		//case StatusCancelled:
		//	validStages = []string{StageCompleteTicket}
	}

	for _, validStage := range validStages {
		if validStage == stage {
			return true, ""
		}
	}

	validStagesStr := ""
	for i, stage := range validStages {
		if i > 0 {
			validStagesStr += ", "
		}
		validStagesStr += stage
	}

	return false, fmt.Sprintf("当前状态[%s]只允许触发以下阶段: %s", currentStatus, validStagesStr)
}
