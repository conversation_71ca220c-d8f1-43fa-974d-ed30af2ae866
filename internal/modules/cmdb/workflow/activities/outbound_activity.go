package activities

import (
	"backend/internal/modules/cmdb/model/outbound/common"
	"context"
)

// 公用
type outboundActivities interface {
	// 更新工单和历史记录
	UpdateTicketAndHistory(ctx context.Context, input common.UpdateTicketInput) error
	// 更新CMDB
	UpdateCMDB(ctx context.Context, outboundReason string, TicketID uint) error
	// 发送飞书通知
	SendOutboundMsg(ctx context.Context, outboundNo string) error
	// 发送飞书通知到保安群
	SendOutboundMsgToToSecurityGuard(ctx context.Context, outboundNo string) error

	// 转换至中间状态
	TransitionToMiddleStatus(ctx context.Context, outboundReason, MiddleStatus string, TicketID uint) error
	// 回退状态
	RevertStatus(ctx context.Context, outboundReason, Status string, TicketID uint) error
}

// 配件出库
type OutboundPartActivities interface {
	outboundActivities
	UpdateRelatedDeviceSN(ctx context.Context, TicketID uint) error
}

// 设备出库（服务器、网络设备）
type OutboundDeviceActivities interface {
	outboundActivities
}
