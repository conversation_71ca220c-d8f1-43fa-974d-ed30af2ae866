package activities

import (
	"backend/internal/common/utils/notifier"
	"backend/internal/modules/cmdb/model/outbound"
	"backend/internal/modules/cmdb/model/outbound/common"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"
	outboundSrv "backend/internal/modules/cmdb/service/outbound"
	cmdbWorkflow "backend/internal/modules/cmdb/workflow"
	fileSvc "backend/internal/modules/file/service"
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

type deviceOutboundActivities struct {
	db              *gorm.DB
	outboundSvc     outboundSrv.OutboundTicketService
	fileSvc         fileSvc.FileService
	inventorySvc    inventorySvc.InventoryService
	inboundNotifier *notifier.InboundNotifier
}

func (d deviceOutboundActivities) TransitionToMiddleStatus(ctx context.Context, outboundReason, MiddleStatus string, TicketID uint) error {
	return d.outboundSvc.TransitionToMiddleStatus(ctx, cmdbWorkflow.OutboundTypeDevice, outboundReason, MiddleStatus, TicketID)
}

func (d deviceOutboundActivities) RevertStatus(ctx context.Context, outboundReason, Status string, TicketID uint) error {
	return d.outboundSvc.RevertStatus(ctx, cmdbWorkflow.OutboundTypeDevice, outboundReason, Status, TicketID)
}

func (d deviceOutboundActivities) SendOutboundMsgToToSecurityGuard(ctx context.Context, outboundNo string) error {
	var (
		photoInfos []string
		photo      string
	)
	ticket, err := d.outboundSvc.GetOutboundTicketByTicketNo(ctx, outboundNo)
	if err != nil {
		return fmt.Errorf("获取飞书通知前置信息失败: %v", err)
	}
	details, err := d.outboundSvc.GetTicketDetailsByID(ctx, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取飞书通知前置详情信息失败: %v", err)
	}
	files, err := d.fileSvc.GetFilesByModule("outbound-ticket-photo", ticket.ID)
	if err != nil {
		return fmt.Errorf("获取出入室图片失败: %v", err)
	}
	for i, file := range files {
		photoInfo := fmt.Sprintf("[图片%d](%s)", i+1, file.URL)
		photoInfos = append(photoInfos, photoInfo)
	}
	photo = strings.Join(photoInfos, "\n")
	// 发送飞书通知
	err = d.inboundNotifier.SendOutboundMsgToSecurityGuard(ticket, details, photo)
	if err != nil {
		return fmt.Errorf("发送飞书信息到保安群失败: %v", err)
	}
	return nil
}

//func (d deviceOutboundActivities) UpdateCMDB(ctx context.Context, outboundReason string, TicketID uint) error {
//	var deviceSns []string
//	ticket, err := d.outboundSvc.GetOutboundTicketByID(ctx, TicketID)
//	if err != nil {
//		return err
//	}
//	details, err := d.outboundSvc.GetTicketDetailsByID(ctx, TicketID)
//	if err != nil {
//		return err
//	}
//	infos, err := d.outboundSvc.GetTicketInfoByID(ctx, TicketID)
//	if err != nil {
//		return err
//	}
//
//	for _, detail := range details {
//		if detail.DeviceSN == "" {
//			return fmt.Errorf("出库工单详情中设备SN不能为空, TicketID: %d, DetailID: %d", TicketID, detail.ID)
//		}
//		deviceSns = append(deviceSns, detail.DeviceSN)
//	}
//	switch outboundReason {
//	case common.OutboundReasonRack: // 上架出库
//		err = d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
//			err = tx.Model(&asset.Device{}).Where("sn IN ?", deviceSns).Updates(map[string]interface{}{
//				"asset_status": constants.AssetStatusOutStock,
//			}).Error
//			if err != nil {
//				return fmt.Errorf("更新设备状态失败: %w", err)
//			}
//			// 把配件也一起出库了
//			err = tx.Model(&asset.AssetSpare{}).
//				Where("related_asset_sn IN ?", deviceSns).
//				Updates(map[string]interface{}{
//					"asset_status": constants.AssetStatusInUse,
//				}).Error
//			if err != nil {
//				return fmt.Errorf("更新配件状态失败: %w", err)
//			}
//			// 更新库存
//			for _, info := range infos {
//				purpose := fmt.Sprintf("设备上架出库: %s, 数量: %d", ticket.TicketNo, info.Amount)
//				inventoryDetail, err := d.inventorySvc.GetByProductAndWarehouse(ctx, info.ProductID, ticket.SourceWarehouseID)
//				if err != nil {
//					return err
//				}
//				ctx = context.WithValue(ctx, constants.ContextKeyUserID, ticket.ReporterID)
//				ctx = context.WithValue(ctx, constants.ContextKeyUserInfo, map[string]interface{}{"realName": ticket.ReporterName})
//				err = d.inventorySvc.AllocateStock(ctx, inventoryDetail.ID, info.Amount, purpose)
//				if err != nil {
//					return err
//				}
//			}
//
//			return nil
//
//		})
//		if err != nil {
//			return err
//		}
//	case common.OutboundReasonAllocate: // 调拨出库
//		var (
//			roomID  uint
//			project string
//		)
//		err = d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
//			// 获取调拨目的地所属项目
//			if err := d.db.Model(&asset.Warehouse{}).Select("room_id").Where("id = ?", ticket.DestWarehouseID).First(&roomID).Error; err != nil {
//				return fmt.Errorf("获取仓库房间ID失败: %w", err)
//			}
//			err := tx.Model(&locationModel.Room{}).
//				Select("regions.name").
//				Joins("JOIN data_centers ON rooms.data_center_id = data_centers.id").
//				Joins("JOIN azs ON data_centers.az_id = azs.id").
//				Joins("JOIN regions ON azs.region_id = regions.id").
//				Where("rooms.id = ?", roomID).
//				Scan(&project).Error
//			if err != nil {
//				return fmt.Errorf("获取调拨目的地所属项目失败: %w", err)
//			}
//			// 调拨设备
//			err = tx.Model(&asset.Resource{}).Joins("left join asset_devices on asset_devices.id=resources.asset_id").Where("sn IN ?", deviceSns).
//				Updates(map[string]interface{}{
//					"cabinet_id": sql.NullInt64{
//						Int64: 0,
//						Valid: false, // 关键：告诉 GORM 这是 NULL
//					},
//					"room_id": roomID,
//					"project": project,
//				}).Error
//			if err != nil {
//				return fmt.Errorf("更新设备位置失败: %w", err)
//			}
//			// 把配件也一起调拨了
//			err = tx.Model(&asset.AssetSpare{}).
//				Where("related_asset_sn IN ?", deviceSns).
//				Updates(map[string]interface{}{
//					"warehouse_id": ticket.DestWarehouseID,
//				}).Error
//			if err != nil {
//				return fmt.Errorf("更新配件位置失败: %w", err)
//			}
//			return nil
//		})
//		if err != nil {
//			return err
//		}
//		// 更新库存
//		for _, info := range infos {
//			reason := fmt.Sprintf("调拨出库: %s，从 %s 调拨到：%s", ticket.TicketNo, ticket.SourceLocation, ticket.DestLocation)
//			detail, err := d.inventorySvc.GetByProductAndWarehouse(ctx, info.ProductID, ticket.SourceWarehouseID)
//			if err != nil {
//				return err
//			}
//			// 更新设备库存
//			err = d.inventorySvc.AdjustStock(ctx, detail.ID, info.Amount, constants.ChangeTypeOutbound, reason, 0, TicketID)
//			if err != nil {
//				fmt.Println("更新库存失败:", err)
//			}
//			detail, err = d.inventorySvc.GetByProductAndWarehouse(ctx, info.ProductID, ticket.DestWarehouseID)
//			if err != nil {
//				return err
//			}
//			// 更新库存设备库存
//			err = d.inventorySvc.AdjustStock(ctx, detail.ID, info.Amount, constants.ChangeTypeInbound, reason, 0, TicketID)
//			if err != nil {
//				fmt.Println("更新库存失败:", err)
//			}
//
//			// TODO 更新备件库存
//		}
//	default:
//		return fmt.Errorf("不支持的出库原因: %s", outboundReason)
//	}
//	return nil
//}

func (d deviceOutboundActivities) UpdateCMDB(ctx context.Context, outboundReason string, ticketID uint) error {
	return d.outboundSvc.UpdateCMDB(ctx, cmdbWorkflow.OutboundTypeDevice, outboundReason, ticketID)
}

func (d deviceOutboundActivities) UpdateTicketAndHistory(ctx context.Context, input common.UpdateTicketInput) error {
	ticket, err := d.outboundSvc.GetOutboundTicketByID(ctx, input.TicketID)
	if err != nil {
		return err
	}
	history := &outbound.OutboundTicketStatusHistory{
		OutboundTicketID: ticket.ID,
		OperatorID:       input.OperatorID,
		OperatorName:     input.OperatorName,
		Stage:            input.CurrentStage,
		NewStatus:        input.CurrentStatus,
		PreviousStatus:   ticket.Status,
		OperationTime:    time.Now(),
		Remarks:          input.Comments,
	}
	ticket.Status = input.NextStatus
	ticket.Stage = input.Stage

	err = d.outboundSvc.UpdateTicketAndHistory(ctx, ticket, history)
	if err != nil {
		return fmt.Errorf("更新出库工单失败: %w", err)
	}
	return nil
}

func (d deviceOutboundActivities) UpdateInboundListStage(ctx context.Context, inboundNo, Stage string) error {
	//TODO implement me
	panic("implement me")
}

func (d deviceOutboundActivities) SendOutboundMsg(ctx context.Context, outboundNo string) error {
	ticket, err := d.outboundSvc.GetOutboundTicketByTicketNo(ctx, outboundNo)
	if err != nil {
		return fmt.Errorf("获取飞书通知前置信息失败: %v", err)
	}
	history, err := d.outboundSvc.GetOutboundTicketStatusHistory(ctx, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取出库历史记录失败: %v", err)
	}
	details, err := d.outboundSvc.GetTicketDetailsByID(ctx, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取出库详情记录失败: %v", err)
	}
	amount, err := d.outboundSvc.GetDetailsLen(ctx, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取出库数量失败: %v", err)
	}
	err = d.inboundNotifier.SendOutboundNotification(ticket, details, history, amount)
	if err != nil {
		return fmt.Errorf("发送飞书通知失败: %v", err)
	}
	return nil
}

func InitDeviceOutboundActivities(db *gorm.DB, outboundSvc outboundSrv.OutboundTicketService, fileSvc fileSvc.FileService, inventorySvc inventorySvc.InventoryService, inboundNotifier *notifier.InboundNotifier) OutboundDeviceActivities {
	return &deviceOutboundActivities{
		db:              db,
		outboundSvc:     outboundSvc,
		fileSvc:         fileSvc,
		inventorySvc:    inventorySvc,
		inboundNotifier: inboundNotifier,
	}
}
