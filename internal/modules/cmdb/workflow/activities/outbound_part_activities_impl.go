package activities

import (
	"backend/internal/common/constants"
	"backend/internal/common/utils/notifier"
	"backend/internal/modules/cmdb/model/inventory"
	"backend/internal/modules/cmdb/model/outbound"
	"backend/internal/modules/cmdb/model/outbound/common"
	assetService "backend/internal/modules/cmdb/service/asset"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"
	outboundSrv "backend/internal/modules/cmdb/service/outbound"
	cmdbWorkflow "backend/internal/modules/cmdb/workflow"
	fileService "backend/internal/modules/file/service"
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

var trans = map[string]string{
	constants.ChangeReasonReplace:      "改配出库",
	constants.ChangeReasonRepair:       "维修出库",
	constants.ChangeReasonReturnRepair: "返修出库",
}

/* 重写配件入库Activities */
type partOutboundActivities struct {
	db              *gorm.DB
	outboundSvc     outboundSrv.OutboundTicketService
	assetSpareSvc   assetService.SpareService
	inventorySvc    inventorySvc.InventoryService
	fileSvc         fileService.FileService
	inboundNotifier *notifier.InboundNotifier
}

// TransitionToMiddleStatus 转换到中间状态
func (p partOutboundActivities) TransitionToMiddleStatus(ctx context.Context, outboundReason, MiddleStatus string, TicketID uint) error {
	return p.outboundSvc.TransitionToMiddleStatus(ctx, cmdbWorkflow.OutboundTypePart, outboundReason, MiddleStatus, TicketID)
}

// RevertStatus 从中间状态回退
func (p partOutboundActivities) RevertStatus(ctx context.Context, outboundReason, Status string, TicketID uint) error {
	return p.outboundSvc.RevertStatus(ctx, cmdbWorkflow.OutboundTypePart, outboundReason, Status, TicketID)
}

func (p partOutboundActivities) UpdateTicketAndHistory(ctx context.Context, input common.UpdateTicketInput) error {
	ticket, err := p.outboundSvc.GetOutboundTicketByID(ctx, input.TicketID)
	if err != nil {
		return err
	}
	history := &outbound.OutboundTicketStatusHistory{
		OutboundTicketID: ticket.ID,
		OperatorID:       input.OperatorID,
		OperatorName:     input.OperatorName,
		Stage:            input.CurrentStage,
		NewStatus:        input.CurrentStatus,
		PreviousStatus:   ticket.Status,
		OperationTime:    time.Now(),
		Remarks:          input.Comments,
	}
	ticket.Status = input.NextStatus
	ticket.Stage = input.Stage

	err = p.outboundSvc.UpdateTicketAndHistory(ctx, ticket, history)
	if err != nil {
		return fmt.Errorf("更新出库工单失败: %w", err)
	}
	return nil
}

// UpdateCMDB 最终更新操作
//func (p partOutboundActivities) UpdateCMDB(ctx context.Context, outboundReason string, TicketID uint) error {
//	var (
//		componentSns []string
//	)
//	ticket, err := p.outboundSvc.GetOutboundTicketByID(ctx, TicketID)
//	if err != nil {
//		return err
//	}
//	details, err := p.outboundSvc.GetTicketDetailsByID(ctx, TicketID)
//	if err != nil {
//		return fmt.Errorf("获取出库详情失败: %w", err)
//	}
//	for _, detail := range details {
//		componentSns = append(componentSns, detail.ComponentSN)
//	}
//	infos, err := p.outboundSvc.GetTicketInfoByID(ctx, TicketID)
//	if err != nil {
//		return fmt.Errorf("获取出库信息失败: %w", err)
//	}
//	spares, err := p.assetSpareSvc.GetSpareBySNs(ctx, componentSns)
//	if err != nil {
//		return fmt.Errorf("获取备件信息失败: %w", err)
//	}
//
//	ctx = context.WithValue(ctx, constants.ContextKeyUserID, ticket.ReporterID)
//	ctx = context.WithValue(ctx, constants.ContextKeyUserInfo, map[string]interface{}{"realName": ticket.ReporterName})
//
//	// 启动事务操作
//	err = p.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
//		ctx = context.WithValue(ctx, constants.ContextKeyTX, tx)
//		switch outboundReason {
//		case constants.ChangeReasonReplace: // 改配
//			for i := range spares {
//				spares[i].RelatedAssetSN = details[i].DeviceSN
//				spares[i].AssetStatus = constants.AssetStatusInUse
//				//spare.SourceType = constants.SourceTypeReplace
//			}
//			// 更新库存历史
//			for _, info := range infos {
//				// 目标仓库数量减少
//				err = p.AllocateInventory(ctx, ticket.TicketNo, info.ProductID, ticket.SourceWarehouseID, info.Amount, outboundReason)
//				if err != nil {
//					return err
//				}
//			}
//
//		case common.OutboundReasonAllocate: // 调拨
//			for i := range spares {
//				spares[i].WarehouseID = ticket.DestWarehouseID
//				spares[i].Location = ""
//				spares[i].AssetStatus = constants.AssetStatusIdle
//				spares[i].SourceType = constants.SourceTypeAllocate
//				//spare.SourceType = constants.SourceTypeAllocate
//			}
//
//			for _, info := range infos {
//				// 先恢复中间状态产生的修改
//
//				// 原仓库库存减少
//				changeReason := fmt.Sprintf("调拨出库：%s , 数量：%d", ticket.TicketNo, info.Amount)
//				err = p.adjustInventory(ctx, ticket.TicketNo, info.ProductID, ticket.SourceWarehouseID, constants.ChangeTypeOutbound, changeReason)
//				if err != nil {
//					return err
//				}
//				changeReason = fmt.Sprintf("调拨入库：%s , 数量：%d", ticket.TicketNo, info.Amount)
//				err = p.adjustInventory(ctx, ticket.TicketNo, info.ProductID, ticket.DestWarehouseID, constants.ChangeTypeInbound, changeReason)
//				if err != nil {
//					return err
//				}
//			}
//		case common.OutboundReasonReturnRepair: // 返修出库
//			for i := range spares {
//				spares[i].RelatedAssetSN = ""
//				spares[i].RelatedAssetID = 0
//				spares[i].Location = ""
//				spares[i].AssetStatus = constants.AssetStatusRepairing
//				spares[i].HardwareStatus = constants.HardwareStatusFaulty
//			}
//			// 更新库存历史
//			for _, info := range infos {
//				// 原仓库库存减少
//				err = p.AllocateInventory(ctx, ticket.TicketNo, info.ProductID, ticket.SourceWarehouseID, info.Amount, outboundReason)
//				if err != nil {
//					return err
//				}
//			}
//		case common.OutboundReasonRepair: // 维修
//			for i := range spares {
//				spares[i].RelatedAssetSN = ticket.DeviceSN
//				spares[i].AssetStatus = constants.AssetStatusInUse
//				spares[i].HardwareStatus = constants.HardwareStatusNormal
//			}
//			// 更新库存历史
//			for _, info := range infos {
//				// 目标仓库数量减少
//				err := p.AllocateInventory(ctx, ticket.TicketNo, info.ProductID, ticket.SourceWarehouseID, info.Amount, outboundReason)
//				if err != nil {
//					return err
//				}
//			}
//
//		case common.OutboundReasonSell: // 售卖
//			for i := range spares {
//				spares[i].RelatedAssetSN = ""
//				spares[i].RelatedAssetID = 0
//				spares[i].AssetStatus = constants.AssetStatusSoldOut
//			}
//			changeReason := fmt.Sprintf("售卖出库：%s", ticket.TicketNo)
//			// 更新库存历史
//			for _, info := range infos {
//				// 目标仓库数量减少
//				err := p.adjustInventory(ctx, ticket.TicketNo, info.ProductID, ticket.SourceWarehouseID, constants.ChangeTypeOutbound, changeReason)
//				if err != nil {
//					return err
//				}
//			}
//		default:
//			return fmt.Errorf("不支持的出库原因")
//		}
//		// 更新备件状态
//		err = p.assetSpareSvc.UpdateSpares(ctx, spares)
//		if err != nil {
//			return err
//		}
//		return nil
//	})
//	if err != nil {
//		return err
//	}
//	return nil
//}

func (p partOutboundActivities) UpdateCMDB(ctx context.Context, outboundReason string, ticketID uint) error {
	return p.outboundSvc.UpdateCMDB(ctx, cmdbWorkflow.OutboundTypePart, outboundReason, ticketID)
}

func (p partOutboundActivities) SendOutboundMsg(ctx context.Context, outboundNo string) error {
	ticket, err := p.outboundSvc.GetOutboundTicketByTicketNo(ctx, outboundNo)
	if err != nil {
		return fmt.Errorf("获取飞书通知前置信息失败: %v", err)
	}
	history, err := p.outboundSvc.GetOutboundTicketStatusHistory(ctx, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取出库历史记录失败: %v", err)
	}
	details, err := p.outboundSvc.GetTicketDetailsByID(ctx, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取出库详情记录失败: %v", err)
	}
	amount, err := p.outboundSvc.GetDetailsLen(ctx, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取出库数量失败: %v", err)
	}
	err = p.inboundNotifier.SendOutboundNotification(ticket, details, history, amount)
	if err != nil {
		return fmt.Errorf("发送飞书通知失败: %v", err)
	}
	return nil
}

// SendOutboundMsgToToSecurityGuard 发送出库飞书通知到保安群
func (p partOutboundActivities) SendOutboundMsgToToSecurityGuard(ctx context.Context, outboundNo string) error {
	var (
		photoInfos []string
		photo      string
	)
	ticket, err := p.outboundSvc.GetOutboundTicketByTicketNo(ctx, outboundNo)
	if err != nil {
		return fmt.Errorf("获取飞书通知前置信息失败: %v", err)
	}
	details, err := p.outboundSvc.GetTicketDetailsByID(ctx, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取飞书通知前置详情信息失败: %v", err)
	}
	files, err := p.fileSvc.GetFilesByModule("outbound-ticket-photo", ticket.ID)
	if err != nil {
		return fmt.Errorf("获取出入室图片失败: %v", err)
	}
	for i, file := range files {
		photoInfo := fmt.Sprintf("[图片%d](%s)", i+1, file.URL)
		photoInfos = append(photoInfos, photoInfo)
	}
	photo = strings.Join(photoInfos, "\n")
	// 发送飞书通知
	err = p.inboundNotifier.SendOutboundMsgToSecurityGuard(ticket, details, photo)
	if err != nil {
		return fmt.Errorf("发送飞书信息到保安群失败: %v", err)
	}
	return nil
}

func (p partOutboundActivities) AllocateInventory(ctx context.Context, ticketNo string, productID, warehouseID uint, delta int, reason string) error {
	var (
		detail  inventory.InventoryDetail
		purpose string
	)
	err := p.db.Model(&inventory.InventoryDetail{}).Where("product_id = ? AND warehouse_id = ?", productID, warehouseID).First(&detail).Error
	if err != nil {
		return fmt.Errorf("获取库存详情失败: %w", err)
	}
	purpose = fmt.Sprintf("出库工单号: %s,出库原因：%s, 出库数量: %d", ticketNo, trans[reason], delta)
	err = p.inventorySvc.AllocateStock(ctx, detail.ID, delta, purpose)
	if err != nil {
		return err
	}
	return nil
}

func (p partOutboundActivities) ReleaseInventory(ctx context.Context, ticketNo string, productID, warehouseID uint, delta int) error {
	var (
		detail inventory.InventoryDetail
	)
	err := p.db.Model(&inventory.InventoryDetail{}).Where("product_id = ? AND warehouse_id = ?", productID, warehouseID).First(&detail).Error
	if err != nil {
		return fmt.Errorf("获取库存详情失败: %w", err)
	}
	err = p.inventorySvc.ReleaseAllocatedStock(ctx, detail.ID, delta)
	if err != nil {
		return err
	}
	return nil
}

//func (p partOutboundActivities) adjustInventory(ctx context.Context, ticketNo string, productID, warehouseID uint, changeType, reason string) error {
//	var (
//		detail inventory.InventoryDetail
//	)
//	ticket, err := p.outboundSvc.GetOutboundTicketByTicketNo(ctx, ticketNo)
//	if err != nil {
//		return err
//	}
//	err = p.db.Model(&inventory.InventoryDetail{}).Where("product_id = ? AND warehouse_id = ?", productID, warehouseID).First(&detail).Error
//	if err != nil {
//		return fmt.Errorf("获取库存详情失败: %w", err)
//	}
//	for _, info := range ticket.Info {
//		// 更新库存
//		err = p.inventorySvc.AdjustStock(ctx, detail.ID, info.Amount, changeType, reason, 0, ticket.ID)
//		if err != nil {
//			fmt.Println("更新库存失败:", err)
//		}
//	}
//	return nil
//}

func (p partOutboundActivities) UpdateRelatedDeviceSN(ctx context.Context, TicketID uint) error {
	var componentSns []string
	details, err := p.outboundSvc.GetTicketDetailsByID(ctx, TicketID)
	if err != nil {
		return err
	}
	for i, detail := range details {
		if detail.DeviceSN == "" {
			return fmt.Errorf("出库详情中第 %d 条记录的设备SN不能为空", i+1)
		}
		componentSns = append(componentSns, detail.ComponentSN)
	}
	spares, err := p.assetSpareSvc.GetSpareBySNs(ctx, componentSns)
	if err != nil {
		return fmt.Errorf("获取备件信息失败: %w", err)
	}
	for i := range spares {
		spares[i].RelatedAssetSN = details[i].DeviceSN
	}
	// 更新备件状态
	err = p.assetSpareSvc.UpdateSpares(ctx, spares)
	if err != nil {
		return err
	}
	return nil
}

// 调拨，增加目标仓库数量
//func (p partOutboundActivities) updateInventoryAllocate(ctx context.Context, ticketID, productID, warehouseID uint, delta int, reason string) error {
//	var (
//		detail inventory.InventoryDetail
//	)
//	err := p.db.Model(&inventory.InventoryDetail{}).Where("product_id = ? AND warehouse_id = ?", productID, warehouseID).First(&detail).Error
//	if err != nil {
//		return fmt.Errorf("获取库存详情失败: %w", err)
//	}
//	err = p.inventorySvc.AdjustStock(ctx, detail.ID, delta, constants.ChangeTypeInbound, reason, 0, ticketID)
//	if err != nil {
//		return err
//	}
//	return nil
//}

func InitPartOutboundActivities(db *gorm.DB, outboundSvc outboundSrv.OutboundTicketService, assetSpareSvc assetService.SpareService, inventorySvc inventorySvc.InventoryService, fileSvc fileService.FileService, inboundNotifier *notifier.InboundNotifier) OutboundPartActivities {
	return &partOutboundActivities{
		db:              db,
		outboundSvc:     outboundSvc,
		assetSpareSvc:   assetSpareSvc,
		inventorySvc:    inventorySvc,
		fileSvc:         fileSvc,
		inboundNotifier: inboundNotifier,
	}
}
