package workflow

import (
	"backend/internal/modules/cmdb/model/outbound/common"
	"fmt"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

type DeviceOutboundState struct {
	OutboundTicketID uint
	OutboundNo       string
	CurrentStatus    string
	CurrentStage     string
}

// DeviceOutboundWorkflow 设备出库单工作流
func DeviceOutboundWorkflow(ctx workflow.Context, input common.DeviceOutboundWorkflowInput) error {
	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: common.ActivityStartToCloseTimeout,
		HeartbeatTimeout:    common.ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        common.InitialInterval,
			BackoffCoefficient:     common.BackoffCoefficient,
			MaximumInterval:        common.MaximumInterval,
			MaximumAttempts:        common.MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)
	// 创建工作流日志
	logger := workflow.GetLogger(ctx)
	logger.Info("初始化DeviceOboundWorkflow工作流日志成功")
	logger.Info("输入信息为", "DeviceOutboundInput", input)

	outboundState := DeviceOutboundState{
		OutboundTicketID: input.TicketID,
		OutboundNo:       input.TicketNo,
		CurrentStatus:    common.StatusWaitingAssetApproval,
		CurrentStage:     common.StageAssetApproval,
	}

	// 发送飞书通知
	workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo)

	var isWorkflowComplete bool
	completeChan := make(chan struct{})
	fmt.Println("开始注册UpdateHandler，接收更新信号")
	// 注册 SetUpdateHandler ，接收更新信号
	err := workflow.SetUpdateHandler(ctx, common.DeviceOutboundUpdateSignal, func(ctx workflow.Context, signal common.DeviceOutboundSignal) error {
		logger.Info("收到UpdateHandler信号",
			"DeviceOutboundTicketID", input.TicketID,
			"status", signal.Status,
			"operator", signal.OperatorName)
		ctx = workflow.WithActivityOptions(ctx, ao)
		// 处理信号
		switch signal.Status {
		case common.StatusEngineerApprovalPass:
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageEngineerApproval,
				CurrentStatus: common.StatusEngineerApprovalPass,
				Stage:         common.StageAssetApproval,
				NextStatus:    common.StatusWaitingAssetApproval,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo)
			// 更新内部状态
			outboundState.CurrentStatus = common.StatusWaitingAssetApproval
			outboundState.CurrentStage = common.StageAssetApproval

		case common.StatusEngineerApprovalFail:
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageEngineerApproval,
				CurrentStatus: common.StatusEngineerApprovalFail,
				Stage:         common.StageRejected,
				NextStatus:    common.StatusRejected,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 发送飞书通知
			workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo)
			// 终止流程
			isWorkflowComplete = true
			close(completeChan)

		case common.StatusAssetApprovalPass:
			switch input.OutboundReason {
			case OutboundReasonAllocate:
				outboundState.CurrentStage = common.StageAssetDestApproval
				outboundState.CurrentStatus = common.StatusWaitingAssetDestApproval
			default:
				outboundState.CurrentStage = common.StageCompleteOutbound
				outboundState.CurrentStatus = common.StatusCompleted
			}
			// 更新ticket和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageAssetApproval,
				CurrentStatus: common.StatusAssetApprovalPass,
				Stage:         outboundState.CurrentStage,
				NextStatus:    outboundState.CurrentStatus,
			}

			if outboundState.CurrentStage == common.StageCompleteOutbound {
				// 更新CMDB
				err := workflow.ExecuteActivity(ctx, "UpdateCMDB", input.OutboundReason, input.TicketID).Get(ctx, nil)
				if err != nil {
					return err
				}
				err = workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
				if err != nil {
					return err
				}

				// 发送飞书通知
				err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
				if err != nil {
					logger.Error("发送飞书通知失败", "err", err)
				}
				if sendToGuard(input.OutboundReason) {
					// 发送飞书通知到保安群
					err = workflow.ExecuteActivity(ctx, "SendOutboundMsgToToSecurityGuard", input.TicketNo).Get(ctx, nil)
					if err != nil {
						logger.Error("发送飞书通知失败", "err", err)
					}
				}

				// 终止流程
				isWorkflowComplete = true
				close(completeChan)
				break
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}

		case common.StatusAssetApprovalFail:
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageAssetApproval,
				CurrentStatus: common.StatusAssetApprovalFail,
				Stage:         common.StageRejected,
				NextStatus:    common.StatusRejected,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		case common.StatusAssetDestApprovalPass: // 目的地资管审核通过
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageAssetDestApproval,
				CurrentStatus: common.StatusAssetDestApprovalPass,
				Stage:         common.StageCompleteOutbound,
				NextStatus:    common.StatusCompleted,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateCMDB", input.OutboundReason, input.TicketID).Get(ctx, nil)
			if err != nil {
				return err
			}
			err = workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			// 发送飞书通知到保安群
			//err = workflow.ExecuteActivity(ctx, "SendOutboundMsgToToSecurityGuard", input.TicketNo).Get(ctx, nil)
			//if err != nil {
			//	logger.Error("发送飞书通知失败", "err", err)
			//}
			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		case common.StatusAssetDestApprovalFail: // 目的地资管审核拒绝
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageAssetDestApproval,
				CurrentStatus: common.StatusAssetDestApprovalFail,
				Stage:         common.StageRejected,
				NextStatus:    common.StatusRejected,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		}
		return nil
	})
	if err != nil {
		logger.Error("SetUpdateHandler failed.", "error:", err, zap.Error(err))
	}
	fmt.Println("等待结束信号")
	// 使用 workflow.Await 等待条件满足
	err = workflow.Await(ctx, func() bool {
		select {
		case <-completeChan:
			return true
		default:
			return isWorkflowComplete
		}
	})

	if err != nil {
		logger.Error("Workflow await failed.", "error:", err, zap.Error(err))
		return err
	}
	return nil
}
