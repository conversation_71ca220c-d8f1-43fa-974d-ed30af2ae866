package workflow

import (
	"backend/internal/common/constants"
	"backend/internal/modules/cmdb/model/outbound/common"
	"fmt"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

type PartOutboundState struct {
	OutboundTicketID uint
	OutboundNo       string
	CurrentStatus    string
	CurrentStage     string
}

// PartOutboundWorkflow 设备出库单工作流
func PartOutboundWorkflow(ctx workflow.Context, input common.PartOutboundWorkflowInput) error {
	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: common.ActivityStartToCloseTimeout,
		HeartbeatTimeout:    common.ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        common.InitialInterval,
			BackoffCoefficient:     common.BackoffCoefficient,
			MaximumInterval:        common.MaximumInterval,
			MaximumAttempts:        common.MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)
	// 创建工作流日志
	logger := workflow.GetLogger(ctx)
	logger.Info("初始化PartOboundWorkflow工作流日志成功")
	logger.Info("输入信息为", "PartOutboundInput", input)

	outboundState := PartOutboundState{
		OutboundTicketID: input.TicketID,
		OutboundNo:       input.TicketNo,
		CurrentStatus:    common.StatusWaitingEngineerApproval,
		CurrentStage:     common.StageEngineerApproval,
	}

	// 发送飞书通知
	err := workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
	if err != nil {
		logger.Error("发送飞书通知失败", "err", err)
	}

	var isWorkflowComplete bool
	completeChan := make(chan struct{})

	//if input.OutboundReason == common.OutboundReasonReturnRepair { // 返修出库直接关闭
	//	// 更新ticket 和history
	//	updateInput := common.UpdateTicketInput{
	//		TicketID:      input.TicketID,
	//		TicketNo:      input.TicketNo,
	//		OperatorID:    input.OperatorID,
	//		OperatorName:  input.OperatorName,
	//		Comments:      "",
	//		CurrentStage:  "",
	//		CurrentStatus: "",
	//		Stage:         common.StageCompleteOutbound,
	//		NextStatus:    common.StatusCompleted,
	//	}
	//	err = workflow.ExecuteActivity(ctx, "UpdateCMDB", input.OutboundReason, input.TicketID).Get(ctx, nil)
	//	if err != nil {
	//		return err
	//	}
	//
	//	err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
	//	if err != nil {
	//		return err
	//	}
	//
	//	// 发送飞书通知
	//	err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
	//	if err != nil {
	//		logger.Error("发送飞书通知失败", "err", err)
	//	}
	//	// 终止流程
	//	isWorkflowComplete = true
	//	close(completeChan)
	//}

	// 注册 SetUpdateHandler ，接收更新信号
	err = workflow.SetUpdateHandler(ctx, common.PartOutboundUpdateSignal, func(ctx workflow.Context, signal common.PartOutboundSignal) error {
		logger.Info("收到UpdateHandler信号",
			"PartOutboundTicketID", input.TicketID,
			"status", signal.Status,
			"operator", signal.OperatorName)
		ctx = workflow.WithActivityOptions(ctx, ao)
		// 处理信号
		switch signal.Status {
		case common.StatusEngineerApprovalPass:
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageEngineerApproval,
				CurrentStatus: common.StatusEngineerApprovalPass,
				Stage:         common.StageAssetApproval,
				NextStatus:    common.StatusWaitingAssetApproval,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}

			// 更新内部状态
			outboundState.CurrentStatus = common.StatusWaitingAssetApproval
			outboundState.CurrentStage = common.StageAssetApproval

		case common.StatusEngineerApprovalFail:
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageEngineerApproval,
				CurrentStatus: common.StatusEngineerApprovalFail,
				Stage:         common.StageRejected,
				NextStatus:    common.StatusRejected,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			// 终止流程
			isWorkflowComplete = true
			close(completeChan)

		case common.StatusAssetApprovalPass, common.StatusBuyerApprovalPass:
			switch input.OutboundReason {
			case OutboundReasonSell, OutboundReasonRepair, OutboundReasonReturnRepair: // 维修、售卖、返修
				outboundState.CurrentStage = common.StageCompleteOutbound
				outboundState.CurrentStatus = common.StatusCompleted
			case OutboundReasonAllocate:
				// 更新至中间状态
				err = workflow.ExecuteActivity(ctx, "TransitionToMiddleStatus", input.OutboundReason, constants.AssetStatusOutStock, input.TicketID).Get(ctx, nil)
				if err != nil {
					return err
				}
				// 切换到目的地资管审核
				outboundState.CurrentStage = common.StageAssetDestApproval
				outboundState.CurrentStatus = common.StatusWaitingAssetDestApproval
			case OutboundReasonReplace: // 更新为等待改配负责人操作
				// 更新至中间状态
				err = workflow.ExecuteActivity(ctx, "TransitionToMiddleStatus", input.OutboundReason, constants.AssetStatusOutStock, input.TicketID).Get(ctx, nil)
				if err != nil {
					return err
				}
				outboundState.CurrentStage = common.StageReplaceApproval
				outboundState.CurrentStatus = common.StatusWaitingReplaceOperate
			}
			// 更新ticket和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageAssetApproval,
				CurrentStatus: common.StatusAssetApprovalPass,
				Stage:         outboundState.CurrentStage,
				NextStatus:    outboundState.CurrentStatus,
			}
			if outboundState.CurrentStage == common.StageCompleteOutbound {
				// 更新CMDB
				err = workflow.ExecuteActivity(ctx, "UpdateCMDB", input.OutboundReason, input.TicketID).Get(ctx, nil)
				if err != nil {
					return err
				}
				err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
				if err != nil {
					return err
				}

				// 发送飞书通知
				err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
				if err != nil {
					logger.Error("发送飞书通知失败", "err", err)
				}
				if sendToGuard(input.OutboundReason) {
					// 发送飞书通知到保安群
					err = workflow.ExecuteActivity(ctx, "SendOutboundMsgToToSecurityGuard", input.TicketNo).Get(ctx, nil)
					if err != nil {
						logger.Error("发送飞书通知失败", "err", err)
					}
				}

				// 终止流程
				isWorkflowComplete = true
				close(completeChan)
				break
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}

		case common.StatusAssetApprovalFail, common.StatusBuyerApprovalFail:
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageAssetApproval,
				CurrentStatus: common.StatusAssetApprovalFail,
				Stage:         common.StageRejected,
				NextStatus:    common.StatusRejected,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		case common.StatusReplaceApprovalPass: // 改配负责人
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageReplaceApproval,
				CurrentStatus: common.StatusReplaceApprovalPass,
				Stage:         common.StageCompleteOutbound,
				NextStatus:    common.StatusCompleted,
			}

			// 更新关联的设备SN
			err = workflow.ExecuteActivity(ctx, "UpdateCMDB", input.OutboundReason, input.TicketID).Get(ctx, nil)

			if err != nil {
				return err
			}

			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			isWorkflowComplete = true
			close(completeChan)
		case common.StatusReplaceApprovalFail:
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageReplaceApproval,
				CurrentStatus: common.StatusReplaceApprovalPass,
				Stage:         common.StageRejected,
				NextStatus:    common.StatusRejected,
			}

			// 回滚状态为闲置中
			err = workflow.ExecuteActivity(ctx, "RevertStatus", input.OutboundReason, constants.AssetStatusIdle, input.TicketID).Get(ctx, nil)
			if err != nil {
				return err
			}

			err = workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		case common.StatusAssetDestApprovalPass: // 目的地资管审核通过
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageAssetDestApproval,
				CurrentStatus: common.StatusAssetDestApprovalPass,
				Stage:         common.StageCompleteOutbound,
				NextStatus:    common.StatusCompleted,
			}
			err = workflow.ExecuteActivity(ctx, "UpdateCMDB", input.OutboundReason, input.TicketID).Get(ctx, nil)
			if err != nil {
				return err
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		case common.StatusAssetDestApprovalFail: // 目的地资管审核拒绝
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				TicketID:      input.TicketID,
				TicketNo:      input.TicketNo,
				OperatorID:    signal.OperatorID,
				OperatorName:  signal.OperatorName,
				Comments:      signal.Comments,
				CurrentStage:  common.StageAssetDestApproval,
				CurrentStatus: common.StatusAssetDestApprovalFail,
				Stage:         common.StageRejected,
				NextStatus:    common.StatusRejected,
			}

			// 回滚状态为闲置中
			err = workflow.ExecuteActivity(ctx, "RevertStatus", input.OutboundReason, constants.AssetStatusIdle, input.TicketID).Get(ctx, nil)
			if err != nil {
				return err
			}

			workflow.ExecuteActivity(ctx, "")
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendOutboundMsg", input.TicketNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		default:
			return fmt.Errorf("不支持的更新方法：%v", signal.Status)
		}
		return nil
	})
	if err != nil {
		logger.Error("SetUpdateHandler failed.", "error:", err, zap.Error(err))
	}

	// 使用 workflow.Await 等待条件满足
	err = workflow.Await(ctx, func() bool {
		select {
		case <-completeChan:
			return true
		default:
			return isWorkflowComplete
		}
	})

	if err != nil {
		logger.Error("Workflow await failed.", "error:", err, zap.Error(err))
		return err
	}
	return nil
}

func sendToGuard(outboundReason string) bool {
	switch outboundReason {
	case common.OutboundReasonSell, common.OutboundReasonAllocate, common.OutboundReasonReturnRepair:
		return true
	default:
		return false
	}
}
