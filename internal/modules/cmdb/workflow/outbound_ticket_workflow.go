package workflow

import (
	"backend/internal/modules/cmdb/model/outbound"
	"backend/internal/modules/ticket/common"
	"fmt"
	"strings"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// OutboundTicketWorkflow 出库单工作流
func OutboundTicketWorkflow(ctx workflow.Context, input OutboundTicketWorkflowInput) error {
	// 如果工作流已完成，直接返回
	if input.Completed {
		logger := workflow.GetLogger(ctx)
		logger.Info("工作流已完成，直接返回", "ticketID", input.TicketID)
		return nil
	}

	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: ActivityStartToCloseTimeout,
		HeartbeatTimeout:    ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        InitialInterval,
			BackoffCoefficient:     BackoffCoefficient,
			MaximumInterval:        MaximumInterval,
			MaximumAttempts:        MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	logger := workflow.GetLogger(ctx)
	logger.Info("OutboundTicket workflow started", "ticketID", input.TicketID)

	// 创建工作流状态
	workflowState := struct {
		TicketID              uint
		CurrentStatus         string
		CurrentStage          string
		RepairType            string
		RepairTicketID        uint
		NeedsCustomerApproval bool
		DiagnosisResult       string

		SpareIds string
	}{
		TicketID:      input.TicketID,
		CurrentStatus: input.Status, // 使用输入中的状态
		CurrentStage:  "",           // 初始没有阶段
	}

	// 创建信号通道 - 这将是我们接收所有信号的主要通道
	signalChan := workflow.GetSignalChannel(ctx, WorkflowControlSignalName)

	// TODO 发送飞书通知

	// 根据初始状态设置当前阶段
	switch workflowState.CurrentStatus {
	case StatusWaitingApproval:
		workflowState.CurrentStage = StageCustomerApproval
	case StatusWaitingSecondApproval:
		workflowState.CurrentStage = StageSecondApproval
	case StatusOutbounding:
		workflowState.CurrentStage = StageStartOutbound
	//case StatusCompleteOutbound:
	//	workflowState.CurrentStage = StageCompleteOutbound
	case StatusCompleted:
		workflowState.CurrentStage = StageCompleteTicket
	case StatusCancelled:
		workflowState.CurrentStage = StageCancelled
	}

	// 工作流主循环 - 完全基于信号驱动
	for {
		// 等待下一个信号
		logger.Info("等待工作流控制信号",
			"ticketID", input.TicketID,
			"currentStage", workflowState.CurrentStage,
			"currentStatus", workflowState.CurrentStatus)

		// 无限期等待信号
		var signal common.WorkflowControlSignal
		signalChan.Receive(ctx, &signal)

		logger.Info("收到工作流控制信号",
			"ticketID", input.TicketID,
			"stage", signal.Stage,
			"operator", signal.OperatorName)

		// 处理信号
		switch signal.Stage {
		case StageCustomerApproval:
			// 处理客户审批信号
			err := handleApproval(ctx, input.TicketID, input.TicketNo, signal, &workflowState)
			if err != nil {
				logger.Error("处理客户审批失败", "error", err)
				continue
			}

			// TODO 发送飞书通知

		case StageStartOutbound:
			// 处理用户填完备件 点击开启出库按钮信号
			err := handleStartOutbound(ctx, input, signal, &workflowState)
			if err != nil {
				logger.Error("出库失败", "error", err)
				continue
			}

			// TODO 发送飞书通知

			// 工单已完成，结束工作流主循环
			logger.Info("工单已完成，结束工作流", "ticketID", input.TicketID)
			return nil

		case StageRejected:
			// 处理拒绝出库
			err := handleRejectOutbound(ctx, input, signal, &workflowState)
			if err != nil {
				logger.Error("出库失败", "error", err)
				continue
			}

			// TODO 发送飞书通知

			// 工单已完成，结束工作流主循环
			logger.Info("工单已完成，结束工作流", "ticketID", input.TicketID)
			return nil

		//case StageCompleteOutbound:
		//	// 处理完成工单信号
		//	err := handleCompleteTicket(ctx, input.TicketID, signal, &workflowState)
		//	if err != nil {
		//		logger.Error("处理完成工单失败", "error", err)
		//		continue
		//	}
		//	// 工单已完成，结束工作流主循环
		//	logger.Info("工单已完成，结束工作流", "ticketID", input.TicketID)
		//	return nil

		default:
			logger.Error("未知的工作流阶段", "stage", signal.Stage)
		}

		// 检查工作流是否已完成，如果状态是completed或cancelled，则结束工作流
		if workflowState.CurrentStatus == StatusCompleted || workflowState.CurrentStatus == StatusCancelled || workflowState.CurrentStatus == StatusRejected {
			logger.Info("工单状态已变更为已完成或已取消，结束工作流",
				"ticketID", input.TicketID,
				"status", workflowState.CurrentStatus)
			return nil
		}
	}
}

// handleCustomerApproval 处理客户审批信号
func handleApproval(ctx workflow.Context, ticketID uint, ticketNo string, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	SpareIds              string
}) error {
	logger := workflow.GetLogger(ctx)

	// 解析客户审批数据
	var approvalResult CustomerApprovalResult
	if err := workflow.ExecuteActivity(ctx, "GetApprovalActivity", ticketNo).Get(ctx, &approvalResult); err != nil {
		return fmt.Errorf("获取客户审批失败: %w", err)
	}

	//if approvalResult.Status != "approved" {
	//	return errors.New("客户未批准维修方案")
	//}

	// 检查审批状态
	switch approvalResult.Status {
	case "approved":
		state.CurrentStatus = StatusOutbounding
		state.CurrentStage = StageStartOutbound

		// 再更新数据库工单状态
		err := workflow.ExecuteActivity(ctx, "UpdateOutboundTicketStatusActivity",
			ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		return nil
	case "rejected":
		// 审批拒绝
		logger.Info("审批未通过", "ticketID", ticketID)
		state.CurrentStage = StageCompleteTicket
		state.CurrentStatus = StatusRejected

		// 再更新数据库工单状态
		err := workflow.ExecuteActivity(ctx, "UpdateOutboundTicketStatusActivity",
			ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		return nil
	default:
		// 审批状态未知，保持当前状态
		logger.Warn("未知的审批状态", "status", approvalResult.Status)
		return fmt.Errorf("未知的审批状态: %s", approvalResult.Status)
	}
}

// handleCompleteTicket 处理完成工单信号
//
//nolint:all
func handleCompleteTicket(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	SpareIds              string
}) error {
	// 先更新工作流内部状态
	state.CurrentStatus = StatusCompleted

	// 再更新数据库工单状态
	err := workflow.ExecuteActivity(ctx, "UpdateOutboundTicketStatusActivity",
		ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	return nil
}

// handleStartOutbound 处理开始出库信号
func handleStartOutbound(ctx workflow.Context, input OutboundTicketWorkflowInput, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	SpareIds              string
}) error {
	logger := workflow.GetLogger(ctx)
	// 先更新工作流内部状态  出库中
	state.CurrentStatus = StatusOutbounding

	ticketID := input.TicketID
	// 再更新数据库工单状态
	err := workflow.ExecuteActivity(ctx, "UpdateOutboundTicketStatusActivity",
		ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	if signal.Data != nil {
		if val, ok := signal.Data["spareIds"]; ok {
			if stringVal, isString := val.(string); isString {
				state.SpareIds = stringVal
				logger.Info("从信号数据获取到备件数据",
					"spareIds", state.SpareIds,
					"ticketID", ticketID)
			}
		}
	}

	if state.SpareIds == "" {
		logger.Error("未获取到传入的备件ID数据", "spareIds", state.SpareIds)
		return fmt.Errorf("未获取到备件ID: %s", state.SpareIds)
	}

	spareIds := handleStrings(state.SpareIds)
	logger.Info("获得处理后的备件数据", "spareIds", spareIds, "length", len(spareIds))

	var outboundTicket *outbound.SpareOutboundTicket
	////nolint:all
	//if err := workflow.ExecuteActivity(ctx, "GetOutboundTicketByIDActivity", ticketID).Get(ctx, &outboundTicket); err != nil {
	//	logger.Error("获取出库单信息失败", "error", err)
	//	// 获取出库单失败不应阻塞工作流主流程
	//} else if outboundTicket != nil {
	//	outboundType := outboundTicket.OutboundType
	//	if outboundType == Allocate {
	//		//err = workflow.ExecuteActivity(ctx, "ExecuteAllocateOutboundActivity",
	//		//	ticketID, state.CurrentStatus, signal.OperatorID, signal.Comments, spareIds).Get(ctx, "")
	//		//if err != nil {
	//		//	return fmt.Errorf("出库失败: %w", err)
	//		//}
	//	} else {
	//		err = workflow.ExecuteActivity(ctx, "ExecuteSpareOutboundActivity",
	//			ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName, outboundType, spareIds).Get(ctx, "")
	//		if err != nil {
	//			return fmt.Errorf("出库失败: %w", err)
	//		}
	//	}
	//}

	err = workflow.ExecuteActivity(ctx, "GetOutboundTicketByIDActivity", ticketID).Get(ctx, &outboundTicket)
	if err != nil {
		logger.Error("获取出库单信息失败", "ticketID", ticketID, "error", err)
		// 获取失败不影响主流程
	}

	if outboundTicket == nil {
		logger.Warn("未找到出库单", "ticketID", ticketID)
		return nil
	}

	outboundReason := outboundTicket.OutboundReason

	switch outboundReason {
	case Allocate:
		// err := workflow.ExecuteActivity(ctx, "ExecuteAllocateOutboundActivity",
		// 	 ticketID, state.CurrentStatus, signal.OperatorID, signal.Comments, spareIds).Get(ctx, "")
		// if err != nil {
		//     return fmt.Errorf("出库失败: %w", err)
		// }

		logger.Info("跳过 Allocate 类型出库处理", "ticketID", ticketID)
	default:
		err := workflow.ExecuteActivity(ctx, "ExecuteSpareOutboundActivity",
			ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName, outboundReason, spareIds).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("出库失败: %w", err)
		}
	}

	logger.Info("从前端获取到的备件列表",
		"spareIds", spareIds)

	// 出库完成
	state.CurrentStatus = StatusCompleted

	// 再更新数据库工单状态
	err = workflow.ExecuteActivity(ctx, "UpdateOutboundTicketStatusActivity",
		ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	return nil
}

// handleRejectOutbound 处理拒绝出库信号
func handleRejectOutbound(ctx workflow.Context, input OutboundTicketWorkflowInput, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	SpareIds              string
}) error {
	// 先更新工作流内部状态  拒绝
	state.CurrentStatus = StatusRejected

	ticketID := input.TicketID
	// 再更新数据库工单状态
	err := workflow.ExecuteActivity(ctx, "UpdateOutboundTicketStatusActivity",
		ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}
	return nil
}

func handleStrings(s string) []string {
	return strings.Split(s, ",")
}
