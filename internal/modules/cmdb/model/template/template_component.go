package template

import (
	"backend/internal/modules/cmdb/model/product"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// TemplateComponent 模板组件模型
type TemplateComponent struct {
	ID         uint            `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt  time.Time       `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt  time.Time       `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt  gorm.DeletedAt  `json:"-" gorm:"index"`
	TemplateID uint            `json:"template_id" gorm:"not null;comment:模板ID" example:"1"`
	ProductID  uint            `json:"product_id" gorm:"not null;comment:产品ID" example:"1"`
	Product    product.Product `json:"product" gorm:"foreignKey:ProductID"`
	Quantity   int             `json:"quantity" gorm:"not null;default:1;comment:数量" example:"1"`
	Slot       string          `json:"slot" gorm:"type:varchar(50);comment:槽位" example:"CPU1"`
	SlotInfo   datatypes.JSON  `json:"slot_info" gorm:"type:json;comment:槽位详细信息" example:"[{\"slot\":\"CPU1\",\"position\":\"主板右上角\"}]"`
}

// TableName 指定表名
func (TemplateComponent) TableName() string {
	return "template_components"
}

// SlotDetail 槽位详细信息
type SlotDetail struct {
	Slot     string `json:"slot"`     // 槽位标识，如 CPU1, CPU2, DIMM_A1
	Position string `json:"position"` // 物理位置描述
	Index    int    `json:"index"`    // 索引号，用于排序
}
