package location

import (
	"time"

	"gorm.io/gorm"
)

// Room 房间模型
type Room struct {
	ID           uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt    time.Time      `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
	Name         string         `json:"name" gorm:" type:varchar(100);not null;comment:房间名称"`
	DataCenterID uint           `json:"dataCenterID" gorm:"not null;comment:机房ID"`
	DataCenter   *DataCenter     `json:"dataCenter" gorm:"foreignKey:DataCenterID" swaggerignore:"true"`
	Description  string         `json:"description,omitempty" gorm:"type:text;comment:描述"`
	Status       string         `json:"status" gorm:"type:varchar(20);default:active;comment:状态(active:启用,disabled:禁用)"`
}

// TableName 指定表名
func (Room) TableName() string {
	return "rooms"
}
