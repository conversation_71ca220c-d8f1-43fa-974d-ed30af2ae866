package location

import (
	"time"

	"gorm.io/gorm"
)

// Region 区域模型
type Region struct {
	ID          uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt   time.Time      `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt   time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"` // 软删除字段
	Name        string         `json:"name" gorm:"type:varchar(100) ;not null;comment:区域名称"`
	Status      string         `json:"status" gorm:"type:varchar(20);default:active;comment:状态(active:启用,disabled:禁用)"`
	Description string         `json:"description,omitempty" gorm:"type:text;comment:描述"`
}

// TableName 指定表名
func (Region) TableName() string {
	return "regions"
}
