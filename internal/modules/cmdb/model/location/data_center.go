package location

import (
	"time"

	"gorm.io/gorm"
)

// DataCenter 机房模型
type DataCenter struct {
	ID          uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt   time.Time      `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
	Name        string         `json:"name" gorm:" type:varchar(100);not null;comment:机房名称"`
	AZID        uint           `json:"azId" gorm:"not null;comment:可用区ID"`
	AZ          AZ             `json:"az" gorm:"foreignKey:AZID"`
	Address     string         `json:"address" gorm:"type:varchar(255);comment:地址"`
	Description string         `json:"description,omitempty" gorm:"type:text;comment:描述"`
	Rooms       []Room         `json:"rooms" gorm:"foreignKey:DataCenterID;constraint:OnDelete:CASCADE" swaggerignore:"true"`
	Status      string         `json:"status" gorm:"type:varchar(20);default:active;comment:状态(active:启用,disabled:禁用)"`
}

// TableName 指定表名
func (DataCenter) TableName() string {
	return "data_centers"
}
