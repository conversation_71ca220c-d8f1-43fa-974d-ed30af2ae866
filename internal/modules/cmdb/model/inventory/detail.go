package inventory

import (
	"backend/internal/modules/cmdb/model/product"
	"backend/pkg/utils"
	"time"

	"gorm.io/gorm"
)

// InventoryDetail 库存明细模型
type InventoryDetail struct {
	ID          uint            `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt   time.Time       `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt   time.Time       `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt   gorm.DeletedAt  `json:"-" gorm:"index"`
	ProductID   uint            `json:"product_id" gorm:"comment:产品ID" example:"1"`
	Product     product.Product `json:"product" gorm:"foreignKey:ProductID"`
	WarehouseID uint            `json:"warehouse_id" gorm:"comment:仓库ID" example:"1"`
	Warehouse   string          `json:"warehouse" gorm:"type:varchar(100);comment:仓库" example:"主仓库"`
	//WarehouseModel    asset.Warehouse `json:"warehouseModel" gorm:"foreignKey:WarehouseID"`
	WarehouseLocation string `json:"warehouse_location" gorm:"type:varchar(100);comment:库位" example:"A区-01-02"`
	CurrentStock      int    `json:"current_stock" gorm:"comment:当前库存" example:"10"`
	AllocatedStock    int    `json:"allocated_stock" gorm:"comment:已分配库存" example:"3"`
	AvailableStock    int    `json:"available_stock" gorm:"comment:可用库存" example:"7"`
	BatchNumber       string `json:"batch_number" gorm:"type:varchar(50);comment:批次号" example:"B20230615"`
	//UnitPrice         float64    `json:"unit_price" gorm:"type:decimal(10,2);comment:单价" example:"1200.50"`
	//InboundDate  utils.Date `json:"inbound_date" gorm:"comment:入库日期" example:"2023-01-01T00:00:00Z"`
	//OutboundDate utils.Date `json:"outbound_date" gorm:"comment:出库日期" example:"2023-01-01T00:00:00Z"`
	//WarrantyStart     utils.Date `json:"warranty_start" gorm:"comment:维保开始" example:"2023-01-01T00:00:00Z"`
	//WarrantyEnd       utils.Date `json:"warranty_end" gorm:"comment:维保结束" example:"2026-01-01T00:00:00Z"`
	//PartInboundID uint `json:"part_inbound_id" gorm:"comment:维修入库单ID" example:"1"`
	// NewInboundID  uint `json:"new_inbound_id" gorm:"comment:新设备入库单ID" example:"1"`
	//NewInbound        inbound.NewInbound `json:"new_inbound" gorm:"foreignKey:NewInboundID"`
	//OutboundID uint `json:"outbound_id" gorm:"comment:出库单ID" example:"1"`
	//Status     string `json:"status" gorm:"type:varchar(20);default:active;comment:状态" example:"active"`
}

// TableName 指定表名
func (InventoryDetail) TableName() string {
	return "inventory_details"
}

// InventorySummary 库存汇总信息
type InventorySummary struct {
	ProductID      uint   `json:"product_id"`
	ProductModel   string `json:"product_model"`
	ProductPN      string `json:"product_pn"`
	TotalStock     int    `json:"total_stock"`
	AllocatedStock int    `json:"allocated_stock"`
	AvailableStock int    `json:"available_stock"`
	InboundItems   int    `json:"inbound_items"`
	OutboundItems  int    `json:"outbound_items"`
}

// LowStockProduct 低库存产品
type LowStockProduct struct {
	ProductID    uint   `json:"product_id"`
	ProductModel string `json:"product_model"`
	CurrentStock int    `json:"current_stock"`
	Threshold    int    `json:"threshold"`
	Category     string `json:"category"`
}

// ExpiringWarrantyItem 即将过保项目
type ExpiringWarrantyItem struct {
	ID            uint       `json:"id"`
	ProductID     uint       `json:"product_id"`
	ProductModel  string     `json:"product_model"`
	BatchNumber   string     `json:"batch_number"`
	WarrantyEnd   utils.Date `json:"warranty_end"`
	RemainingDays int        `json:"remaining_days"`
	CurrentStock  int        `json:"current_stock"`
}
