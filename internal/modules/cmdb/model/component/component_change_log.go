package component

import (
	"backend/internal/modules/cmdb/model/asset"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// ChangeType 变更类型
type ChangeType string

const (
	// ChangeTypeInstall 安装
	ChangeTypeInstall ChangeType = "install"
	// ChangeTypeRemove 拆除
	ChangeTypeRemove ChangeType = "remove"
	// ChangeTypeReplace 替换
	ChangeTypeReplace ChangeType = "replace"
	// ChangeTypeRepair 维修
	ChangeTypeRepair ChangeType = "repair"
)

// ComponentChangeLog 组件变更日志模型
type ComponentChangeLog struct {
	ID                  uint             `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt           time.Time        `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt           time.Time        `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt           gorm.DeletedAt   `json:"-" gorm:"index"`
	ServerID            uint             `json:"server_id" gorm:"not null;comment:服务器ID" example:"1"`
	ComponentID         uint             `json:"component_id" gorm:"not null;comment:组件ID" example:"1"`
	PreviousComponentID uint             `json:"previous_component_id" gorm:"comment:前一个组件ID" example:"0"`
	SpareID             uint             `json:"spare_id" gorm:"comment:备件ID;default:null" example:"1"`
	Spare               asset.AssetSpare `json:"spare" gorm:"foreignKey:SpareID"`
	ChangeType          string           `json:"change_type" gorm:"type:varchar(20);comment:变更类型" example:"replace"`
	Reason              string           `json:"reason" gorm:"type:varchar(255);comment:变更原因" example:"硬件故障"`
	OperatorID          uint             `json:"operator_id" gorm:"comment:操作人ID" example:"1"`
	OperatorName        string           `json:"operator_name" gorm:"type:varchar(50);comment:操作人" example:"管理员"`
	ChangeTime          time.Time        `json:"change_time" gorm:"comment:变更时间" example:"2023-01-01T00:00:00Z"`
	Details             datatypes.JSON   `json:"details" gorm:"type:json;comment:变更详情" example:"{\"old_status\":\"normal\",\"new_status\":\"faulty\"}"`
	Remark              string           `json:"remark" gorm:"type:text;comment:备注" example:"CPU过热导致系统不稳定"`
}

// TableName 指定表名
func (ComponentChangeLog) TableName() string {
	return "component_change_logs"
}
