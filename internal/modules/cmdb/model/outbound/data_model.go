package outbound

import "time"

// OutboundApproval 出库单审批记录
type OutboundApproval struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	TicketID     uint      `json:"ticket_id" gorm:"index;comment:关联的出库单ID"`
	TicketNo     string    `json:"ticketNo" gorm:"unique;type:varchar(50);not null;comment:工单号"`
	Status       string    `json:"status" gorm:"type:varchar(20);comment:审批状态(approved/rejected)"`
	ResponseTime time.Time `json:"response_time" gorm:"comment:响应时间"`
	Comments     string    `json:"comments" gorm:"type:text;comment:审批意见"`
	CustomerID   uint      `json:"customer_id" gorm:"comment:客户ID"`
	CustomerName string    `json:"customer_name" gorm:"type:varchar(50);comment:客户姓名"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 指定表名
func (OutboundApproval) TableName() string {
	return "outbound_approval"
}
