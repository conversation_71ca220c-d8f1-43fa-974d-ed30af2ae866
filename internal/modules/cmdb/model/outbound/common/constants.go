package common

import "time"

// 工作流相关
const (
	WorkflowInputTypeDevice = "device_workflow_input"
	WorkflowInputTypePart   = "part_workflow_input"

	WorkflowSignalDevice = "device_workflow_signal"
	WorkflowSignalPart   = "part_workflow_signal"
)

const (
	// 活动超时设置
	ActivityStartToCloseTimeout = 7 * 24 * time.Hour
	ActivityHeartbeatTimeout    = 12 * time.Hour

	// 重试策略
	MaxAttempts        = 5
	InitialInterval    = 5 * time.Second
	BackoffCoefficient = 2.0
	MaximumInterval    = 2 * time.Hour
)

// 工单状态常量
const (
	StatusCompleted = "completed" // 已完成

	// 备件出库阶段常量
	StatusWaitingApproval = "waiting_approval" // 待审批
	StatusOutbounding     = "outbounding"      // 出库中

	// 入库状态常量
	StatusRejected             = "rejected"               //审核不通过
	StatusWaitingAssetApproval = "waiting_asset_approval" // 等待资产管理员审核
	StatusAssetApprovalPass    = "asset_approval_pass"    //资产管理员审核通过
	StatusAssetApprovalFail    = "asset_approval_fail"    //资产管理员审核不通过

	// 备件出库常量
	StatusSubmitingApply           = "submiting_apply"             // 提交申请
	StatusWaitingEngineerApproval  = "waiting_engineer_approval"   // 等待专业工程师审核
	StatusEngineerApprovalPass     = "engineer_approval_pass"      // 专业工程师审核通过
	StatusEngineerApprovalFail     = "engineer_approval_fail"      // 专业工程师审核不通过
	StatusWaitingReplaceOperate    = "waiting_replace_operate"     // 等待改配负责人操作
	StatusReplaceApprovalPass      = "replace_approval_pass"       // 改配负责人通过
	StatusReplaceApprovalFail      = "replace_approval_fail"       // 改配负责人不通过
	StatusWaitingAssetDestApproval = "waiting_asset_dest_approval" // 等待目的地资产管理员审核
	// #nosec G101
	StatusAssetDestApprovalPass = "asset_dest_approval_pass" // 目的地资产管理员审核通过
	StatusAssetDestApprovalFail = "asset_dest_approval_fail" // 目的地资产管理员审核不通过
	StatusWaitingBuyerApproval  = "waiting_buyer_approval"   // 等待买方验收
	StatusBuyerApprovalPass     = "buyer_approval_pass"      // 买方同意签收
	StatusBuyerApprovalFail     = "buyer_approval_fail"      // 买方拒绝签收
)

// 工作流阶段常量
const (
	StageSubmitApply      = "submit_apply"   // 提交申请
	StageAssetApproval    = "asset_approval" //资产管理员审核阶段
	StageRejected         = "rejected"       //审批驳回
	StageCompleteOutbound = "complete_outbound"

	StageEngineerApproval  = "engineer_approval"   // 专业工程师审核阶段
	StageReplaceApproval   = "replace_approval"    // 改配负责人审核
	StageAssetDestApproval = "asset_dest_approval" // 目的地资产管理员审核
	StageBuyerApproval     = "buyer_approval"      // 买方验收
)

// 工作流控制信号名称常量
const (
	DeviceOutboundUpdateSignal = "device_outbound_update_signal" // 设备出库更新信号
	PartOutboundUpdateSignal   = "part_outbound_update_signal"   // 配件出库更新信号
)

// 出库类型
const (
	OutboundTypePart   = "part"   // 配件
	OutboundTypeDevice = "device" // 设备出库
)

// 出库原因
const (
	OutboundReasonRepair       = "repair"
	OutboundReasonReplacement  = "replacement"
	OutboundReasonAllocate     = "allocate"
	OutboundReasonReturnRepair = "return_repair"
	OutboundReasonSell         = "sell"
	OutboundReasonRack         = "rack" // 上架
)
