package asset

import (
	"backend/internal/modules/cmdb/model/location"
	"backend/pkg/utils"
	"time"

	"gorm.io/gorm"
)

// 业务状态常量
const (
	// 业务状态
	BizStatusActive      = "active"      // 运行中(已上线且已交付客户)
	BizStatusMaintaining = "maintaining" // 维护中(满足上线条件或作为备机)
	BizStatusOutage      = "outage"      // 故障(运行状态异常，需要维修)

	// 资源状态
	ResStatusAllocated   = "allocated"   // 已分配
	ResStatusUnallocated = "unallocated" // 未分配
)

// Resource 资源模型（资产与物理位置关联）
type Resource struct {
	ID                  uint              `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt           time.Time         `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt           time.Time         `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt           gorm.DeletedAt    `gorm:"index" json:"-"`
	SN                  string            `json:"sn" gorm:"type:varchar(100);index;comment:SN"`
	Hostname            string            `json:"hostname" gorm:"type:varchar(100);comment:主机名"`
	Cluster             string            `json:"cluster" gorm:"type:varchar(100);comment:集群"`
	VpcIP               string            `json:"vpcIP" gorm:"type:varchar(50);comment:VPC_IP"`
	TenantIP            string            `json:"tenantIP" gorm:"type:varchar(50);comment:租户IP"`
	AssetID             uint              `json:"assetID" gorm:"not null;unique;comment:资产设备关联ID"`
	Device              Device            `json:"device" gorm:"foreignKey:AssetID;references:ID"`
	BizStatus           string            `json:"bizStatus" gorm:"type:varchar(20);default:maintaining;comment:业务状态"`
	ResStatus           string            `json:"resStatus" gorm:"type:varchar(20);default:unallocated;comment:资源状态"`
	Project             string            `json:"project" gorm:"type:varchar(100);comment:所属项目"`
	CabinetID           uint              `json:"cabinetID" gorm:"comment:机柜关联ID;default:null"`
	Cabinet             *location.Cabinet `json:"cabinet" gorm:"foreignKey:CabinetID"`
	RoomID              uint              `json:"roomID" gorm:"comment:房间关联ID;;default:null;comment:用于统计库存的RoomID，除了入库和调拨外不要改动"`
	Room                *location.Room    `json:"room" gorm:"foreignKey:RoomID"`
	RackPosition        int               `json:"rackPosition" gorm:"comment:机架位"`
	Height              int               `json:"height" gorm:"default:1;comment:设备高度(U)"`
	BmcIP               string            `json:"bmcIP" gorm:"type:varchar(50);comment:BMC_IP"`
	IsBackup            bool              `json:"isBackup" gorm:"default:false;comment:是否备机"`
	LastBizStatusChange utils.Date        `json:"lastBizStatusChange" gorm:"type:datetime;comment:最后业务状态变更时间"`
	RackingTime         utils.Date        `json:"rackingTime" gorm:"type:date;comment:上架时间"`
	DeliveryTime        utils.Date        `json:"deliveryTime" gorm:"type:date;comment:交付时间"`
	Remark              string            `json:"remark" gorm:"type:text;comment:备注"`
}

// TableName 指定表名
func (Resource) TableName() string {
	return "resources"
}

// IsValidBizStatus 检查业务状态是否有效
func IsValidBizStatus(status string) bool {
	validStatuses := []string{
		BizStatusActive,
		BizStatusMaintaining,
		BizStatusOutage,
	}

	for _, s := range validStatuses {
		if s == status {
			return true
		}
	}
	return false
}
