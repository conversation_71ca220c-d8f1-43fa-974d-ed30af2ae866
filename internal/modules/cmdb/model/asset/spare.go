package asset

import (
	"backend/internal/modules/cmdb/model/inventory"
	"backend/internal/modules/cmdb/model/product"
	"backend/pkg/utils"
	"time"

	"gorm.io/gorm"
)

// AssetSpare 备件资产模型
type AssetSpare struct {
	ID              uint             `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt       time.Time        `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt       time.Time        `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt       gorm.DeletedAt   `json:"-" gorm:"index"`
	SN              string           `json:"sn" gorm:"type:varchar(100);comment:SN;index" example:"SP123456"`
	ProductID       uint             `json:"product_id" gorm:"comment:产品ID" example:"1"`
	Product         *product.Product `json:"product" gorm:"foreignKey:ProductID"`
	SourceType      string           `json:"source_type" gorm:"type:varchar(50);comment:来源类型" example:"新购"`
	RelatedAssetID  uint             `json:"related_asset_id" gorm:"default:0;comment:关联资产ID" example:"0"`
	RelatedAssetSN  string           `json:"related_asset_sn" gorm:"type:varchar(100);comment:关联资产SN" example:""`
	PurchaseDate    utils.Date       `json:"purchase_date" gorm:"comment:购买时间" example:"2023-01-01T00:00:00Z"`
	WarrantyExpire  utils.Date       `json:"warranty_expire" gorm:"comment:过保时间" example:"2026-01-01T00:00:00Z"`
	AssetStatus     string           `json:"asset_status" gorm:"type:varchar(20);default:idle;comment:资产状态" example:"idle"`
	HardwareStatus  string           `json:"hardware_status" gorm:"type:varchar(20);default:normal;comment:硬件状态" example:"normal"`
	FirmwareVersion string           `json:"firmware_version" gorm:"type:varchar(50);comment:固件版本" example:"1.2.3"`
	Price           float64          `json:"price" gorm:"type:decimal(10,2);comment:金额" example:"1200.50"`
	WarehouseID     uint             `json:"warehouse_id" gorm:"comment:仓库ID" example:"1"`
	Warehouse       *Warehouse       `json:"warehouse" gorm:"foreignKey:WarehouseID"`
	Location        string           `json:"location" gorm:"type:varchar(100);comment:存放位置" example:"架位A-1-2"`
	Remark          string           `json:"remark" gorm:"type:text;comment:备注" example:"从服务器SN12345拆下的CPU"`
	BatchNumber     string           `json:"batch_number" gorm:"type:varchar(50);comment:批次号" example:"B202401"`
}

// TableName 指定表名
func (AssetSpare) TableName() string {
	return "asset_spares"
}

// AssetSpareWithDetails 备件详情，包含关联信息
type AssetSpareWithDetails struct {
	AssetSpare
	ProductInfo     ProductInfo               `json:"product_info,omitempty"`
	WarehouseInfo   WarehouseInfo             `json:"warehouse_info,omitempty"`
	InventoryDetail inventory.InventoryDetail `json:"inventory_detail,omitempty"`
}

// ProductInfo 产品信息
type ProductInfo struct {
	Name            string `json:"name"`
	Brand           string `json:"brand"`
	Model           string `json:"model"`
	Spec            string `json:"spec"`
	ProductCategory string `json:"product_category"`
}

// WarehouseInfo 仓库信息
type WarehouseInfo struct {
	Name     string `json:"name"`
	Code     string `json:"code"`
	Type     string `json:"type"`
	Location string `json:"location"`
}

// SpareStatistics 备件统计信息
type SpareStatistics struct {
	TotalSpares int            `json:"total_spares"`
	ByType      map[string]int `json:"by_type"`
	ByStatus    map[string]int `json:"by_status"`
	ByWarehouse map[string]int `json:"by_warehouse"`
}
