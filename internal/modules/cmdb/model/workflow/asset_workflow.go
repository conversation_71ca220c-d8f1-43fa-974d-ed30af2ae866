package workflow

import (
	"time"

	"gorm.io/gorm"
)

// 工作流状态常量
const (
	WorkflowStatusPending   = "pending"   // 待处理
	WorkflowStatusApproving = "approving" // 审批中
	WorkflowStatusApproved  = "approved"  // 已批准
	WorkflowStatusRejected  = "rejected"  // 已拒绝
	WorkflowStatusCompleted = "completed" // 已完成
	WorkflowStatusCancelled = "cancelled" // 已取消
)

// 工作流类型常量
const (
	WorkflowTypeStorage     = "storage"     // 入库
	WorkflowTypeOutbound    = "outbound"    // 出库
	WorkflowTypeRacking     = "rack"        // 上架
	WorkflowTypeDelivery    = "delivery"    // 交付
	WorkflowTypeMaintenance = "maintenance" // 维修
	WorkflowTypeScrap       = "scrap"       // 报废
)

// AssetWorkflow 资产工作流模型
type AssetWorkflow struct {
	ID              uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt       time.Time      `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt       time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`
	WorkflowID      string         `json:"workflow_id" gorm:"type:varchar(50);not null;unique;comment:工作流ID" example:"WF-2023-001"`
	WorkflowType    string         `json:"workflow_type" gorm:"type:varchar(20);not null;comment:工作流类型" example:"storage"`
	AssetID         uint           `json:"asset_id" gorm:"not null;comment:资产ID" example:"1"`
	Status          string         `json:"status" gorm:"type:varchar(20);default:pending;comment:状态" example:"pending"`
	InitiatorID     uint           `json:"initiator_id" gorm:"not null;comment:发起人ID" example:"1"`
	InitiatorName   string         `json:"initiator_name" gorm:"type:varchar(50);not null;comment:发起人姓名" example:"张三"`
	ApproverID      uint           `json:"approver_id" gorm:"comment:审批人ID" example:"2"`
	ApproverName    string         `json:"approver_name" gorm:"type:varchar(50);comment:审批人姓名" example:"李四"`
	ApprovalTime    *time.Time     `json:"approval_time" gorm:"comment:审批时间" example:"2023-01-02T00:00:00Z"`
	ApprovalComment string         `json:"approval_comment" gorm:"type:text;comment:审批意见" example:"同意入库"`
	CompletionTime  *time.Time     `json:"completion_time" gorm:"comment:完成时间" example:"2023-01-03T00:00:00Z"`
	Description     string         `json:"description" gorm:"type:text;comment:描述" example:"新购服务器入库"`
	ExtraData       string         `json:"extra_data" gorm:"type:text;comment:额外数据(JSON格式)" example:"{\"cabinet_id\":1,\"rack_position\":10}"`
}

// TableName 指定表名
func (AssetWorkflow) TableName() string {
	return "asset_workflows"
}
