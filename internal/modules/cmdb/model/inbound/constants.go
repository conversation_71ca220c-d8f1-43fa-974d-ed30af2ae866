package inbound

import "errors"

// AssetStatus 资产状态
const (
	AssetStatusWaitingInbound   = "waiting_inbound"   // 待入库
	AssetStatusInbounded        = "inbounded"         // 已入库
	AssetStatusWaitingOutbound  = "waiting_outbound"  // 待出库
	AssetStatusOutbounded       = "outbounded"        // 已出库
	AssetStatusIdle             = "idle"              // 闲置中
	AssetStatusInUse            = "in_use"            // 使用中
	AssetStatusUnderMaintenance = "under_maintenance" // 维修中
	AssetStatusPendingScrapping = "pending_scrapping" // 待报废
	AssetStatusScrapped         = "scrapped"          // 已报废
)

// HardwareStatus 硬件状态
const (
	HardwareStatusNormal  = "normal"  // 正常
	HardwareStatusFaulty  = "faulty"  // 故障
	HardwareStatusWarning = "warning" // 警告
)

// SourceType 来源类型
const (
	SourceTypeNewPurchase    = "新购" // 新购
	SourceTypeDismantled     = "拆机" // 拆机
	SourceTypeRepaired       = "维修" // 维修
	SourceTypeReturned       = "退回" // 退回
	SourceTypeOther          = "其他" // 其他
	SourceTypeReturnRepaired = "返修" // 返修

	//SourceTypeNewPurchase    = "new_purchase"    // 新购
	//SourceTypeDismantled     = "dismantled"      // 拆机
	//SourceTypeRepaired       = "repaired"        // 维修
	//SourceTypeReturnRepaired = "return_repaired" // 返修
	//SourceTypeReturned       = "return"          // 退回
	//SourceTypeOther          = "other"           // 其他

)

// InboundType 入库类型
const (
	TypeNewPartInbound        = "new_part"        // 新购配件入库
	TypeDismantledPartInbound = "dismantled_part" //拆机配件入库
	TypeRepairedPartInbound   = "repaired_part"   // 维修配件入库
	TypeSwitchInbound         = "switch_inbound"  //交换机入库
	TypeServerInbound         = "server_inbound"  //服务器入库
	TypeGPUServerInbound      = "gpu_server_inbound"

	// 重构后
	TypePartInbound   = "part_inbound"   // 配件入库
	TypeDeviceInbound = "device_inbound" // 整机设备入库

)

// 资产状态映射
var AssetStatusMap = map[string]string{
	"待入库": AssetStatusWaitingInbound,
	"已入库": AssetStatusInbounded,
	"待出库": AssetStatusWaitingOutbound,
	"已出库": AssetStatusOutbounded,
	"闲置中": AssetStatusIdle,
	"使用中": AssetStatusInUse,
	"维修中": AssetStatusUnderMaintenance,
	"待报废": AssetStatusPendingScrapping,
	"已报废": AssetStatusScrapped,
}

// 硬件状态映射
var HardwareStatusMap = map[string]string{
	"正常": HardwareStatusNormal,
	"故障": HardwareStatusFaulty,
	"警告": HardwareStatusWarning,
}

// 来源类型映射
var SourceTypeMap = map[string]string{
	"新购": SourceTypeNewPurchase,
	"拆机": SourceTypeDismantled,
	"维修": SourceTypeRepaired,
	"退回": SourceTypeReturned,
	"其他": SourceTypeOther,
}

// 定义工单类型前缀映射
var InboundTypePrefixes = map[string]string{
	// 备件
	"NPIT": TypeNewPartInbound,
	"RPIT": TypeRepairedPartInbound,
	"DPIT": TypeDismantledPartInbound,
	// 整机
	"SWIT": TypeSwitchInbound,
	"SVIT": TypeServerInbound,
}

// 状态转换错误
var (
	ErrInvalidAssetStatus    = errors.New("无效的资产状态")
	ErrInvalidHardwareStatus = errors.New("无效的硬件状态")
	ErrInvalidSourceType     = errors.New("无效的来源类型")
)

// 状态转换函数
func ConvertAssetStatus(status string) (string, error) {
	if englishStatus, ok := AssetStatusMap[status]; ok {
		return englishStatus, nil
	}
	return "", ErrInvalidAssetStatus
}

func ConvertHardwareStatus(status string) (string, error) {
	if englishStatus, ok := HardwareStatusMap[status]; ok {
		return englishStatus, nil
	}
	return "", ErrInvalidHardwareStatus
}

func ConvertSourceType(sourceType string) (string, error) {
	if englishType, ok := SourceTypeMap[sourceType]; ok {
		return englishType, nil
	}
	return "", ErrInvalidSourceType
}
