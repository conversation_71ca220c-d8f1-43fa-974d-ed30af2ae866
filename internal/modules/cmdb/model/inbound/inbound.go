package inbound

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/product"
	"backend/internal/modules/cmdb/model/template"
	"backend/pkg/utils"
	"time"

	"gorm.io/gorm"
)

type InboundInterface interface {
	InboundType() string
}
type InboundAdapter struct {
	DismantledInbounds []DismantledInbound
}

func (i InboundAdapter) InboundType() string {
	return "adapter"
}

// 查询参数
type GetListQuery struct {
	Role          string `json:"role" `
	Status        string `json:"status"`
	OrderCategory string `json:"orderCategory"`
	Page          int    `json:"page" binding:"required min=1"`
	PageSize      int    `json:"pagesize" binding:"min=10,max=100"`
}

// 我的入库工单，用于返回给前端(暂时不使用，返回PartInboundTicket给前端）
type Myticket struct {
	InboundNo     string    `json:"inboundNo"`     //工单ID
	OrderCategory string    `json:"orderCategory"` //工单类型（inbound、outbound）
	Status        string    `json:"status"`        //工单状态(未开始、维修中、维修完成)
	Title         string    `json:"title"`         //工单标题
	Description   string    `json:"description"`   //工单故障描述
	RepairType    string    `json:"repairType"`    //维修类型（软修复、硬修复、待定）
	Submitter     string    `json:"submitter"`     //工单的提交人
	SubmitterID   uint      `json:"submitterID"`   //工单提交人ID
	CreatedAt     time.Time `json:"createdAt"`     //创建时间
	UpdatedAt     time.Time `json:"updatedAt"`     //更新时间
	CompletedAt   time.Time `json:"completedAt"`   //完成时间
}

// 入库单汇总表
type InboundList struct {
	ID              uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt       time.Time      `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt       time.Time      `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index" swaggerignore:"true"`
	InboundTicketID uint           `json:"inbound_ticket_id" gorm:"comment:工单ID;default:null"`
	InboundNo       string         `gorm:"column:inbound_no;index;comment:入库单编号" json:"inbound_no"`
	Project         string         `gorm:"column:project;comment:项目名称" json:"project" binding:"required"`
	InboundType     string         `gorm:"column:inbound_type" json:"inbound_type"`
	InboundReason   string         `gorm:"column:inbound_reason" json:"inbound_reason" binding:"required"`
	InboundTitle    string         `gorm:"column:inbound_title" json:"inbound_title" binding:"required"`
	AssetType       string         `gorm:"column:asset_type" json:"asset_type"`
	CreateBy        string         `gorm:"column:create_by" json:"create_by"`
	CreaterID       uint           `gorm:"column:creater_id" json:"creater_id"`
	CompletedAt     *time.Time     `gorm:"column:completed_at" json:"completed_at"`
	Stage           string         `gorm:"column:stage" json:"stage"`
}

func (InboundList) TableName() string { return "inbound_list" }

// 入库单（维修入库）
type PartInbound struct {
	ID                       uint             `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt                time.Time        `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt                time.Time        `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	DeletedAt                gorm.DeletedAt   `json:"-" gorm:"index" swaggerignore:"true"`
	InboundNo                string           `json:"inbound_no" binding:"required" gorm:"commit:入库单号"`
	RepairTicketID           uint             `json:"repair_ticket_id" binding:"required" gorm:"comment:维修工单ID"`
	ComponentID              string           `json:"component_id" binding:"required" gorm:"type:text;comment:组件ID（JSON数组）"`
	ComponentSN              string           `json:"component_sn" gorm:"type:text;comment:组件序列号（JSON数组）"`
	WarehouseID              uint             `json:"warehouse_id" gorm:"comment:仓库ID"`
	Warehouse                *asset.Warehouse `json:"warehouse" gorm:"foreignKey:WarehouseID"`
	WarehouseLocation        string           `json:"warehouse_location" gorm:"type:varchar(255);comment:仓库位置"`
	InboundTime              *time.Time       `json:"inbound_time,omitempty" gorm:"comment:入库时间"`
	EngineerID               uint             `json:"engineer_id" gorm:"comment:工程师ID"`
	EngineerName             string           `json:"engineer_name" gorm:"type:varchar(100);comment:工程师姓名"`
	FaultLevel               string           `json:"fault_level" gorm:"type:varchar(50);comment:故障级别，取值：minor/major/fatal"`
	HardwareStatus           string           `json:"hardware_status" gorm:"type:varchar(50);comment:硬件状态，取值：faulty/repairable/scrapped"`
	FaultDescription         string           `json:"fault_description" gorm:"type:text;comment:故障描述"`
	HandlingSuggestion       string           `json:"handling_suggestion" gorm:"type:varchar(50);comment:处理建议，取值：repair/scrap/return_to_vendor"`
	RecipientID              uint             `json:"recipient_id" gorm:"comment:接收人ID"`
	RecipientName            string           `json:"recipient_name" gorm:"type:varchar(100);comment:接收人姓名"`
	InspectionResult         string           `json:"inspection_result" gorm:"type:text;comment:检验结果"`
	InboundNotes             string           `json:"inbound_notes,omitempty" gorm:"type:text;comment:入库备注"`
	RequiresRMA              bool             `json:"requires_rma" gorm:"comment:是否需要RMA"`
	RMANumber                string           `json:"rma_number" gorm:"type:varchar(100);comment:RMA编号"`
	ManufacturerReturnStatus string           `json:"manufacturer_return_status" gorm:"type:varchar(50);comment:厂商返回状态，取值：pending/approved/in-progress/completed/rejected"`
	ManufacturerReturnDate   *time.Time       `json:"manufacturer_return_date,omitempty" gorm:"comment:厂商返回日期"`
	Lock                     bool             `json:"lock" gorm:"comment:锁定状态"`
}

// TableName 定义表名
func (PartInbound) TableName() string { return "part_inbound" }

// 新购入库单
type NewInbound struct {
	ID              uint               `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt       time.Time          `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt       time.Time          `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	DeletedAt       gorm.DeletedAt     `json:"-" gorm:"index" swaggerignore:"true"`
	InboundTitle    string             `gorm:"type:text;comment:入库标题" json:"inbound_title" `
	Project         string             `gorm:"comment:项目" json:"project" `
	CreateBy        string             `gorm:"varchar(50);comment:创建人" json:"create_by"`
	CreateID        uint               `gorm:"comment:创建人ID" json:"create_id"`
	InboundNo       string             `gorm:"column:inbound_no;index" json:"inbound_no"`
	PurchaseOrderID uint               `gorm:"comment:采购合同ID" json:"purchase_order_id"`                  // 采购合同ID
	PurchaseOrderNo string             `gorm:"varchar(100)" json:"purchase_order_no" binding:"required"` //采购合同编号
	SupplierName    string             `gorm:"varchar(100)" json:"supplier_name" binding:"required"`     // 供应商名称
	NewInfo         []NewInboundInfo   `gorm:"comment:入库粗略信息" json:"new_info" binding:"required"`
	NewDetails      []NewInboundDetail `gorm:"comment:入库详细信息" json:"new_details"`
	Amount          uint               `gorm:"comment:数量" json:"amount"` // 数量
	Comment         string             `gorm:"comment:备注信息" json:"comment"`
	TrackingInfo    string             `gorm:"type:text;comment:物流信息" json:"tracking_info"`
	MayArriveAt     *time.Time         `gorm:"comment:预计到达时间" json:"may_arrive_at"`
	Lock            bool               `gorm:"comment:锁定状态" json:"lock"`
}

func (i *NewInbound) InboundType() string {
	return TypeNewPartInbound
}

func (NewInbound) TableName() string { return "new_inbound" }

// 新购入库粗略信息
type NewInboundInfo struct {
	ID           uint            `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt    time.Time       `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt    time.Time       `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	DeletedAt    gorm.DeletedAt  `json:"-" gorm:"index" swaggerignore:"true"`
	NewInboundID uint            `gorm:"new_inbound_id;comment:关联入库单" json:"new_inbound_id"`
	ProductID    uint            `gorm:"product_id;comment:规格信息" json:"product_id" binding:"required"`
	Product      product.Product `gorm:"foreignKey:ProductID"`
	Amount       uint            `gorm:"comment:数量" json:"amount" binding:"required"`
}

func (NewInboundInfo) TableName() string { return "new_inbound_info" }

// 新购入库信息，用于细化入库的信息
type NewInboundDetail struct {
	ID             uint             `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt      time.Time        `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt      time.Time        `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	DeletedAt      gorm.DeletedAt   `json:"-" gorm:"index" swaggerignore:"true"`
	NewInboundID   uint             `gorm:"new_inbound_id;comment:关联入库单" json:"new_inbound_id"`
	ProductID      uint             `gorm:"product_id;comment:规格信息" json:"product_id"`
	Product        *product.Product `gorm:"foreignKey:ProductID" json:"product"`
	SN             string           `json:"sn" gorm:"type:varchar(100);comment:SN" example:"SP123456"`
	DataCenterID   uint             `json:"data_center_id" gorm:"comment:数据中心ID"`
	DataCenterName string           `json:"data_center_name" gorm:"comment:数据中心名称"`
	RoomID         uint             `json:"room_id" gorm:"comment:房间ID"`
	RoomName       string           `json:"room_name" gorm:"comment:房间名称"`
	WarehouseID    uint             `json:"warehouse_id" gorm:"comment:仓库ID" example:"1"`
	WarehouseName  string           `json:"warehouse_name" gorm:"comment:仓库名称"`
	Location       string           `json:"location" gorm:"type:varchar(100);comment:存放位置" example:"架位A-1-2"`

	// 目前不关注
	SourceType      string     `json:"source_type" gorm:"type:varchar(50);comment:来源类型" example:"新购"`
	RelatedAssetID  uint       `json:"related_asset_id" gorm:"default:0;comment:关联资产ID" example:"0"`
	RelatedAssetSN  string     `json:"related_asset_sn" gorm:"type:varchar(100);comment:关联资产SN" example:""`
	PurchaseDate    utils.Date `json:"purchase_date" gorm:"comment:购买时间" example:"2023-01-01T00:00:00Z"`
	WarrantyExpire  utils.Date `json:"warranty_expire" gorm:"comment:过保时间" example:"2026-01-01T00:00:00Z"`
	AssetStatus     string     `json:"asset_status" gorm:"type:varchar(20);default:idle;comment:资产状态" example:"idle"`
	HardwareStatus  string     `json:"hardware_status" gorm:"type:varchar(20);default:normal;comment:硬件状态" example:"normal"`
	FirmwareVersion string     `json:"firmware_version" gorm:"type:varchar(50);comment:固件版本" example:"1.2.3"`
	Price           float64    `json:"price" gorm:"type:decimal(10,2);comment:金额" example:"1200.50"`

	Remark      string `json:"remark" gorm:"type:text;comment:备注" example:"从服务器SN12345拆下的CPU"`
	BatchNumber string `json:"batch_number" gorm:"type:varchar(50);comment:批次号" example:"B202401"`
	TryCount    uint   `json:"try_count" gorm:"comment:尝试次数" example:"0"`
}

func (NewInboundDetail) TableName() string { return "new_inbound_details" }

// 返修入库
type RepairInbound struct {
	ID            uint                   `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt     time.Time              `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt     time.Time              `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	DeletedAt     gorm.DeletedAt         `json:"-" gorm:"index" swaggerignore:"true"`
	InboundNo     string                 `json:"inbound_no" gorm:"comment:关联入库单号;index;unique"`
	InboundTitle  string                 `json:"inbound_title" gorm:"comment:入库标题"`
	Project       string                 `json:"project" gorm:"comment:项目"`
	RepairDetails []RepairInboundDetails `json:"repair_details" binding:"required"`
	CreateBy      string                 `json:"create_by" gorm:"comment:创建人"`
	CreateID      uint                   `json:"create_id" gorm:"创建人ID"`
}

func (r *RepairInbound) InboundType() string {
	return TypeRepairedPartInbound
}
func (RepairInbound) TableName() string { return "repair_inbounds" }

type RepairInboundDetails struct {
	ID              uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt       time.Time      `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt       time.Time      `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index" swaggerignore:"true"`
	RepairInboundID uint           `json:"repair_inbound_id" gorm:"comment:关联的返修入库单ID"`
	SN              string         `json:"sn" gorm:"type:varchar(100);comment:SN" binding:"required" example:"SP123456"`
	ReplaceSN       string         `json:"replace_sn" gorm:"type:varchar(100);comment:被更换的SN" binding:"required" example:""`
	PN              string         `json:"pn" gorm:"type:varchar(100);comment:PN" example:"B202401"`
	ReplacePN       string         `json:"replace_pn" gorm:"type:varchar(100);comment:被更换的PN" example:"B202401"`
	RepairType      string         `json:"repair_type" gorm:"comment:类型" binding:"required" example:"维修 | 换新===repaired | renew"`
	WarehouseID     uint           `json:"warehouse_id" binding:"required" gorm:"comment:关联仓库ID"`
	WarehouseName   string         `json:"warehouse_name" binding:"required" gorm:"comment:关联仓库名称"`
	DataCenterID    uint           `json:"data_center_id" gorm:"comment:关联数据中心ID"`
	DataCenterName  string         `json:"data_center_name" gorm:"comment:数据中心名称"`
}

func (RepairInboundDetails) TableName() string { return "repair_inbound_details" }

// 拆机入库
type DismantledInbound struct {
	ID           uint                       `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt    time.Time                  `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt    time.Time                  `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	DeletedAt    gorm.DeletedAt             `json:"-" gorm:"index" swaggerignore:"true"`
	InboundNo    string                     `json:"inbound_no" gorm:"comment:入库单号;index"`
	Project      string                     `json:"project" gorm:"comment:项目"`
	InboundTitle string                     `json:"inbound_title" gorm:"comment:工单标题"`
	Details      []DismantledInboundDetails `json:"details" binding:"required"`
	CreateBy     string                     `json:"create_by" gorm:"comment:创建人"`
	CreateID     uint                       `json:"create_id" gorm:"comment:创建人ID"`
}

func (d *DismantledInbound) InboundType() string {
	return TypeDismantledPartInbound
}

func (DismantledInbound) TableName() string { return "dismantled_inbounds" }

// 拆机入库详情
type DismantledInboundDetails struct {
	ID                  uint             `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt           time.Time        `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt           time.Time        `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	DeletedAt           gorm.DeletedAt   `json:"-" gorm:"index" swaggerignore:"true"`
	DismantledInboundID uint             `json:"dismantled_inbound_id" gorm:"comment:拆机入库单ID"`
	MainDeviceSN        string           `json:"main_device_sn" binding:"required" gorm:"comment:主设备SN" example:"SN123456"`
	ComponentSN         string           `json:"component_sn" binding:"required" gorm:"comment:更换下来的配件SN" example:"SN123456"`
	ComponentState      string           `json:"component_state" binding:"required" gorm:"comment:资产状态" example:"正常 | 故障 | 维护 | 报废"`
	ProductID           uint             `json:"product_id" gorm:"comment: 关联资产ID" example:"B202401"`
	Product             *product.Product `gorm:"foreignkey:ProductID" json:"product"`
	WarehouseID         uint             `json:"warehouse_id" binding:"required" gorm:"comment:关联仓库ID"`
	WarehouseName       string           `json:"warehouse_name" binding:"required" gorm:"comment:关联仓库名称"`
	DataCenterID        uint             `json:"data_center_id" gorm:"comment:关联数据中心ID"`
	DataCenterName      string           `json:"data_center_name" gorm:"comment:数据中心名称"`
	NeedReturn          bool             `json:"need_return" binding:"required" gorm:"comment:是否需要返厂"`
}

func (DismantledInboundDetails) TableName() string {
	return "dismantled_inbound_details"
}

// 设备入库详情 服务器/交换机
type DeviceInbound struct {
	gorm.Model
	InboundTitle    string                `gorm:"type:text;comment:入库标题" json:"inbound_title" binding:"required"`
	Project         string                `gorm:"comment:项目" json:"project" binding:"required"`
	DeviceType      string                `gorm:"comment:设备详情" json:"device_type" binding:"required" example:"server | switch " `
	CreateBy        string                `gorm:"varchar(50);comment:创建人" json:"create_by"`
	CreateID        uint                  `gorm:"comment:创建人ID" json:"create_id"`
	InboundNo       string                `gorm:"column:inbound_no;index" json:"inbound_no"`
	PurchaseOrderID uint                  `gorm:"comment:采购合同ID" json:"purchase_order_id"`                  // 采购合同ID
	PurchaseOrderNo string                `gorm:"varchar(100)" json:"purchase_order_no" binding:"required"` //采购合同编号
	SupplierName    string                `gorm:"varchar(100)" json:"supplier_name" binding:"required"`     // 供应商名称
	DeviceInfo      []DeviceInboundInfo   `gorm:"comment:入库粗略信息" json:"new_info" binding:"required"`
	DeviceDetails   []DeviceInboundDetail `gorm:"comment:入库详细信息" json:"new_details"`
	Amount          uint                  `gorm:"comment:数量" json:"amount"` // 数量
	Comment         string                `gorm:"comment:备注信息" json:"comment"`
	WarehouseID     uint                  `gorm:"comment:仓库ID" json:"warehouse_id"`
	Warehouse       *asset.Warehouse      `gorm:"foreignkey:WarehouseID" json:"warehouse"`
	RequireVarify   bool                  `gorm:"comment:需要验收" json:"require_varify"`
	TrackingInfo    string                `gorm:"type:text;comment:物流信息" json:"tracking_info"`
	MayArriveAt     *time.Time            `gorm:"comment:预计到达时间" json:"may_arrive_at"`
}

func (DeviceInbound) TableName() string { return "device_inbounds" }

func (d *DeviceInbound) InboundType() string {
	return TypeDeviceInbound
}

type DeviceInboundInfo struct {
	gorm.Model
	DeviceInboundID uint                      `gorm:"device_inbound_id;comment:关联入库单" json:"device_inbound_id"`
	TemplateID      uint                      `gorm:"template_id;comment:模板信息" json:"template_id" binding:"required"`
	Template        *template.MachineTemplate `gorm:"foreignKey:TemplateID"`
	Amount          uint                      `gorm:"comment:数量" json:"amount" binding:"required"`
}

func (DeviceInboundInfo) TableName() string { return "device_inbound_infos" }

type DeviceInboundDetail struct {
	gorm.Model
	DeviceInboundID uint                      `gorm:"device_inbound_id;comment:关联入库单" json:"device_inbound_id"`
	TemplateID      uint                      `gorm:"template_id;comment:模板信息" json:"template_id"`
	Template        *template.MachineTemplate `gorm:"foreignKey:TemplateID"`
	SN              string                    `json:"sn" binding:"required" gorm:"type:varchar(100);comment:SN" example:"SP123456"`
	DataCenterID    uint                      `json:"data_center_id" gorm:"comment:数据中心ID"`
	DataCenterName  string                    `json:"data_center_name" gorm:"comment:数据中心名称"`
	RoomID          uint                      `json:"room_id" gorm:"comment:房间ID"`
	RoomName        string                    `json:"room_name,omitempty" gorm:"comment:房间名称"`
	WarehouseID     uint                      `json:"warehouse_id" binding:"required" gorm:"comment:仓库ID;default:null" example:"1"`
	Warehouse       *asset.Warehouse          `gorm:"foreignKey:WarehouseID"`
	WarehouseName   string                    `json:"warehouse_name" gorm:"comment:仓库名称"`
	Location        string                    `json:"location" gorm:"type:varchar(100);comment:存放位置" example:"架位A-1-2"`
}

func (DeviceInboundDetail) TableName() string { return "device_inbound_details" }

type SubmitterInfo struct {
	ID   uint   `json:"id" gorm:"column:create_id"`
	Name string `json:"name" gorm:"column:created_by"`
}

// ListDTO 入库单列表查询参数
type ListDTO struct {
	Page          uint   `json:"page" binding:"required" form:"page"`
	PageSize      uint   `json:"page_size" binding:"required" form:"page_size"`
	Project       string `json:"project" form:"project"`
	InboundNo     string `json:"inbound_no" form:"inbound_no"`
	InboundType   string `json:"inbound_type" form:"inbound_type"`
	InboundReason string `json:"inbound_reason" form:"inbound_reason"`
	CreateID      uint   `json:"create_id" form:"create_id"`
	CreateBy      string `json:"create_by" form:"create_by"`
	Stage         string `json:"stage" form:"stage"`
}
