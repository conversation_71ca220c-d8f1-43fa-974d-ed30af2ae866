package service

import (
	"backend/internal/modules/cmdb/repository"
	"context"
)

// ResourceService 资源服务接口
type ResourceService interface {
	// 获取项目列表
	GetProjectList(ctx context.Context) ([]string, error)
}

// resourceService 资源服务实现
type resourceService struct {
	repo repository.ResourceRepository
}

// NewResourceService 创建资源服务
func NewResourceService(repo repository.ResourceRepository) ResourceService {
	return &resourceService{repo: repo}
}

// GetProjectList 获取项目列表
func (s *resourceService) GetProjectList(ctx context.Context) ([]string, error) {
	return s.repo.GetProjectList(ctx)
}
