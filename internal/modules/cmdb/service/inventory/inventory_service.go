package inventory

import (
	"backend/internal/modules/cmdb/model/inventory"
	"context"
	"time"
)

// InventoryService 库存服务接口
type InventoryService interface {
	// 基础CRUD操作
	CreateInventory(ctx context.Context, detail *inventory.InventoryDetail) error
	UpdateInventory(ctx context.Context, detail *inventory.InventoryDetail) error
	DeleteInventory(ctx context.Context, id uint) error
	GetInventoryByID(ctx context.Context, id uint) (*inventory.InventoryDetail, error)
	GetInventoryByProductIDandWarehouseID(ctx context.Context, productID, warehouseID uint) (*inventory.InventoryDetail, error)
	ListInventory(ctx context.Context, page, pageSize int, productID uint, warehouse string, params map[string]interface{}) ([]*inventory.InventoryDetail, int64, error)

	// 使用基本参数创建库存记录
	CreateSimpleInventory(ctx context.Context, productID, warehouseID uint, stock int, warehouseName, status string) error

	// 库存操作
	AdjustStock(ctx context.Context, id uint, quantity int, changeType, reason string, inboundID, outboundID uint) error
	AllocateStock(ctx context.Context, id uint, quantity int, purpose string) error
	ReleaseAllocatedStock(ctx context.Context, id uint, quantity int) error
	RemoveAllocatedStock(ctx context.Context, id uint, quantity int, purpose string) error

	// 统计和报表
	GetProductInventorySummary(ctx context.Context, productID uint) (*inventory.InventorySummary, error)
	GetLowStockProducts(ctx context.Context, threshold int) ([]*inventory.LowStockProduct, error)
	GetExpiringWarrantyItems(ctx context.Context, days int) ([]*inventory.ExpiringWarrantyItem, error)

	// 按仓库查询
	ListByWarehouse(ctx context.Context, warehouseID uint, page, pageSize int) ([]*inventory.InventoryDetail, int64, error)

	// 获取特定仓库中某一种规格的最新信息,提供给配件新购入库使用
	GetLatestInventoryDetail(ctx context.Context, productID, warehouseID uint) (*inventory.InventoryDetail, error)

	// 根据产品ID和仓库ID查询库存
	GetByProductAndWarehouse(ctx context.Context, productID, warehouseID uint) (*inventory.InventoryDetail, error)

	// 历史记录
	GetStockHistory(ctx context.Context, detailID, productID, warehouseID uint, startTime, endTime time.Time) ([]*inventory.StockHistory, error)

	// SyncInventoryWithSpareStatus 根据备件状态同步库存数据
	SyncInventoryWithSpareStatus(ctx context.Context, productID, warehouseID uint) error
	SyncInventoryWithDeviceStatus(ctx context.Context, productID, warehouseID uint) error
}
