package component

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/component"
	repo "backend/internal/modules/cmdb/repository/component"
	assetSvc "backend/internal/modules/cmdb/service/asset"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"
	"context"
)

// ServerComponentService 服务器组件服务接口
type ServerComponentService interface {
	CreateServerComponent(ctx context.Context, component *component.ServerComponent) error
	UpdateServerComponent(ctx context.Context, component *component.ServerComponent) error
	DeleteServerComponent(ctx context.Context, id uint) error
	GetServerComponentByID(ctx context.Context, id uint) (*component.ServerComponent, error)
	GetServerComponentBySN(ctx context.Context, sn string) (*component.ServerComponent, error)
	ListServerComponentsByServerID(ctx context.Context, serverID uint) ([]*component.ServerComponent, error)
	ListServerComponents(ctx context.Context, page, pageSize int, query string, componentType string) ([]*component.ServerComponent, int64, error)

	// 新增方法
	GetComponentWithDetails(ctx context.Context, id uint) (*component.ServerComponentWithDetails, error)
	GetComponentStatistics(ctx context.Context, componentType string) (*component.ComponentStatistics, error)
	ListComponentsWithDetails(ctx context.Context, page, pageSize int, query string, componentType string) ([]*component.ServerComponentWithDetails, int64, error)

	// 备件相关
	GetComponentSpares(ctx context.Context, componentID uint, page, pageSize int) ([]*asset.AssetSpareWithDetails, int64, error)
	GetSparesByComponentType(ctx context.Context, componentType string, page, pageSize int) ([]*asset.AssetSpareWithDetails, int64, error)
}

// serverComponentService 服务器组件服务实现
type serverComponentService struct {
	repo             repo.ServerComponentRepository
	inventoryService inventorySvc.InventoryService
	spareService     assetSvc.SpareService
}

// NewServerComponentService 创建服务器组件服务
func NewServerComponentService(repo repo.ServerComponentRepository, inventoryService inventorySvc.InventoryService, spareService assetSvc.SpareService) ServerComponentService {
	return &serverComponentService{
		repo:             repo,
		inventoryService: inventoryService,
		spareService:     spareService,
	}
}

// CreateServerComponent 创建服务器组件
func (s *serverComponentService) CreateServerComponent(ctx context.Context, component *component.ServerComponent) error {
	return s.repo.Create(ctx, component)
}

// UpdateServerComponent 更新服务器组件
func (s *serverComponentService) UpdateServerComponent(ctx context.Context, component *component.ServerComponent) error {
	return s.repo.Update(ctx, component)
}

// DeleteServerComponent 删除服务器组件
func (s *serverComponentService) DeleteServerComponent(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetServerComponentByID 根据ID获取服务器组件
func (s *serverComponentService) GetServerComponentByID(ctx context.Context, id uint) (*component.ServerComponent, error) {
	return s.repo.GetByID(ctx, id)
}

// GetServerComponentBySN 根据SN获取服务器组件
func (s *serverComponentService) GetServerComponentBySN(ctx context.Context, sn string) (*component.ServerComponent, error) {
	return s.repo.GetBySN(ctx, sn)
}

// ListServerComponentsByServerID 根据服务器ID获取组件列表
func (s *serverComponentService) ListServerComponentsByServerID(ctx context.Context, serverID uint) ([]*component.ServerComponent, error) {
	return s.repo.ListByServerID(ctx, serverID)
}

// ListServerComponents 分页查询服务器组件列表
func (s *serverComponentService) ListServerComponents(ctx context.Context, page, pageSize int, query string, componentType string) ([]*component.ServerComponent, int64, error) {
	return s.repo.List(ctx, page, pageSize, query, componentType)
}

// GetComponentWithDetails 获取组件详情
func (s *serverComponentService) GetComponentWithDetails(ctx context.Context, id uint) (*component.ServerComponentWithDetails, error) {
	componentWithDetails, err := s.repo.GetComponentWithDetails(ctx, id)
	if err != nil {
		return nil, err
	}

	// 如果有产品ID，获取产品库存信息
	if componentWithDetails.ProductID > 0 {
		inventorySummary, err := s.inventoryService.GetProductInventorySummary(ctx, componentWithDetails.ProductID)
		if err == nil && inventorySummary != nil {
			// 根据库存汇总信息计算组件详情中的库存指标
			inUseCount := s.calculateInUseCount(ctx, componentWithDetails.ProductID)
			componentWithDetails.InventoryDetail = component.InventoryDetail{
				TotalStock:  inventorySummary.TotalStock,
				InUseCount:  inUseCount,
				IdleCount:   inventorySummary.TotalStock - inUseCount,
				GoodCount:   inUseCount, // 默认情况下，在用的组件都是好的
				DefectCount: 0,          // 默认为0，可以从其他地方计算
			}
		}
	}

	return componentWithDetails, nil
}

// calculateInUseCount 计算指定产品类型的在用组件数量
func (s *serverComponentService) calculateInUseCount(ctx context.Context, productID uint) int {
	// 简单查询方式，实际应根据业务需求优化
	components, _, err := s.repo.List(ctx, 0, 0, "", "")
	if err != nil {
		return 0
	}

	var count int
	for _, comp := range components {
		if comp.ProductID == productID && comp.ServerID > 0 {
			count++
		}
	}
	return count
}

// ListComponentsWithDetails 获取带库存信息的组件列表
func (s *serverComponentService) ListComponentsWithDetails(ctx context.Context, page, pageSize int, query string, componentType string) ([]*component.ServerComponentWithDetails, int64, error) {
	componentsWithDetails, total, err := s.repo.ListWithDetails(ctx, page, pageSize, query, componentType)
	if err != nil {
		return nil, 0, err
	}

	// 为每个组件获取库存信息
	for _, comp := range componentsWithDetails {
		if comp.ProductID > 0 {
			inventorySummary, err := s.inventoryService.GetProductInventorySummary(ctx, comp.ProductID)
			if err == nil && inventorySummary != nil {
				// 根据库存汇总信息计算组件详情中的库存指标
				inUseCount := s.calculateInUseCount(ctx, comp.ProductID)
				comp.InventoryDetail = component.InventoryDetail{
					TotalStock:  inventorySummary.TotalStock,
					InUseCount:  inUseCount,
					IdleCount:   inventorySummary.TotalStock - inUseCount,
					GoodCount:   inUseCount, // 简化计算，实际应根据组件状态统计
					DefectCount: 0,          // 简化计算，实际应根据组件状态统计
				}
			}
		}
	}

	return componentsWithDetails, total, nil
}

// GetComponentStatistics 获取组件统计信息
func (s *serverComponentService) GetComponentStatistics(ctx context.Context, componentType string) (*component.ComponentStatistics, error) {
	stats, err := s.repo.GetComponentStatistics(ctx, componentType)
	if err != nil {
		return nil, err
	}

	// 如果指定了组件类型，尝试获取产品ID
	if componentType != "" {
		// 简单获取第一个组件以确定产品ID
		components, _, err := s.repo.List(ctx, 1, 1, "", componentType)
		if err == nil && len(components) > 0 && components[0].ProductID > 0 {
			productID := components[0].ProductID
			inventorySummary, err := s.inventoryService.GetProductInventorySummary(ctx, productID)
			if err == nil && inventorySummary != nil {
				// 计算库存统计数据
				inUseCount := s.calculateInUseCount(ctx, productID)
				stats.Inventory = component.InventoryStatistics{
					TotalStock:     inventorySummary.TotalStock,
					InUseCount:     inUseCount,
					IdleCount:      inventorySummary.TotalStock - inUseCount,
					GoodCount:      inUseCount, // 简化计算
					DefectCount:    0,          // 简化计算
					AllocatedStock: inventorySummary.AllocatedStock,
					AllocatedCount: inventorySummary.AllocatedStock, // 简化计算
				}
			}
		}
	}

	return stats, nil
}

// GetComponentSpares 获取组件相关的备件
func (s *serverComponentService) GetComponentSpares(ctx context.Context, componentID uint, page, pageSize int) ([]*asset.AssetSpareWithDetails, int64, error) {
	// 1. 获取组件信息以确定组件类型和产品ID
	comp, err := s.GetServerComponentByID(ctx, componentID)
	if err != nil {
		return nil, 0, err
	}

	// 2. 如果有产品ID，优先按产品ID查询备件
	if comp.ProductID > 0 {
		return s.spareService.ListSparesByProductID(ctx, comp.ProductID, page, pageSize)
	}

	// 3. 如果没有产品ID，按组件类型查询备件
	return s.spareService.ListSparesByComponentType(ctx, comp.ComponentType, page, pageSize)
}

// GetSparesByComponentType 根据组件类型获取可用备件
func (s *serverComponentService) GetSparesByComponentType(ctx context.Context, componentType string, page, pageSize int) ([]*asset.AssetSpareWithDetails, int64, error) {
	return s.spareService.ListSparesByComponentType(ctx, componentType, page, pageSize)
}
