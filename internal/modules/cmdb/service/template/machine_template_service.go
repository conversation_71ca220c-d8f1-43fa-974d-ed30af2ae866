package template

import (
	"context"
	"encoding/json"
	"errors"

	"backend/internal/modules/cmdb/model/template"
	repo "backend/internal/modules/cmdb/repository/template"
)

// MachineTemplateService 套餐模板服务接口
type MachineTemplateService interface {
	// List 查询套餐模板列表
	List(ctx context.Context, page, pageSize int, query, category string) ([]*template.MachineTemplate, int64, error)
	// GetByID 根据ID获取套餐模板
	GetByID(ctx context.Context, id uint) (*template.MachineTemplate, error)
	// Create 创建套餐模板
	Create(ctx context.Context, template *template.MachineTemplate) (*template.MachineTemplate, error)
	// Update 更新套餐模板
	Update(ctx context.Context, template *template.MachineTemplate) (*template.MachineTemplate, error)
	// Delete 删除套餐模板
	Delete(ctx context.Context, id uint) error
}

// machineTemplateService 套餐模板服务实现
type machineTemplateService struct {
	templateRepo  repo.MachineTemplateRepository
	componentRepo repo.TemplateComponentRepository
}

// NewMachineTemplateService 创建套餐模板服务
func NewMachineTemplateService(templateRepo repo.MachineTemplateRepository, componentRepo repo.TemplateComponentRepository) MachineTemplateService {
	return &machineTemplateService{
		templateRepo:  templateRepo,
		componentRepo: componentRepo,
	}
}

// List 查询套餐模板列表
func (s *machineTemplateService) List(ctx context.Context, page, pageSize int, query, category string) ([]*template.MachineTemplate, int64, error) {
	return s.templateRepo.List(ctx, page, pageSize, query, category)
}

// GetByID 根据ID获取套餐模板
func (s *machineTemplateService) GetByID(ctx context.Context, id uint) (*template.MachineTemplate, error) {
	return s.templateRepo.GetByID(ctx, id)
}

// Create 创建套餐模板
func (s *machineTemplateService) Create(ctx context.Context, tmpl *template.MachineTemplate) (*template.MachineTemplate, error) {
	// 验证模板名称不能为空
	if tmpl.TemplateName == "" {
		return nil, errors.New("模板名称不能为空")
	}

	// 保存组件列表到JSON字段
	if len(tmpl.Components) > 0 {
		componentJSON, err := json.Marshal(tmpl.Components)
		if err != nil {
			return nil, err
		}
		tmpl.ComponentList = template.JSONMap(componentJSON)
	}

	// 创建模板
	createdTemplate, err := s.templateRepo.Create(ctx, tmpl)
	if err != nil {
		return nil, err
	}

	// 创建组件
	if len(tmpl.Components) > 0 {
		for i := range tmpl.Components {
			tmpl.Components[i].TemplateID = createdTemplate.ID
		}

		// 将 []template.TemplateComponent 转换为 []*template.TemplateComponent
		componentPointers := make([]*template.TemplateComponent, len(tmpl.Components))
		for i := range tmpl.Components {
			componentPointers[i] = &tmpl.Components[i]
		}

		if err := s.componentRepo.BatchCreate(ctx, componentPointers); err != nil {
			// 如果创建组件失败，需要删除已创建的模板
			deleteErr := s.templateRepo.Delete(ctx, createdTemplate.ID)
			if deleteErr != nil {
				// 记录删除失败，但仍然返回原始错误
				return nil, errors.New("创建组件失败且清理模板失败: " + err.Error() + ", " + deleteErr.Error())
			}
			return nil, err
		}
	}

	// 返回带有组件的完整模板
	return s.templateRepo.GetByID(ctx, createdTemplate.ID)
}

// Update 更新套餐模板
func (s *machineTemplateService) Update(ctx context.Context, tmpl *template.MachineTemplate) (*template.MachineTemplate, error) {
	// 验证模板ID和名称不能为空
	if tmpl.ID == 0 {
		return nil, errors.New("模板ID不能为空")
	}

	if tmpl.TemplateName == "" {
		return nil, errors.New("模板名称不能为空")
	}

	// 检查模板是否存在
	_, err := s.templateRepo.GetByID(ctx, tmpl.ID)
	if err != nil {
		return nil, errors.New("模板不存在")
	}

	// 保存组件列表到JSON字段
	if len(tmpl.Components) > 0 {
		componentJSON, err := json.Marshal(tmpl.Components)
		if err != nil {
			return nil, err
		}
		tmpl.ComponentList = template.JSONMap(componentJSON)
	}

	// 更新模板
	updatedTemplate, err := s.templateRepo.Update(ctx, tmpl)
	if err != nil {
		return nil, err
	}

	// 更新组件：先删除旧组件，再添加新组件
	if err := s.componentRepo.DeleteByTemplateID(ctx, tmpl.ID); err != nil {
		return nil, err
	}

	if len(tmpl.Components) > 0 {
		for i := range tmpl.Components {
			tmpl.Components[i].TemplateID = tmpl.ID
			tmpl.Components[i].ID = 0 // 清除ID以便创建新记录
		}

		// 将 []template.TemplateComponent 转换为 []*template.TemplateComponent
		componentPointers := make([]*template.TemplateComponent, len(tmpl.Components))
		for i := range tmpl.Components {
			componentPointers[i] = &tmpl.Components[i]
		}

		if err := s.componentRepo.BatchCreate(ctx, componentPointers); err != nil {
			return nil, err
		}
	}

	// 返回带有组件的完整模板
	return s.templateRepo.GetByID(ctx, updatedTemplate.ID)
}

// Delete 删除套餐模板
func (s *machineTemplateService) Delete(ctx context.Context, id uint) error {
	// 检查模板是否存在
	_, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		return errors.New("模板不存在")
	}

	// 级联删除:GORM将自动删除关联的组件
	return s.templateRepo.Delete(ctx, id)
}
