package template

import (
	"context"
	"errors"

	"backend/internal/modules/cmdb/model/template"
	productRepo "backend/internal/modules/cmdb/repository/product"
	repo "backend/internal/modules/cmdb/repository/template"
)

// TemplateComponentService 模板组件服务接口
type TemplateComponentService interface {
	// ListByTemplateID 根据模板ID获取组件列表
	ListByTemplateID(ctx context.Context, templateID uint) ([]*template.TemplateComponent, error)
	// Create 创建模板组件
	Create(ctx context.Context, component *template.TemplateComponent) (*template.TemplateComponent, error)
	// Update 更新模板组件
	Update(ctx context.Context, component *template.TemplateComponent) (*template.TemplateComponent, error)
	// Delete 删除模板组件
	Delete(ctx context.Context, id uint) error
}

// ComponentService 模板组件服务实现
type ComponentService struct {
	componentRepo repo.TemplateComponentRepository
	templateRepo  repo.MachineTemplateRepository
	productRepo   productRepo.ProductRepository
}

// NewTemplateComponentService 创建模板组件服务
func NewTemplateComponentService(
	componentRepo repo.TemplateComponentRepository,
	templateRepo repo.MachineTemplateRepository,
	productRepo productRepo.ProductRepository,
) TemplateComponentService {
	return &ComponentService{
		componentRepo: componentRepo,
		templateRepo:  templateRepo,
		productRepo:   productRepo,
	}
}

// ListByTemplateID 根据模板ID获取组件列表
func (s *ComponentService) ListByTemplateID(ctx context.Context, templateID uint) ([]*template.TemplateComponent, error) {
	// 验证参数
	if templateID == 0 {
		return nil, errors.New("模板ID不能为空")
	}

	// 检查模板是否存在
	_, err := s.templateRepo.GetByID(ctx, templateID)
	if err != nil {
		return nil, errors.New("模板不存在")
	}

	// 获取组件列表
	return s.componentRepo.ListByTemplateID(ctx, templateID)
}

// Create 创建模板组件
func (s *ComponentService) Create(ctx context.Context, component *template.TemplateComponent) (*template.TemplateComponent, error) {
	// 验证必要字段
	if component == nil {
		return nil, errors.New("组件信息不能为空")
	}

	if component.TemplateID == 0 {
		return nil, errors.New("模板ID不能为空")
	}

	if component.ProductID == 0 {
		return nil, errors.New("产品ID不能为空")
	}

	if component.Quantity <= 0 {
		component.Quantity = 1 // 默认数量为1
	}

	// 检查模板是否存在
	_, err := s.templateRepo.GetByID(ctx, component.TemplateID)
	if err != nil {
		return nil, errors.New("模板不存在")
	}

	// 检查产品是否存在
	_, err = s.productRepo.GetByID(ctx, component.ProductID)
	if err != nil {
		return nil, errors.New("产品不存在")
	}

	// 创建组件
	return s.componentRepo.Create(ctx, component)
}

// Update 更新模板组件
func (s *ComponentService) Update(ctx context.Context, component *template.TemplateComponent) (*template.TemplateComponent, error) {
	// 验证必要字段
	if component == nil {
		return nil, errors.New("组件信息不能为空")
	}

	if component.ID == 0 {
		return nil, errors.New("组件ID不能为空")
	}

	if component.ProductID == 0 {
		return nil, errors.New("产品ID不能为空")
	}

	if component.Quantity <= 0 {
		component.Quantity = 1 // 默认数量为1
	}

	// 检查产品是否存在
	_, err := s.productRepo.GetByID(ctx, component.ProductID)
	if err != nil {
		return nil, errors.New("产品不存在")
	}

	// 更新组件
	return s.componentRepo.Update(ctx, component)
}

// Delete 删除模板组件
func (s *ComponentService) Delete(ctx context.Context, id uint) error {
	// 验证参数
	if id == 0 {
		return errors.New("组件ID不能为空")
	}

	// 删除组件
	return s.componentRepo.Delete(ctx, id)
}
