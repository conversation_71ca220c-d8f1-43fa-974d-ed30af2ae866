package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/inventory"
	assetRepo "backend/internal/modules/cmdb/repository/asset"
	"context"
)

// WarehouseService 仓库服务接口
type WarehouseService interface {
	// 基本CRUD操作
	CreateWarehouse(ctx context.Context, warehouse *asset.Warehouse) error
	UpdateWarehouse(ctx context.Context, warehouse *asset.Warehouse) error
	DeleteWarehouse(ctx context.Context, id uint) error
	GetWarehouseByID(ctx context.Context, id uint) (*asset.Warehouse, error)
	GetWarehouseByCode(ctx context.Context, code string) (*asset.Warehouse, error)
	ListWarehouses(ctx context.Context, page, pageSize int, query, warehouseType string) ([]*asset.Warehouse, int64, error)

	// 仓库统计接口
	GetWarehouseWithStats(ctx context.Context, id uint) (*asset.WarehouseWithStats, error)

	// 仓库内容查询
	GetWarehouseSpares(ctx context.Context, warehouseID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error)
	GetWarehouseInventory(ctx context.Context, warehouseID uint, page, pageSize int) ([]*inventory.InventoryDetail, int64, error)

	// 获取仓库类型
	GetWarehouseTypes(ctx context.Context) []string
}

// warehouseService 仓库服务实现
type warehouseService struct {
	repo assetRepo.WarehouseRepository
}

// NewWarehouseService 创建仓库服务
func NewWarehouseService(repo assetRepo.WarehouseRepository) WarehouseService {
	return &warehouseService{repo: repo}
}

// CreateWarehouse 创建仓库
func (s *warehouseService) CreateWarehouse(ctx context.Context, warehouse *asset.Warehouse) error {
	return s.repo.Create(ctx, warehouse)
}

// UpdateWarehouse 更新仓库
func (s *warehouseService) UpdateWarehouse(ctx context.Context, warehouse *asset.Warehouse) error {
	return s.repo.Update(ctx, warehouse)
}

// DeleteWarehouse 删除仓库
func (s *warehouseService) DeleteWarehouse(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetWarehouseByID 根据ID获取仓库
func (s *warehouseService) GetWarehouseByID(ctx context.Context, id uint) (*asset.Warehouse, error) {
	return s.repo.GetByID(ctx, id)
}

// GetWarehouseByCode 根据编码获取仓库
func (s *warehouseService) GetWarehouseByCode(ctx context.Context, code string) (*asset.Warehouse, error) {
	return s.repo.GetByCode(ctx, code)
}

// ListWarehouses 分页查询仓库列表
func (s *warehouseService) ListWarehouses(ctx context.Context, page, pageSize int, query, warehouseType string) ([]*asset.Warehouse, int64, error) {
	return s.repo.List(ctx, page, pageSize, query, warehouseType)
}

// GetWarehouseWithStats 获取仓库及统计信息
func (s *warehouseService) GetWarehouseWithStats(ctx context.Context, id uint) (*asset.WarehouseWithStats, error) {
	return s.repo.GetWarehouseWithStats(ctx, id)
}

// GetWarehouseSpares 获取仓库内所有备件
func (s *warehouseService) GetWarehouseSpares(ctx context.Context, warehouseID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error) {
	return s.repo.GetWarehouseSpares(ctx, warehouseID, page, pageSize)
}

// GetWarehouseInventory 获取仓库内库存明细
func (s *warehouseService) GetWarehouseInventory(ctx context.Context, warehouseID uint, page, pageSize int) ([]*inventory.InventoryDetail, int64, error) {
	return s.repo.GetWarehouseInventory(ctx, warehouseID, page, pageSize)
}

// GetWarehouseTypes 获取所有仓库类型
func (s *warehouseService) GetWarehouseTypes(ctx context.Context) []string {
	return []string{"备件仓", "主机房仓", "网络设备仓", "通用仓库"}
}
