package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	repo "backend/internal/modules/cmdb/repository/asset"
	"backend/pkg/utils"
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
)

// StatusChangeService 资产状态变更服务接口
type StatusChangeService interface {
	// LogStatusChange 记录状态变更
	LogStatusChange(ctx context.Context, log *asset.StatusChangeLog) error

	// GetAssetStatusHistory 获取资产状态变更历史
	GetAssetStatusHistory(ctx context.Context, assetID uint,params asset.ListParams) ([]*asset.StatusChangeLog, int64, error)

	// GetDeviceOriginalStatusByWorkflowID 获取设备在工单处理前的原始状态
	GetDeviceOriginalStatusByWorkflowID(ctx context.Context, workflowID string) (*asset.StatusChangeLog, error)

	// GetDeviceOriginalStatusBySN 通过设备SN获取指定工单的原始状态
	GetDeviceOriginalStatusBySN(ctx context.Context, deviceSN string, workflowID string) (*asset.StatusChangeLog, error)

	// ChangeAssetStatus 变更资产状态
	ChangeAssetStatus(ctx context.Context, assetID uint, newStatus string, reason string, operatorID uint, operatorName string) error

	// ChangeBizStatus 变更业务状态
	ChangeBizStatus(ctx context.Context, resourceID uint, newStatus string, reason string, operatorID uint, operatorName string) error

	// ProcessAssetStorage 资产入库流程
	ProcessAssetStorage(ctx context.Context, assetID uint, operatorID uint, operatorName string, approverID uint, approverName string) error

	// ProcessAssetOutbound 资产出库流程
	ProcessAssetOutbound(ctx context.Context, assetID uint, operatorID uint, operatorName string, approverID uint, approverName string) error

	// ProcessAssetRacking 资产上架流程
	ProcessAssetRacking(ctx context.Context, assetID uint, cabinetID uint, rackPosition int, operatorID uint, operatorName string) error

	// ProcessAssetDelivery 资产交付流程
	ProcessAssetDelivery(ctx context.Context, assetID uint, project string, operatorID uint, operatorName string) error

	// ProcessAssetMaintenance 资产维修流程
	ProcessAssetMaintenance(ctx context.Context, assetID uint, reason string, operatorID uint, operatorName string) error

	// ProcessAssetScrap 资产报废流程
	ProcessAssetScrap(ctx context.Context, assetID uint, reason string, operatorID uint, operatorName string, approverID uint, approverName string) error
}

// statusChangeService 资产状态变更服务实现
type statusChangeService struct {
	statusRepo   repo.StatusChangeRepository
	deviceRepo   repo.DeviceRepository
	resourceRepo repo.ResourceRepository
}

// NewStatusChangeService 创建资产状态变更服务
func NewStatusChangeService(
	statusRepo repo.StatusChangeRepository,
	deviceRepo repo.DeviceRepository,
	resourceRepo repo.ResourceRepository,
) StatusChangeService {
	return &statusChangeService{
		statusRepo:   statusRepo,
		deviceRepo:   deviceRepo,
		resourceRepo: resourceRepo,
	}
}

// LogStatusChange 记录状态变更
func (s *statusChangeService) LogStatusChange(ctx context.Context, log *asset.StatusChangeLog) error {
	return s.statusRepo.LogStatusChange(ctx, log)
}

// GetAssetStatusHistory 获取资产状态变更历史
func (s *statusChangeService) GetAssetStatusHistory(ctx context.Context, assetID uint,params asset.ListParams) ([]*asset.StatusChangeLog, int64, error) {
	return s.statusRepo.GetAssetStatusHistory(ctx, assetID,params)
}

// GetDeviceOriginalStatusByWorkflowID 获取设备在工单处理前的原始状态
func (s *statusChangeService) GetDeviceOriginalStatusByWorkflowID(ctx context.Context, workflowID string) (*asset.StatusChangeLog, error) {
	return s.statusRepo.GetDeviceOriginalStatusByWorkflowID(ctx, workflowID)
}

// GetDeviceOriginalStatusBySN 通过设备SN获取指定工单的原始状态
func (s *statusChangeService) GetDeviceOriginalStatusBySN(ctx context.Context, deviceSN string, workflowID string) (*asset.StatusChangeLog, error) {
	return s.statusRepo.GetDeviceOriginalStatusBySN(ctx, deviceSN, workflowID)
}

// ChangeAssetStatus 变更资产状态
func (s *statusChangeService) ChangeAssetStatus(ctx context.Context, assetID uint, newStatus string, reason string, operatorID uint, operatorName string) error {
	return s.statusRepo.ChangeAssetStatus(ctx, assetID, newStatus, reason, operatorID, operatorName)
}

// ChangeBizStatus 变更业务状态
func (s *statusChangeService) ChangeBizStatus(ctx context.Context, resourceID uint, newStatus string, reason string, operatorID uint, operatorName string) error {
	return s.statusRepo.ChangeBizStatus(ctx, resourceID, newStatus, reason, operatorID, operatorName)
}

// ProcessAssetStorage 资产入库流程
func (s *statusChangeService) ProcessAssetStorage(ctx context.Context, assetID uint, operatorID uint, operatorName string, approverID uint, approverName string) error {
	// 获取当前资产信息
	device, err := s.deviceRepo.GetByID(ctx, assetID)
	if err != nil {
		return err
	}

	// 检查当前状态是否为待入库
	if device.AssetStatus != asset.AssetStatusPendingStorage {
		return errors.New("资产当前状态不是待入库，无法执行入库操作")
	}

	// 记录状态变更日志
	log := &asset.StatusChangeLog{
		AssetID:        assetID,
		OldAssetStatus: device.AssetStatus,
		NewAssetStatus: asset.AssetStatusInStorage,
		ChangeReason:   "资产入库流程完成",
		OperatorID:     operatorID,
		OperatorName:   operatorName,
		ApproverID:     approverID,
		ApproverName:   approverName,
	}

	if err := s.statusRepo.LogStatusChange(ctx, log); err != nil {
		return err
	}

	// 更新资产状态
	return s.statusRepo.ChangeAssetStatus(ctx, assetID, asset.AssetStatusInStorage, "资产入库流程完成", operatorID, operatorName)
}

// ProcessAssetOutbound 资产出库流程
func (s *statusChangeService) ProcessAssetOutbound(ctx context.Context, assetID uint, operatorID uint, operatorName string, approverID uint, approverName string) error {
	// 获取当前资产信息
	device, err := s.deviceRepo.GetByID(ctx, assetID)
	if err != nil {
		return err
	}

	// 检查当前状态是否为待出库
	if device.AssetStatus != asset.AssetStatusPendingOutbound {
		return errors.New("资产当前状态不是待出库，无法执行出库操作")
	}

	// 记录状态变更日志
	log := &asset.StatusChangeLog{
		AssetID:        assetID,
		OldAssetStatus: device.AssetStatus,
		NewAssetStatus: asset.AssetStatusOutbound,
		ChangeReason:   "资产出库流程完成",
		OperatorID:     operatorID,
		OperatorName:   operatorName,
		ApproverID:     approverID,
		ApproverName:   approverName,
	}

	if err := s.statusRepo.LogStatusChange(ctx, log); err != nil {
		return err
	}

	// 更新资产状态
	return s.statusRepo.ChangeAssetStatus(ctx, assetID, asset.AssetStatusOutbound, "资产出库流程完成", operatorID, operatorName)
}

// ProcessAssetRacking 资产上架流程
func (s *statusChangeService) ProcessAssetRacking(ctx context.Context, assetID uint, cabinetID uint, rackPosition int, operatorID uint, operatorName string) error {
	// 获取当前资产信息
	device, err := s.deviceRepo.GetByID(ctx, assetID)
	if err != nil {
		return err
	}

	// 检查当前状态是否为已出库
	if device.AssetStatus != asset.AssetStatusOutbound {
		return errors.New("资产当前状态不是已出库，无法执行上架操作")
	}

	// 查找是否已有关联的资源记录
	resource, err := s.resourceRepo.GetByAssetID(ctx, assetID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 如果没有资源记录，创建一个新的
	if resource == nil || errors.Is(err, gorm.ErrRecordNotFound) {
		resource = &asset.Resource{
			AssetID:      assetID,
			SN:           device.SN,
			CabinetID:    cabinetID,
			RackPosition: rackPosition,
			BizStatus:    asset.BizStatusMaintaining,
			ResStatus:    asset.ResStatusUnallocated,
			RackingTime:  utils.Date(time.Now()),
		}
		if err := s.resourceRepo.Create(ctx, resource); err != nil {
			return err
		}
	} else {
		// 更新现有资源记录
		resource.CabinetID = cabinetID
		resource.RackPosition = rackPosition
		resource.RackingTime = utils.Date(time.Now())
		if err := s.resourceRepo.Update(ctx, resource); err != nil {
			return err
		}
	}

	// 记录状态变更日志
	log := &asset.StatusChangeLog{
		AssetID:        assetID,
		OldAssetStatus: device.AssetStatus,
		NewAssetStatus: asset.AssetStatusIdle,
		ChangeReason:   "资产上架流程完成",
		OperatorID:     operatorID,
		OperatorName:   operatorName,
	}

	if err := s.statusRepo.LogStatusChange(ctx, log); err != nil {
		return err
	}

	// 更新资产状态
	return s.statusRepo.ChangeAssetStatus(ctx, assetID, asset.AssetStatusIdle, "资产上架流程完成", operatorID, operatorName)
}

// ProcessAssetDelivery 资产交付流程
func (s *statusChangeService) ProcessAssetDelivery(ctx context.Context, assetID uint, project string, operatorID uint, operatorName string) error {
	// 获取当前资产信息
	device, err := s.deviceRepo.GetByID(ctx, assetID)
	if err != nil {
		return err
	}

	// 检查当前状态是否为闲置中
	if device.AssetStatus != asset.AssetStatusIdle {
		return errors.New("资产当前状态不是闲置中，无法执行交付操作")
	}

	// 获取关联的资源记录
	resource, err := s.resourceRepo.GetByAssetID(ctx, assetID)
	if err != nil {
		return err
	}

	// 更新资源记录
	resource.Project = project
	resource.BizStatus = asset.BizStatusActive
	resource.ResStatus = asset.ResStatusAllocated
	resource.DeliveryTime = utils.Date(time.Now())
	if err := s.resourceRepo.Update(ctx, resource); err != nil {
		return err
	}

	// 记录状态变更日志
	log := &asset.StatusChangeLog{
		AssetID:        assetID,
		OldAssetStatus: device.AssetStatus,
		NewAssetStatus: asset.AssetStatusInUse,
		OldBizStatus:   resource.BizStatus,
		NewBizStatus:   asset.BizStatusActive,
		ChangeReason:   "资产交付流程完成",
		OperatorID:     operatorID,
		OperatorName:   operatorName,
	}

	if err := s.statusRepo.LogStatusChange(ctx, log); err != nil {
		return err
	}

	// 更新资产状态
	return s.statusRepo.ChangeAssetStatus(ctx, assetID, asset.AssetStatusInUse, "资产交付流程完成", operatorID, operatorName)
}

// ProcessAssetMaintenance 资产维修流程
func (s *statusChangeService) ProcessAssetMaintenance(ctx context.Context, assetID uint, reason string, operatorID uint, operatorName string) error {
	// 获取当前资产信息
	device, err := s.deviceRepo.GetByID(ctx, assetID)
	if err != nil {
		return err
	}

	// 检查当前状态是否为使用中
	if device.AssetStatus != asset.AssetStatusInUse {
		return errors.New("资产当前状态不是使用中，无法执行维修操作")
	}

	// 获取关联的资源记录
	resource, err := s.resourceRepo.GetByAssetID(ctx, assetID)
	if err != nil {
		return err
	}

	// 更新资源记录
	oldBizStatus := resource.BizStatus
	resource.BizStatus = asset.BizStatusOutage
	if err := s.resourceRepo.Update(ctx, resource); err != nil {
		return err
	}

	// 记录状态变更日志
	log := &asset.StatusChangeLog{
		AssetID:        assetID,
		OldAssetStatus: device.AssetStatus,
		NewAssetStatus: asset.AssetStatusMaintaining,
		OldBizStatus:   oldBizStatus,
		NewBizStatus:   asset.BizStatusOutage,
		ChangeReason:   reason,
		OperatorID:     operatorID,
		OperatorName:   operatorName,
	}

	if err := s.statusRepo.LogStatusChange(ctx, log); err != nil {
		return err
	}

	// 更新资产状态
	return s.statusRepo.ChangeAssetStatus(ctx, assetID, asset.AssetStatusMaintaining, reason, operatorID, operatorName)
}

// ProcessAssetScrap 资产报废流程
func (s *statusChangeService) ProcessAssetScrap(ctx context.Context, assetID uint, reason string, operatorID uint, operatorName string, approverID uint, approverName string) error {
	// 获取当前资产信息
	device, err := s.deviceRepo.GetByID(ctx, assetID)
	if err != nil {
		return err
	}

	// 检查当前状态是否为待报废
	if device.AssetStatus != asset.AssetStatusPendingScrap {
		return errors.New("资产当前状态不是待报废，无法执行报废操作")
	}

	// 记录状态变更日志
	log := &asset.StatusChangeLog{
		AssetID:        assetID,
		OldAssetStatus: device.AssetStatus,
		NewAssetStatus: asset.AssetStatusScrapped,
		ChangeReason:   reason,
		OperatorID:     operatorID,
		OperatorName:   operatorName,
		ApproverID:     approverID,
		ApproverName:   approverName,
	}

	if err := s.statusRepo.LogStatusChange(ctx, log); err != nil {
		return err
	}

	// 更新资产状态
	return s.statusRepo.ChangeAssetStatus(ctx, assetID, asset.AssetStatusScrapped, reason, operatorID, operatorName)
}
