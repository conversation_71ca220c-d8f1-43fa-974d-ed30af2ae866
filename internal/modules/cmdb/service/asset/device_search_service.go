package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	repo "backend/internal/modules/cmdb/repository/asset"
	"context"
	"errors"
	"fmt"
)

// SearchResult 搜索结果结构体
type SearchResult struct {
	Device   *asset.Device   `json:"device,omitempty"`
	Resource *asset.Resource `json:"resource,omitempty"`
	Source   string          `json:"source"` // device, resource, network_device, spare
}

// DeviceSearchService 设备搜索服务接口
type DeviceSearchService interface {
	// 通过SN在多个资产表中进行查询
	SearchBySN(ctx context.Context, sn string) (*SearchResult, error)
}

// deviceSearchService 设备搜索服务实现
type deviceSearchService struct {
	deviceRepo   repo.DeviceRepository
	resourceRepo repo.ResourceRepository
	spareRepo    repo.SpareRepository
}

// NewDeviceSearchService 创建设备搜索服务
func NewDeviceSearchService(
	deviceRepo repo.DeviceRepository,
	resourceRepo repo.ResourceRepository,
	spareRepo repo.SpareRepository,
) DeviceSearchService {
	return &deviceSearchService{
		deviceRepo:   deviceRepo,
		resourceRepo: resourceRepo,
		spareRepo:    spareRepo,
	}
}

// SearchBySN 根据SN在多个表中查询
func (s *deviceSearchService) SearchBySN(ctx context.Context, sn string) (*SearchResult, error) {
	if sn == "" {
		return nil, errors.New("SN不能为空")
	}

	// 首先在设备表中查询
	device, err := s.deviceRepo.GetBySN(ctx, sn)
	if err == nil && device != nil {
		// 尝试查找关联的资源信息
		resource, resourceErr := s.resourceRepo.GetByAssetID(ctx, device.ID)
		if resourceErr == nil && resource != nil {
			// 如果找到关联资源，返回设备和资源信息
			return &SearchResult{
				Device:   device,
				Resource: resource,
				Source:   "device",
			}, nil
		}
		// 如果没有找到关联资源，只返回设备信息
		return &SearchResult{
			Device: device,
			Source: "device",
		}, nil
	}

	// 在资源表中查询
	resource, err := s.resourceRepo.GetBySN(ctx, sn)
	if err == nil && resource != nil {
		// 如果资源表中找到，尝试加载关联的设备信息
		if resource.AssetID > 0 {
			device, err := s.deviceRepo.GetByID(ctx, resource.AssetID)
			if err == nil && device != nil {
				return &SearchResult{
					Device:   device,
					Resource: resource,
					Source:   "resource",
				}, nil
			}
		}
		return &SearchResult{
			Resource: resource,
			Source:   "resource",
		}, nil
	}

	// 在备件表中查询（如果有备件仓库）
	if s.spareRepo != nil {
		spare, err := s.spareRepo.GetBySN(ctx, sn)
		if err == nil && spare != nil {
			return &SearchResult{
				Source: "spare",
			}, nil
		}
	}

	// 如果都找不到
	return nil, fmt.Errorf("未找到SN为[%s]的设备或资源", sn)
}
