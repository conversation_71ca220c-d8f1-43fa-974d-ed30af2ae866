package location

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/location"
	repo "backend/internal/modules/cmdb/repository/location"
	"context"
)

// RegionService 区域服务接口
type RegionService interface {
	CreateRegion(ctx context.Context, region *location.Region) error
	UpdateRegion(ctx context.Context, region *location.Region) error
	DeleteRegion(ctx context.Context, id uint) error
	GetRegionByID(ctx context.Context, id uint) (*location.Region, error)
	GetRegionByName(ctx context.Context, name string) (*location.Region, error)
	ListRegions(ctx context.Context, page, pageSize int, query string) ([]*location.Region, int64, error)
	GetWarehousesByProject(ctx context.Context, project string) ([]asset.Warehouse, error)
}

// regionService 区域服务实现
type regionService struct {
	repo repo.RegionRepository
}

// NewRegionService 创建区域服务
func NewRegionService(repo repo.RegionRepository) RegionService {
	return &regionService{repo: repo}
}

// CreateRegion 创建区域
func (s *regionService) CreateRegion(ctx context.Context, region *location.Region) error {
	return s.repo.Create(ctx, region)
}

// UpdateRegion 更新区域
func (s *regionService) UpdateRegion(ctx context.Context, region *location.Region) error {
	return s.repo.Update(ctx, region)
}

// DeleteRegion 删除区域
func (s *regionService) DeleteRegion(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetRegionByID 根据ID获取区域
func (s *regionService) GetRegionByID(ctx context.Context, id uint) (*location.Region, error) {
	return s.repo.GetByID(ctx, id)
}

// GetRegionByName 根据名称获取区域
func (s *regionService) GetRegionByName(ctx context.Context, name string) (*location.Region, error) {
	return s.repo.GetByName(ctx, name)
}

// ListRegions 分页查询区域列表
func (s *regionService) ListRegions(ctx context.Context, page, pageSize int, query string) ([]*location.Region, int64, error) {
	return s.repo.List(ctx, page, pageSize, query)
}

func (s *regionService) GetWarehousesByProject(ctx context.Context, project string) ([]asset.Warehouse, error) {
	return s.repo.GetWarehousesByProject(ctx, project)
}
