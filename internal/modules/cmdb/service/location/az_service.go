// service/location/az_service.go
package location

import (
	"backend/internal/modules/cmdb/model/location"
	repo "backend/internal/modules/cmdb/repository/location"
	"context"
)

// AZService 可用区服务接口
type AZService interface {
	CreateAZ(ctx context.Context, az *location.AZ) error
	UpdateAZ(ctx context.Context, az *location.AZ) error
	DeleteAZ(ctx context.Context, id uint) error
	GetAZByID(ctx context.Context, id uint) (*location.AZ, error)
	GetAZByName(ctx context.Context, name string) (*location.AZ, error)
	GetAZsByRegionID(ctx context.Context, regionID uint) ([]*location.AZ, error)
	ListAZs(ctx context.Context, page, pageSize int, query string, regionId int) ([]*location.AZ, int64, error)
}

// azService 可用区服务实现
type azService struct {
	repo repo.AZRepository
}

// NewAZService 创建可用区服务
func NewAZService(repo repo.AZRepository) AZService {
	return &azService{repo: repo}
}

// CreateAZ 创建可用区
func (s *azService) CreateAZ(ctx context.Context, az *location.AZ) error {
	return s.repo.Create(ctx, az)
}

// UpdateAZ 更新可用区
func (s *azService) UpdateAZ(ctx context.Context, az *location.AZ) error {
	return s.repo.Update(ctx, az)
}

// DeleteAZ 删除可用区
func (s *azService) DeleteAZ(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetAZByID 根据ID获取可用区
func (s *azService) GetAZByID(ctx context.Context, id uint) (*location.AZ, error) {
	return s.repo.GetByID(ctx, id)
}

// GetAZByName 根据名称获取可用区
func (s *azService) GetAZByName(ctx context.Context, name string) (*location.AZ, error) {
	return s.repo.GetByName(ctx, name)
}

// GetAZsByRegionID 根据区域ID获取可用区列表
func (s *azService) GetAZsByRegionID(ctx context.Context, regionID uint) ([]*location.AZ, error) {
	return s.repo.GetByRegionID(ctx, regionID)
}

// ListAZs 分页查询可用区列表
func (s *azService) ListAZs(ctx context.Context, page, pageSize int, query string, regionID int) ([]*location.AZ, int64, error) {
	return s.repo.List(ctx, page, pageSize, query, regionID)
}
