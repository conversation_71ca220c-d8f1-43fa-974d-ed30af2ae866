// service/location/data_center_service.go
package location

import (
	"backend/internal/modules/cmdb/model/location"
	repo "backend/internal/modules/cmdb/repository/location"
	"context"
)

// DataCenterService 机房服务接口
type DataCenterService interface {
	CreateDataCenter(ctx context.Context, dataCenter *location.DataCenter) error
	UpdateDataCenter(ctx context.Context, dataCenter *location.DataCenter) error
	DeleteDataCenter(ctx context.Context, id uint) error
	GetDataCenterByID(ctx context.Context, id uint) (*location.DataCenter, error)
	GetDataCenterByName(ctx context.Context, name string) (*location.DataCenter, error)
	GetDataCentersByAZID(ctx context.Context, azID uint) ([]*location.DataCenter, error)
	ListDataCenters(ctx context.Context, page, pageSize int, query string) ([]*location.DataCenter, int64, error)
}

// dataCenterService 机房服务实现
type dataCenterService struct {
	repo repo.DataCenterRepository
}

// NewDataCenterService 创建机房服务
func NewDataCenterService(repo repo.DataCenterRepository) DataCenterService {
	return &dataCenterService{repo: repo}
}

// CreateDataCenter 创建机房
func (s *dataCenterService) CreateDataCenter(ctx context.Context, dataCenter *location.DataCenter) error {
	return s.repo.Create(ctx, dataCenter)
}

// UpdateDataCenter 更新机房
func (s *dataCenterService) UpdateDataCenter(ctx context.Context, dataCenter *location.DataCenter) error {
	return s.repo.Update(ctx, dataCenter)
}

// DeleteDataCenter 删除机房
func (s *dataCenterService) DeleteDataCenter(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetDataCenterByID 根据ID获取机房
func (s *dataCenterService) GetDataCenterByID(ctx context.Context, id uint) (*location.DataCenter, error) {
	return s.repo.GetByID(ctx, id)
}

// GetDataCenterByName 根据名称获取机房
func (s *dataCenterService) GetDataCenterByName(ctx context.Context, name string) (*location.DataCenter, error) {
	return s.repo.GetByName(ctx, name)
}

// GetDataCentersByAZID 根据可用区ID获取机房列表
func (s *dataCenterService) GetDataCentersByAZID(ctx context.Context, azID uint) ([]*location.DataCenter, error) {
	return s.repo.GetByAZID(ctx, azID)
}

// ListDataCenters 分页查询机房列表
func (s *dataCenterService) ListDataCenters(ctx context.Context, page, pageSize int, query string) ([]*location.DataCenter, int64, error) {
	return s.repo.List(ctx, page, pageSize, query)
}
