package product

import (
	"context"
	"errors"

	"backend/internal/common/utils"
	"backend/internal/modules/cmdb/model/product"
	productRepo "backend/internal/modules/cmdb/repository/product"
)

var (
	// ErrProductNotFound 产品不存在
	ErrProductNotFound = errors.New("产品不存在")
	// ErrInvalidProductData 无效的产品数据
	ErrInvalidProductData = errors.New("无效的产品数据")
)

// CreateProductDTO 创建产品DTO
type CreateProductDTO = product.CreateProductDTO

// UpdateProductDTO 更新产品DTO
type UpdateProductDTO = product.UpdateProductDTO

// ProductListQuery 产品列表查询参数
type ProductListQuery struct {
	PN              string `form:"pn"`
	MaterialType    string `form:"material_type"`
	Brand           string `form:"brand"`
	Model           string `form:"model"`
	Spec            string `form:"spec"`
	ProductCategory string `form:"product_category"`
	Status          int8   `form:"status"`
	Page            int    `form:"page" binding:"required,min=1"`
	PageSize        int    `form:"pageSize" binding:"required,min=1,max=99999"`
}

// ProductListResult 产品列表查询结果
type ProductListResult struct {
	Total int64              `json:"total"`
	List  []*product.Product `json:"list"`
}

// ProductService 产品服务接口
type ProductService interface {
	// GetByID 获取产品详情
	GetByID(ctx context.Context, id uint) (*product.Product, error)

	// List 获取产品列表
	List(ctx context.Context, query ProductListQuery) (*ProductListResult, error)

	// Create 创建产品
	Create(ctx context.Context, dto CreateProductDTO) (*product.Product, error)

	// Update 更新产品
	Update(ctx context.Context, dto UpdateProductDTO) (*product.Product, error)

	// Delete 删除产品
	Delete(ctx context.Context, id uint) error

	// GetMaterialTypes 获取所有物料类型
	GetMaterialTypes(ctx context.Context) ([]string, error)

	// GetProductCategories 获取所有产品类别
	GetProductCategories(ctx context.Context) ([]string, error)

	// GetBrands 获取所有品牌
	GetBrands(ctx context.Context) ([]string, error)

	// GetSpecsByMaterialType 获取特定物料类型的规格列表
	GetSpecsByMaterialType(ctx context.Context, materialType string) ([]string, error)

	// GetAllSpecs 获取所有规格列表
	GetAllSpecs(ctx context.Context) ([]string, error)

	// ListByPNs 通过PN获取组件列表
	ListByPNs(ctx context.Context, pns []string) ([]*product.Product, error)

	// GetByPNAndBrand 根据PN和品牌查找产品
	GetByPNAndBrand(ctx context.Context, pn, brand string) (*product.Product, error)

	// UpdateUnit 更新产品单位
	UpdateUnit(ctx context.Context, productID uint, unit string) error
}

type productService struct {
	repo productRepo.ProductRepository
}

// NewProductService 创建产品服务
func NewProductService(repo productRepo.ProductRepository) ProductService {
	return &productService{repo: repo}
}

// GetByID 获取产品详情
func (s *productService) GetByID(ctx context.Context, id uint) (*product.Product, error) {
	p, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, ErrProductNotFound
	}
	return p, nil
}

// List 获取产品列表
func (s *productService) List(ctx context.Context, query ProductListQuery) (*ProductListResult, error) {
	filter := productRepo.ProductFilter{
		PN:              query.PN,
		MaterialType:    query.MaterialType,
		Brand:           query.Brand,
		Model:           query.Model,
		Spec:            query.Spec,
		ProductCategory: query.ProductCategory,
		Status:          query.Status,
	}

	pagination := productRepo.PaginationOptions{
		Page:     query.Page,
		PageSize: query.PageSize,
	}

	products, total, err := s.repo.List(ctx, filter, pagination)
	if err != nil {
		return nil, err
	}

	return &ProductListResult{
		Total: total,
		List:  products,
	}, nil
}

// Create 创建产品
func (s *productService) Create(ctx context.Context, dto CreateProductDTO) (*product.Product, error) {
	// 数据验证逻辑可以在这里添加
	if dto.MaterialType == "" || dto.Brand == "" || dto.Model == "" || dto.PN == "" {
		return nil, ErrInvalidProductData
	}

	// 使用DTO中的ToProduct方法创建产品对象
	p, err := dto.ToProduct()
	if err != nil {
		return nil, err
	}

	// 设置创建者信息
	userID := utils.GetUserIDWithDefault(ctx, 0)
	p.CreatedBy = userID
	p.UpdatedBy = userID

	if err := s.repo.Create(ctx, p); err != nil {
		return nil, err
	}

	return p, nil
}

// Update 更新产品
func (s *productService) Update(ctx context.Context, dto UpdateProductDTO) (*product.Product, error) {
	// 先检查产品是否存在
	existingProduct, err := s.repo.GetByID(ctx, dto.ID)
	if err != nil {
		return nil, ErrProductNotFound
	}

	// 使用DTO中的UpdateProduct方法更新产品字段
	if err := dto.UpdateProduct(existingProduct); err != nil {
		return nil, err
	}

	// 设置更新者信息
	existingProduct.UpdatedBy = utils.GetUserIDWithDefault(ctx, 0)

	if err := s.repo.Update(ctx, existingProduct); err != nil {
		return nil, err
	}

	return existingProduct, nil
}

// Delete 删除产品
func (s *productService) Delete(ctx context.Context, id uint) error {
	// 先检查产品是否存在
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return ErrProductNotFound
	}

	// 这里可以添加检查产品是否被其他模块引用的逻辑

	return s.repo.Delete(ctx, id)
}

// GetMaterialTypes 获取所有物料类型
func (s *productService) GetMaterialTypes(ctx context.Context) ([]string, error) {
	// 预定义的基础物料类型列表
	predefinedTypes := []string{
		"CPU", "内存", "硬盘", "SSD", "显卡", "主板", "机箱", "电源模块", "风扇", "散热器", "跳线", "BMC板",
		"网卡", "线缆", "光模块", "raid卡", "GPU底板", "Switch板", "背板", "服务器", "交换机", "路由器", "维保", "AOC", "其他",
	}

	// 从数据库获取现有类型
	dbTypes, err := s.repo.GetMaterialTypes(ctx)
	if err != nil {
		// 如果数据库查询失败，只返回预定义类型
		return predefinedTypes, nil
	}

	// 合并预定义类型和数据库类型，并去重
	allTypes := append(predefinedTypes, dbTypes...)
	result := make([]string, 0)
	seen := make(map[string]bool)

	for _, t := range allTypes {
		if !seen[t] && t != "" {
			seen[t] = true
			result = append(result, t)
		}
	}

	return result, nil
}

// GetProductCategories 获取所有产品类别
func (s *productService) GetProductCategories(ctx context.Context) ([]string, error) {
	// 预定义的产品类别
	predefinedCategories := []string{
		"服务器", "存储设备", "网络设备", "网络设备配件", "配件", "线缆", "维保", "AOC", "其他",
	}

	// 从数据库获取现有类别
	dbCategories, err := s.repo.GetProductCategories(ctx)
	if err != nil {
		return predefinedCategories, nil
	}

	// 合并并去重
	allCategories := append(predefinedCategories, dbCategories...)
	result := make([]string, 0)
	seen := make(map[string]bool)

	for _, c := range allCategories {
		if !seen[c] && c != "" {
			seen[c] = true
			result = append(result, c)
		}
	}

	return result, nil
}

// GetBrands 获取所有品牌
func (s *productService) GetBrands(ctx context.Context) ([]string, error) {
	// 预定义的常见品牌
	predefinedBrands := []string{
		"Intel", "AMD", "Nvidia", "Samsung", "Huawei", "Dell", "HP",
		"Lenovo", "Cisco", "Juniper", "Seagate", "Western Digital", "其他",
	}

	// 从数据库获取现有品牌
	dbBrands, err := s.repo.GetBrands(ctx)
	if err != nil {
		return predefinedBrands, nil
	}

	// 合并并去重
	allBrands := append(predefinedBrands, dbBrands...)
	result := make([]string, 0)
	seen := make(map[string]bool)

	for _, b := range allBrands {
		if !seen[b] && b != "" {
			seen[b] = true
			result = append(result, b)
		}
	}

	return result, nil
}

// GetSpecsByMaterialType 获取特定物料类型的规格列表
func (s *productService) GetSpecsByMaterialType(ctx context.Context, materialType string) ([]string, error) {
	// 预定义的规格，按物料类型分类
	predefinedSpecs := map[string][]string{
		"CPU": {
			"6448Y 2.1G 32C",
			"8462Y+ 2.8G 32C",
			"8468 3.0G 48C",
			"8468v 2.1G 48C",
			"8480+ 2.0G 56C",
			"8558 2.1G 48C",
			"9654 2.4G 96C",
		},
		"GPU底板": {
			"H800 SXM5 GPU 80GB HBM3",
			"Cray XD670 H800 GPU 底板",
			"DGXH-G648F+P2INI36 H800 GPU 底板",
			"ESC N8-E11 H800 GPU 底板",
			"G593-SD2 H800 GPU 底板",
			"G8600V7 H800 GPU 底板",
			"NF5688M7 H800 GPU 底板",
			"R5560G6 H800 GPU 底板",
		},
		"网卡": {
			"200G HDR CX6 单口",
			"10GE NAGATE 端口",
			"10GE 端口",
			"25GE RJ-11",
			"40GE 端口",
			"200G HQH CSU 端口",
			"25G RJ1332A 端口",
		},
		"硬盘": {
			"3.84T NVME SSD",
			"1.92T NVME M2",
			"3.84T NVME SSD",
			"480G NVME M2 SSD",
			"960G NVME SSD",
			"7.68T NVME SSD",
			"SYS-R2TGE-TN18R 1890 GPU 底板",
			"ACC-R04C-QSFP56D/2QSFP56-80M",
			"NMI-MP1(V2.CNA4.D3.5-30M",
			"NMI-MP1(V2.CNA4.D3.5-15M",
			"ESC N8-E11 1890 GPU底板",
			"G8600V7 H800 底板底",
			"C935-D2 H890 底板底",
			"R5560G6 H890 底部板",
		},
		"主板": {
			"DGXH-G648F+P2INI36 H800 主板",
			"R5560G6 H800 主板",
			"Cray XD670 H800 主板",
			"ESC N8-E11 H800 主板",
			"G593-SD2 H800 主板",
			"G8600V7 H800 主板",
			"NF5688M7 H800 主板",
		},
		"线缆": {
			"ACC-R04C-QSFP56D/2QSFP56-80M",
			"ACC-R04C-QSFP56D/2QSFP56-10M",
			"LC/LC OM2 D3.0 30M",
			"LC/LC OM2 D3.0 50M",
			"LC/LC OM3 D3.0 5M",
			"LC/LC OM2 D3.0 10M",
			"LC/LC OM3 D3.0 10M",
			"MMF-MPO12-OM4-D4.5-10M",
			"MMF-MPO12-OM4-D4.5-20M",
			"MMF-MPO12-OM4-D4.5-30M",
			"MMF-MPO16-OM3-30M",
			"MMF-MPO16-OM3-50M",
			"MMF-MPO16-OM4-D3.5-10M",
			"MMF-MPO16-OM4-D3.5-20M",
			"MMF-MPO16-OM4-D3.5-30M",
			"MMF-MPO16-OM4-D3.5-40M",
			"MMF-MPO8-OM4-D3.0-40M",
			"SM-MPO12-OS2-D4.5-100M",
			"SM-MPO12-OS2-D4.5-10M",
			"SM-MPO12-OS2-D4.5-20M",
			"SM-MPO12-OS2-D4.5-35M",
			"SM-MPO12-OS2-D4.5-45M",
			"SM-MPO12-OS2-D4.5-50M",
			"SM-MPO12-OS2-D4.5-55M",
			"SM-MPO12-OS2-D4.5-60M",
			"SM-MPO12-OS2-D4.5-65M",
			"SM-MPO12-OS2-D4.5-70M",
			"SM-MPO12-OS2-D4.5-150M",
		},
		"光模块": {
			"COREB-IB-R-9790",
			"400G-OSFP-SR4-MMF50M",
			"800G-OSFP-2*SR4-MMF50M",
			"800G-OSFP-2*DR4-SMF100M",
			"800G-OSFP-2*DR4-SMF500M",
			"400G-QSFP-DD-SR8-MM-850-100M",
			"400G-QSFP-DD-FR4-SM1310-2KM",
			"400G-QSFP-DD-LR4-SM1310-10KM",
			"200G-QSFP56-SR4-MM850-100M",
			"100G-QSFP28-CWDM4-SM1310-2KM",
			"100G-QSFP28-CWDM-MM850-100M",
			"10G-SFP-XG-LX-SM1310-10KM",
			"400G-QSFP-DD-DR4-SM1310-500M",
			"400G-QSFP-DD-SR8-MM850-100M",
			"100G-QSFP28-CWDM4-SM2KM",
			"100G-QSFP28-SR4-MM850-100M",
			"100G-QSFP28-LR4-SM1310-10KM",
		},
		"交换机": {
			"H3C-6850-56HF交换机",
			"H3C-S5560X-54C-EI交换机",
			"H3C-S9825-64D交换机",
			"H3C-S9827-64EP交换机",
			"IB-MQ9790-NS2F交换机",
			"维谛串口管理器ACS8000",
			"SYS-R2TGE-TN18R 1890 底板底",
			"DGXH-G648F+P2INI36 1890 交换机",
			"G593-SD2 H890 交换机",
			"G8600V7 H890 交换机",
			"NF5688M7 H890 交换机",
			"SYS-R2TGE-TN18R 交换机",
		},
		"BMC板": {
			"Cray XD670 H800 BMC板",
			"DGXH-G648F+P2INI36 H800 BMC板",
			"ESC N8-E11 H800 BMC板",
			"G593-SD2 H800 BMC板",
			"G8600V7 H800 BMC板",
			"NF5688M7 H800 BMC板",
			"R5560G6 H800 BMC板",
			"SYS-821GE-TNHR H800 BMC板",
		},
		"电源模块": {
			"H3C-S5560X-54C-EI交换机电源模块",
			"H3C-S9825-64D交换机电源模块",
		},
		"维保": {
			"9790交换机5年维保",
			"IB模块5年维保",
		},
		"内存": {
			"32G DDR4 3200",
			"64G DDR4 3200",
			"16G DDR4 3200",
			"96G DDR5 5400",
		},
		"服务器规格": {
			"ESC N8-E11 8665v CPU 服务器资产类型",
			"G8600V7 8468 CPU 服务器",
			"NF5688M7 6448 CPU 服务器资产类型",
			"C935-D2 E7-4870 CPU 数据服务器",
			"R5560G6 8468 CPU 服务器资产类型",
		},
	}

	// 如果有预定义规格，与数据库规格合并
	if specs, ok := predefinedSpecs[materialType]; ok {
		dbSpecs, err := s.repo.GetSpecsByMaterialType(ctx, materialType)
		if err != nil {
			return specs, nil // 数据库查询失败时返回预定义规格
		}

		// 合并并去重
		allSpecs := append(specs, dbSpecs...)
		result := make([]string, 0)
		seen := make(map[string]bool)

		for _, spec := range allSpecs {
			if !seen[spec] && spec != "" {
				seen[spec] = true
				result = append(result, spec)
			}
		}

		return result, nil
	}

	// 无预定义规格时从数据库获取
	return s.repo.GetSpecsByMaterialType(ctx, materialType)
}

// GetAllSpecs 获取所有规格列表
func (s *productService) GetAllSpecs(ctx context.Context) ([]string, error) {
	// 预定义的规格，按物料类型分类
	predefinedSpecs := map[string][]string{
		"CPU": {
			"6448Y 2.1G 32C",
			"8462Y+ 2.8G 32C",
			"8468 3.0G 48C",
			"8468v 2.1G 48C",
			"8480+ 2.0G 56C",
			"8558 2.1G 48C",
			"9654 2.4G 96C",
		},
		"GPU底板": {
			"H800 SXM5 GPU 80GB HBM3",
			"Cray XD670 H800 GPU 底板",
			"DGXH-G648F+P2INI36 H800 GPU 底板",
			"ESC N8-E11 H800 GPU 底板",
			"G593-SD2 H800 GPU 底板",
			"G8600V7 H800 GPU 底板",
			"NF5688M7 H800 GPU 底板",
			"R5560G6 H800 GPU 底板",
		},
		"网卡": {
			"200G HDR CX6 单口",
			"10GE NAGATE 端口",
			"10GE 端口",
			"25GE RJ-11",
			"40GE 端口",
			"200G HQH CSU 端口",
			"25G RJ1332A 端口",
		},
		"硬盘": {
			"3.84T NVME SSD",
			"1.92T NVME M2",
			"3.84T NVME SSD",
			"480G NVME M2 SSD",
			"960G NVME SSD",
			"7.68T NVME SSD",
			"SYS-R2TGE-TN18R 1890 GPU 底板",
			"ACC-R04C-QSFP56D/2QSFP56-80M",
			"NMI-MP1(V2.CNA4.D3.5-30M",
			"NMI-MP1(V2.CNA4.D3.5-15M",
			"ESC N8-E11 1890 GPU底板",
			"G8600V7 H800 底板底",
			"C935-D2 H890 底板底",
			"R5560G6 H890 底部板",
		},
		"主板": {
			"DGXH-G648F+P2INI36 H800 主板",
			"R5560G6 H800 主板",
			"Cray XD670 H800 主板",
			"ESC N8-E11 H800 主板",
			"G593-SD2 H800 主板",
			"G8600V7 H800 主板",
			"NF5688M7 H800 主板",
		},
		"线缆": {
			"ACC-R04C-QSFP56D/2QSFP56-80M",
			"ACC-R04C-QSFP56D/2QSFP56-10M",
			"LC/LC OM2 D3.0 30M",
			"LC/LC OM2 D3.0 50M",
			"LC/LC OM3 D3.0 5M",
			"LC/LC OM2 D3.0 10M",
			"LC/LC OM3 D3.0 10M",
			"MMF-MPO12-OM4-D4.5-10M",
			"MMF-MPO12-OM4-D4.5-20M",
			"MMF-MPO12-OM4-D4.5-30M",
			"MMF-MPO16-OM3-30M",
			"MMF-MPO16-OM3-50M",
			"MMF-MPO16-OM4-D3.5-10M",
			"MMF-MPO16-OM4-D3.5-20M",
			"MMF-MPO16-OM4-D3.5-30M",
			"MMF-MPO16-OM4-D3.5-40M",
			"MMF-MPO8-OM4-D3.0-40M",
			"SM-MPO12-OS2-D4.5-100M",
			"SM-MPO12-OS2-D4.5-10M",
			"SM-MPO12-OS2-D4.5-20M",
			"SM-MPO12-OS2-D4.5-35M",
			"SM-MPO12-OS2-D4.5-45M",
			"SM-MPO12-OS2-D4.5-50M",
			"SM-MPO12-OS2-D4.5-55M",
			"SM-MPO12-OS2-D4.5-60M",
			"SM-MPO12-OS2-D4.5-65M",
			"SM-MPO12-OS2-D4.5-70M",
			"SM-MPO12-OS2-D4.5-150M",
		},
		"光模块": {
			"COREB-IB-R-9790",
			"400G-OSFP-SR4-MMF50M",
			"800G-OSFP-2*SR4-MMF50M",
			"800G-OSFP-2*DR4-SMF100M",
			"800G-OSFP-2*DR4-SMF500M",
			"400G-QSFP-DD-SR8-MM-850-100M",
			"400G-QSFP-DD-FR4-SM1310-2KM",
			"400G-QSFP-DD-LR4-SM1310-10KM",
			"200G-QSFP56-SR4-MM850-100M",
			"100G-QSFP28-CWDM4-SM1310-2KM",
			"100G-QSFP28-CWDM-MM850-100M",
			"10G-SFP-XG-LX-SM1310-10KM",
			"400G-QSFP-DD-DR4-SM1310-500M",
			"400G-QSFP-DD-SR8-MM850-100M",
			"100G-QSFP28-CWDM4-SM2KM",
			"100G-QSFP28-SR4-MM850-100M",
			"100G-QSFP28-LR4-SM1310-10KM",
		},
		"交换机": {
			"H3C-6850-56HF交换机",
			"H3C-S5560X-54C-EI交换机",
			"H3C-S9825-64D交换机",
			"H3C-S9827-64EP交换机",
			"IB-MQ9790-NS2F交换机",
			"维谛串口管理器ACS8000",
			"SYS-R2TGE-TN18R 1890 底板底",
			"DGXH-G648F+P2INI36 1890 交换机",
			"G593-SD2 H890 交换机",
			"G8600V7 H890 交换机",
			"NF5688M7 H890 交换机",
			"SYS-R2TGE-TN18R 交换机",
		},
		"BMC板": {
			"Cray XD670 H800 BMC板",
			"DGXH-G648F+P2INI36 H800 BMC板",
			"ESC N8-E11 H800 BMC板",
			"G593-SD2 H800 BMC板",
			"G8600V7 H800 BMC板",
			"NF5688M7 H800 BMC板",
			"R5560G6 H800 BMC板",
			"SYS-821GE-TNHR H800 BMC板",
		},
		"电源模块": {
			"H3C-S5560X-54C-EI交换机电源模块",
			"H3C-S9825-64D交换机电源模块",
		},
		"维保": {
			"9790交换机5年维保",
			"IB模块5年维保",
		},
		"内存": {
			"32G DDR4 3200",
			"64G DDR4 3200",
			"16G DDR4 3200",
			"96G DDR5 5400",
		},
		"服务器规格": {
			"ESC N8-E11 8665v CPU 服务器资产类型",
			"G8600V7 8468 CPU 服务器",
			"NF5688M7 6448 CPU 服务器资产类型",
			"C935-D2 E7-4870 CPU 数据服务器",
			"R5560G6 8468 CPU 服务器资产类型",
		},
	}

	// 合并所有预定义规格
	allSpecs := make([]string, 0)
	for _, specs := range predefinedSpecs {
		allSpecs = append(allSpecs, specs...)
	}

	// 从数据库获取现有规格
	dbSpecs, err := s.repo.GetAllSpecs(ctx)
	if err != nil {
		return allSpecs, nil // 数据库查询失败时返回预定义规格
	}

	// 合并并去重
	allSpecs = append(allSpecs, dbSpecs...)
	result := make([]string, 0)
	seen := make(map[string]bool)

	for _, spec := range allSpecs {
		if !seen[spec] && spec != "" {
			seen[spec] = true
			result = append(result, spec)
		}
	}

	return result, nil
}

// ListByPNs
func (s *productService) ListByPNs(ctx context.Context, pns []string) ([]*product.Product, error) {
	return s.repo.ListByPNs(ctx, pns)
}

// GetByPNAndBrand 根据PN和品牌查找产品
func (s *productService) GetByPNAndBrand(ctx context.Context, pn, brand string) (*product.Product, error) {
	if pn == "" || brand == "" {
		return nil, ErrInvalidProductData
	}
	return s.repo.GetByPNAndBrand(ctx, pn, brand)
}

// UpdateUnit 更新产品单位
func (s *productService) UpdateUnit(ctx context.Context, productID uint, unit string) error {
	if productID == 0 || unit == "" {
		return ErrInvalidProductData
	}

	// 先获取产品
	prod, err := s.repo.GetByID(ctx, productID)
	if err != nil {
		return ErrProductNotFound
	}

	// 只有当前单位为空时才更新
	if prod.Unit == "" {
		prod.Unit = unit
		prod.UpdatedBy = utils.GetUserIDWithDefault(ctx, 0)
		return s.repo.Update(ctx, prod)
	}

	return nil // 如果单位不为空，不做任何操作
}
