package common

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"backend/response"
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// GetCurrentUserInfo 从上下文获取当前用户信息
func GetCurrentUserInfo(ctx *gin.Context) (uint, string, error) {
	userIDVal, exists := ctx.Get("userID")
	if !exists {
		return 0, "", response.NewErrorWithStatusCode("未获取到用户ID", http.StatusUnauthorized)
	}
	userID, ok := userIDVal.(uint)
	if !ok {
		return 0, "", response.NewErrorWithStatusCode("用户ID格式错误", http.StatusUnauthorized)
	}

	// 获取用户信息
	userInfoVal, exists := ctx.Get("userInfo")
	if !exists {
		return 0, "", response.NewErrorWithStatusCode("未获取到用户信息", http.StatusUnauthorized)
	}
	userInfo, ok := userInfoVal.(map[string]interface{})
	if !ok {
		return 0, "", response.NewErrorWithStatusCode("用户信息格式错误", http.StatusUnauthorized)
	}

	// 获取用户真实姓名
	realName, ok := userInfo["realName"].(string)
	if !ok || realName == "" {
		return 0, "", response.NewErrorWithStatusCode("未获取到用户姓名", http.StatusUnauthorized)
	}

	return userID, realName, nil
}

// RecordStatusHistory 记录状态历史
func RecordStatusHistory(ctx context.Context, repo repository.FaultTicketRepository, ticketID uint, previousStatus, newStatus string, operatorID uint, operatorName, remarks string) error {
	history := &model.FaultTicketStatusHistory{
		FaultTicketID:    ticketID,
		PreviousStatus:   previousStatus,
		NewStatus:        newStatus,
		OperatorID:       operatorID,
		OperatorName:     operatorName,
		Remarks:          remarks,
		OperationTime:    time.Now(),
		ActivityCategory: getActivityCategory(newStatus),
		IsSLAPause:       isSLAPauseStatus(newStatus),
		PauseReason:      getPauseReason(newStatus),
	}
	return repo.CreateStatusHistory(ctx, history)
}

// getActivityCategory 根据状态获取活动类别
func getActivityCategory(status string) string {
	switch status {
	case "waiting_accept":
		return "ticket_acceptance"
	case "investigating":
		return "diagnosis"
	case "waiting_approval":
		return "customer_approval"
	case "approved_waiting_action":
		return "repair_preparation"
	case "repairing", "restarting", "migrating", "software_fixing":
		return "repair_execution"
	case "waiting_verification":
		return "verification"
	case "summarizing":
		return "summary"
	case "completed":
		return "completion"
	case "cancelled":
		return "cancellation"
	default:
		return "status_change"
	}
}

// isSLAPauseStatus 判断是否为SLA暂停状态
func isSLAPauseStatus(status string) bool {
	switch status {
	case "waiting_approval", "approved_waiting_action":
		return true
	default:
		return false
	}
}

// getPauseReason 获取暂停原因
func getPauseReason(status string) string {
	switch status {
	case "waiting_approval":
		return "等待客户审批"
	case "approved_waiting_action":
		return "等待开始维修"
	default:
		return ""
	}
}

// CalculateDuration 计算时间间隔（分钟）
func CalculateDuration(startTime, endTime time.Time) int {
	if startTime.IsZero() || endTime.IsZero() {
		return 0
	}
	return int(endTime.Sub(startTime).Minutes())
}

// FormatTime 格式化时间
func FormatTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// ParseTime 解析时间字符串
func ParseTime(timeStr string) (time.Time, error) {
	if timeStr == "" {
		return time.Time{}, nil
	}
	return time.Parse("2006-01-02 15:04:05", timeStr)
}

// ParseUint 解析字符串为uint
func ParseUint(str string) (uint, error) {
	if str == "" {
		return 0, nil
	}
	val, err := strconv.ParseUint(str, 10, 32)
	if err != nil {
		return 0, err
	}
	return uint(val), nil
}

// HandleError 统一错误处理
func HandleError(ctx *gin.Context, err error, statusCode int, message string) {
	if err != nil {
		response.Fail(ctx, statusCode, message+": "+err.Error())
		return
	}
}

// ValidateID 验证ID参数
func ValidateID(ctx *gin.Context, idStr string) (uint, error) {
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return 0, response.NewErrorWithStatusCode("无效的ID", http.StatusBadRequest)
	}
	return uint(id), nil
}

// GetPageParams 获取分页参数
func GetPageParams(ctx *gin.Context) (int, int) {
	// 默认值
	page := 1
	pageSize := 20

	// 尝试获取页码
	if pageStr := ctx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	// 尝试获取每页数量
	if pageSizeStr := ctx.Query("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 {
			// 限制每页最大数量为100
			if ps > 100 {
				ps = 100
			}
			pageSize = ps
		}
	}

	return page, pageSize
}
