package common

import (
	"time"
)

// 工单状态常量
const (
	StatusWaitingAccept = "waiting_accept" // 待接单
	StatusInvestigating = "investigating"  // 排查中

	StatusWaitingApproval     = "waiting_approval"        // 客户待审批
	StatusApprovedWaiting     = "approved_waiting_action" // 客户已批准待手动触发
	StatusRepairing           = "repairing"               // 维修中
	StatusRestarting          = "restarting"              // 重启中
	StatusMigrating           = "migrating"               // 迁移中
	StatusSoftwareFixing      = "software_fixing"         // 软件修复中
	StatusWaitingVerification = "waiting_verification"    // 验证中
	StatusSummarizing         = "summarizing"             // 故障总结中
	StatusCompleted           = "completed"               // 已完成
	StatusCancelled           = "cancelled"               // 已取消
	StatusRepairFailed        = "repair_failed"           // 维修失败状态

	// 入室
	StatusWaitingSecondApproval = "waiting_second_approval" //待二次审批
	StatusApproved              = "approved"                // 审批通过
	//StatusRejected              = "rejected"                // 审批拒绝

	// 验证结果状态
	StatusVerificationPassed = "verification_passed" // 验证通过
	StatusVerificationFailed = "verification_failed" // 验证失败

	// 入库阶段常量
	StatusSubmitingApply        = "submiting_apply"         // 提交申请
	StatusRejected              = "rejected"                //审核不通过
	StatusWaitingManageApproval = "waiting_manage_approval" //等待审核
	StatusManageApproval        = "manage_approval"         //审核完成
	StatusCounting              = "counting"                //入库清点中
	StatusCompletedCount        = "completed_count"         //清点完成
	StatusInbounding            = "inbounding"              //入库中
	StatusCompletedInboud       = "completed_inbound"       //入库完成
	StatusWaitingAssetApproval  = "waiting_asset_approval"  // 等待资产管理员同意
	StatusAssetApprovalPass     = "asset_approval_pass"     //资产管理员审核通过
	StatusAssetApprovalFail     = "asset_approval_fail"     //资产管理员审核不通过
	StatusWaitingVerify         = "waiting_verify"          //等待验证
	StatusReEdit                = "re_edited"               //重新编辑
)

// 工作流阶段常量
const (
	StageAcceptTicket         = "accept_ticket"         // 接单
	StageRepairSelection      = "repair_selection"      // 选择维修方式
	StageCustomerApproval     = "customer_approval"     // 客户审批
	StageStartRepair          = "start_repair"          // 开始维修
	StageCompleteRepair       = "complete_repair"       // 完成维修
	StageStartVerification    = "start_verification"    // 开始验证
	StageCompleteVerification = "complete_verification" // 完成验证
	StageSummary              = "summary"               // 总结
	StageCompleteTicket       = "complete_ticket"       // 完成工单
	StageCancelTicket         = "cancel_ticket"         // 取消工单

	//入库阶段
	StageSubmitApply           = "submit_apply"            // 提交申请
	StageWaitingManageApproval = "waiting_manage_approval" //等待负责人审批
	StageManageApproval        = "manage_approval"         //负责人完成
	StageCount                 = "count"                   //清点阶段
	StageCompleteCount         = "complete_count"          //完成清点
	StageInbound               = "inbound"                 // 入库阶段
	StageCompleteInbound       = "complete_inbound"        //完成入库
	StageAssetApproval         = "asset_approval"          //资产管理员审核阶段
	StageReEdit                = "re_edit"                 //重新编辑阶段
	StageCancelled             = "cancelled"               //取消工单
	StageRejected              = "rejected"                //审批驳回
	StageFailed                = "failed"                  // 入库失败

	// 维修工单工作流特有阶段
	StageAuthorize               = "authorize"                 // 授权
	StageEngineerTake            = "engineer_take"             // 工程师接单
	StageArriveOnSite            = "arrive_on_site"            // 到达现场
	StageStartHardwareReplace    = "start_hardware_replace"    // 开始硬件更换
	StageCompleteHardwareReplace = "complete_hardware_replace" // 完成硬件更换
	StageVerify                  = "verify"                    // 验证

	// 入室
	StageSecondApproval = "second_approval" // 二次审批

)

// 维修类型常量
const (
	RepairTypeRestart       = "restart"        // 重启
	RepairTypeColdMigration = "cold_migration" // 冷迁移
	RepairTypeSoftwareFix   = "software_fix"   // 软件修复
	RepairTypeHardwareFix   = "hardware_fix"   // 硬件维修
)

// 任务队列常量
const (
	FaultTicketTaskQueue       = "FAULT_TICKET_TASK_QUEUE"
	RepairTicketTaskQueue      = "REPAIR_TICKET_TASK_QUEUE"
	PartInboundTicketTask      = "PART_INBOUND_TICKET_TASK_QUEUE"
	NewInboundTaskQueue        = "NEW_INBOUND_TICKET_TASK_QUEUE"
	RepairInboundTaskQueue     = "REPAIR_INBOUND_TICKET_TASK_QUEUE"
	DismantledInboundTaskQueue = "DISMANTLED_INBOUND_TICKET_TASK_QUEUE"
	// 重构完
	PartInboundTaskQueue   = "PART_INBOUND_TICKET_TASK_QUEUE"
	PartOutboundTaskQueue  = "PART_OUTBOUND_TICKET_TASK_QUEUE"
	DeviceInboundTaskQueue = "DEVICE_INBOUND_TICKET_TASK_QUEUE"
	DeviceOutboundTaskQueue = "DEVICE_OUTBOUND_TICKET_TASK_QUEUE"
)

// 工作流ID前缀常量
const (
	FaultTicketWorkflowIDPrefix  = "fault_ticket_"
	RepairTicketWorkflowIDPrefix = "repair_ticket_"
)

// 工作流控制信号名称常量
const (
	WorkflowControlSignalName         = "workflow_control_signal"           // 工作流控制信号名称
	NewInboundUpdateSignal            = "new_inbound_update_signal"         // 新购入库单更新信号
	RepairPartInboundUpdateSignal     = "repair_part_inbound_update_signal" // 返修入库工单更新信号
	DismantledPartInboundUpdateSignal = "dismantled_inbound_update_signal"  // 拆机入库工单更新信号

	// 重构后只有这两种
	PartInboundUpdateSignal   = "part_inbound_update_signal"   // 配件入库工单更新信号
	DeviceInboundUpdateSignal = "device_inbound_update_signal" // 设备入库工单更新信号
)

// 工作流活动超时常量
const (
	ActivityStartToCloseTimeout = 24 * time.Hour
	ActivityHeartbeatTimeout    = 10 * time.Second
)

// 重试策略常量
const (
	InitialInterval    = 1 * time.Second
	BackoffCoefficient = 2.0
	MaximumInterval    = 100 * time.Second
	MaxAttempts        = 3
)

const DefaultWarehouse = 99999
