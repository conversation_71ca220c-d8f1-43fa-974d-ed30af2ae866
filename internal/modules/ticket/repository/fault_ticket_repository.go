package repository

import (
	"backend/internal/modules/ticket/model"
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// FaultTicketRepository 报障单仓库接口
type FaultTicketRepository interface {
	Create(ctx context.Context, ticket *model.FaultTicket) error
	GetByID(ctx context.Context, id uint) (*model.FaultTicket, error)
	GetByTicketNo(ctx context.Context, ticketNo string) (*model.FaultTicket, error)
	Update(ctx context.Context, ticket *model.FaultTicket) error
	Delete(ctx context.Context, id uint) error
	List(ctx context.Context, page, pageSize int, conditions map[string]interface{}) ([]*model.FaultTicket, int64, error)
	CreateStatusHistory(ctx context.Context, history *model.FaultTicketStatusHistory) error
	GetStatusHistory(ctx context.Context, ticketID uint) ([]*model.FaultTicketStatusHistory, error)
	UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error
	WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo FaultTicketRepository) error) error
	// FindTicketsForRetry 查找需要重试的工单
	FindTicketsForRetry(ctx context.Context) ([]*model.FaultTicket, error)
	// CountRecentFaultsByDeviceSN 统计指定设备在近期内的故障次数
	CountRecentFaultsByDeviceSN(ctx context.Context, deviceSN string, days int) (int64, error)
	// CountMonthlyFaultsByDeviceSN 统计指定设备在指定月份的故障次数，可排除重复故障和误报
	CountMonthlyFaultsByDeviceSN(ctx context.Context, deviceSN string, startTime, endTime time.Time) (int64, error)
	// FindActiveTicketsByResourceIdentifier 查找指定IP的未完成报障单
	FindActiveTicketsByResourceIdentifier(ctx context.Context, resourceIdentifier string, excludeID uint) ([]*model.FaultTicket, error)
}

// faultTicketRepository 报障单仓库实现
type faultTicketRepository struct {
	db *gorm.DB
}

// NewFaultTicketRepository 创建报障单仓库
func NewFaultTicketRepository(db *gorm.DB) FaultTicketRepository {
	return &faultTicketRepository{db: db}
}

// Create 创建报障单
func (r *faultTicketRepository) Create(ctx context.Context, ticket *model.FaultTicket) error {
	// 使用事务进行创建操作，确保数据完整性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 使用明确的Create方法而不是Save，避免潜在的全表更新风险
		if err := tx.Create(ticket).Error; err != nil {
			return err
		}
		// 添加日志记录成功创建的工单
		fmt.Printf("成功创建工单: ID=%d, TicketNo=%s\n", ticket.ID, ticket.TicketNo)
		return nil
	})
}

// GetByID 根据ID获取报障单
func (r *faultTicketRepository) GetByID(ctx context.Context, id uint) (*model.FaultTicket, error) {
	var ticket model.FaultTicket
	err := r.db.WithContext(ctx).
		Preload("Resource.Device").
		Preload("Resource.Cabinet").
		Preload("Resource.Room").
		Preload("Resource.Room.DataCenter").
		First(&ticket, id).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

// GetByTicketNo 根据工单号获取报障单
func (r *faultTicketRepository) GetByTicketNo(ctx context.Context, ticketNo string) (*model.FaultTicket, error) {
	var ticket model.FaultTicket
	err := r.db.WithContext(ctx).
		Preload("Resource").
		Where("ticket_no = ?", ticketNo).
		First(&ticket).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

// Update 更新报障单
func (r *faultTicketRepository) Update(ctx context.Context, ticket *model.FaultTicket) error {
	// 确保有ID，避免无条件更新
	if ticket.ID == 0 {
		return fmt.Errorf("无法更新报障单：ID不能为0")
	}

	// 先检查记录是否存在
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.FaultTicket{}).Where("id = ?", ticket.ID).Count(&count).Error; err != nil {
		return fmt.Errorf("检查工单是否存在失败: %w", err)
	}
	if count == 0 {
		return fmt.Errorf("工单(ID=%d)不存在，无法更新", ticket.ID)
	}

	// 使用事务进行更新操作，确保数据完整性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 对CurrentWaitingStage字段进行验证，确保不包含非法字符
		if ticket.CurrentWaitingStage != "" && (ticket.CurrentWaitingStage == "reaitingTime" ||
			strings.Contains(ticket.CurrentWaitingStage, "Time:") ||
			strings.Contains(ticket.CurrentWaitingStage, ":")) {

			// 记录警告
			fmt.Printf("警告: 检测到工单(ID=%d)的CurrentWaitingStage字段包含非法字符: %s\n",
				ticket.ID, ticket.CurrentWaitingStage)

			// 尝试清理字段值
			cleanedStage := ""

			// 处理特殊错误模式
			if ticket.CurrentWaitingStage == "reaitingTime" {
				cleanedStage = "repair_selection"
				fmt.Printf("修复工单(ID=%d)的CurrentWaitingStage: reaitingTime -> repair_selection\n", ticket.ID)
			} else if strings.Contains(ticket.CurrentWaitingStage, "Time:") {
				// 提取前缀部分
				parts := strings.Split(ticket.CurrentWaitingStage, "Time:")
				if len(parts) > 0 {
					potentialStage := parts[0]
					fmt.Printf("从Time:分隔提取的阶段名称: %s\n", potentialStage)
					cleanedStage = potentialStage
				}
			}

			// 如果清理失败，设置为空值
			if cleanedStage == "" {
				fmt.Printf("无法修复工单(ID=%d)的CurrentWaitingStage，已清空该字段\n", ticket.ID)
				ticket.CurrentWaitingStage = ""
			} else {
				ticket.CurrentWaitingStage = cleanedStage
				fmt.Printf("已修复工单(ID=%d)的CurrentWaitingStage: %s\n", ticket.ID, cleanedStage)
			}
		}

		// 使用明确的Where条件和Updates方法，避免潜在的全表更新风险
		// 不直接使用整个ticket对象，而是明确指定要更新的字段
		result := tx.Model(&model.FaultTicket{}).
			Where("id = ?", ticket.ID).
			Updates(map[string]interface{}{
				"status":               ticket.Status,
				"fault_type":           ticket.FaultType,
				"fault_description":    ticket.FaultDescription,
				"fault_summary":        ticket.FaultSummary,
				"symptom":              ticket.Symptom,
				"repair_method":        ticket.RepairMethod,
				"prevention_measures":  ticket.PreventionMeasures,
				"remarks":              ticket.Remarks,
				"assigned_to":          ticket.AssignedTo,
				"priority":             ticket.Priority,
				"sla_status":           ticket.SLAStatus,
				"needs_workflow_retry": ticket.NeedsWorkflowRetry,
				"is_frequent_fault":    ticket.IsFrequentFault,
				// 添加工作流等待相关字段
				"waiting_manual_trigger": ticket.WaitingManualTrigger,
				"current_waiting_stage":  ticket.CurrentWaitingStage,
				"last_waiting_time":      ticket.LastWaitingTime,

				// 添加指针类型的时间字段，需要用gorm.Expr判断是否为nil
				"acknowledge_time":         ticket.AcknowledgeTime,
				"triage_complete_time":     ticket.TriageCompleteTime,
				"assignment_time":          ticket.AssignmentTime,
				"customer_approval_time":   ticket.CustomerApprovalTime,
				"expected_fix_time":        ticket.ExpectedFixTime,
				"actual_fix_time":          ticket.ActualFixTime,
				"verification_start_time":  ticket.VerificationStartTime,
				"verification_end_time":    ticket.VerificationEndTime,
				"close_time":               ticket.CloseTime,
				"last_workflow_retry_time": ticket.LastWorkflowRetryTime,

				// 添加计算的时长字段
				"response_duration":        ticket.ResponseDuration,
				"hardware_repair_duration": ticket.HardwareRepairDuration,
				"software_fix_duration":    ticket.SoftwareFixDuration,
				"total_downtime":           ticket.TotalDowntime,
				"business_impact_time":     ticket.BusinessImpactTime,

				// 添加取消相关字段
				"count_in_sla":       ticket.CountInSLA,
				"is_false_alarm":     ticket.IsFalseAlarm,
				"is_duplicate_fault": ticket.IsDuplicateFault,
				"related_ticket_id":  ticket.RelatedTicketID,
				"repair_ticket_id":   ticket.RepairTicketID,
			})

		if result.Error != nil {
			return fmt.Errorf("更新工单失败: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			fmt.Printf("警告: 工单(ID=%d)更新影响行数为0\n", ticket.ID)
		} else {
			fmt.Printf("工单(ID=%d)更新成功，影响行数: %d\n", ticket.ID, result.RowsAffected)
		}

		return nil
	})
}

// Delete 删除报障单
func (r *faultTicketRepository) Delete(ctx context.Context, id uint) error {
	if id == 0 {
		return fmt.Errorf("无法删除报障单：ID不能为0")
	}

	// 记录删除操作
	fmt.Printf("删除工单(ID=%d)\n", id)

	// 先检查记录是否存在
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.FaultTicket{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("检查工单是否存在失败: %w", err)
	}
	if count == 0 {
		return fmt.Errorf("工单(ID=%d)不存在，无法删除", id)
	}

	// 使用事务进行删除操作
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 使用明确的删除条件，避免全表删除风险
		result := tx.Where("id = ?", id).Delete(&model.FaultTicket{})
		if result.Error != nil {
			return fmt.Errorf("删除工单失败: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			return fmt.Errorf("删除工单(ID=%d)失败: 影响行数为0", id)
		}

		fmt.Printf("成功删除工单(ID=%d)\n", id)
		return nil
	})
}

// List 分页获取报障单列表
func (r *faultTicketRepository) List(ctx context.Context, page, pageSize int, conditions map[string]interface{}) ([]*model.FaultTicket, int64, error) {
	var tickets []*model.FaultTicket
	var total int64
	db := r.db.WithContext(ctx).Model(&model.FaultTicket{})

	// 处理关键词搜索
	if query, ok := conditions["query"].(string); ok && query != "" {
		db = db.Where("title LIKE ? OR ticket_no LIKE ? OR device_sn LIKE ? OR fault_description LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// 处理状态筛选 - 支持单个状态或状态数组
	if statusArray, ok := conditions["status"].([]string); ok && len(statusArray) > 0 {
		db = db.Where("status IN ?", statusArray)
	} else if status, ok := conditions["status"].(string); ok && status != "" {
		db = db.Where("status = ?", status)
	}

	// 处理优先级筛选
	if priority, ok := conditions["priority"].(string); ok && priority != "" {
		db = db.Where("priority = ?", priority)
	}

	// 处理故障标题筛选
	if title, ok := conditions["title"].(string); ok && title != "" {
		db = db.Where("title LIKE ?", "%"+title+"%")
	}

	// 处理租户IP筛选
	if resourceIdentifier, ok := conditions["resource_identifier"].(string); ok && resourceIdentifier != "" {
		db = db.Where("resource_identifier LIKE ?", "%"+resourceIdentifier+"%")
	}

	// 处理故障类型筛选
	if faultType, ok := conditions["fault_type"].(string); ok && faultType != "" {
		db = db.Where("fault_type = ?", faultType)
	}

	// 处理具体故障类型筛选
	if faultDetailType, ok := conditions["fault_detail_type"].(string); ok && faultDetailType != "" {
		db = db.Where("fault_detail_type = ?", faultDetailType)
	}

	// 处理报障人筛选
	if reporterName, ok := conditions["reporter_name"].(string); ok && reporterName != "" {
		db = db.Where("reporter_name LIKE ?", "%"+reporterName+"%")
	}

	// 处理接单人筛选
	if assignedTo, ok := conditions["assigned_to"].(string); ok && assignedTo != "" {
		db = db.Where("assigned_to LIKE ?", "%"+assignedTo+"%")
	}

	// 处理创建时间范围
	if startTime, ok := conditions["start_time"].(*time.Time); ok && startTime != nil {
		db = db.Where("creation_time >= ?", startTime)
	}
	if endTime, ok := conditions["end_time"].(*time.Time); ok && endTime != nil {
		db = db.Where("creation_time <= ?", endTime)
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = db.Preload("Resource").
		Preload("Resource.Device").
		Preload("Resource.Cabinet.Room.DataCenter").
		Preload("Resource.Room").
		Offset(offset).
		Limit(pageSize).
		Order("created_at DESC").
		Find(&tickets).Error
	if err != nil {
		return nil, 0, err
	}
	return tickets, total, nil
}

// CreateStatusHistory 创建报障单状态历史
func (r *faultTicketRepository) CreateStatusHistory(ctx context.Context, history *model.FaultTicketStatusHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// GetStatusHistory 获取报障单状态历史
func (r *faultTicketRepository) GetStatusHistory(ctx context.Context, ticketID uint) ([]*model.FaultTicketStatusHistory, error) {
	var histories []*model.FaultTicketStatusHistory
	err := r.db.WithContext(ctx).
		Where("fault_ticket_id = ?", ticketID).
		Order("operation_time DESC").
		Find(&histories).Error
	if err != nil {
		return nil, err
	}
	return histories, nil
}

// UpdateFields 安全地更新报障单的特定字段，避免全表更新风险
func (r *faultTicketRepository) UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	if id == 0 {
		return errors.New("invalid ticket id: id cannot be zero")
	}

	// 记录更新操作
	fmt.Printf("正在更新工单(ID=%d)的指定字段: %+v\n", id, fields)

	// 字段名映射 - 将Go结构体字段名映射到数据库列名
	fieldMapping := map[string]string{
		"deviceSN":               "device_sn",
		"componentSN":            "component_sn",
		"componentType":          "component_type",
		"responseDuration":       "response_duration",
		"hardwareRepairDuration": "hardware_repair_duration",
		"softwareFixDuration":    "software_fix_duration",
		"totalDowntime":          "total_downtime",
		"businessImpactTime":     "business_impact_time",
		"countInSLA":             "count_in_sla", // 确保正确映射countInSLA字段
		"faultSummary":           "fault_summary",
		"repairMethod":           "repair_method",
		"preventionMeasures":     "prevention_measures",
		"businessImpact":         "business_impact",
		"slaStatus":              "sla_status",
		"actualFixTime":          "actual_fix_time",
		"expectedFixTime":        "expected_fix_time",
		"closeTime":              "close_time",
		"verificationStartTime":  "verification_start_time",
		"verificationEndTime":    "verification_end_time",
		"isFrequentFault":        "is_frequent_fault",
		"requireApproval":        "require_approval",
		"immediateRepair":        "immediate_repair",
		"isFalseAlarm":           "is_false_alarm",     // 添加是否误报字段映射
		"isDuplicateFault":       "is_duplicate_fault", // 添加是否重复故障字段映射
		"relatedTicketID":        "related_ticket_id",  // 添加关联工单ID字段映射
		// 可以根据需要添加更多映射
	}

	// 处理字段名映射
	mappedFields := make(map[string]interface{})
	for k, v := range fields {
		// 检查是否需要映射
		if mappedKey, exists := fieldMapping[k]; exists {
			mappedFields[mappedKey] = v
			fmt.Printf("字段名映射: %s -> %s\n", k, mappedKey)
		} else {
			mappedFields[k] = v
		}
	}

	// 特殊处理：无论字段名是snake_case还是camelCase，都确保count_in_sla字段能被正确处理
	if _, exists := fields["CountInSLA"]; exists {
		mappedFields["count_in_sla"] = fields["CountInSLA"]
		fmt.Printf("特殊处理CountInSLA字段: CountInSLA -> count_in_sla\n")
	}
	if _, exists := fields["countInSLA"]; exists {
		mappedFields["count_in_sla"] = fields["countInSLA"]
		fmt.Printf("特殊处理countInSLA字段: countInSLA -> count_in_sla\n")
	}

	// 如果字段中包含CurrentWaitingStage，验证其值
	if waitingStage, ok := mappedFields["current_waiting_stage"].(string); ok {
		if waitingStage == "reaitingTime" ||
			strings.Contains(waitingStage, "Time:") ||
			strings.Contains(waitingStage, ":") {
			// 检测到异常值，尝试修复
			cleanedStage := ""
			if waitingStage == "reaitingTime" {
				cleanedStage = "repair_selection"
			} else if strings.Contains(waitingStage, "Time:") {
				parts := strings.Split(waitingStage, "Time:")
				if len(parts) > 0 {
					cleanedStage = parts[0]
				}
			}

			if cleanedStage != "" {
				mappedFields["current_waiting_stage"] = cleanedStage
				fmt.Printf("修复了异常的current_waiting_stage值: %s -> %s\n", waitingStage, cleanedStage)
			} else {
				// 无法修复时清空该字段
				mappedFields["current_waiting_stage"] = ""
				fmt.Printf("无法修复异常的current_waiting_stage值，已清空: %s\n", waitingStage)
			}
		}
	}

	// 显示最终要更新的字段
	fmt.Printf("最终更新字段: %+v\n", mappedFields)

	// 使用事务进行更新，确保原子操作
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("开始事务失败: %w", tx.Error)
	}

	// 确保工单存在
	var count int64
	if err := tx.Model(&model.FaultTicket{}).Where("id = ?", id).Count(&count).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("检查工单是否存在失败: %w", err)
	}

	if count == 0 {
		tx.Rollback()
		return fmt.Errorf("工单不存在(ID=%d)", id)
	}

	// 更新字段
	result := tx.Model(&model.FaultTicket{}).Where("id = ?", id).Updates(mappedFields)
	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("更新字段失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		// 这可能是因为字段值没有变化，所以不返回错误
		fmt.Printf("更新工单(ID=%d)的字段没有影响任何行，可能是值没有变化\n", id)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("提交事务失败: %w", err)
	}

	fmt.Printf("工单(ID=%d)更新成功，影响行数: %d\n", id, result.RowsAffected)
	return nil
}

// WithTransaction 在事务中执行操作
func (r *faultTicketRepository) WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo FaultTicketRepository) error) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建新的仓库实例，使用事务对象
		txRepo := &faultTicketRepository{db: tx}
		// 执行回调函数
		return fn(ctx, txRepo)
	})
}

// FindTicketsForRetry 查找需要重试的工单
func (r *faultTicketRepository) FindTicketsForRetry(ctx context.Context) ([]*model.FaultTicket, error) {
	var tickets []*model.FaultTicket
	err := r.db.WithContext(ctx).Where("needs_workflow_retry = ?", true).Find(&tickets).Error
	if err != nil {
		return nil, err
	}
	return tickets, nil
}

// CountRecentFaultsByDeviceSN 统计指定设备在近期内的故障次数
func (r *faultTicketRepository) CountRecentFaultsByDeviceSN(ctx context.Context, deviceSN string, days int) (int64, error) {
	if deviceSN == "" {
		return 0, errors.New("设备SN不能为空")
	}

	// 计算指定天数前的时间点
	startTime := time.Now().AddDate(0, 0, -days)

	var count int64
	err := r.db.WithContext(ctx).Model(&model.FaultTicket{}).
		Where("device_sn = ? AND creation_time >= ? AND is_duplicate_fault != ? AND is_false_alarm != ? AND related_ticket_id != ?", deviceSN, startTime, true, true, 0).
		Count(&count).Error

	if err != nil {
		return 0, fmt.Errorf("统计设备近期故障次数失败: %w", err)
	}

	return count, nil
}

// CountMonthlyFaultsByDeviceSN 统计指定设备在指定月份的故障次数，可排除重复故障和误报
func (r *faultTicketRepository) CountMonthlyFaultsByDeviceSN(ctx context.Context, deviceSN string, startTime, endTime time.Time) (int64, error) {
	if deviceSN == "" {
		return 0, errors.New("设备SN不能为空")
	}

	//排除重复故障和误报
	query := r.db.WithContext(ctx).Model(&model.FaultTicket{}).
		Where("device_sn = ? AND creation_time >= ? AND creation_time <= ? AND is_duplicate_fault != ? AND is_false_alarm != ?", deviceSN, startTime, endTime, true, true)

	var count int64
	err := query.Count(&count).Error

	if err != nil {
		return 0, fmt.Errorf("统计设备指定月份故障次数失败: %w", err)
	}

	return count, nil
}

// FindActiveTicketsByResourceIdentifier 查找指定IP的未完成报障单
func (r *faultTicketRepository) FindActiveTicketsByResourceIdentifier(ctx context.Context, resourceIdentifier string, excludeID uint) ([]*model.FaultTicket, error) {
	var tickets []*model.FaultTicket

	// 构建基本查询
	query := r.db.WithContext(ctx).
		Where("resource_identifier = ?", resourceIdentifier).
		// 只查询未完成的工单（不是已完成或已取消状态）
		Where("status NOT IN (?)", []string{"completed", "cancelled"})

	// 如果提供了排除的ID，则排除该ID的工单
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	// 执行查询
	if err := query.Find(&tickets).Error; err != nil {
		return nil, err
	}

	return tickets, nil
}
