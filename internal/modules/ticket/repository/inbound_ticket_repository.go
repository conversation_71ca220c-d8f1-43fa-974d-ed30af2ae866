package repository

import (
	"backend/internal/common/constants"
	"backend/internal/common/utils"
	"backend/internal/modules/cmdb/common"
	cmdbAsset "backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/inbound"
	inventoryRepo "backend/internal/modules/cmdb/repository/inventory"
	purchasemodel "backend/internal/modules/purchase/model"
	"backend/internal/modules/ticket/model"
	"context"
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// InboundTicketRepository 入库工单仓库接口
type InboundTicketRepository interface {
	// 维修入库
	CreatePartTicket(ctx context.Context, ticket *model.PartInboundTicket) error
	GetPartTicketByID(ctx context.Context, id uint) (*model.PartInboundTicket, error)
	GetPartTicketByRepairTicketID(ctx context.Context, repairTicketID uint) (*model.PartInboundTicket, error)
	UpdatePartTicket(ctx context.Context, id uint, ticket *model.PartInboundTicket) error
	DeletePartTicket(ctx context.Context, id uint) error
	GetPartTicketByNo(ctx context.Context, inboundNo string) (*model.PartInboundTicket, error)
	ListPartTickets(ctx context.Context, query *model.ListParams) ([]*model.PartInboundTicket, int64, error)

	// 新购入库
	CreateNewInboundTicket(ctx context.Context, ticket *model.NewInboundTicket) error
	InitNewInbound(ctx context.Context, ticket *model.NewInboundTicket, history *model.InboundTicketHistory) error
	UpdateNewTicket(ctx context.Context, id uint, ticket *model.NewInboundTicket) error
	GetNewInboundTicketByID(ctx context.Context, id uint) (*model.NewInboundTicket, error)
	GetNewTicketByNo(ctx context.Context, inboundNo string) (*model.NewInboundTicket, error)
	ListNewTickets(ctx context.Context, query *model.ListParams) ([]*model.NewInboundTicket, int64, error)

	/* 返修入库*/
	InitRepairInboundTicket(ctx context.Context, ticket *model.RepairPartInboundTicket, history *model.RepairPartTicketHistory) error
	GetRepairPartInboundTicketByNo(ctx context.Context, inboundNo string) (*model.RepairPartInboundTicket, error)

	/* 拆机配件入库 */
	InitDismantledInboundTicket(ctx context.Context, ticket *model.DismantledPartInboundTicket, history *model.DismantledPartTicketHistory) error
	GetDismantledPartInboundTicketByNo(ctx context.Context, inboundNo string) (*model.DismantledPartInboundTicket, error)

	/* 整机入库 */
	InitDeviceInbound(ctx context.Context, inbound *inbound.DeviceInbound, list *inbound.InboundList, ticket *model.DeviceInboundTicket, history *model.DeviceInboundTicketHistory) error
	GetDeviceInboundTicketByNo(ctx context.Context, inboundNo string) (*model.DeviceInboundTicket, error)
	GetDeviceInboundByNo(ctx context.Context, inboundNo string) (*inbound.DeviceInbound, *model.DeviceInboundTicket, error)

	// 公用
	CreateTicketHistory(ctx context.Context, history *model.InboundTicketHistory) error

	GetHistoryByTicketID(ctx context.Context, ticketID uint) ([]*model.InboundTicketHistory, error)
	GetHistoryByInboundNo(ctx context.Context, inboundNo string) ([]*model.InboundTicketHistory, error)

	buildPartUpdateFields(req *model.PartInboundTicket) map[string]interface{}
	buildNewUpdateFields(req *model.NewInboundTicket) map[string]interface{}
	ParseInboundTimeFormat(Time []string) ([]time.Time, error)

	/** 重构后的接口 */
	CreateInboundTicket(ctx context.Context, ticket *model.InboundTicket, history *model.InboundHistory, list *inbound.InboundList) error

	// 更新入库明细
	UpdateInboundDetails(ctx context.Context, details []model.InboundDetail) error

	// 获取入库信息
	GetInboundTicketByNo(ctx context.Context, inboundNo string) (*model.InboundTicket, error)
	GetInboundDetailsByNo(ctx context.Context, inboundType, inboundNo string) ([]model.InboundDetail, error)
	GetInboundDetailListByNo(ctx context.Context, inboundType, ticketNo string, page, pageSize int) ([]model.InboundDetail, int64, error)
	GetInboundInfosByNo(ctx context.Context, inboundType, inboundNo string) ([]model.InboundInfo, error)
	GetInboundHistoryByNo(ctx context.Context, inboundNo string) ([]model.InboundHistory, error)

	// 检查数据库中是否存在重复的SN
	CheckExistingSNs(ctx context.Context, inboundType string, sns []string) ([]string, error)

	// 更新CMDB
	UpdateCMDBWithNewPurchase(ctx context.Context, inboundType string, details []model.InboundDetail) error
	UpdateCMDBWithDismantled(ctx context.Context, existDetail []model.InboundDetail, newDetail []model.InboundDetail, ticket *model.InboundTicket, infos []model.InboundInfo) error
	UpdateCMDBWithReturnRepaired(ctx context.Context, inboundType string, details []model.InboundDetail) error

	// 采购单相关
	CreatePurchaseAssetStatusChangeLog(ctx context.Context, SNs []string, contract *purchasemodel.PurchaseContract, inquiry *purchasemodel.PurchaseInquiry, request *purchasemodel.PurchaseRequest) error

	GetDB() (*gorm.DB, error)
}

// inboundTicketRepo 入库工单仓库实现
type inboundTicketRepo struct {
	db            *gorm.DB
	inventoryRepo inventoryRepo.InventoryRepository
}

func (r *inboundTicketRepo) GetDB() (*gorm.DB, error) {
	if r.db == nil {
		return nil, fmt.Errorf("DB实例为空")
	}
	return r.db, nil
}

// 返修入库组件更新和创建
func (r *inboundTicketRepo) UpdateCMDBWithReturnRepaired(ctx context.Context, inboundType string, details []model.InboundDetail) error {
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, detail := range details {
			if detail.ReturnRepairType == "repair" {
				err := tx.Model(&cmdbAsset.AssetSpare{}).Where("sn = ?", detail.ComponentSN).Updates(map[string]interface{}{
					"asset_status":    constants.AssetStatusIdle,
					"hardware_status": detail.ComponentState,
				}).Error
				if err != nil {
					return fmt.Errorf("更新返修组件状态失败: %v", err)
				}
			} else {
				var (
					oldSpare cmdbAsset.AssetSpare
					newSpare cmdbAsset.AssetSpare
				)
				if err := tx.Model(&cmdbAsset.AssetSpare{}).Where("sn = ?", detail.ComponentSN).First(&oldSpare).Error; err != nil {
					return fmt.Errorf("查询旧备件信息失败: %w", err)
				}
				newSpare = oldSpare
				// 重置新部件
				newSpare.SN = detail.NewComponentSN
				newSpare.ID = 0
				newSpare.AssetStatus = constants.AssetStatusIdle
				newSpare.SourceType = constants.SourceTypeReturnRepair
				newSpare.HardwareStatus = constants.HardwareStatusNormal
				err := tx.Model(&cmdbAsset.AssetSpare{}).Create(&newSpare).Error
				if err != nil {
					return fmt.Errorf("创建新部件失败: %v", err)
				}

				// 报废旧部件
				err = tx.Model(&cmdbAsset.AssetSpare{}).Where("sn = ?", detail.ComponentSN).Updates(map[string]interface{}{
					"asset_status":    constants.AssetStatusScrapped,
					"hardware_status": constants.HardwareStatusFaulty,
				}).Error
				if err != nil {
					return fmt.Errorf("更新返修组件状态失败: %v", err)
				}
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (r *inboundTicketRepo) UpdateCMDBWithNewPurchase(ctx context.Context, inboundType string, details []model.InboundDetail) error {
	// 获取ticket数据
	ticket, err := r.GetInboundTicketByNo(ctx, details[0].InboundNo)
	if err != nil {
		return err
	}
	infos, err := r.GetInboundInfosByNo(ctx, inboundType, details[0].InboundNo)
	if err != nil {
		return err
	}

	db := utils.GetDB(ctx, r.db)

	// 执行事务操作
	err = db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if inboundType == inbound.TypePartInbound { // 处理配件新购入库
			var (
				spares []cmdbAsset.AssetSpare
			)
			for _, detail := range details {
				spare := cmdbAsset.AssetSpare{
					SN:             detail.ComponentSN,
					ProductID:      detail.ProductID,
					WarehouseID:    ticket.WarehouseID,
					SourceType:     constants.SourceTypeNewPurchase,
					AssetStatus:    constants.AssetStatusIdle,
					HardwareStatus: constants.HardwareStatusNormal,
				}
				spares = append(spares, spare)
			}
			if err := tx.Create(&spares).Error; err != nil {
				return fmt.Errorf("创建新备件失败: %v", err)
			}
		} else { // 处理设备新购入库
			var (
				devices         []cmdbAsset.Device
				assetStatusLogs []cmdbAsset.StatusChangeLog
			)
			for _, detail := range details {
				device := cmdbAsset.Device{
					SN:             detail.DeviceSN,
					PurchaseOrder:  ticket.PurchaseNo,
					Brand:          detail.Product.Brand,
					Model:          detail.Product.Model,
					ProductID:      detail.ProductID,
					AssetType:      common.MapMaterialTypeToAssetType[detail.Product.MaterialType],
					AssetStatus:    constants.AssetStatusIdle,
					HardwareStatus: constants.HardwareStatusNormal,
				}
				resource := &cmdbAsset.Resource{
					SN:      detail.DeviceSN,
					Project: ticket.Project,
					RoomID:  ticket.Warehouse.RoomID,
				}
				device.Resource = resource
				switch common.MapMaterialTypeToAssetType[detail.Product.MaterialType] {
				case constants.AssetTypeSwitch, constants.AssetTypeLoadbalancer, constants.AssetTypeFirewall, constants.AssetTypeRouter: // 网络设备
					newWorkDevice := &cmdbAsset.NetworkDevice{}
					device.NetworkDevice = newWorkDevice
				case constants.AssetTypeServer, constants.AssetTypeGPUServer: // CPU服务器、GPU服务器
				default:
					return fmt.Errorf("不支持的资产类型: %s", detail.Product.MaterialType)
				}
				devices = append(devices, device)
			}
			err := tx.Create(&devices).Error
			if err != nil {
				return fmt.Errorf("导入数据到CMDB失败: %w", err)
			}

			// 创建入库单asset_status_change_log
			for _, device := range devices {
				changeReason := fmt.Sprintf("新购入库到%v", ticket.Warehouse.Name)
				assetStatusLog := cmdbAsset.StatusChangeLog{
					AssetID:           device.ID,
					NewAssetStatus:    device.AssetStatus,
					NewBizStatus:      constants.BizStatusMaintaining,
					NewHardwareStatus: constants.HardwareStatusNormal,
					ChangeReason:      changeReason,
					OperatorID:        ticket.CreateID,
					OperatorName:      ticket.CreateBy,
					TicketNo:          ticket.InboundNo,
					WorkflowID:        "device_inbound_" + strconv.FormatUint(uint64(ticket.ID), 10),
					Source:            constants.SourceTypeInbound,
				}
				assetStatusLogs = append(assetStatusLogs, assetStatusLog)
			}
			if err := tx.Create(assetStatusLogs).Error; err != nil {
				return fmt.Errorf("创建资产状态变更记录失败")
			}
		}
		// 更新库存
		for _, info := range infos {
			// 获取库存数据
			inventoryDetail, err := r.inventoryRepo.GetLatestInventoryDetail(ctx, info.ProductID, ticket.WarehouseID)
			if err != nil {
				return err
			}
			reason := fmt.Sprintf("新购入库,%s %d件", info.Product.MaterialType, info.Amount)
			err = r.inventoryRepo.AdjustStock(ctx, inventoryDetail.ID, info.Amount, constants.ChangeTypeInbound, reason, ticket.ID, 0)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

// CreatePurchaseAssetStatusChangeLog 创建采购相关的资产状态变更
func (r *inboundTicketRepo) CreatePurchaseAssetStatusChangeLog(ctx context.Context, SNs []string, contract *purchasemodel.PurchaseContract, inquiry *purchasemodel.PurchaseInquiry, request *purchasemodel.PurchaseRequest) error {
	var (
		devices         []cmdbAsset.Device
		assetStatusLogs []cmdbAsset.StatusChangeLog
		companyName     []string
		projectName     []string
	)
	db := utils.GetDB(ctx, r.db)
	if db == r.db {
		return fmt.Errorf("未开启事务操作")
	}
	err := db.WithContext(ctx).Model(&cmdbAsset.Device{}).Where("sn in ?", SNs).Find(&devices).Error
	if err != nil {
		return fmt.Errorf("获取入库的设备信息失败")
	}

	err = db.WithContext(ctx).Model(&purchasemodel.Company{}).Select("company_name").Where("id = ?", contract.OurCompanyID).Pluck("company_name", &companyName).Error
	if err != nil {
		return fmt.Errorf("获取我方公司失败")
	}
	err = db.WithContext(ctx).Model(&purchasemodel.Project{}).Select("project_name").Where("id = ?", contract.ProjectID).Pluck("project_name", &projectName).Error
	if err != nil {
		return fmt.Errorf("获取项目失败")
	}
	// 创建入库单asset_status_change_log
	for _, device := range devices {
		changeReason := fmt.Sprintf("设备采购，供应商：%s，项目：%s，采购主体：%s", contract.SupplierName, projectName[0], companyName[0])
		assetStatusLog := cmdbAsset.StatusChangeLog{
			CreatedAt:    contract.CreatedAt,
			UpdatedAt:    contract.UpdatedAt,
			AssetID:      device.ID,
			ChangeReason: changeReason,
			OperatorID:   contract.CreatedBy,
			OperatorName: contract.CreatorName,
			TicketNo:     contract.ContractNo,
			WorkflowID:   "purchase_contract_" + strconv.FormatUint(uint64(contract.ID), 10),
			Source:       constants.SourceTypeNewPurchase,
		}
		assetStatusLogs = append(assetStatusLogs, assetStatusLog)
		if inquiry != nil {
			changeReason = fmt.Sprintf("采购询价，供应商选择说明：%s", inquiry.SupplierDescription)
			assetStatusLog = cmdbAsset.StatusChangeLog{
				CreatedAt:    inquiry.CreatedAt,
				UpdatedAt:    inquiry.UpdatedAt,
				AssetID:      device.ID,
				ChangeReason: changeReason,
				OperatorID:   inquiry.CreatedBy,
				OperatorName: inquiry.CreatorName,
				TicketNo:     inquiry.InquiryNo,
				WorkflowID:   "purchase_inquiry_" + strconv.FormatUint(uint64(inquiry.ID), 10),
				Source:       constants.SourceTypeNewPurchase,
			}
			assetStatusLogs = append(assetStatusLogs, assetStatusLog)
		}
		if request != nil {
			changeReason = fmt.Sprintf("采购申请，所属项目：%s，申请原因：%s", projectName[0], request.Reason)
			assetStatusLog = cmdbAsset.StatusChangeLog{
				CreatedAt:    request.CreatedAt,
				UpdatedAt:    request.UpdatedAt,
				AssetID:      device.ID,
				ChangeReason: changeReason,
				OperatorID:   request.CreatedBy,
				OperatorName: request.CreatorName,
				TicketNo:     request.RequestNo,
				WorkflowID:   "purchase_request_" + strconv.FormatUint(uint64(request.ID), 10),
				Source:       constants.SourceTypeNewPurchase,
			}
			assetStatusLogs = append(assetStatusLogs, assetStatusLog)
		}
	}
	if err := db.Create(assetStatusLogs).Error; err != nil {
		return fmt.Errorf("创建资产状态变更记录失败")
	}
	return nil
}

// 更新已存在的组件
func (r *inboundTicketRepo) UpdateExistingComponents(ctx context.Context, details []model.InboundDetail) error {
	var spares []cmdbAsset.AssetSpare
	for _, detail := range details {
		var spare cmdbAsset.AssetSpare
		if err := r.db.WithContext(ctx).Where("sn = ?", detail.ComponentSN).First(&spare).Error; err != nil {
			return fmt.Errorf("未找到组件: %s", detail.ComponentSN)
		}
		// 更新备件信息
		spare.AssetStatus = constants.AssetStatusIdle
		spare.HardwareStatus = detail.ComponentState
		spares = append(spares, spare)
	}
	if len(spares) > 0 {
		err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			for _, spare := range spares {
				err := tx.Model(&cmdbAsset.AssetSpare{}).
					Where("id = ?", spare.ID).
					Updates(map[string]interface{}{
						"asset_status":    spare.AssetStatus,
						"hardware_status": spare.HardwareStatus,
					}).Error
				if err != nil {
					return fmt.Errorf("更新备件表失败: %w", err)
				}
			}
			return nil
		})
		if err != nil {
			return fmt.Errorf("事务更新备件失败: %v", err)
		}
	} else {
		log.Println("没有需要更新的备件")
	}
	return nil
}

// 创建新的组件
func (r *inboundTicketRepo) CreateNewComponents(ctx context.Context, details []model.InboundDetail) error {
	var newSpares []cmdbAsset.AssetSpare
	ticket, err := r.GetInboundTicketByNo(ctx, details[0].InboundNo)
	if err != nil {
		return err
	}
	for _, detail := range details {
		// 创建新的备件
		newSpare := cmdbAsset.AssetSpare{
			WarehouseID:    ticket.WarehouseID,
			SN:             detail.ComponentSN,
			SourceType:     constants.SourceTypeDismantled, // 来源类型设置为拆机
			AssetStatus:    constants.AssetStatusIdle,
			ProductID:      detail.ProductID,
			HardwareStatus: detail.ComponentState,
		}

		if detail.Product != nil && detail.Product.ID > 0 {
			newSpare.ProductID = detail.Product.ID
		}

		newSpares = append(newSpares, newSpare)
	}
	err = r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err = tx.Create(&newSpares).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return fmt.Errorf("创建新备件失败: %v", err)
	}
	return nil
}

// UpdateCMDBWithDismantled 在同一事务中处理拆机入库的组件更新和创建
func (r *inboundTicketRepo) UpdateCMDBWithDismantled(ctx context.Context, existDetail []model.InboundDetail, newDetail []model.InboundDetail, ticket *model.InboundTicket, infos []model.InboundInfo) error {
	// 开始事务
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 处理已存在的组件更新
		if len(existDetail) > 0 {
			var spares []cmdbAsset.AssetSpare
			for _, detail := range existDetail {
				var spare cmdbAsset.AssetSpare
				if err := tx.Where("sn = ?", detail.ComponentSN).First(&spare).Error; err != nil {
					return fmt.Errorf("未找到组件: %s", detail.ComponentSN)
				}
				// 更新备件信息
				spare.AssetStatus = constants.AssetStatusIdle
				spare.HardwareStatus = detail.ComponentState
				spares = append(spares, spare)
			}

			// 批量更新已存在组件
			for _, spare := range spares {
				err := tx.Model(&cmdbAsset.AssetSpare{}).
					Where("id = ?", spare.ID).
					Updates(map[string]interface{}{
						"asset_status":    spare.AssetStatus,
						"hardware_status": spare.HardwareStatus,
					}).Error
				if err != nil {
					return fmt.Errorf("更新备件表失败: %w", err)
				}
			}
		}

		// 处理需要创建的新组件
		if len(newDetail) > 0 {
			var newSpares []cmdbAsset.AssetSpare

			for _, detail := range newDetail {
				// 创建新的备件
				newSpare := cmdbAsset.AssetSpare{
					WarehouseID:    ticket.WarehouseID,
					SN:             detail.ComponentSN,
					SourceType:     constants.SourceTypeDismantled, // 来源类型设置为拆机
					AssetStatus:    constants.AssetStatusIdle,
					ProductID:      detail.ProductID,
					HardwareStatus: detail.ComponentState,
				}

				if detail.Product != nil && detail.Product.ID > 0 {
					newSpare.ProductID = detail.Product.ID
				}

				newSpares = append(newSpares, newSpare)
			}

			// 批量创建新组件
			if len(newSpares) > 0 {
				if err := tx.Create(&newSpares).Error; err != nil {
					return fmt.Errorf("创建新备件失败: %v", err)
				}
			}
		}

		// 1. 构建 existDetailMap 和 newDetailMap：ProductID -> count
		existDetailMap := make(map[uint]int)
		for _, detail := range existDetail {
			existDetailMap[detail.ProductID]++
		}

		newDetailMap := make(map[uint]int)
		for _, detail := range newDetail {
			newDetailMap[detail.ProductID]++
		}
		// 更新库存
		for _, info := range infos {
			inventoryDetail, err := r.inventoryRepo.GetByProductIDAndWarehouseID(ctx, info.ProductID, ticket.WarehouseID)
			if err != nil {
				return err
			}
			// 获取匹配数量
			existCount := existDetailMap[info.ProductID]
			newCount := newDetailMap[info.ProductID]

			reason := fmt.Sprintf("拆机入库,%s %d件,工单编号：%s", info.Product.MaterialType, info.Amount, ticket.InboundNo)
			// 设置上下文
			ctx = context.WithValue(ctx, constants.ContextKeyUserID, ticket.CreateID)
			ctx = context.WithValue(ctx, constants.ContextKeyUserInfo, map[string]interface{}{"realName": ticket.CreateBy})
			ctx = context.WithValue(ctx, constants.ContextKeyChangeReason, reason)

			// 如果在已存在明细中
			if existCount > 0 {
				err = r.inventoryRepo.ReleaseAllocatedStock(ctx, inventoryDetail.ID, existCount)
				if err != nil {
					return err
				}
			}

			// 如果在新明细中
			if newCount > 0 {
				err = r.inventoryRepo.AdjustStock(ctx, inventoryDetail.ID, newCount, constants.ChangeTypeInbound,
					reason, ticket.ID, 0)
				if err != nil {
					return err
				}
			}

			//// 如果库存明细存在，则调用 ReleaseAllocatedStock 释放已分配的
			//if len(existDetail) > 0 {
			//	ctx = context.WithValue(ctx, "userID", ticket.CreateID)
			//	ctx = context.WithValue(ctx, "userInfo", map[string]interface{}{
			//		"realName": ticket.CreateBy,
			//	})
			//	ctx = context.WithValue(ctx, "reason", fmt.Sprintf("拆机入库,%s %d件,工单编号：%s", info.Product.MaterialType, info.Amount, ticket.InboundNo))
			//	err = r.inventoryRepo.ReleaseAllocatedStock(ctx, inventoryDetail.ID, info.Amount)
			//	if err != nil {
			//		return err
			//	}
			//}
			//// 如果库存明细不存在，则调用 adjustStock 调整库存
			//if len(newDetail) > 0 {
			//	ctx = context.WithValue(ctx, "userID", ticket.CreateID)
			//	ctx = context.WithValue(ctx, "userInfo", map[string]interface{}{
			//		"realName": ticket.CreateBy,
			//	})
			//	ctx = context.WithValue(ctx, "reason", fmt.Sprintf("拆机入库,%s %d件,工单编号：%s", info.Product.MaterialType, info.Amount, ticket.InboundNo))
			//	err = r.inventoryRepo.AdjustStock(ctx, inventoryDetail.ID, info.Amount, constants.ChangeTypeInbound, fmt.Sprintf("拆机入库,%s %d件,工单编号：%s", info.Product.MaterialType, info.Amount, ticket.InboundNo), ticket.ID, 0)
			//	if err != nil {
			//		return err
			//	}
			//}
		}
		return nil
	})
}

func (r *inboundTicketRepo) GetInboundDetailsByNo(ctx context.Context, inboundType, inboundNo string) ([]model.InboundDetail, error) {
	var (
		Detail []model.InboundDetail
		err    error
	)
	switch inboundType {
	case inbound.TypePartInbound, inbound.TypeDeviceInbound:
		err = r.db.WithContext(ctx).Model(&model.InboundDetail{}).Where("inbound_no = ?", inboundNo).Preload("Product").Find(&Detail).Error
	}
	if err != nil {
		return nil, fmt.Errorf("获取入库详情失败: %w", err)
	}
	return Detail, nil
}

func (r *inboundTicketRepo) GetInboundInfosByNo(ctx context.Context, inboundType, inboundNo string) ([]model.InboundInfo, error) {
	var (
		Info []model.InboundInfo
		err  error
	)
	switch inboundType {
	case inbound.TypePartInbound, inbound.TypeDeviceInbound:
		err = r.db.WithContext(ctx).Model(&model.InboundInfo{}).Where("inbound_no = ?", inboundNo).Preload("Product").Find(&Info).Error
	}
	if err != nil {
		return nil, fmt.Errorf("获取入库信息失败: %w", err)
	}
	return Info, nil
}

func (r *inboundTicketRepo) GetInboundTicketByNo(ctx context.Context, inboundNo string) (*model.InboundTicket, error) {
	var InboundTicket model.InboundTicket
	err := r.db.WithContext(ctx).Model(&model.InboundTicket{}).Where("inbound_no = ?", inboundNo).Preload("Warehouse").First(&InboundTicket).Error
	if err != nil {
		return nil, fmt.Errorf("获取入库工单失败: %w", err)
	}
	return &InboundTicket, nil
}

func (r *inboundTicketRepo) CreateInboundTicket(ctx context.Context, ticket *model.InboundTicket, history *model.InboundHistory, list *inbound.InboundList) error {
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err := tx.Create(ticket).Error
		if err != nil {
			return fmt.Errorf("创建入库单失败: %v", err)
		}
		history.InboundTicketID = ticket.ID
		err = tx.Create(history).Error
		if err != nil {
			return fmt.Errorf("创建入库历史失败: %v", err)
		}
		list.InboundTicketID = ticket.ID
		err = tx.Create(list).Error
		if err != nil {
			return fmt.Errorf("创建列表失败: %v", err)
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// CreateTicket 创建入库工单
func (r *inboundTicketRepo) CreatePartTicket(ctx context.Context, ticket *model.PartInboundTicket) error {
	return r.db.WithContext(ctx).Create(&ticket).Error
}

// GetTicketByID 根据ID获取入库工单
func (r *inboundTicketRepo) GetPartTicketByID(ctx context.Context, id uint) (*model.PartInboundTicket, error) {
	var ticket model.PartInboundTicket
	err := r.db.WithContext(ctx).
		Preload("PartInbound").
		Preload("RepairTicket").
		Where("id = ?", id).
		First(&ticket).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("入库工单不存在: ID=%d", id)
		}
		return nil, fmt.Errorf("获取入库工单失败: %w", err)
	}
	return &ticket, nil
}

// UpdateTicket 更新入库工单
func (r *inboundTicketRepo) UpdatePartTicket(ctx context.Context, id uint, ticket *model.PartInboundTicket) error {
	updates := r.buildPartUpdateFields(ticket)
	return r.db.WithContext(ctx).Model(&model.PartInboundTicket{}).Where("id = ?", id).Updates(updates).Error
}

// UpdateNewTicket
func (r *inboundTicketRepo) UpdateNewTicket(ctx context.Context, id uint, ticket *model.NewInboundTicket) error {
	updates := r.buildNewUpdateFields(ticket)
	return r.db.WithContext(ctx).Model(&model.NewInboundTicket{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteTicket 删除入库工单
func (r *inboundTicketRepo) DeletePartTicket(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&model.PartInboundTicket{}).Error
}

// ListPartTickets 获取维修入库工单列表
// TODO 更新联动history(未完成)
func (r *inboundTicketRepo) ListPartTickets(ctx context.Context, query *model.ListParams) ([]*model.PartInboundTicket, int64, error) {
	var tickets []*model.PartInboundTicket
	var total int64

	db := r.db.WithContext(ctx).Model(&model.PartInboundTicket{})

	// 根据查询条件动态构建查询
	// 入库状态
	if query.InboundStatus != "" {
		db = db.Where("status = ?", query.InboundStatus)
	}
	// 入库人员
	if query.InboundUser != "" {
		db = db.Where("submitter = ?", query.InboundUser)
	}
	// 入库单号
	if query.InboundNo != "" {
		db = db.Where("inbound_no = ?", query.InboundNo)
	}
	// 入库时间
	if query.InboundTime != nil {
		inboundTime, err := r.ParseInboundTimeFormat(query.InboundTime)
		if err != nil {
			return nil, 0, err
		}
		db = db.Where("created_at BETWEEN ? AND ?", inboundTime[0], inboundTime[1])
	}

	// 预加载关联数据
	db = db.Preload("PartInbound").
		Preload("RepairTicket")

	// 查询总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取工单总数失败: %w", err)
	}

	// 分页查询
	if query.Page > 0 && query.PageSize > 0 {
		offset := (query.Page - 1) * query.PageSize
		db = db.Offset(offset).Limit(query.PageSize)
	}

	// 添加排序
	db = db.Order("created_at DESC")

	// 执行查询
	err = db.Find(&tickets).Error
	if err != nil {
		return nil, 0, fmt.Errorf("查询工单列表失败: %w", err)
	}

	return tickets, total, nil
}

// ListNewTickets 获取新购入库工单列表
func (r *inboundTicketRepo) ListNewTickets(ctx context.Context, query *model.ListParams) ([]*model.NewInboundTicket, int64, error) {
	var tickets []*model.NewInboundTicket
	var total int64

	db := r.db.WithContext(ctx).Model(&model.NewInboundTicket{})

	// 根据查询条件动态构建查询
	// 入库状态
	if query.InboundStatus != "" {
		db = db.Where("status = ?", query.InboundStatus)
	}
	// 入库人员
	if query.InboundUser != "" {
		db = db.Where("submitter = ?", query.InboundUser)
	}
	// 入库单号
	if query.InboundNo != "" {
		db = db.Where("inbound_no = ?", query.InboundNo)
	}
	// 入库时间
	if query.InboundTime != nil {
		inboundTime, err := r.ParseInboundTimeFormat(query.InboundTime)
		if err != nil {
			return nil, 0, err
		}
		db = db.Where("created_at BETWEEN ? AND ?", inboundTime[0], inboundTime[1])
	}
	// 预加载关联数据
	if query.PurchaseOrderNo != "" {
		db = db.Preload("History").Preload("NewInbound", "purchase_order_no = ?", query.PurchaseOrderNo)
	} else {
		db = db.Preload("History").Preload("NewInbound")
	}

	// TODO 通过SN查询的不使用这个逻辑
	//if query.SN != "" {
	//
	//}

	// 查询总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取工单总数失败: %w", err)
	}

	// 分页查询
	if query.Page > 0 && query.PageSize > 0 {
		offset := (query.Page - 1) * query.PageSize
		db = db.Offset(offset).Limit(query.PageSize)
	}

	// 添加排序
	db = db.Order("created_at DESC")

	// 执行查询
	err = db.Find(&tickets).Error
	if err != nil {
		return nil, 0, fmt.Errorf("查询工单列表失败: %w", err)
	}

	return tickets, total, nil
}

// CreateTicketHistory 创建入库工单历史记录
func (r *inboundTicketRepo) CreateTicketHistory(ctx context.Context, history *model.InboundTicketHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// GetHistoryByPartTicketID 获取入库工单历史记录
func (r *inboundTicketRepo) GetHistoryByTicketID(ctx context.Context, ticketID uint) ([]*model.InboundTicketHistory, error) {
	var histories []*model.InboundTicketHistory
	err := r.db.WithContext(ctx).Where("inbound_no = ?", ticketID).Order("operation_time desc").Find(&histories).Error
	if err != nil {
		return nil, err
	}
	return histories, nil
}

// GetPartTicketByNo 根据入库单号获取入库工单
func (r *inboundTicketRepo) GetPartTicketByNo(ctx context.Context, inboundNo string) (*model.PartInboundTicket, error) {
	var ticket model.PartInboundTicket
	err := r.db.WithContext(ctx).Where("inbound_no = ?", inboundNo).First(&ticket).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("入库单号 %s 不存在", inboundNo)
		}
		return nil, fmt.Errorf("查询入库工单失败: %w", err)
	}
	return &ticket, nil
}

// GetPartTicketByRepairTicketID 根据维修工单ID获取入库工单
func (r *inboundTicketRepo) GetPartTicketByRepairTicketID(ctx context.Context, repairTicketID uint) (*model.PartInboundTicket, error) {
	var ticket model.PartInboundTicket
	err := r.db.WithContext(ctx).Where("repair_ticket_id = ?", repairTicketID).First(&ticket).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

// GetHistoryByInboundNo 根据入库单号获取历史记录
func (r *inboundTicketRepo) GetHistoryByInboundNo(ctx context.Context, inboundNo string) ([]*model.InboundTicketHistory, error) {
	var histories []*model.InboundTicketHistory

	// 根据入库单号查询历史记录
	err := r.db.WithContext(ctx).
		Where("inbound_no = ?", inboundNo).
		Order("operation_time DESC").
		Find(&histories).Error
	if err != nil {
		return nil, fmt.Errorf("查询历史记录失败: %w", err)
	}

	return histories, nil
}

func (r *inboundTicketRepo) CreateNewInboundTicket(ctx context.Context, ticket *model.NewInboundTicket) error {
	if err := r.db.WithContext(ctx).Create(ticket).Error; err != nil {
		return err
	}
	return nil
}

func (r *inboundTicketRepo) GetNewInboundTicketByID(ctx context.Context, id uint) (*model.NewInboundTicket, error) {
	var ticket model.NewInboundTicket
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&ticket).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

func (r *inboundTicketRepo) InitNewInbound(ctx context.Context, ticket *model.NewInboundTicket, history *model.InboundTicketHistory) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.WithContext(ctx).Create(ticket).Error; err != nil {
			return err
		}
		if err := tx.WithContext(ctx).Create(history).Error; err != nil {
			return err
		}
		return nil
	})
}

func (r *inboundTicketRepo) GetNewTicketByNo(ctx context.Context, inboundNo string) (*model.NewInboundTicket, error) {
	var ticket model.NewInboundTicket
	err := r.db.WithContext(ctx).Where("inbound_no = ?", inboundNo).First(&ticket).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("入库单号 %s 不存在", inboundNo)
		}
		return nil, fmt.Errorf("查询入库工单失败: %w", err)
	}
	return &ticket, nil
}

func (r *inboundTicketRepo) buildPartUpdateFields(req *model.PartInboundTicket) map[string]interface{} {
	updates := make(map[string]interface{})
	log.Printf("PartInboundUpdate fields: %+v", updates)
	// 注释的字段为不允许更新
	updates["try_count"] = req.TryCount
	//if req.InboundNo != "" {
	//	updates["inbound_no"] = req.InboundNo
	//}
	if req.CompletedAt != nil {
		updates["completed_at"] = req.CompletedAt
	}
	//if req.PartInboundID != 0 {
	//	updates["part_inbound_id"] = req.PartInboundID
	//}
	//if len(req.ComponentID) > 0 {
	//	updates["component_id"] = req.ComponentID
	//}
	if req.RepairTicketID != 0 {
		updates["repair_ticket_id"] = req.RepairTicketID
	}
	//if len(req.AssetID) > 0 {
	//	updates["asset_id"] = req.AssetID
	//}
	if req.Submitter != "" {
		updates["submitter"] = req.Submitter
	}
	if req.SubmitterID != 0 {
		updates["submitter_id"] = req.SubmitterID
	}
	if req.Approver_before != "" {
		updates["approver_before"] = req.Approver_before
	}
	if req.ApproverID_before != 0 {
		updates["approver_id_before"] = req.ApproverID_before
	}
	if req.ApproverDescribe != "" {
		updates["approver_describe"] = req.ApproverDescribe
	}
	if req.Counter != "" {
		updates["counter"] = req.Counter
	}
	if req.CounterID != 0 {
		updates["counter_id"] = req.CounterID
	}
	if req.Operator != "" {
		updates["operator"] = req.Operator
	}
	if req.OperatorID != 0 {
		updates["operator_id"] = req.OperatorID
	}
	if req.Approver_after != "" {
		updates["approver_after"] = req.Approver_after
	}
	if req.ApproverID_after != 0 {
		updates["approver_id_after"] = req.ApproverID_after
	}
	if req.Stage != "" {
		updates["stage"] = req.Stage
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.PreviousStatus != "" {
		updates["previous_status"] = req.PreviousStatus
	}
	if req.Lock {
		updates["lock"] = req.Lock
	}
	return updates
}

func (r *inboundTicketRepo) buildNewUpdateFields(req *model.NewInboundTicket) map[string]interface{} {
	updates := make(map[string]interface{})
	log.Printf("NewInboundUpdate fields: %+v", updates)
	// 注释的字段为不允许更新
	//if req.InboundNo != "" {
	//	updates["inbound_no"] = req.InboundNo
	//}
	if req.AssetApprover != "" {
		updates["asset_approver"] = req.AssetApprover
	}
	if req.AssetApproverID != 0 {
		updates["asset_approver_id"] = req.AssetApproverID
	}
	if req.EngineerApprover != "" {
		updates["engineer_approver"] = req.EngineerApprover
	}
	if req.EngineerApproverID != 0 {
		updates["engineer_approver_id"] = req.EngineerApproverID
	}
	if req.CompletedAt != nil {
		updates["completed_at"] = req.CompletedAt
	}
	if req.Submitter != "" {
		updates["submitter"] = req.Submitter
	}
	if req.SubmitterID != 0 {
		updates["submitter_id"] = req.SubmitterID
	}
	if req.Approver_before != "" {
		updates["approver_before"] = req.Approver_before
	}
	if req.ApproverID_before != 0 {
		updates["approver_id_before"] = req.ApproverID_before
	}
	if req.Counter != "" {
		updates["counter"] = req.Counter
	}
	if req.CounterID != 0 {
		updates["counter_id"] = req.CounterID
	}
	if req.Operator != "" {
		updates["operator"] = req.Operator
	}
	if req.OperatorID != 0 {
		updates["operator_id"] = req.OperatorID
	}
	if req.Approver_after != "" {
		updates["approver_after"] = req.Approver_after
	}
	if req.ApproverID_after != 0 {
		updates["approver_id_after"] = req.ApproverID_after
	}
	if req.Stage != "" {
		updates["stage"] = req.Stage
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.PreviousStatus != "" {
		updates["previous_status"] = req.PreviousStatus
	}
	return updates
}

// 进行时间处理
func (r *inboundTicketRepo) ParseInboundTimeFormat(Time []string) ([]time.Time, error) {
	var result []time.Time
	if len(Time) == 2 {
		// 定义时间格式，与validate标签中的格式保持一致
		layout := "2006-01-02"

		// 解析开始时间
		start, err := time.Parse(layout, Time[0])
		if err != nil {
			return nil, fmt.Errorf("解析开始时间失败: %v", err)
		}

		// 解析结束时间
		end, err := time.Parse(layout, Time[1])
		if err != nil {
			return nil, fmt.Errorf("解析结束时间失败: %v", err)
		}

		// 添加一天使查询包含结束日期当天
		end = end.AddDate(0, 0, 1)
		result = append(result, start)
		result = append(result, end)
		if result[0].After(result[1]) {
			return nil, fmt.Errorf("开始时间晚于结束时间")
		}
		return result, nil
	}
	return nil, fmt.Errorf("时间格式为空或者长度小于2")
}

// InitRepairInboundTicket 初始化返修配件入库单+入库历史
func (r *inboundTicketRepo) InitRepairInboundTicket(ctx context.Context, ticket *model.RepairPartInboundTicket, history *model.RepairPartTicketHistory) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(ticket).Error; err != nil {
			return fmt.Errorf("创建返修入库工单失败：: %v", err)
		}
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("创建入库单历史失败: %v", err)
		}
		return nil
	})
}

// GetRepairPartInboundTicketByNo 通过inbound 获取ticket信息
func (r *inboundTicketRepo) GetRepairPartInboundTicketByNo(ctx context.Context, inboundNo string) (*model.RepairPartInboundTicket, error) {
	var RepairPartInboundTicket model.RepairPartInboundTicket
	err := r.db.WithContext(ctx).Model(&model.RepairPartInboundTicket{}).Where("inbound_no = ?", inboundNo).Preload("History").First(&RepairPartInboundTicket).Error
	if err != nil {
		return nil, err
	}
	return &RepairPartInboundTicket, nil
}

// InitDismantledInboundTicket 初始化拆机配件入库单+入库历史
func (r *inboundTicketRepo) InitDismantledInboundTicket(ctx context.Context, ticket *model.DismantledPartInboundTicket, history *model.DismantledPartTicketHistory) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(ticket).Error; err != nil {
			return fmt.Errorf("创建拆机入库工单失败：: %v", err)
		}
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("创建入库单历史失败: %v", err)
		}
		return nil
	})
}

// GetDismantledPartInboundTicketByNo 通过inbound 获取ticket信息
func (r *inboundTicketRepo) GetDismantledPartInboundTicketByNo(ctx context.Context, inboundNo string) (*model.DismantledPartInboundTicket, error) {
	var DismantledPartInboundTicket model.DismantledPartInboundTicket
	err := r.db.WithContext(ctx).Model(&model.DismantledPartInboundTicket{}).Where("inbound_no = ?", inboundNo).Preload("History").First(&DismantledPartInboundTicket).Error
	if err != nil {
		return nil, err
	}
	return &DismantledPartInboundTicket, nil
}

// InitDeviceInbound 初始化整机入库单+入库历史
func (r *inboundTicketRepo) InitDeviceInbound(ctx context.Context, inbound *inbound.DeviceInbound, list *inbound.InboundList, ticket *model.DeviceInboundTicket, history *model.DeviceInboundTicketHistory) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(list).Error; err != nil {
			return fmt.Errorf("创建工单列表失败: %v", err)
		}
		if err := tx.Create(inbound).Error; err != nil {
			return fmt.Errorf("创建设备入库单失败: %v", err)
		}
		if err := tx.Create(ticket).Error; err != nil {
			return fmt.Errorf("创建整机入库工单失败：: %v", err)
		}
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("创建入库单历史失败: %v", err)
		}
		return nil
	})
}

// GetDeviceInboundTicketByNo 通过inbound 获取ticket信息
func (r *inboundTicketRepo) GetDeviceInboundTicketByNo(ctx context.Context, inboundNo string) (*model.DeviceInboundTicket, error) {
	var DeviceInboundTicket model.DeviceInboundTicket
	err := r.db.WithContext(ctx).Model(&model.DeviceInboundTicket{}).Where("inbound_no = ?", inboundNo).Preload("History").First(&DeviceInboundTicket).Error
	if err != nil {
		return nil, err
	}
	return &DeviceInboundTicket, nil
}

// GetDeviceInboundByNo 通过入库单号获取所有信息
func (r *inboundTicketRepo) GetDeviceInboundByNo(ctx context.Context, inboundNo string) (*inbound.DeviceInbound, *model.DeviceInboundTicket, error) {
	var (
		deviceInbound *inbound.DeviceInbound
		ticket        *model.DeviceInboundTicket
	)
	err := r.db.WithContext(ctx).Model(&inbound.DeviceInbound{}).Where("inbound_no = ?", inboundNo).Preload("DeviceInfo").Preload("DeviceInfo.Template").Preload("DeviceDetails").Preload("DeviceDetails.Template").Preload("DeviceDetails.Warehouse").Preload("Warehouse").Take(&deviceInbound).Error
	if err != nil {
		return nil, nil, fmt.Errorf("查找deviceInbound失败：%v", err)
	}
	err = r.db.WithContext(ctx).Model(&model.DeviceInboundTicket{}).Preload("History").Where("inbound_no = ?", inboundNo).Take(&ticket).Error
	if err != nil {
		return nil, nil, fmt.Errorf("查找deviceInboundTicket失败：%v", err)
	}
	return deviceInbound, ticket, nil

}

func (r *inboundTicketRepo) GetNewInboundTicketByNo(ctx context.Context, inboundNo string) (*model.DeviceInboundTicket, error) {
	var (
		ticket *model.DeviceInboundTicket
	)
	err := r.db.WithContext(ctx).Model(&model.DeviceInboundTicket{}).Preload("History").Where("inbound_no = ?", inboundNo).Take(&ticket).Error
	if err != nil {
		return nil, fmt.Errorf("查找deviceInboundTicket失败：%v", err)
	}
	return ticket, nil
}

// GetInboundHistoryByNo 根据入库单号获取入库单历史记录
func (r *inboundTicketRepo) GetInboundHistoryByNo(ctx context.Context, inboundNo string) ([]model.InboundHistory, error) {
	var histories []model.InboundHistory

	// 查询历史记录
	err := r.db.WithContext(ctx).Model(&model.InboundHistory{}).
		Where("inbound_no = ?", inboundNo).
		Order("created_at DESC").
		Find(&histories).Error

	if err != nil {
		return nil, fmt.Errorf("获取入库单历史记录失败: %w", err)
	}

	return histories, nil
}

// UpdateInboundDetails 更新入库明细
func (r *inboundTicketRepo) UpdateInboundDetails(ctx context.Context, details []model.InboundDetail) error {
	err := r.db.WithContext(ctx).Model(&model.InboundDetail{}).Transaction(func(tx *gorm.DB) error {
		for _, detail := range details {
			err := tx.Where("id = ?", detail.ID).Updates(map[string]interface{}{
				"device_sn":    detail.DeviceSN,
				"component_sn": detail.ComponentSN,
				// 拆机入库
				"component_state": detail.ComponentState,
				"need_return":     detail.NeedReturn,
				// 返修入库
				"return_repair_type": detail.ReturnRepairType,
				"new_component_sn":   detail.NewComponentSN,
			}).Error
			if err != nil {
				return fmt.Errorf("更新入库明细失败: %w", err)
			}
		}
		return nil
	})
	if err != nil {
		return fmt.Errorf("更新入库明细失败: %w", err)
	}
	return nil
}

// CheckExistingSNs 检查数据库中是否存在重复的SN
func (r *inboundTicketRepo) CheckExistingSNs(ctx context.Context, inboundType string, sns []string) ([]string, error) {
	var existingSNs []string

	switch inboundType {
	case inbound.TypePartInbound:
		// 检查组件SN是否存在
		var assets []cmdbAsset.AssetSpare
		err := r.db.WithContext(ctx).
			Model(&cmdbAsset.AssetSpare{}).
			Where("sn IN ?", sns).
			Find(&assets).Error
		if err != nil {
			return nil, fmt.Errorf("检查组件SN时发生错误: %w", err)
		}

		// 收集已存在的组件SN
		for _, asset := range assets {
			if asset.SN != "" {
				existingSNs = append(existingSNs, asset.SN)
			}
		}

	case inbound.TypeDeviceInbound:
		// 检查设备SN是否存在
		var devices []cmdbAsset.Device
		err := r.db.WithContext(ctx).
			Model(&cmdbAsset.Device{}).
			Where("sn IN ?", sns).
			Find(&devices).Error
		if err != nil {
			return nil, fmt.Errorf("检查设备SN时发生错误: %w", err)
		}

		// 收集已存在的设备SN
		for _, device := range devices {
			if device.SN != "" {
				existingSNs = append(existingSNs, device.SN)
			}
		}
	default:
		return nil, fmt.Errorf("不支持的入库类型: %s", inboundType)
	}

	return existingSNs, nil
}

// NewInboundTicketRepository 创建入库工单仓库
func NewInboundTicketRepository(db *gorm.DB, inventoryRepo inventoryRepo.InventoryRepository) InboundTicketRepository {
	return &inboundTicketRepo{db: db, inventoryRepo: inventoryRepo}
}

// GetInboundDetailListByNo 分页获取入库单详情列表
func (r *inboundTicketRepo) GetInboundDetailListByNo(ctx context.Context, inboundType, ticketNo string, page, pageSize int) ([]model.InboundDetail, int64, error) {
	// 获取详情列表（不分页）
	allDetails, err := r.GetInboundDetailsByNo(ctx, inboundType, ticketNo)
	if err != nil {
		return nil, 0, err
	}

	// 计算总数
	total := int64(len(allDetails))

	// 计算分页参数
	offset := (page - 1) * pageSize
	end := offset + pageSize

	// 边界检查
	if offset >= len(allDetails) {
		return []model.InboundDetail{}, total, nil
	}

	if end > len(allDetails) {
		end = len(allDetails)
	}

	// 返回分页后的数据
	return allDetails[offset:end], total, nil
}
