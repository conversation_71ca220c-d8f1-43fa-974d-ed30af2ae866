package repository

import (
	"backend/internal/modules/ticket/model"
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// EntryTicketRepository 入室单仓库接口
type EntryTicketRepository interface {
	Create(ctx context.Context, ticket *model.EntryTicket) error
	GetByID(ctx context.Context, id uint) (*model.EntryTicket, error)
	GetByTicketNo(ctx context.Context, ticketNo string) (*model.EntryTicket, error)
	Update(ctx context.Context, ticket *model.EntryTicket) error
	Delete(ctx context.Context, id uint) error
	List(ctx context.Context, page, pageSize int, conditions map[string]interface{}) ([]*model.EntryTicket, int64, error)
	CreateStatusHistory(ctx context.Context, history *model.EntryTicketStatusHistory) error
	GetStatusHistory(ctx context.Context, ticketID uint) ([]*model.EntryTicketStatusHistory, error)
	UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error
	WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo EntryTicketRepository) error) error
	// FindTicketsForRetry 查找需要重试的工单
	FindTicketsForRetry(ctx context.Context) ([]*model.EntryTicket, error)
}

// entryTicketRepository 入室单仓库实现
type entryTicketRepository struct {
	db *gorm.DB
}

// NewEntryTicketRepository 创建入室单仓库
func NewEntryTicketRepository(db *gorm.DB) EntryTicketRepository {
	return &entryTicketRepository{db: db}
}

// Create 创建入室单
func (r *entryTicketRepository) Create(ctx context.Context, ticket *model.EntryTicket) error {
	// 使用事务进行创建操作，确保数据完整性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 使用明确的Create方法而不是Save，避免潜在的全表更新风险
		if err := tx.Create(ticket).Error; err != nil {
			return err
		}
		// 添加日志记录成功创建的工单
		fmt.Printf("成功创建工单: ID=%d, TicketNo=%s\n", ticket.ID, ticket.TicketNo)
		return nil
	})
}

// GetByID 根据ID获取入室单
func (r *entryTicketRepository) GetByID(ctx context.Context, id uint) (*model.EntryTicket, error) {
	var ticket model.EntryTicket
	err := r.db.WithContext(ctx).
		//Preload("Resource.Device").
		//Preload("Resource.Cabinet").
		//Preload("Resource.Room").
		//Preload("Resource.Room.DataCenter").
		First(&ticket, id).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

// GetByTicketNo 根据工单号获取入室单
func (r *entryTicketRepository) GetByTicketNo(ctx context.Context, ticketNo string) (*model.EntryTicket, error) {
	var ticket model.EntryTicket
	err := r.db.WithContext(ctx).
		//Preload("Resource").
		Where("ticket_no = ?", ticketNo).
		First(&ticket).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

// Update 更新入室单
func (r *entryTicketRepository) Update(ctx context.Context, ticket *model.EntryTicket) error {
	// 确保有ID，避免无条件更新
	if ticket.ID == 0 {
		return fmt.Errorf("无法更新入室单：ID不能为0")
	}

	// 先检查记录是否存在
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.EntryTicket{}).Where("id = ?", ticket.ID).Count(&count).Error; err != nil {
		return fmt.Errorf("检查工单是否存在失败: %w", err)
	}
	if count == 0 {
		return fmt.Errorf("工单(ID=%d)不存在，无法更新", ticket.ID)
	}

	// 记录操作，便于调试
	fmt.Printf("更新工单(ID=%d): %+v\n", ticket.ID, ticket)

	// 使用事务进行更新操作，确保数据完整性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 对CurrentWaitingStage字段进行验证，确保不包含非法字符
		if ticket.CurrentWaitingStage != "" && (ticket.CurrentWaitingStage == "reaitingTime" ||
			strings.Contains(ticket.CurrentWaitingStage, "Time:") ||
			strings.Contains(ticket.CurrentWaitingStage, ":")) {

			// 记录警告
			fmt.Printf("警告: 检测到工单(ID=%d)的CurrentWaitingStage字段包含非法字符: %s\n",
				ticket.ID, ticket.CurrentWaitingStage)

			// 尝试清理字段值
			cleanedStage := ""

			// 处理特殊错误模式
			if ticket.CurrentWaitingStage == "reaitingTime" {
				cleanedStage = "repair_selection"
				fmt.Printf("修复工单(ID=%d)的CurrentWaitingStage: reaitingTime -> repair_selection\n", ticket.ID)
			} else if strings.Contains(ticket.CurrentWaitingStage, "Time:") {
				// 提取前缀部分
				parts := strings.Split(ticket.CurrentWaitingStage, "Time:")
				if len(parts) > 0 {
					potentialStage := parts[0]
					fmt.Printf("从Time:分隔提取的阶段名称: %s\n", potentialStage)
					cleanedStage = potentialStage
				}
			}

			// 如果清理失败，设置为空值
			if cleanedStage == "" {
				fmt.Printf("无法修复工单(ID=%d)的CurrentWaitingStage，已清空该字段\n", ticket.ID)
				ticket.CurrentWaitingStage = ""
			} else {
				ticket.CurrentWaitingStage = cleanedStage
				fmt.Printf("已修复工单(ID=%d)的CurrentWaitingStage: %s\n", ticket.ID, cleanedStage)
			}
		}

		// 使用明确的Where条件和Updates方法，避免潜在的全表更新风险
		// 不直接使用整个ticket对象，而是明确指定要更新的字段
		result := tx.Model(&model.EntryTicket{}).
			Where("id = ?", ticket.ID).
			Updates(map[string]interface{}{
				"status": ticket.Status,
				//"fault_type":           ticket.FaultType,
				//"fault_description":    ticket.FaultDescription,
				//"fault_summary":        ticket.FaultSummary,
				//"symptom":              ticket.Symptom,
				//"repair_method":        ticket.RepairMethod,
				//"prevention_measures":  ticket.PreventionMeasures,
				//"remarks":              ticket.Remarks,
				//"assigned_to":          ticket.AssignedTo,
				//"priority":             ticket.Priority,
				//"sla_status":           ticket.SLAStatus,
				"needs_workflow_retry": ticket.NeedsWorkflowRetry,
				//"is_frequent_fault":    ticket.IsFrequentFault,
				// 添加工作流等待相关字段
				"waiting_manual_trigger": ticket.WaitingManualTrigger,
				"current_waiting_stage":  ticket.CurrentWaitingStage,
				"last_waiting_time":      ticket.LastWaitingTime,

				// 添加指针类型的时间字段，需要用gorm.Expr判断是否为nil
				//"acknowledge_time":         ticket.AcknowledgeTime,
				//"triage_complete_time":     ticket.TriageCompleteTime,
				//"assignment_time":          ticket.AssignmentTime,
				//"customer_approval_time":   ticket.CustomerApprovalTime,
				//"expected_fix_time":        ticket.ExpectedFixTime,
				//"actual_fix_time":          ticket.ActualFixTime,
				//"verification_start_time":  ticket.VerificationStartTime,
				//"verification_end_time":    ticket.VerificationEndTime,
				"close_time":               ticket.CloseTime,
				"last_workflow_retry_time": ticket.LastWorkflowRetryTime,
			})

		if result.Error != nil {
			return fmt.Errorf("更新工单失败: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			fmt.Printf("警告: 工单(ID=%d)更新影响行数为0\n", ticket.ID)
		} else {
			fmt.Printf("工单(ID=%d)更新成功，影响行数: %d\n", ticket.ID, result.RowsAffected)
		}

		return nil
	})
}

// Delete 删除入室单
func (r *entryTicketRepository) Delete(ctx context.Context, id uint) error {
	if id == 0 {
		return fmt.Errorf("无法删除入室单：ID不能为0")
	}

	// 记录删除操作
	fmt.Printf("删除工单(ID=%d)\n", id)

	// 先检查记录是否存在
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.EntryTicket{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("检查工单是否存在失败: %w", err)
	}
	if count == 0 {
		return fmt.Errorf("工单(ID=%d)不存在，无法删除", id)
	}

	// 使用事务进行删除操作
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 使用明确的删除条件，避免全表删除风险
		result := tx.Where("id = ?", id).Delete(&model.EntryTicket{})
		if result.Error != nil {
			return fmt.Errorf("删除工单失败: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			return fmt.Errorf("删除工单(ID=%d)失败: 影响行数为0", id)
		}

		fmt.Printf("成功删除工单(ID=%d)\n", id)
		return nil
	})
}

// List 分页获取入室单列表
func (r *entryTicketRepository) List(ctx context.Context, page, pageSize int, conditions map[string]interface{}) ([]*model.EntryTicket, int64, error) {
	var tickets []*model.EntryTicket
	var total int64

	db := r.db.WithContext(ctx).Model(&model.EntryTicket{})

	// 处理关键词搜索
	if query, ok := conditions["query"].(string); ok && query != "" {
		db = db.Where("ticket_no LIKE ? ",
			"%"+query+"%")
	}

	// 处理状态筛选 - 支持单个状态或状态数组
	if statusArray, ok := conditions["status"].([]string); ok && len(statusArray) > 0 {
		db = db.Where("status IN ?", statusArray)
	} else if status, ok := conditions["status"].(string); ok && status != "" {
		db = db.Where("status = ?", status)
	}

	// 处理入室人筛选
	if reporterName, ok := conditions["reporter_name"].(string); ok && reporterName != "" {
		db = db.Where("applicant_name LIKE ?", "%"+reporterName+"%")
	}

	// 处理创建时间范围
	if startTime, ok := conditions["start_time"].(*time.Time); ok && startTime != nil {
		db = db.Where("creation_time >= ?", startTime)
	}
	if endTime, ok := conditions["end_time"].(*time.Time); ok && endTime != nil {
		db = db.Where("creation_time <= ?", endTime)
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = db.
		//Preload("Resource").
		Offset(offset).
		Limit(pageSize).
		Order("created_at DESC").
		Find(&tickets).Error
	if err != nil {
		return nil, 0, err
	}

	return tickets, total, nil
}

// CreateStatusHistory 创建入室单状态历史
func (r *entryTicketRepository) CreateStatusHistory(ctx context.Context, history *model.EntryTicketStatusHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// GetStatusHistory 获取入室单状态历史
func (r *entryTicketRepository) GetStatusHistory(ctx context.Context, ticketID uint) ([]*model.EntryTicketStatusHistory, error) {
	var histories []*model.EntryTicketStatusHistory
	err := r.db.WithContext(ctx).
		Where("entry_ticket_id = ?", ticketID).
		Order("operation_time DESC").
		Find(&histories).Error
	if err != nil {
		return nil, err
	}
	return histories, nil
}

// UpdateFields 安全地更新入室单的特定字段，避免全表更新风险
func (r *entryTicketRepository) UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	if id == 0 {
		return errors.New("invalid ticket id: id cannot be zero")
	}

	// 记录更新操作
	fmt.Printf("正在更新工单(ID=%d)的指定字段: %+v\n", id, fields)

	// 字段名映射 - 将Go结构体字段名映射到数据库列名
	fieldMapping := map[string]string{
		"deviceSN":      "device_sn",
		"componentSN":   "component_sn",
		"componentType": "component_type",
		// 可以根据需要添加更多映射
	}

	// 处理字段名映射
	mappedFields := make(map[string]interface{})
	for k, v := range fields {
		// 检查是否需要映射
		if mappedKey, exists := fieldMapping[k]; exists {
			mappedFields[mappedKey] = v
			fmt.Printf("字段名映射: %s -> %s\n", k, mappedKey)
		} else {
			mappedFields[k] = v
		}
	}

	// 如果字段中包含CurrentWaitingStage，验证其值
	if stageValue, exists := mappedFields["current_waiting_stage"]; exists {
		if stage, ok := stageValue.(string); ok && stage != "" {
			if stage == "reaitingTime" || strings.Contains(stage, "Time:") || strings.Contains(stage, ":") {
				fmt.Printf("警告: 检测到UpdateFields尝试设置非法的CurrentWaitingStage值: %s\n", stage)

				cleanedStage := ""

				// 处理特殊错误模式
				if stage == "reaitingTime" {
					cleanedStage = "repair_selection"
					fmt.Printf("修复UpdateFields的CurrentWaitingStage: reaitingTime -> repair_selection\n")
				} else if strings.Contains(stage, "Time:") {
					// 提取前缀部分
					parts := strings.Split(stage, "Time:")
					if len(parts) > 0 {
						potentialStage := parts[0]
						fmt.Printf("从Time:分隔提取的阶段名称: %s\n", potentialStage)
						cleanedStage = potentialStage
					}
				}

				// 如果清理失败，设置为空值
				if cleanedStage == "" {
					fmt.Printf("无法修复UpdateFields的CurrentWaitingStage，已清空该字段\n")
					mappedFields["current_waiting_stage"] = ""
				} else {
					mappedFields["current_waiting_stage"] = cleanedStage
					fmt.Printf("已修复UpdateFields的CurrentWaitingStage: %s\n", cleanedStage)
				}
			}
		}
	}

	// 检查记录是否存在
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.EntryTicket{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("验证工单存在性失败: %w", err)
	}
	if count == 0 {
		return fmt.Errorf("工单不存在(ID=%d)", id)
	}

	// 执行更新，明确指定WHERE条件
	//tx := r.db.Begin()
	//if tx.Error != nil {
	//	return fmt.Errorf("开始事务失败: %w", tx.Error)
	//}
	//
	//defer func() {
	//	if r := recover(); r != nil {
	//		tx.Rollback()
	//	}
	//}()

	// 明确使用WHERE条件，只更新指定ID的记录
	result := r.db.Model(&model.EntryTicket{}).Where("id = ?", id).Updates(mappedFields)
	if result.Error != nil {
		//tx.Rollback()
		return fmt.Errorf("更新字段失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		//tx.Rollback()
		return fmt.Errorf("更新失败: 没有记录被修改(ID=%d)", id)
	}

	// 提交事务
	//if err := tx.Commit().Error; err != nil {
	//	tx.Rollback()
	//	return fmt.Errorf("提交事务失败: %w", err)
	//}

	fmt.Printf("成功更新工单(ID=%d)的指定字段\n", id)
	return nil
}

// WithTransaction 在事务中执行操作
func (r *entryTicketRepository) WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo EntryTicketRepository) error) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建新的仓库实例，使用事务对象
		txRepo := &entryTicketRepository{db: tx}
		// 执行回调函数
		return fn(ctx, txRepo)
	})
}

// FindTicketsForRetry 查找需要重试的工单
func (r *entryTicketRepository) FindTicketsForRetry(ctx context.Context) ([]*model.EntryTicket, error) {
	var tickets []*model.EntryTicket
	err := r.db.WithContext(ctx).Where("needs_workflow_retry = ?", true).Find(&tickets).Error
	if err != nil {
		return nil, err
	}
	return tickets, nil
}
