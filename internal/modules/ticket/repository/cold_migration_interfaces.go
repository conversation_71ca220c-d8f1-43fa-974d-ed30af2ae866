package repository

import (
	"backend/internal/modules/ticket/model"
	"context"
)

// ColdMigrationRepository 冷迁移记录仓库接口
type ColdMigrationRepository interface {
	// Create 创建冷迁移记录
	Create(ctx context.Context, coldMigration *model.ColdMigration) error

	// GetByID 根据ID获取冷迁移记录
	GetByID(ctx context.Context, id uint) (*model.ColdMigration, error)

	// GetByTicketID 根据故障单ID获取冷迁移记录
	GetByTicketID(ctx context.Context, ticketID uint) (*model.ColdMigration, error)

	// GetByFaultDeviceSN 根据故障机SN获取冷迁移记录
	GetByFaultDeviceSN(ctx context.Context, faultDeviceSN string) ([]*model.ColdMigration, error)

	// GetByBackupDeviceSN 根据备机SN获取冷迁移记录
	GetByBackupDeviceSN(ctx context.Context, backupDeviceSN string) ([]*model.ColdMigration, error)

	// Update 更新冷迁移记录
	Update(ctx context.Context, coldMigration *model.ColdMigration) error

	// Delete 删除冷迁移记录
	Delete(ctx context.Context, id uint) error

	// List 获取冷迁移记录列表
	List(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.ColdMigration, int64, error)
}
