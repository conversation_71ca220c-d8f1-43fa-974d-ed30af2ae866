package repository

import (
	"backend/internal/modules/ticket/model"
	"context"
	"fmt"

	"gorm.io/gorm"
)

// RepairTicketRepository 维修单仓库接口
type RepairTicketRepository interface {
	// GetByID 根据ID获取维修单
	GetByID(ctx context.Context, id uint) (*model.RepairTicket, error)

	// GetByTicketNo 根据维修单号获取维修单
	GetByTicketNo(ctx context.Context, ticketNo string) (*model.RepairTicket, error)

	// GetByFaultTicketID 根据故障单ID获取维修单
	GetByFaultTicketID(ctx context.Context, faultTicketID uint) (*model.RepairTicket, error)

	// Create 创建维修单
	Create(ctx context.Context, repairTicket *model.RepairTicket) error

	// Update 更新维修单
	Update(ctx context.Context, repairTicket *model.RepairTicket) error

	// Delete 删除维修单
	Delete(ctx context.Context, id uint) error

	// List 获取维修单列表（旧方法，已被ListByQuery替代，为兼容性保留）
	List(ctx context.Context, page, pageSize int, query, status string, engineerID uint) ([]*model.RepairTicket, int64, error)

	// ListByStatus 根据状态获取维修单列表
	ListByStatus(ctx context.Context, status string, limit, offset int) ([]*model.RepairTicket, int64, error)

	// ListByEngineerID 根据工程师ID获取维修单列表
	ListByEngineerID(ctx context.Context, engineerID uint, limit, offset int) ([]*model.RepairTicket, int64, error)

	// ListByQuery 根据查询条件获取维修单列表
	ListByQuery(ctx context.Context, query *model.RepairTicketQuery) ([]*model.RepairTicket, int64, error)

	// CreateStatusHistory 创建维修单状态历史
	CreateStatusHistory(ctx context.Context, history *model.RepairTicketStatusHistory) error

	// GetStatusHistory 获取维修单状态历史
	GetStatusHistory(ctx context.Context, ticketID uint) ([]*model.RepairTicketStatusHistory, error)

	// WithTransaction 事务操作
	WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo RepairTicketRepository) error) error

	// 获取维修单历史记录
	GetRepairTicketHistory(ctx context.Context, repairTicketID uint) ([]model.RepairTicketStatusHistory, error)

	// UpdateFields 更新维修单指定字段
	UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error
}

// repairTicketRepository 维修单仓库实现
type repairTicketRepository struct {
	db *gorm.DB
}

// NewRepairTicketRepository 创建维修单仓库
func NewRepairTicketRepository(db *gorm.DB) RepairTicketRepository {
	return &repairTicketRepository{
		db: db,
	}
}

// GetByID 根据ID获取维修单
func (r *repairTicketRepository) GetByID(ctx context.Context, id uint) (*model.RepairTicket, error) {
	var repairTicket model.RepairTicket
	if err := r.db.WithContext(ctx).
		Preload("FaultTicket.Resource").
		// Preload("FaultTicket.Resource.Device").
		Preload("FaultTicket.Resource.Cabinet").
		Preload("FaultTicket.Resource.Room").
		Preload("FaultTicket.Resource.Room.DataCenter").
		Where("id = ?", id).First(&repairTicket).Error; err != nil {
		return nil, err
	}
	return &repairTicket, nil
}

// GetByTicketNo 根据维修单号获取维修单
func (r *repairTicketRepository) GetByTicketNo(ctx context.Context, ticketNo string) (*model.RepairTicket, error) {
	var repairTicket model.RepairTicket
	if err := r.db.WithContext(ctx).Where("ticket_no = ?", ticketNo).First(&repairTicket).Error; err != nil {
		return nil, err
	}
	return &repairTicket, nil
}

// GetByFaultTicketID 根据故障单ID获取维修单
func (r *repairTicketRepository) GetByFaultTicketID(ctx context.Context, faultTicketID uint) (*model.RepairTicket, error) {
	var repairTicket model.RepairTicket
	if err := r.db.WithContext(ctx).Where("fault_ticket_id = ?", faultTicketID).First(&repairTicket).Error; err != nil {
		return nil, err
	}
	return &repairTicket, nil
}

// Create 创建维修单
func (r *repairTicketRepository) Create(ctx context.Context, repairTicket *model.RepairTicket) error {
	return r.db.WithContext(ctx).Create(repairTicket).Error
}

// Update 更新维修单
func (r *repairTicketRepository) Update(ctx context.Context, repairTicket *model.RepairTicket) error {
	return r.db.WithContext(ctx).Save(repairTicket).Error
}

// Delete 删除维修单
func (r *repairTicketRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.RepairTicket{}, id).Error
}

// List 获取维修单列表（旧方法，已被ListByQuery替代，为兼容性保留）
func (r *repairTicketRepository) List(ctx context.Context, page, pageSize int, query, status string, engineerID uint) ([]*model.RepairTicket, int64, error) {
	var tickets []*model.RepairTicket
	var total int64

	db := r.db.WithContext(ctx).Model(&model.RepairTicket{})

	if query != "" {
		// 使用子查询方式联结fault_tickets表，根据device_sn进行搜索
		subQuery := r.db.WithContext(ctx).Table("fault_tickets").
			Select("id").
			Where("device_sn LIKE ?", "%"+query+"%")

		db = db.Where("ticket_no LIKE ? OR fault_ticket_id IN (?)", "%"+query+"%", subQuery)
	}

	if status != "" {
		db = db.Where("status = ?", status)
	}

	if engineerID > 0 {
		db = db.Where("assigned_engineer_id = ?", engineerID)
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = db.Preload("FaultTicket.Resource").
		Preload("FaultTicket.Resource.Device").
		Offset(offset).
		Limit(pageSize).
		Order("created_at DESC").
		Find(&tickets).Error
	if err != nil {
		return nil, 0, err
	}

	return tickets, total, nil
}

// ListByStatus 根据状态获取维修单列表
func (r *repairTicketRepository) ListByStatus(ctx context.Context, status string, limit, offset int) ([]*model.RepairTicket, int64, error) {
	var repairTickets []*model.RepairTicket
	var total int64

	// 获取总数
	if err := r.db.WithContext(ctx).Model(&model.RepairTicket{}).Where("status = ?", status).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	if err := r.db.WithContext(ctx).Where("status = ?", status).Limit(limit).Offset(offset).Find(&repairTickets).Error; err != nil {
		return nil, 0, err
	}

	return repairTickets, total, nil
}

// ListByEngineerID 根据工程师ID获取维修单列表
func (r *repairTicketRepository) ListByEngineerID(ctx context.Context, engineerID uint, limit, offset int) ([]*model.RepairTicket, int64, error) {
	var repairTickets []*model.RepairTicket
	var total int64

	// 获取总数
	if err := r.db.WithContext(ctx).Model(&model.RepairTicket{}).Where("assigned_engineer_id = ?", engineerID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	if err := r.db.WithContext(ctx).Where("assigned_engineer_id = ?", engineerID).Limit(limit).Offset(offset).Find(&repairTickets).Error; err != nil {
		return nil, 0, err
	}

	return repairTickets, total, nil
}

// ListByQuery 根据查询条件获取维修单列表
func (r *repairTicketRepository) ListByQuery(ctx context.Context, query *model.RepairTicketQuery) ([]*model.RepairTicket, int64, error) {
	var repairTickets []*model.RepairTicket
	var total int64

	// 构建查询
	db := r.db.WithContext(ctx).Model(&model.RepairTicket{})

	// 应用过滤条件
	if query.TicketNo != "" {
		db = db.Where("ticket_no LIKE ?", "%"+query.TicketNo+"%")
	}

	if query.FaultTicketID > 0 {
		db = db.Where("fault_ticket_id = ?", query.FaultTicketID)
	}

	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}

	if query.RepairType != "" {
		db = db.Where("repair_type = ?", query.RepairType)
	}

	if query.AssignedEngineerID > 0 {
		db = db.Where("assigned_engineer_id = ?", query.AssignedEngineerID)
	}

	if !query.StartTime.IsZero() {
		db = db.Where("created_at >= ?", query.StartTime)
	}

	if !query.EndTime.IsZero() {
		db = db.Where("created_at <= ?", query.EndTime)
	}

	if query.SearchKeyword != "" {
		db = db.Where("ticket_no LIKE ? OR solution LIKE ? OR repair_steps LIKE ? OR repair_result LIKE ?",
			"%"+query.SearchKeyword+"%",
			"%"+query.SearchKeyword+"%",
			"%"+query.SearchKeyword+"%",
			"%"+query.SearchKeyword+"%")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用排序
	if query.OrderBy != "" {
		direction := "DESC"
		if query.OrderDirection == "asc" {
			direction = "ASC"
		}
		db = db.Order(query.OrderBy + " " + direction)
	} else {
		db = db.Order("created_at DESC")
	}

	// 应用分页
	if query.PageSize > 0 {
		offset := (query.Page - 1) * query.PageSize
		db = db.Offset(int(offset)).Limit(int(query.PageSize))
	}

	// 获取数据（添加关联预加载）
	if err := db.Preload("FaultTicket.Resource").Preload("FaultTicket.Resource.Device").Find(&repairTickets).Error; err != nil {
		return nil, 0, fmt.Errorf("查询维修单失败: %w", err)
	}

	return repairTickets, total, nil
}

// CreateStatusHistory 创建维修单状态历史
func (r *repairTicketRepository) CreateStatusHistory(ctx context.Context, history *model.RepairTicketStatusHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// GetStatusHistory 获取维修单状态历史
func (r *repairTicketRepository) GetStatusHistory(ctx context.Context, ticketID uint) ([]*model.RepairTicketStatusHistory, error) {
	var histories []*model.RepairTicketStatusHistory
	err := r.db.WithContext(ctx).
		Where("repair_ticket_id = ?", ticketID).
		Order("operation_time DESC").
		Find(&histories).Error
	if err != nil {
		return nil, err
	}
	return histories, nil
}

// WithTransaction 事务操作
func (r *repairTicketRepository) WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo RepairTicketRepository) error) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return fn(ctx, &repairTicketRepository{db: tx})
	})
}

// GetRepairTicketHistory 获取维修单历史记录
func (r *repairTicketRepository) GetRepairTicketHistory(ctx context.Context, repairTicketID uint) ([]model.RepairTicketStatusHistory, error) {
	var histories []model.RepairTicketStatusHistory

	result := r.db.Where("repair_ticket_id = ?", repairTicketID).
		Order("operation_time DESC").
		Find(&histories)

	if result.Error != nil {
		return nil, fmt.Errorf("获取维修单历史记录失败: %w", result.Error)
	}

	return histories, nil
}

// UpdateFields 更新维修单指定字段
func (r *repairTicketRepository) UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.RepairTicket{}).Where("id = ?", id).Updates(fields).Error
}
