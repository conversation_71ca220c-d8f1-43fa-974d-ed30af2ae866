package mysql

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type entryPersonRepository struct {
	db *gorm.DB
}

// NewEntryPersonRepository 创建新的入室人员仓库
func NewEntryPersonRepository(db *gorm.DB) repository.EntryPersonRepository {
	return &entryPersonRepository{db: db}
}

// Create 创建入室人员记录
func (r *entryPersonRepository) Create(ctx context.Context, repairSelection *model.EntryPerson) error {
	return r.db.WithContext(ctx).Create(repairSelection).Error
}

// GetByTicketID 根据工单ID获取入室人员记录
func (r *entryPersonRepository) GetByTicketID(ctx context.Context, ticketID uint) (*model.EntryPerson, error) {
	var repairSelection model.EntryPerson
	// 添加排序，确保获取最新的记录
	err := r.db.WithContext(ctx).
		Where("ticket_id = ?", ticketID).
		Order("created_at DESC").
		First(&repairSelection).Error
	if err != nil {
		return nil, err
	}
	return &repairSelection, nil
}

// GetByTicketID 根据工单ID获取入室人员记录
func (r *entryPersonRepository) GetByID(ctx context.Context, id uint) (*model.EntryPerson, error) {
	var repairSelection model.EntryPerson
	// 添加排序，确保获取最新的记录
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		//Order("created_at DESC").
		First(&repairSelection).Error
	if err != nil {
		return nil, err
	}
	return &repairSelection, nil
}

// Update 更新入室人员记录
func (r *entryPersonRepository) Update(ctx context.Context, repairSelection *model.EntryPerson) error {
	return r.db.WithContext(ctx).Save(repairSelection).Error
}

// UpdateFields 安全地更新入室单的特定字段，避免全表更新风险
func (r *entryPersonRepository) UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	if id == 0 {
		return errors.New("invalid ticket id: id cannot be zero")
	}

	// 记录更新操作
	fmt.Printf("正在更新人员(ID=%d)的指定字段: %+v\n", id, fields)

	// 字段名映射 - 将Go结构体字段名映射到数据库列名
	fieldMapping := map[string]string{
		//"deviceSN":      "device_sn",
		//"componentSN":   "component_sn",
		//"componentType": "component_type",
		// 可以根据需要添加更多映射
	}

	// 处理字段名映射
	mappedFields := make(map[string]interface{})
	for k, v := range fields {
		// 检查是否需要映射
		if mappedKey, exists := fieldMapping[k]; exists {
			mappedFields[mappedKey] = v
			fmt.Printf("字段名映射: %s -> %s\n", k, mappedKey)
		} else {
			mappedFields[k] = v
		}
	}

	//// 如果字段中包含CurrentWaitingStage，验证其值
	//if stageValue, exists := mappedFields["current_waiting_stage"]; exists {
	//	if stage, ok := stageValue.(string); ok && stage != "" {
	//		if stage == "reaitingTime" || strings.Contains(stage, "Time:") || strings.Contains(stage, ":") {
	//			fmt.Printf("警告: 检测到UpdateFields尝试设置非法的CurrentWaitingStage值: %s\n", stage)
	//
	//			cleanedStage := ""
	//
	//			// 处理特殊错误模式
	//			if stage == "reaitingTime" {
	//				cleanedStage = "repair_selection"
	//				fmt.Printf("修复UpdateFields的CurrentWaitingStage: reaitingTime -> repair_selection\n")
	//			} else if strings.Contains(stage, "Time:") {
	//				// 提取前缀部分
	//				parts := strings.Split(stage, "Time:")
	//				if len(parts) > 0 {
	//					potentialStage := parts[0]
	//					fmt.Printf("从Time:分隔提取的阶段名称: %s\n", potentialStage)
	//					cleanedStage = potentialStage
	//				}
	//			}
	//
	//			// 如果清理失败，设置为空值
	//			if cleanedStage == "" {
	//				fmt.Printf("无法修复UpdateFields的CurrentWaitingStage，已清空该字段\n")
	//				mappedFields["current_waiting_stage"] = ""
	//			} else {
	//				mappedFields["current_waiting_stage"] = cleanedStage
	//				fmt.Printf("已修复UpdateFields的CurrentWaitingStage: %s\n", cleanedStage)
	//			}
	//		}
	//	}
	//}

	// 检查记录是否存在
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.EntryPerson{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("验证人员存在性失败: %w", err)
	}
	if count == 0 {
		return fmt.Errorf("人员不存在(ID=%d)", id)
	}

	// 执行更新，明确指定WHERE条件
	//tx := r.db.Begin()
	//if tx.Error != nil {
	//	return fmt.Errorf("开始事务失败: %w", tx.Error)
	//}
	//
	//defer func() {
	//	if r := recover(); r != nil {
	//		tx.Rollback()
	//	}
	//}()

	// 明确使用WHERE条件，只更新指定ID的记录
	result := r.db.Model(&model.EntryPerson{}).Where("id = ?", id).Updates(mappedFields)
	if result.Error != nil {
		//tx.Rollback()
		return fmt.Errorf("更新字段失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		//tx.Rollback()
		return fmt.Errorf("更新失败: 没有记录被修改(ID=%d)", id)
	}

	// 提交事务
	//if err := tx.Commit().Error; err != nil {
	//	tx.Rollback()
	//	return fmt.Errorf("提交事务失败: %w", err)
	//}

	fmt.Printf("成功更新人员(ID=%d)的指定字段\n", id)
	return nil
}

// Delete 删除入室人员记录
func (r *entryPersonRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.EntryPerson{}, id).Error
}

func (r *entryPersonRepository) List(ctx context.Context, page, pageSize int, conditions map[string]interface{}) ([]*model.EntryPerson, int64, error) {
	var persons []*model.EntryPerson
	var total int64

	db := r.db.WithContext(ctx).Model(&model.EntryPerson{})

	// 处理关键词搜索
	//if query, ok := conditions["query"].(string); ok && query != "" {
	//	db = db.Where("ticket_no LIKE ? ",
	//		"%"+query+"%")
	//}

	// 处理状态筛选 - 支持单个状态或状态数组
	//if statusArray, ok := conditions["status"].([]string); ok && len(statusArray) > 0 {
	//	db = db.Where("status IN ?", statusArray)
	//} else if status, ok := conditions["status"].(string); ok && status != "" {
	//	db = db.Where("status = ?", status)
	//}

	// 处理入室人筛选
	//if reporterName, ok := conditions["reporter_name"].(string); ok && reporterName != "" {
	//	db = db.Where("applicant_name LIKE ?", "%"+reporterName+"%")
	//}

	// 处理创建时间范围
	//if startTime, ok := conditions["start_time"].(*time.Time); ok && startTime != nil {
	//	db = db.Where("creation_time >= ?", startTime)
	//}
	//if endTime, ok := conditions["end_time"].(*time.Time); ok && endTime != nil {
	//	db = db.Where("creation_time <= ?", endTime)
	//}

	if time, ok := conditions["time"].(*time.Time); ok && time != nil {
		db = db.Where("entry_end_time >= ?", time)
	}

	db = db.Where("status = ?", "enabled")

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = db.
		//Preload("Resource").
		Offset(offset).
		Limit(pageSize).
		Order("created_at DESC").
		Find(&persons).Error
	if err != nil {
		return nil, 0, err
	}

	return persons, total, nil
}

// WithTransaction 在事务中执行操作
func (r *entryPersonRepository) WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo repository.EntryPersonRepository) error) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建新的仓库实例，使用事务对象
		txRepo := &entryPersonRepository{db: tx}
		// 执行回调函数
		return fn(ctx, txRepo)
	})
}
