package mysql

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"

	"gorm.io/gorm"
)

type customerApprovalRepository struct {
	db *gorm.DB
}

// NewCustomerApprovalRepository 创建新的客户审批仓库
func NewCustomerApprovalRepository(db *gorm.DB) repository.CustomerApprovalRepository {
	return &customerApprovalRepository{db: db}
}

// Create 创建客户审批记录
func (r *customerApprovalRepository) Create(ctx context.Context, customerApproval *model.CustomerApproval) error {
	return r.db.WithContext(ctx).Create(customerApproval).Error
}

// GetByTicketID 根据工单ID获取客户审批记录
func (r *customerApprovalRepository) GetByTicketID(ctx context.Context, ticketID uint) (*model.CustomerApproval, error) {
	var customerApproval model.CustomerApproval
	err := r.db.WithContext(ctx).Where("ticket_id = ?", ticketID).First(&customerApproval).Error
	if err != nil {
		return nil, err
	}
	return &customerApproval, nil
}

// Update 更新客户审批记录
func (r *customerApprovalRepository) Update(ctx context.Context, customerApproval *model.CustomerApproval) error {
	return r.db.WithContext(ctx).Save(customerApproval).Error
}

// Delete 删除客户审批记录
func (r *customerApprovalRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.CustomerApproval{}, id).Error
}
