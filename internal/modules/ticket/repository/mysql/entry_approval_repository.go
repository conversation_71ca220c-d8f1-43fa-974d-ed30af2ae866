package mysql

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"

	"gorm.io/gorm"
)

type entryApprovalRepository struct {
	db *gorm.DB
}

// NewEntryApprovalRepository 创建新的客户审批仓库
func NewEntryApprovalRepository(db *gorm.DB) repository.EntryApprovalRepository {
	return &entryApprovalRepository{db: db}
}

// Create 创建客户审批记录
func (r *entryApprovalRepository) Create(ctx context.Context, customerApproval *model.EntryApproval) error {
	return r.db.WithContext(ctx).Create(customerApproval).Error
}

// GetByTicketID 根据工单ID获取审批记录
func (r *entryApprovalRepository) GetByTicketID(ctx context.Context, ticketID uint) (*model.EntryApproval, error) {
	var customerApproval model.EntryApproval
	err := r.db.WithContext(ctx).Where("ticket_id = ?", ticketID).First(&customerApproval).Error
	if err != nil {
		return nil, err
	}
	return &customerApproval, nil
}

// Update 更新审批记录
func (r *entryApprovalRepository) Update(ctx context.Context, customerApproval *model.EntryApproval) error {
	return r.db.WithContext(ctx).Save(customerApproval).Error
}

// Delete 删除审批记录
func (r *entryApprovalRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.EntryApproval{}, id).Error
}
