package activity

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// FaultTicketActivity 是报障单相关的活动
type FaultTicketActivity struct {
	faultTicketRepo repository.FaultTicketRepository
	logger          *zap.Logger
}

// NewFaultTicketActivity 创建报障单活动实例
func NewFaultTicketActivity(faultTicketRepo repository.FaultTicketRepository, logger *zap.Logger) *FaultTicketActivity {
	return &FaultTicketActivity{
		faultTicketRepo: faultTicketRepo,
		logger:          logger,
	}
}

// MarkTicketForManualTriggerActivity 标记工单为手动触发的活动
func (a *FaultTicketActivity) MarkTicketForManualTriggerActivity(ctx context.Context, input *model.MarkTicketForManualTriggerInput) error {
	a.logger.Info("执行标记工单为手动触发活动",
		zap.Uint("ticket_id", input.TicketID),
		zap.String("waiting_stage", input.WaitingStage),
	)

	// 查找工单
	ticket, err := a.faultTicketRepo.GetByID(ctx, input.TicketID)
	if err != nil {
		a.logger.Error("查找工单失败",
			zap.Uint("ticket_id", input.TicketID),
			zap.Error(err),
		)
		return fmt.Errorf("查找工单失败: %w", err)
	}

	// 更新工单为等待手动触发
	ticket.WaitingManualTrigger = true
	ticket.CurrentWaitingStage = input.WaitingStage
	ticket.LastWaitingTime = &time.Time{}

	if err := a.faultTicketRepo.Update(ctx, ticket); err != nil {
		a.logger.Error("更新工单为等待手动触发失败",
			zap.Uint("ticket_id", input.TicketID),
			zap.Error(err),
		)
		return fmt.Errorf("更新工单为等待手动触发失败: %w", err)
	}

	a.logger.Info("成功标记工单为手动触发",
		zap.Uint("ticket_id", input.TicketID),
		zap.String("waiting_stage", input.WaitingStage),
	)
	return nil
}
