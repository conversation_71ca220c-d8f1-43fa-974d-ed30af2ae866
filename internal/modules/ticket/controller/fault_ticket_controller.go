package controller

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/service"
	"backend/internal/modules/ticket/workflow"
	"backend/response"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/repository"

	"github.com/gin-gonic/gin"
)

// FaultTicketController 报障单控制器
type FaultTicketController struct {
	service service.FaultTicketService
}

// NewFaultTicketController 创建报障单控制器
func NewFaultTicketController(service service.FaultTicketService) *FaultTicketController {
	return &FaultTicketController{service: service}
}

// RegisterRoutes 注册路由
func (c *FaultTicketController) RegisterRoutes(router *gin.RouterGroup) {
	faultTicketRouter := router.Group("/fault-tickets")
	{
		faultTicketRouter.POST("", c.<PERSON>reateFaultTicket)
		faultTicketRouter.GET("/:id", c.GetFaultTicket)
		faultTicketRouter.PUT("/:id", c.UpdateFaultTicket)
		faultTicketRouter.PUT("/:id/assign", c.AssignFaultTicket)
		//faultTicketRouter.PUT("/:id/close", c.CloseFaultTicket)
		faultTicketRouter.GET("", c.ListFaultTickets)
		faultTicketRouter.GET("/:id/history", c.GetFaultTicketStatusHistory)
		faultTicketRouter.PUT("/:id/transition", c.TransitionFaultTicket)
		faultTicketRouter.PUT("/:id/fields", c.UpdateFaultTicketFields)
		faultTicketRouter.GET("/device/:sn/fault-count", c.GetDeviceMonthlyFaultCount)
	}
}

// CreateFaultTicket 创建报障单
// @Summary 创建报障单
// @Description 创建新的报障单，只需提供服务器SN
// @Tags 工单管理-报障单
// @Accept json
// @Produce json
// @Param fault_ticket body object true "报障单信息 (只需要deviceSN字段)"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/fault-tickets [post]
func (c *FaultTicketController) CreateFaultTicket(ctx *gin.Context) {
	var requestBody struct {
		DeviceSN           string `json:"deviceSN" binding:"required"`
		Title              string `json:"title"`
		FaultType          string `json:"faultType" binding:"required"`
		FaultDescription   string `json:"faultDescription" binding:"required"`
		Priority           string `json:"priority" binding:"required"`
		Source             string `json:"source"`
		ResourceIdentifier string `json:"resource_identifier"` //租户IP
		SlotPosition       string `json:"slotPosition"`
		CreationTime       string `json:"creationTime"`
		Remarks            string `json:"remarks"`
		RequireApproval    *bool  `json:"require_approval"`
	}

	if err := ctx.ShouldBindJSON(&requestBody); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 清除DeviceSN前后的空格和换行符
	requestBody.DeviceSN = strings.TrimSpace(requestBody.DeviceSN)
	requestBody.DeviceSN = strings.ReplaceAll(requestBody.DeviceSN, "\n", "")
	requestBody.DeviceSN = strings.ReplaceAll(requestBody.DeviceSN, "\r", "")

	// 获取当前用户信息
	currentUserID, currentUserName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败: "+err.Error())
		return
	}

	creationTime := time.Now()
	if requestBody.CreationTime != "" {
		parsedTime, err := time.Parse(time.RFC3339, requestBody.CreationTime)
		if err == nil {
			creationTime = parsedTime
		}
	}

	// 创建一个只包含必要信息的报障单对象
	faultTicket := model.FaultTicket{
		DeviceSN:           requestBody.DeviceSN,
		Title:              requestBody.Title,
		FaultType:          requestBody.FaultType,
		FaultDescription:   requestBody.FaultDescription,
		Priority:           requestBody.Priority,
		Source:             requestBody.Source,
		ResourceIdentifier: requestBody.ResourceIdentifier,
		SlotPosition:       requestBody.SlotPosition,
		CreationTime:       creationTime,
		Remarks:            requestBody.Remarks,
		// 设置报障人信息为当前登录用户
		ReporterID:   currentUserID,
		ReporterName: currentUserName,
		// 默认设置为需要审批
		RequireApproval: true,
	}

	// 如果请求中指定了RequireApproval，则使用请求中的值
	if requestBody.RequireApproval != nil {
		faultTicket.RequireApproval = *requestBody.RequireApproval
	}

	// 设置默认值
	if faultTicket.FaultType == "" {
		faultTicket.FaultType = "hardware"
	}
	if faultTicket.Priority == "" {
		faultTicket.Priority = "high"
	}
	if faultTicket.Source == "" {
		faultTicket.Source = "manual"
	}

	if err := c.service.CreateFaultTicket(ctx, &faultTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建报障单失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": faultTicket.ID, "ticket_no": faultTicket.TicketNo}, "创建报障单成功")
}

// GetFaultTicket 获取报障单
// @Summary 获取报障单详情
// @Description 根据ID获取报障单详情
// @Tags 工单管理-报障单
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Success 200 {object} response.ResponseStruct{data=model.FaultTicket}
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/fault-tickets/{id} [get]
func (c *FaultTicketController) GetFaultTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	faultTicket, err := c.service.GetFaultTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取报障单失败: "+err.Error())
		return
	}

	// 将完整的工单对象转换为DTO
	faultTicketDTO := repository.ConvertToDTO(faultTicket)
	response.Success(ctx, faultTicketDTO, "获取报障单成功")
}

// UpdateFaultTicket 更新报障单
// @Summary 更新报障单
// @Description 更新报障单信息
// @Tags 工单管理-报障单
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Param fault_ticket body model.FaultTicket true "报障单信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/fault-tickets/{id} [put]
func (c *FaultTicketController) UpdateFaultTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var faultTicket model.FaultTicket
	if err := ctx.ShouldBindJSON(&faultTicket); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 清除DeviceSN前后的空格和换行符
	faultTicket.DeviceSN = strings.TrimSpace(faultTicket.DeviceSN)
	faultTicket.DeviceSN = strings.ReplaceAll(faultTicket.DeviceSN, "\n", "")
	faultTicket.DeviceSN = strings.ReplaceAll(faultTicket.DeviceSN, "\r", "")

	faultTicket.ID = uint(id)

	// 验证SLAStatus字段是否合法
	if faultTicket.SLAStatus != "" &&
		faultTicket.SLAStatus != "in_compliance" &&
		faultTicket.SLAStatus != "violated" &&
		faultTicket.SLAStatus != "exempted" {
		// 获取原始工单数据
		originalTicket, err := c.service.GetFaultTicketByID(ctx, uint(id))
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "获取报障单失败: "+err.Error())
			return
		}

		// 使用原始工单的SLAStatus值
		faultTicket.SLAStatus = originalTicket.SLAStatus
	}

	if err := c.service.UpdateFaultTicket(ctx, &faultTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新报障单失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新报障单成功")
}

// AssignFaultTicket 分配报障单
// @Summary 分配报障单
// @Description 将报障单分配给工程师
// @Tags 工单管理-报障单
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Param engineer body model.FaultTicketAssignment true "工程师信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/fault-tickets/{id}/assign [put]
func (c *FaultTicketController) AssignFaultTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var assignRequest model.FaultTicketAssignment

	if err := ctx.ShouldBindJSON(&assignRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.AssignFaultTicket(ctx, uint(id), assignRequest.EngineerID); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "分配报障单失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "分配报障单成功")
}

// CloseFaultTicket 关闭报障单
// @Summary 关闭报障单
// @Description 关闭报障单并添加总结
// @Tags 工单管理-报障单
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Param summary body object true "总结信息 (包含summary字段)"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/fault-tickets/{id}/close [put]
func (c *FaultTicketController) CloseFaultTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var closeRequest struct {
		Summary            string  `json:"summary" binding:"required"`
		RepairMethod       string  `json:"repair_method"`
		PreventionMeasures string  `json:"prevention_measures"`
		FaultDetailType    string  `json:"fault_detail_type"`
		CountInSLA         *bool   `json:"count_in_sla"`
		BusinessImpact     string  `json:"business_impact"`
		BusinessImpactTime *int    `json:"business_impact_time"`
		TotalDowntime      *int    `json:"total_downtime"`
		CreationTime       *string `json:"creation_time"`
	}

	if err := ctx.ShouldBindJSON(&closeRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 更新报障单状态为已完成，并设置总结
	// 先获取报障单
	ticket, err := c.service.GetFaultTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取报障单失败: "+err.Error())
		return
	}

	// 检查状态，只有在等待验证或总结状态才能关闭
	if ticket.Status != "waiting_verification" && ticket.Status != "summarizing" {
		response.Fail(ctx, http.StatusBadRequest, "只有在等待验证或总结状态的报障单才能关闭")
		return
	}

	// 处理创建时间
	if closeRequest.CreationTime != nil && *closeRequest.CreationTime != "" {
		creationTime, err := time.Parse(time.RFC3339, *closeRequest.CreationTime)
		if err == nil {
			ticket.CreationTime = creationTime
		}
	}

	// 准备更新字段
	updateFields := map[string]interface{}{
		"fault_summary": closeRequest.Summary,
		"status":        "completed",
		"close_time":    time.Now(),
	}

	// 添加可选字段
	if closeRequest.RepairMethod != "" {
		updateFields["repair_method"] = closeRequest.RepairMethod
	}
	if closeRequest.PreventionMeasures != "" {
		updateFields["prevention_measures"] = closeRequest.PreventionMeasures
	}
	if closeRequest.FaultDetailType != "" {
		updateFields["fault_detail_type"] = closeRequest.FaultDetailType
	}
	if closeRequest.CountInSLA != nil {
		updateFields["count_in_sla"] = *closeRequest.CountInSLA
	}
	if closeRequest.BusinessImpact != "" {
		updateFields["business_impact"] = closeRequest.BusinessImpact
	}
	if closeRequest.BusinessImpactTime != nil {
		updateFields["business_impact_time"] = *closeRequest.BusinessImpactTime
	}
	if closeRequest.TotalDowntime != nil {
		updateFields["total_downtime"] = *closeRequest.TotalDowntime
	}

	// 关闭报障单
	if err := c.service.UpdateFaultTicketFields(ctx, uint(id), updateFields); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "关闭报障单失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "关闭报障单成功")
}

// ListFaultTickets 获取报障单列表
// @Summary 获取报障单列表
// @Description 分页获取报障单列表，支持筛选
// @Tags 工单管理-报障单
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Param status query string false "状态筛选"
// @Param status[] query []string false "多状态筛选"
// @Param priority query string false "优先级筛选"
// @Param title query string false "故障标题"
// @Param resource_identifier query string false "租户IP"
// @Param faultType query string false "故障类型"
// @Param fault_detail_type query string false "具体故障类型"
// @Param reporterName query string false "报障人"
// @Param assignedTo query string false "接单人"
// @Param creationTimeRange query string false "创建时间范围，格式：开始时间,结束时间，如：2023-01-01 00:00:00,2023-01-02 00:00:00"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/fault-tickets [get]
func (c *FaultTicketController) ListFaultTickets(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("query", "")

	// 获取状态参数，支持单个状态和多状态数组
	var statusValues []string
	status := ctx.DefaultQuery("status", "")
	if status != "" {
		statusValues = append(statusValues, status)
	}

	// 获取status[]参数
	statusArray := ctx.QueryArray("status[]")
	if len(statusArray) > 0 {
		statusValues = append(statusValues, statusArray...)
	}

	priority := ctx.DefaultQuery("priority", "")

	// 新增查询参数
	title := ctx.DefaultQuery("title", "")
	resourceIdentifier := ctx.DefaultQuery("resource_identifier", "")
	faultType := ctx.DefaultQuery("faultType", "")
	faultDetailType := ctx.DefaultQuery("fault_detail_type", "")
	reporterName := ctx.DefaultQuery("reporterName", "")
	assignedTo := ctx.DefaultQuery("assignedTo", "")
	var creationTimeRange string

	// 处理创建时间范围
	var startTime, endTime *time.Time

	// 获取开始时间参数
	creationTimeStart := ctx.DefaultQuery("creationTimeStart", "")
	if creationTimeStart != "" {
		parsedTime, err := time.Parse(time.RFC3339, creationTimeStart)
		if err == nil {
			startTime = &parsedTime
		} else {
			// 尝试其他格式
			parsedTime, err = time.Parse("2006-01-02 15:04:05", creationTimeStart)
			if err == nil {
				startTime = &parsedTime
			}
		}
	}

	// 获取结束时间参数
	creationTimeEnd := ctx.DefaultQuery("creationTimeEnd", "")
	if creationTimeEnd != "" {
		parsedTime, err := time.Parse(time.RFC3339, creationTimeEnd)
		if err == nil {
			endTime = &parsedTime
		} else {
			// 尝试其他格式
			parsedTime, err = time.Parse("2006-01-02 15:04:05", creationTimeEnd)
			if err == nil {
				endTime = &parsedTime
			}
		}
	}

	// 为了兼容性，也处理creationTimeRange参数
	creationTimeRange = ctx.DefaultQuery("creationTimeRange", "")
	if creationTimeRange != "" && startTime == nil && endTime == nil {
		timeRanges := strings.Split(creationTimeRange, ",")
		if len(timeRanges) == 2 {
			startTimeStr := strings.TrimSpace(timeRanges[0])
			endTimeStr := strings.TrimSpace(timeRanges[1])

			if startTimeStr != "" && startTime == nil {
				parsedTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr)
				if err == nil {
					startTime = &parsedTime
				}
			}

			if endTimeStr != "" && endTime == nil {
				parsedTime, err := time.Parse("2006-01-02 15:04:05", endTimeStr)
				if err == nil {
					endTime = &parsedTime
				}
			}
		}
	}

	// 构建查询条件
	filters := map[string]interface{}{
		"query":               query,
		"status":              statusValues,
		"priority":            priority,
		"title":               title,
		"resource_identifier": resourceIdentifier,
		"faultType":           faultType,
		"fault_detail_type":   faultDetailType,
		"reporterName":        reporterName,
		"assignedTo":          assignedTo,
		"startTime":           startTime,
		"endTime":             endTime,
	}

	faultTickets, total, err := c.service.ListFaultTickets(ctx, page, pageSize, filters)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取报障单列表失败: "+err.Error())
		return
	}

	// 将完整的工单列表转换为DTO列表
	faultTicketDTOs := repository.ConvertToDTOList(faultTickets)

	response.Success(ctx, gin.H{
		"list":  faultTicketDTOs,
		"total": total,
	}, "获取报障单列表成功")
}

// GetFaultTicketStatusHistory 获取报障单状态历史
// @Summary 获取报障单状态历史
// @Description 获取报障单状态变更历史记录
// @Tags 工单管理-报障单
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/fault-tickets/{id}/history [get]
func (c *FaultTicketController) GetFaultTicketStatusHistory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	history, err := c.service.GetFaultTicketStatusHistory(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取状态历史失败: "+err.Error())
		return
	}

	response.Success(ctx, history, "获取状态历史成功")
}

// TransitionFaultTicket 状态转换与工作流阶段触发
// @Summary 状态转换与触发工作流
// @Description 统一的工单状态转换接口，支持所有工作流阶段
// @Tags 工单管理-报障单
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Param transition body object true "转换信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/fault-tickets/{id}/transition [put]
func (c *FaultTicketController) TransitionFaultTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var transitionRequest struct {
		Stage    string                 `json:"stage" binding:"required"`  // 工作流阶段
		Status   string                 `json:"status" binding:"required"` // 新状态
		Comments string                 `json:"comments"`                  // 备注
		Data     map[string]interface{} `json:"data"`                      // 阶段相关数据
	}

	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 调试信息：打印收到的数据结构
	fmt.Printf("TransitionFaultTicket收到的数据: stage=%s, status=%s, data=%+v\n",
		transitionRequest.Stage, transitionRequest.Status, transitionRequest.Data)

	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取当前工单
	ticket, err := c.service.GetFaultTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取报障单失败: "+err.Error())
		return
	}

	// 验证状态转换是否合法
	if !workflow.IsValidStatusTransition(ticket.Status, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", ticket.Status, transitionRequest.Status))
		return
	}

	// 验证阶段转换是否合法
	isValid, message := common.ValidateStageTransition(ticket.Status, transitionRequest.Stage)
	if !isValid {
		response.Fail(ctx, http.StatusBadRequest, "当前状态下不能触发此阶段: "+message)
		return
	}

	// 如果是维修选择阶段，强制设置需要客户审批
	if transitionRequest.Stage == "repair_selection" {
		if transitionRequest.Data == nil {
			transitionRequest.Data = make(map[string]interface{})
		}

		// 从请求数据中获取是否需要客户审批
		requireApproval := ticket.RequireApproval // 默认使用工单上的RequireApproval字段

		// 如果请求中明确指定了require_customer_approval，则使用请求中的值
		if val, ok := transitionRequest.Data["require_customer_approval"]; ok {
			if boolVal, isBool := val.(bool); isBool {
				requireApproval = boolVal
			}
		}

		// 设置到数据中
		transitionRequest.Data["require_customer_approval"] = requireApproval

		// 更新工单的RequireApproval字段
		updateFields := map[string]interface{}{
			"require_approval": requireApproval,
		}

		// 如果不需要客户审批，且不是客户报障来源，则设置count_in_sla为false
		if !requireApproval {
			// 获取当前报障单
			currentTicket, err := c.service.GetFaultTicketByID(ctx, uint(id))
			if err != nil {
				// 如果获取失败，仍然设置为false（保持原逻辑）
				updateFields["count_in_sla"] = false
			} else if currentTicket.Source != "customer" {
				// 只有在不是客户报障来源的情况下才设置为false
				updateFields["count_in_sla"] = false
			}
		}

		if err := c.service.UpdateFaultTicketFields(ctx, uint(id), updateFields); err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "更新审批要求失败: "+err.Error())
			return
		}

		// 根据是否需要客户审批设置状态
		if requireApproval {
			// 需要客户审批，设置为等待审批状态
			transitionRequest.Status = common.StatusWaitingApproval
		} else {
			// 不需要客户审批，直接设置为已批准等待操作状态
			transitionRequest.Status = common.StatusApprovedWaiting
		}
	}

	// 根据不同的阶段创建相应的业务记录
	switch transitionRequest.Stage {
	case "repair_selection":
		// 创建维修选择记录
		repairType, ok := transitionRequest.Data["repair_type"].(string)
		if !ok {
			response.Fail(ctx, http.StatusBadRequest, "维修类型格式错误")
			return
		}

		diagnosis, ok := transitionRequest.Data["diagnosis"].(string)
		if !ok {
			response.Fail(ctx, http.StatusBadRequest, "诊断信息格式错误")
			return
		}

		repairSelection := &model.RepairSelection{
			TicketID:     uint(id),
			RepairType:   repairType,
			Diagnosis:    diagnosis,
			Comments:     transitionRequest.Comments,
			OperatorID:   operatorID,
			OperatorName: operatorName,
		}

		// 处理故障详细类型 - 支持嵌套对象和简单字符串两种情况
		var faultDetailTypeStr string
		var faultTypeStr string

		// 先检查是否为嵌套对象格式
		if faultDetailTypeObj, ok := transitionRequest.Data["fault_detail_type"].(map[string]interface{}); ok {
			// 从嵌套对象中提取fault_detail_type
			if detailType, hasDetail := faultDetailTypeObj["fault_detail_type"].(string); hasDetail && detailType != "" {
				faultDetailTypeStr = detailType
			}

			// 从嵌套对象中提取fault_type
			if typeValue, hasType := faultDetailTypeObj["fault_type"].(string); hasType && typeValue != "" {
				faultTypeStr = typeValue
			}

		} else if simpleDetailType, ok := transitionRequest.Data["fault_detail_type"].(string); ok && simpleDetailType != "" {
			// 简单字符串情况
			faultDetailTypeStr = simpleDetailType
		}

		// 处理顶级的fault_type字段（优先级低于嵌套对象中的fault_type）
		if faultTypeStr == "" {
			if simpleType, ok := transitionRequest.Data["fault_type"].(string); ok && simpleType != "" {
				faultTypeStr = simpleType
			}
		}

		// 更新维修选择记录中的故障详细类型
		if faultDetailTypeStr != "" {
			repairSelection.FaultDetailType = faultDetailTypeStr

			// 同时更新故障单的具体故障类型字段
			if err := c.service.UpdateFaultTicketFields(ctx, uint(id), map[string]interface{}{
				"fault_detail_type": faultDetailTypeStr,
			}); err != nil {
				response.Fail(ctx, http.StatusInternalServerError, "更新故障具体类型失败: "+err.Error())
				return
			}
		}

		// 更新故障类型
		if faultTypeStr != "" {
			// 更新故障单的故障类型字段
			if err := c.service.UpdateFaultTicketFields(ctx, uint(id), map[string]interface{}{
				"fault_type": faultTypeStr,
			}); err != nil {
				response.Fail(ctx, http.StatusInternalServerError, "更新故障类型失败: "+err.Error())
				return
			}

			// 输出日志确认更新
			fmt.Printf("已更新工单ID=%d的故障类型: %s\n", id, faultTypeStr)
		}

		// 处理插槽位置
		if slotPosition, ok := transitionRequest.Data["slot_position"].(string); ok && slotPosition != "" {
			// 清理多余的空格
			slotPosition = strings.TrimSpace(slotPosition)
			// 将插槽位置保存到维修选择记录
			repairSelection.SlotPosition = slotPosition

			// 同时更新故障单的插槽位置字段
			if err := c.service.UpdateFaultTicketFields(ctx, uint(id), map[string]interface{}{
				"slot_position": slotPosition,
			}); err != nil {
				response.Fail(ctx, http.StatusInternalServerError, "更新插槽位置失败: "+err.Error())
				return
			}
		}

		if err := c.service.CreateRepairSelection(ctx, repairSelection); err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "创建维修选择记录失败: "+err.Error())
			return
		}

	case "customer_approval":
		// 创建客户审批记录
		status, ok := transitionRequest.Data["status"].(string)
		if !ok {
			response.Fail(ctx, http.StatusBadRequest, "审批状态格式错误")
			return
		}

		customerApproval := &model.CustomerApproval{
			TicketID:     uint(id),
			Status:       status,
			ResponseTime: time.Now(),
			Comments:     transitionRequest.Comments,
			CustomerID:   operatorID,
			CustomerName: operatorName,
		}
		if err := c.service.CreateCustomerApproval(ctx, customerApproval); err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "创建客户审批记录失败: "+err.Error())
			return
		}

		// 如果审批通过，将RequireApproval设置为false
		if customerApproval.Status == "approved" {
			// 更新票据RequireApproval字段以及客户审批时间
			updateData := map[string]interface{}{
				"require_approval":       false,
				"customer_approval_time": customerApproval.ResponseTime,
			}

			if err := c.service.UpdateFaultTicketFields(ctx, uint(id), updateData); err != nil {
				response.Fail(ctx, http.StatusInternalServerError, "更新票据审批状态失败: "+err.Error())
				return
			}
		}

	case "verification", "complete_verification":
		// 创建验证记录
		success, ok := transitionRequest.Data["success"].(bool)
		if !ok {
			response.Fail(ctx, http.StatusBadRequest, "验证结果格式错误")
			return
		}

		verification := &model.Verification{
			TicketID:         uint(id),
			Success:          success,
			VerificationTime: time.Now(),
			Comments:         transitionRequest.Comments,
			OperatorID:       operatorID,
			OperatorName:     operatorName,
		}
		if err := c.service.CreateVerification(ctx, verification); err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "创建验证记录失败: "+err.Error())
			return
		}

	case "summary":
		// 处理总结阶段
		updateFields := map[string]interface{}{}

		// 如果状态为cancelled，自动设置count_in_sla为false
		if transitionRequest.Status == common.StatusCancelled {
			updateFields["count_in_sla"] = false
			// 在数据中也更新，确保传递给工作流
			if transitionRequest.Data == nil {
				transitionRequest.Data = make(map[string]interface{})
			}
			transitionRequest.Data["count_in_sla"] = false

			// 处理取消原因
			if cancelReason, ok := transitionRequest.Data["cancel_reason"].(string); ok && cancelReason != "" {
				// 更新控制器中的字段
				updateFields["fault_summary"] = fmt.Sprintf("取消原因: %s", cancelReason)
				// 确保工作流也能收到取消原因
				transitionRequest.Data["cancel_reason"] = cancelReason

				// 日志记录
				fmt.Printf("工单ID=%d的取消原因: %s，已设置到故障总结字段\n", id, cancelReason)
			}

			// 日志记录
			fmt.Printf("检测到summary阶段状态为cancelled，自动设置count_in_sla=false，工单ID=%d\n", id)
		}

		// 处理故障总结 - 只有在非取消状态或没有取消原因时才使用summary字段
		if summary, ok := transitionRequest.Data["summary"].(string); ok && summary != "" {
			if transitionRequest.Status != common.StatusCancelled || updateFields["fault_summary"] == nil {
				updateFields["fault_summary"] = summary
			}
		}

		// 处理维修方法
		if repairMethod, ok := transitionRequest.Data["repair_method"].(string); ok && repairMethod != "" {
			updateFields["repair_method"] = repairMethod
		}

		// 处理预防措施
		if preventionMeasures, ok := transitionRequest.Data["prevention_measures"].(string); ok && preventionMeasures != "" {
			updateFields["prevention_measures"] = preventionMeasures
		}

		// 处理是否计入SLA
		if countInSLA, ok := transitionRequest.Data["count_in_sla"].(bool); ok {
			updateFields["count_in_sla"] = countInSLA
		}

		// 处理故障详细类型 - 支持嵌套对象和简单字符串两种情况
		var faultDetailTypeStr string
		var faultTypeStr string

		// 先检查是否为嵌套对象格式
		if faultDetailTypeObj, ok := transitionRequest.Data["fault_detail_type"].(map[string]interface{}); ok {

			// 从嵌套对象中提取fault_detail_type
			if detailType, hasDetail := faultDetailTypeObj["fault_detail_type"].(string); hasDetail && detailType != "" {
				faultDetailTypeStr = detailType
				updateFields["fault_detail_type"] = detailType
			}

			// 从嵌套对象中提取fault_type
			if typeValue, hasType := faultDetailTypeObj["fault_type"].(string); hasType && typeValue != "" {
				faultTypeStr = typeValue
				updateFields["fault_type"] = typeValue
			}

		} else if simpleDetailType, ok := transitionRequest.Data["fault_detail_type"].(string); ok && simpleDetailType != "" {
			// 简单字符串情况
			faultDetailTypeStr = simpleDetailType
			updateFields["fault_detail_type"] = simpleDetailType
		}

		// 处理顶级的fault_type字段（优先级低于嵌套对象中的fault_type）
		if faultTypeStr == "" {
			if simpleType, ok := transitionRequest.Data["fault_type"].(string); ok && simpleType != "" {
				faultTypeStr = simpleType
				updateFields["fault_type"] = simpleType
			}
		}

		if faultTypeStr != "" || faultDetailTypeStr != "" {
			fmt.Printf("summary阶段将更新工单ID=%d的故障类型: type=%s, detail=%s\n",
				id, faultTypeStr, faultDetailTypeStr)
		}

		// 设置关闭时间为当前时间
		updateFields["close_time"] = time.Now()

		// 更新故障单字段
		if len(updateFields) > 0 {
			if err := c.service.UpdateFaultTicketFields(ctx, uint(id), updateFields); err != nil {
				response.Fail(ctx, http.StatusInternalServerError, "更新总结信息失败: "+err.Error())
				return
			}
		}
	}

	// 先触发工作流，确保工作流可以正常工作
	err = c.service.TriggerWorkflowStage(
		ctx,
		uint(id),
		transitionRequest.Stage,
		operatorID,
		operatorName,
		transitionRequest.Comments,
		transitionRequest.Data,
	)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "触发工作流失败: "+err.Error())
		return
	}

	// 工作流触发成功后，再更新状态
	if err := c.service.UpdateFaultTicketStatus(ctx, uint(id), transitionRequest.Status, operatorID, operatorName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新状态失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "状态转换成功")
}

// UpdateFaultTicketFields 更新报障单指定字段
// @Summary 更新报障单指定字段
// @Description 更新报障单特定字段，避免全字段更新可能引起的验证错误
// @Tags 工单管理-报障单
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Param fields body object true "要更新的字段"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/fault-tickets/{id}/fields [put]
func (c *FaultTicketController) UpdateFaultTicketFields(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 获取原始工单信息，检查工单是否存在
	_, err = c.service.GetFaultTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取报障单失败: "+err.Error())
		return
	}

	// 解析请求体中的字段
	var updateFields map[string]interface{}
	if err := ctx.ShouldBindJSON(&updateFields); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 清理device_sn字段
	if deviceSN, ok := updateFields["device_sn"].(string); ok {
		deviceSN = strings.TrimSpace(deviceSN)
		deviceSN = strings.ReplaceAll(deviceSN, "\n", "")
		deviceSN = strings.ReplaceAll(deviceSN, "\r", "")
		updateFields["device_sn"] = deviceSN
	}
	// 处理deviceSN字段（驼峰命名）
	if deviceSN, ok := updateFields["deviceSN"].(string); ok {
		deviceSN = strings.TrimSpace(deviceSN)
		deviceSN = strings.ReplaceAll(deviceSN, "\n", "")
		deviceSN = strings.ReplaceAll(deviceSN, "\r", "")
		updateFields["deviceSN"] = deviceSN
	}

	// 验证SLA状态字段
	if slaStatus, exists := updateFields["sla_status"]; exists {
		slaStatusStr, ok := slaStatus.(string)
		if !ok || (slaStatusStr != "in_compliance" && slaStatusStr != "violated" && slaStatusStr != "exempted") {
			// 如果SLA状态字段无效，则从更新字段中移除
			delete(updateFields, "sla_status")
		}
	}

	// 验证优先级字段
	if priority, exists := updateFields["priority"]; exists {
		priorityStr, ok := priority.(string)
		if !ok || (priorityStr != "low" && priorityStr != "medium" && priorityStr != "high" && priorityStr != "critical") {
			delete(updateFields, "priority")
		}
	}

	// 验证状态字段
	if status, exists := updateFields["status"]; exists {
		statusStr, ok := status.(string)
		if !ok || !isValidTicketStatus(statusStr) {
			delete(updateFields, "status")
		}
	}

	// 如果没有有效的更新字段，则返回成功但不执行更新
	if len(updateFields) == 0 {
		response.Success(ctx, nil, "无有效更新字段")
		return
	}

	// 调用服务更新字段
	if err := c.service.UpdateFaultTicketFields(ctx, uint(id), updateFields); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新报障单字段失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新报障单字段成功")
}

// 辅助函数：检查状态字符串是否有效
func isValidTicketStatus(status string) bool {
	validStatuses := []string{
		"waiting_accept", "investigating", "waiting_approval",
		"approved_waiting_action", "repairing", "restarting",
		"migrating", "software_fixing", "waiting_verification",
		"summarizing", "completed", "cancelled",
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// GetDeviceMonthlyFaultCount 获取设备本月故障次数
// @Summary 获取设备本月故障次数
// @Description 根据设备SN获取本月有效故障次数（排除重复故障和误报）及本周故障次数
// @Tags 工单管理-报障单
// @Accept json
// @Produce json
// @Param sn path string true "设备序列号"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/fault-tickets/device/{sn}/fault-count [get]
func (c *FaultTicketController) GetDeviceMonthlyFaultCount(ctx *gin.Context) {
	sn := ctx.Param("sn")
	if sn == "" {
		response.Fail(ctx, http.StatusBadRequest, "设备序列号不能为空")
		return
	}

	// 计算本月的起始和结束时间
	now := time.Now()
	currentYear, currentMonth, _ := now.Date()
	firstDayOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, now.Location())
	lastDayOfMonth := firstDayOfMonth.AddDate(0, 1, 0).Add(-time.Nanosecond)

	// 构建本月查询条件
	monthFilters := map[string]interface{}{
		"deviceSN":  sn,
		"startTime": &firstDayOfMonth,
		"endTime":   &lastDayOfMonth,
		// 排除重复故障和误报
		"excludeDuplicate":  true,
		"excludeFalseAlarm": true,
	}

	// 调用服务层方法获取本月符合条件的故障数量
	monthCount, err := c.service.CountDeviceFaults(ctx, monthFilters)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取本月故障次数失败: "+err.Error())
		return
	}

	// 计算本周的起始和结束时间
	// 获取今天是周几 (0是周日, 1-6是周一到周六)
	weekday := now.Weekday()
	// 计算本周一的日期 (如果今天是周日，则向前推6天；其他情况，向前推 weekday-1 天)
	var daysToSubtract int
	if weekday == 0 { // 周日
		daysToSubtract = 6
	} else {
		daysToSubtract = int(weekday - 1)
	}
	firstDayOfWeek := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).AddDate(0, 0, -daysToSubtract)
	lastDayOfWeek := firstDayOfWeek.AddDate(0, 0, 7).Add(-time.Nanosecond) // 下周一凌晨减1纳秒

	// 构建本周查询条件
	weekFilters := map[string]interface{}{
		"deviceSN":  sn,
		"startTime": &firstDayOfWeek,
		"endTime":   &lastDayOfWeek,
		// 排除重复故障和误报
		"excludeDuplicate":  true,
		"excludeFalseAlarm": true,
	}

	// 调用服务层方法获取本周符合条件的故障数量
	weekCount, err := c.service.CountDeviceFaults(ctx, weekFilters)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取本周故障次数失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"device_sn":   sn,
		"month":       fmt.Sprintf("%d-%02d", currentYear, currentMonth),
		"month_count": monthCount,
		"week":        fmt.Sprintf("%s至%s", firstDayOfWeek.Format("2006-01-02"), lastDayOfWeek.Format("2006-01-02")),
		"week_count":  weekCount,
	}, "获取设备故障次数成功")
}
