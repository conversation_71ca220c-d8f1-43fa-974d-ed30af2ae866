package controller

import (
	"backend/internal/modules/cmdb/model/inbound"
	purchaseService "backend/internal/modules/purchase_old/service"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/service"
	"backend/response"
	"fmt"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"

	"github.com/gin-gonic/gin"
)

// InboundTicketController 入库工单控制器
type InboundTicketController struct {
	service     service.InboundTicketService
	purchaseSvc purchaseService.PurchaseService
}

// NewInboundTicketController 创建入库工单控制器
func NewInboundTicketController(service service.InboundTicketService, purchaseSvc purchaseService.PurchaseService) *InboundTicketController {
	return &InboundTicketController{
		service:     service,
		purchaseSvc: purchaseSvc,
	}
}

// RegisterRoutes 注册路由
func (c *InboundTicketController) RegisterRoutes(router *gin.RouterGroup) {
	inboundTicketRouter := router.Group("/inbound-tickets")
	{
		inboundTicketRouter.PUT("/part/:id/transition", c.TransitionPartInboundTicket)
		//inboundTicketRouter.POST("/partStart", c.StartPartInboundWorkflow)
		inboundTicketRouter.GET("/part/:id", c.GetPartInboundTicket)
		inboundTicketRouter.GET("/part", c.ListPartInboundTickets)

		// 新购入库工单
		//inboundTicketRouter.POST("/newStart", c.StartNewInboundWorkflow)
		inboundTicketRouter.POST("/new", c.CreateNewInboundTicket)
		inboundTicketRouter.PUT("/new/:id/transition", c.TransitionNewInboundTicket)
		inboundTicketRouter.GET("/new", c.ListNewInboundTickets)

		inboundTicketRouter.POST("/new-part", c.CreateNewPartInboundTicket)

		/*返修配件入库*/
		inboundTicketRouter.POST("/repair", c.CreateRepairPartInboundTicket)
		inboundTicketRouter.POST("/repair/upload", c.RepairInboundUpload)

		/*拆机配件入库*/
		inboundTicketRouter.POST("/dismantled", c.CreateDismantledPartInboundTicket)

		/* 整机设备入库 */
		inboundTicketRouter.POST("/device-input", c.CreateDeviceInboundInput)

		/* 公用 */

		// 转换工作流状态统一接口
		inboundTicketRouter.PUT(":inboundNo/transition", c.TransitionInboundTicket)

		// 获取工单信息统一接口
		inboundTicketRouter.GET("/history/:InboundNo", c.GetInboundTicketHistory)

		inboundTicketRouter.GET("", c.ListInboundTickets)

		// 重构后接口
		//创建入库工单统一接口
		inboundTicketRouter.POST("", c.CreateInboundTicket)
		// workflow 统一接口
		inboundTicketRouter.POST("/start-workflow", c.StartInboundWorkflow)
		inboundTicketRouter.PUT("/:inboundNo/transitionV2", c.TransitionInboundTicketV2)
		// 获取信息统一接口
		inboundTicketRouter.GET("/:InboundNo", c.GetInboundTicket)
		inboundTicketRouter.GET("/:InboundNo/history", c.GetInboundTicketHistoryV2)
		inboundTicketRouter.GET("/:InboundNo/detail", c.GetInboundDetails)
		inboundTicketRouter.GET("/:InboundNo/detail-list", c.GetInboundDetailsList)
		inboundTicketRouter.GET("/:InboundNo/info", c.GetInboundInfos)
		// 更新入库工单详情
		inboundTicketRouter.PUT(":inboundNo/update-detail", c.UpdateInboundDetail)
		inboundTicketRouter.PUT(":inboundNo/update-detail-import", c.UpdateInboundDetailByImport)
	}
}

// 入库前置信息
type InboundPre struct {
	InboundType string `json:"inbound_type"`
}

type NewInbondReq struct {
	NewInboundID uint `json:"new_inbound_id" binding:"required"`
	FileID       uint `json:"file_id" binding:"required"`
}

// @Summary 创建新购入库工单
// @Description 创建新购入库工单，并初始化相关信息和启动工作流
// @Tags 新购入库工单
// @Accept json
// @Produce json
// @Param request body NewInbondReq true "新购入库请求参数"
// @Success 200 {object} map[string]interface{} "创建成功，返回工单 ID 和工单编号"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /inbound-tickets/new [post]
func (c *InboundTicketController) CreateNewInboundTicket(ctx *gin.Context) {
	var newInboundRequest NewInbondReq
	if err := ctx.ShouldBindJSON(&newInboundRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}
	now := time.Now()
	ticket := &model.NewInboundTicket{
		//NewInboundID: newInboundRequest.NewInboundID,
		SubmitterID: userID,
		Submitter:   userName,
		Stage:       common.StageAssetApproval,         // 初始阶段：资产管理员审核阶段
		Status:      common.StatusWaitingAssetApproval, // 初始状态：等待资产管理员审核
		TryCount:    1,
	}

	history := &model.InboundTicketHistory{
		InboundNo:     ticket.InboundNo,
		OperatorID:    userID,
		OperatorName:  userName,
		OperationTime: now,
		Stage:         common.StageAssetApproval,         // 初始阶段：资产管理员审核阶段
		NewStatus:     common.StatusWaitingAssetApproval, // 初始状态：等待资产管理员审核
		TryCount:      1,
	}

	// 初始化新购入库工单及历史
	if err := c.service.InitNewNewInbound(ctx, ticket, history); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 启动工作流
	input := &common.NewInboundWorkflowInput{
		NewInboundTicketID: ticket.ID,
		InboundNo:          ticket.InboundNo, //初始化后带上的
		FileID:             newInboundRequest.FileID,
		RequireApproval:    true, // 默认需要审核
	}
	//if err := c.service.StartNewInboundWorkflow(ctx, input); err != ni l {
	//	response.Fail(ctx, http.StatusInternalServerError, err.Error())
	//}
	if err := c.service.StartInboundWorkflow(ctx, input); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "启动工作流失败: "+err.Error())
		return
	}
	response.Success(ctx, gin.H{"id": ticket.ID, "ticket_no": ticket.InboundNo}, "创建新购入库工单成功")
}

type newPartInboundDTO struct {
	NewInboundID  uint      `json:"new_inbound_id" binding:"required"`
	InboundNo     string    `json:"new_inbound_no" binding:"required"`
	SubmitterTime time.Time `json:"submitter_time"`
}

// @Summary 创建新购入库工单
// @Description 创建新购入库工单，并初始化相关信息和启动工作流
// @Tags 新购入库工单
// @Accept json
// @Produce json
// @Param request body NewInbondReq true "新购入库请求参数"
// @Success 200 {object} map[string]interface{} "创建成功，返回工单 ID 和工单编号"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /inbound-tickets/new [post]
func (c *InboundTicketController) CreateNewPartInboundTicket(ctx *gin.Context) {
	var DTO newPartInboundDTO
	if err := ctx.ShouldBindJSON(&DTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}
	ticket := &model.NewInboundTicket{
		//NewInboundID: DTO.NewInboundID,
		InboundNo:   DTO.InboundNo,
		SubmitterID: userID,
		Submitter:   userName,
		Stage:       common.StageAssetApproval,         // 初始阶段：资产管理员审核阶段
		Status:      common.StatusWaitingAssetApproval, // 初始状态：等待资产管理员审核
		TryCount:    1,
	}

	history := &model.InboundTicketHistory{
		InboundNo:     DTO.InboundNo,
		OperatorID:    userID,
		OperatorName:  userName,
		OperationTime: DTO.SubmitterTime,
		Stage:         common.StageAssetApproval,         // 初始阶段：资产管理员审核阶段
		NewStatus:     common.StatusWaitingAssetApproval, // 初始状态：等待资产管理员审核
		TryCount:      1,
	}

	// 初始化新购入库工单及历史
	//if err := c.service.InitNewNewInbound(ctx, ticket, history); err != nil {
	//	response.Fail(ctx, http.StatusInternalServerError, err.Error())
	//}

	startWorkflowInput, err := c.service.InitInboundTicketAndHistory(ctx, ticket, history)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	// 启动工作流
	if err := c.service.StartInboundWorkflow(ctx, startWorkflowInput); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": ticket.ID, "ticket_no": ticket.InboundNo}, "创建新购入库工单成功")
}

// TransitioninboundTicket 状态转换与工作流阶段触发
// @Summary 状态转换与触发工作流
// @Description 统一的工单状态转换接口，支持所有工作流阶段
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param id path int true "入库单ID"
// @Param transition body model.InboundTrigger true "转换信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/inbound-tickets/part/{id}/transition [put]
func (c *InboundTicketController) TransitionPartInboundTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var transitionRequest struct {
		Stage    string                 `json:"stage" binding:"required"`  // 工作流阶段
		Status   string                 `json:"status" binding:"required"` // 状态
		Comments string                 `json:"comments"`                  // 备注
		Data     map[string]interface{} `json:"data"`                      // 阶段相关数据
	}

	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取当前工单
	ticket, err := c.service.GetPartInboundTicketByRepairTicketID(ctx, uint(id))

	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取维修入库单失败: "+err.Error())
		return
	}

	// 验证状态转换是否合法
	if !common.IsValidStatusTransition(ticket.Status, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", ticket.Status, transitionRequest.Status))
		return
	}

	// 验证阶段转换是否合法
	isValid, message := common.ValidateStageTransition(ticket.Status, transitionRequest.Stage)
	if !isValid {
		response.Fail(ctx, http.StatusBadRequest, "当前状态下不能触发此阶段: "+message)
		return
	}

	triggers := model.InboundTrigger{
		Stage:        transitionRequest.Stage,
		Status:       transitionRequest.Status,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     transitionRequest.Comments,
		Data:         transitionRequest.Data,
	}
	// 触发工作流，确保工作流可以正常工作
	err = c.service.TriggerWorkflowStage(ctx, uint(id), ticket.InboundNo, triggers)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "状态转换成功")
}

// TransitionNewInboundTicket 状态转换与工作流阶段触发
// @Summary 状态转换与触发工作流
// @Description 统一的工单状态转换接口，支持所有工作流阶段
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param id path int true "入库单ID"
// @Param transition body model.InboundTrigger true "转换信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/inbound-tickets/new/{id}/transition [put]
func (c *InboundTicketController) TransitionNewInboundTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var transitionRequest struct {
		Stage    string                 `json:"stage" binding:"required"`  // 工作流阶段
		Status   string                 `json:"status" binding:"required"` // 状态
		Comments string                 `json:"comments"`                  // 备注
		Data     map[string]interface{} `json:"data"`                      // 阶段相关数据
	}

	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取当前工单
	ticket, err := c.service.GetNewInboundTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取新购入库单失败: "+err.Error())
		return
	}

	// 验证状态转换是否合法
	if !common.IsValidStatusTransition(ticket.Status, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", ticket.Status, transitionRequest.Status))
		return
	}

	// 验证阶段转换是否合法
	isValid, message := common.ValidateStageTransition(ticket.Status, transitionRequest.Stage)
	if !isValid {
		response.Fail(ctx, http.StatusBadRequest, "当前状态下不能触发此阶段: "+message)
		return
	}

	triggers := model.InboundTrigger{
		Stage:        transitionRequest.Stage,
		Status:       transitionRequest.Status,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     transitionRequest.Comments,
		Data:         transitionRequest.Data,
	}
	// 触发工作流，确保工作流可以正常工作
	err = c.service.TriggerWorkflowStage(ctx, uint(id), ticket.InboundNo, triggers)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "状态转换成功")
}

// TransitionNewInboundTicket 状态转换与工作流阶段触发
// @Summary 状态转换与触发工作流
// @Description 统一的工单状态转换接口，支持所有工作流阶段
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param id path int true "入库单ID"
// @Param transition body model.InboundTrigger true "转换信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/inbound-tickets/{inboundNo}/transition [put]
func (c *InboundTicketController) TransitionInboundTicket(ctx *gin.Context) {
	inboundNo := ctx.Param("inboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "inboundNo为空")
	}

	var transitionRequest struct {
		Status           string                 `json:"status" binding:"required"`            // 状态
		RequiredApproval bool                   `json:"required_approval" binding:"required"` // 需要验证?
		RequiredVerified bool                   `json:"required_verified,omitempty"`          // 需要验证？
		Comments         string                 `json:"comments"`                             // 备注
		Data             map[string]interface{} `json:"data"`                                 // 阶段相关数据
	}

	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取工单信息
	var ticket interface{}
	switch {
	case strings.HasPrefix(inboundNo, "PT"):
		ticket, err = c.service.GetPartInboundTicketByNo(ctx, inboundNo)
	case strings.HasPrefix(inboundNo, "NPIT"):
		ticket, err = c.service.GetNewInboundTicketByNo(ctx, inboundNo)
	case strings.HasPrefix(inboundNo, "RPIT"):
		ticket, err = c.service.GetRepairPartInboundTicketByNo(ctx, inboundNo)
	case strings.HasPrefix(inboundNo, "SVIT"), strings.HasPrefix(inboundNo, "SWIT"), strings.HasPrefix(inboundNo, "GSIT"): // 服务器入库、交换机入库、GPU服务器入库
		ticket, err = c.service.GetDeviceInboundTicketByNo(ctx, inboundNo)
	default:
		response.Fail(ctx, http.StatusBadRequest, "无效的inboundNo前缀")
		return
	}

	if err != nil {
		statusCode := http.StatusInternalServerError
		message := "获取部分入库单失败: " + err.Error()
		if strings.HasPrefix(inboundNo, "NPIT") {
			message = "获取新购入库单失败: " + err.Error()
		}
		response.Fail(ctx, statusCode, message)
		return
	}

	var (
		currentStatus  string
		inboundNoToUse string
		id             uint
	)
	switch t := ticket.(type) {
	case *model.PartInboundTicket:
		id = t.ID
		currentStatus = t.Status
		inboundNoToUse = t.InboundNo
	case *model.NewInboundTicket:
		id = t.ID
		currentStatus = t.Status
		inboundNoToUse = t.InboundNo
	case *model.RepairPartInboundTicket:
		id = t.ID
		currentStatus = t.Status
		inboundNoToUse = t.InboundNo
	case *model.DeviceInboundTicket:
		id = t.ID
		currentStatus = t.Status
		inboundNoToUse = t.InboundNo
	default:
		response.Fail(ctx, http.StatusInternalServerError, "未知的工单类型")
		return
	}
	// 获取当前工单

	// 验证状态转换是否合法
	if !common.IsValidStatusTransition(currentStatus, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", currentStatus, transitionRequest.Status))
		return
	}

	// 验证阶段转换是否合法
	//isValid, message := common.ValidateStageTransition(currentStatus, transitionRequest.Stage)
	//if !isValid {
	//	response.Fail(ctx, http.StatusBadRequest, "当前状态下不能触发此阶段: "+message)
	//	return
	//}

	triggers := model.InboundTrigger{
		Status:           transitionRequest.Status,
		OperatorID:       operatorID,
		OperatorName:     operatorName,
		Comments:         transitionRequest.Comments,
		RequiredApproval: transitionRequest.RequiredApproval,
		RequireVerified:  transitionRequest.RequiredVerified,
		Data:             transitionRequest.Data,
	}
	// 触发工作流，确保工作流可以正常工作
	err = c.service.TriggerWorkflowStage(ctx, id, inboundNoToUse, triggers)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "状态转换成功")
}

func (c *InboundTicketController) TransitionInboundTicketV2(ctx *gin.Context) {
	inboundNo := ctx.Param("inboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "入库单号不能为空")
	}
	var transitionRequest struct {
		InboundType string                 `json:"inbound_type" binding:"required"`
		Status      string                 `json:"status" binding:"required"`   // 状态
		Comments    string                 `json:"comments" binding:"required"` // 备注
		Data        map[string]interface{} `json:"data"`                        // 阶段相关数据
	}

	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取工单信息
	ticket, err := c.service.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "触发工作流失败: "+err.Error())
		return
	}

	// 验证状态转换是否合法
	if !common.IsValidStatusTransition(ticket.Status, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", ticket.Status, transitionRequest.Status))
		return
	}

	triggers := model.InboundTrigger{
		Status:       transitionRequest.Status,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     transitionRequest.Comments,
		Data:         transitionRequest.Data,
	}
	// 触发工作流，确保工作流可以正常工作
	err = c.service.TriggerWorkflowStageV2(ctx, ticket.ID, ticket.InboundType, triggers)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "状态转换成功")
}

// GetPartInboundTicket 获取入库工单详情
// @Summary 获取入库工单详情
// @Description 根据工单ID获取入库工单详情
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param id path int true "入库工单ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/inbound-tickets/{id} [get]
func (c *InboundTicketController) GetPartInboundTicket(ctx *gin.Context) {
	// 获取工单ID
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, http.StatusBadRequest, "工单ID不能为空")
		return
	}

	// 转换ID为uint
	ticketID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的工单ID")
		return
	}

	// 获取工单详情
	ticket, err := c.service.GetPartInboundTicketByID(ctx, uint(ticketID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, fmt.Sprintf("获取工单详情失败: %v", err))
		return
	}

	response.Success(ctx, ticket, "获取工单详情成功")
}

// ListPartInboundTickets 获取入库工单列表
// @Summary 获取入库工单列表
// @Description 获取入库工单列表，支持分页和条件查询
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认10"
// @Param status query string false "工单状态"
// @Param order_category query string false "工单类别"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/inbound-tickets/part [get]
func (c *InboundTicketController) ListPartInboundTickets(ctx *gin.Context) {
	// 获取查询参数
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	status := ctx.Query("status")

	// 参数验证
	if page < 1 {
		response.Fail(ctx, http.StatusBadRequest, "页码必须大于0")
		return
	}
	if pageSize < 1 {
		response.Fail(ctx, http.StatusBadRequest, "每页数量必须大于0")
		return
	}

	// 构造查询参数
	query := &model.ListParams{
		Page:          page,
		PageSize:      pageSize,
		InboundStatus: status,
	}

	// 获取工单列表
	tickets, total, err := c.service.ListPartInboundTickets(ctx, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, fmt.Sprintf("获取工单列表失败: %v", err))
		return
	}

	response.Success(ctx, gin.H{
		"list":  tickets,
		"total": total,
		"page": gin.H{
			"current":     page,
			"size":        pageSize,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}, "获取工单列表成功")
}

// GetInboundTicketHistory 获取入库工单历史记录
// @Summary 获取入库工单历史记录
// @Description 获取指定入库单号的所有历史记录
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param InboundNo path string true "入库单号"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/inbound-tickets/part/history/{InboundNo} [get]
func (c *InboundTicketController) GetInboundTicketHistory(ctx *gin.Context) {
	// 获取入库单号
	inboundNo := ctx.Param("InboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "入库单号不能为空")
		return
	}

	// 获取工单历史记录
	histories, err := c.service.GetHistoryByInboundNo(ctx, inboundNo)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, fmt.Sprintf("获取工单历史记录失败: %v", err))
		return
	}

	response.Success(ctx, histories, "获取工单历史记录成功")
}

func (c *InboundTicketController) ListNewInboundTickets(ctx *gin.Context) {
	response.Success(ctx, nil, "获取成功")
}

func (c *InboundTicketController) ListInboundTickets(ctx *gin.Context) {
	// 初始化验证器
	validate := validator.New()
	listReq := model.ListParams{}
	err := ctx.ShouldBindQuery(&listReq)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "绑定失败："+err.Error())
		return
	}
	// 验证参数
	err = validate.Struct(listReq)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "验证失败："+err.Error())
		return
	}
	// 获取列表
	switch listReq.InboundType {
	case "new":
		tickets, totals, err := c.service.ListNewInboundTickets(ctx, &listReq)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, err.Error())
			return
		}
		response.Success(ctx, gin.H{
			"list":  tickets,
			"total": totals,
		}, "获取成功")
		return
	case "part":
		tickets, totals, err := c.service.ListPartInboundTickets(ctx, &listReq)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, err.Error())
			return
		}
		response.Success(ctx, gin.H{
			"list":  tickets,
			"total": totals,
		}, "获取成功")
		return
	default:
		response.Fail(ctx, http.StatusBadRequest, "参数错误")
	}
}

// CreateInboundTicket 一站式初始化入库工单及入库历史
func (c *InboundTicketController) CreateInboundTicket(ctx *gin.Context) {
	var InboundReq model.InboundTicket
	err := ctx.ShouldBindJSON(&InboundReq)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "绑定数据失败"+err.Error())
		return
	}
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取用户信息失败："+err.Error())
		return
	}
	InboundReq.CreateBy = userName
	InboundReq.CreateID = userID

	inboundNo, err := c.service.CreateInboundTicketV2(ctx, InboundReq)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Success(ctx, gin.H{
		"inbound_no": inboundNo,
	}, "创建入库工单成功")
}

// StartInboundWorkflow 用于启动一个工作流
func (c *InboundTicketController) StartInboundWorkflow(ctx *gin.Context) {
	var req model.WorkflowReq
	// 获取参数
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误："+err.Error())
		return
	}

	// 获取入库单信息
	ticket, err := c.service.GetInboundTicketByNo(ctx, req.InboundNo)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取入库单信息失败："+err.Error())
		return
	}

	// 调用服务层启动工作流
	err = c.service.StartInboundWorkflowV2(ctx, ticket.ID, req)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "启动工作流失败："+err.Error())
		return
	}

	response.Success(ctx, nil, "成功启动工作流")
}

type RepairPartTicketDTO struct {
	InboundNo string `json:"inbound_no"`
}

// CreateRepairPartInboundTicket 创建维修配件入库单+历史记录+启动工作流
func (c *InboundTicketController) CreateRepairPartInboundTicket(ctx *gin.Context) {
	var DTO RepairPartTicketDTO
	err := ctx.ShouldBindJSON(&DTO)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误："+err.Error())
		return
	}
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败："+err.Error())
		return
	}
	RepairInboundTicket := &model.RepairPartInboundTicket{
		InboundNo:   DTO.InboundNo,
		Submitter:   userName,
		SubmitterID: userID,
		Stage:       common.StageAssetApproval,         //仓库管理员审核阶段
		Status:      common.StatusWaitingAssetApproval, //等待仓库管理员审核
	}
	if err := c.service.CreateInboundTicket(ctx, RepairInboundTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Success(ctx, nil, "创建入库工单成功")
}

func (c *InboundTicketController) RepairInboundUpload(ctx *gin.Context) {

}

// buildNewTicketAndHistory 构建新购入库工单和历史
// nolint:unused
func buildNewTicketAndHistory(ctx *gin.Context) (Ticket *model.NewInboundTicket, History *model.InboundTicketHistory, err error) {
	var newInboundRequest NewInbondReq

	if err := ctx.ShouldBindJSON(&newInboundRequest); err != nil {
		return nil, nil, fmt.Errorf("参数错误：%s", err.Error())
	}
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("该用户不存在或信息不全: %s", err.Error())
	}

	now := time.Now()
	ticket := &model.NewInboundTicket{
		//NewInboundID: newInboundRequest.NewInboundID,
		SubmitterID: userID,
		Submitter:   userName,
		Stage:       common.StageAssetApproval,         // 初始阶段：资产管理员审核阶段
		Status:      common.StatusWaitingAssetApproval, // 初始状态：等待资产管理员审核
		FileID:      newInboundRequest.FileID,
		TryCount:    1,
	}

	history := &model.InboundTicketHistory{
		InboundNo:     ticket.InboundNo,
		OperatorID:    userID,
		OperatorName:  userName,
		OperationTime: now,
		Stage:         common.StageAssetApproval,         // 初始阶段：资产管理员审核阶段
		NewStatus:     common.StatusWaitingAssetApproval, // 初始状态：等待资产管理员审核
		TryCount:      1,
	}
	return ticket, history, nil
}

type DismantledPartTicketDTO struct {
	InboundNo string `json:"inbound_no" binding:"required"`
}

// CreateDismantledPartInboundTicket 创建拆机配件入库工单+历史记录+启动工作流
func (c *InboundTicketController) CreateDismantledPartInboundTicket(ctx *gin.Context) {
	var DTO DismantledPartTicketDTO

	err := ctx.ShouldBindJSON(&DTO)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误："+err.Error())
		return
	}
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败："+err.Error())
		return
	}
	DismantledInboundTicket := &model.DismantledPartInboundTicket{
		InboundTicketTemplate: model.InboundTicketTemplate{
			InboundNo:   DTO.InboundNo,
			Submitter:   userName,
			SubmitterID: userID,
			Stage:       common.StageCompleteInbound,
			Status:      common.StatusCompleted,
		},
	}
	if err := c.service.CreateInboundTicket(ctx, DismantledInboundTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Success(ctx, nil, "创建拆机配件入库工单成功")
}

// CreateDeviceInbound 创建整机入库工单
func (c *InboundTicketController) CreateDeviceInboundInput(ctx *gin.Context) {
	var deviceInbound inbound.DeviceInbound
	if err := ctx.ShouldBindJSON(&deviceInbound); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误"+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息错误")
		return
	}
	// 设置提交人信息
	deviceInbound.CreateBy = userName
	deviceInbound.CreateID = userID

	// 创建入库单+入库工单+入库工单历史
	if err := c.service.CreateDeviceInboundByInput(ctx, &deviceInbound); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, nil, "创建成功")
}

// GetInboundDetails 获取入库单详情
// @Summary 获取入库单详情
// @Description 根据入库单号获取入库单详细信息，包括产品信息、设备信息等
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param InboundNo path string true "入库单号"
// @Success 200 {object} response.ResponseStruct{data=[]model.InboundDetail} "入库单详情"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 404 {object} response.ResponseStruct "入库单不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /ticket/inbound-tickets/detail/{InboundNo} [get]
func (c *InboundTicketController) GetInboundDetails(ctx *gin.Context) {
	inboundNo := ctx.Param("InboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "入库单号不能为空")
		return
	}

	// 获取入库单基本信息
	ticket, err := c.service.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		response.Fail(ctx, statusCode, fmt.Sprintf("获取入库单失败: %v", err))
		return
	}

	// 获取入库单详情
	details, err := c.service.GetInboundDetailByNo(ctx, ticket.InboundType, inboundNo)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, fmt.Sprintf("获取入库单详情失败: %v", err))
		return
	}

	response.Success(ctx, gin.H{
		"list":  details,
		"total": len(details),
	}, "获取入库单详情成功")
}

// GetInboundInfos 获取入库单信息
// @Summary 获取入库单信息
// @Description 根据入库单号获取入库单基本信息，包括产品信息、数量等汇总信息
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param InboundNo path string true "入库单号"
// @Success 200 {object} response.ResponseStruct{data=[]model.InboundInfo} "入库单信息"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 404 {object} response.ResponseStruct "入库单不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /ticket/inbound-tickets/info/{InboundNo} [get]
func (c *InboundTicketController) GetInboundInfos(ctx *gin.Context) {
	inboundNo := ctx.Param("InboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "入库单号不能为空")
		return
	}

	// 获取入库单基本信息
	ticket, err := c.service.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		response.Fail(ctx, statusCode, fmt.Sprintf("获取入库单失败: %v", err))
		return
	}

	// 获取入库单信息
	infos, err := c.service.GetInboundInfoByNo(ctx, ticket.InboundType, inboundNo)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, fmt.Sprintf("获取入库单信息失败: %v", err))
		return
	}

	response.Success(ctx, gin.H{
		"list":  infos,
		"total": len(infos),
	}, "获取入库单信息成功")
}

// GetInboundTicket 获取入库单基本信息
// @Summary 获取入库单基本信息
// @Description 根据入库单号获取入库单基本信息
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param InboundNo path string true "入库单号"
// @Success 200 {object} response.ResponseStruct{data=model.InboundTicket} "入库单基本信息"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 404 {object} response.ResponseStruct "入库单不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /ticket/inbound-tickets/ticket/{InboundNo} [get]
func (c *InboundTicketController) GetInboundTicket(ctx *gin.Context) {
	inboundNo := ctx.Param("InboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "入库单号不能为空")
		return
	}

	// 获取入库单基本信息
	ticket, err := c.service.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		response.Fail(ctx, statusCode, fmt.Sprintf("获取入库单失败: %v", err))
		return
	}

	response.Success(ctx, ticket, "获取入库单基本信息成功")
}

// GetInboundTicketHistoryV2 获取入库单历史记录（新版）
// @Summary 获取入库单历史记录
// @Description 根据入库单号获取入库单所有历史记录
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param InboundNo path string true "入库单号"
// @Success 200 {object} response.ResponseStruct{data=[]model.InboundHistory} "入库单历史记录"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 404 {object} response.ResponseStruct "入库单不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /ticket/inbound-tickets/historyV2/{InboundNo} [get]
func (c *InboundTicketController) GetInboundTicketHistoryV2(ctx *gin.Context) {
	inboundNo := ctx.Param("InboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "入库单号不能为空")
		return
	}

	// 获取入库单基本信息，确认存在性
	_, err := c.service.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		response.Fail(ctx, statusCode, fmt.Sprintf("获取入库单失败: %v", err))
		return
	}

	// 获取入库单历史记录
	histories, err := c.service.GetInboundHistoryByNo(ctx, inboundNo)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, fmt.Sprintf("获取入库单历史记录失败: %v", err))
		return
	}

	response.Success(ctx, histories, "获取入库单历史记录成功")
}

// UpdateInboundDetail 更新入库明细
// @Summary 更新入库明细
// @Description 根据入库单号更新入库明细信息
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param inboundNo path string true "入库单号"
// @Param details body []model.InboundDetail true "入库明细信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 404 {object} response.Response{msg=string} "入库单不存在"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /ticket/inbound-tickets/{inboundNo}/update [put]
func (c *InboundTicketController) UpdateInboundDetail(ctx *gin.Context) {
	inboundNo := ctx.Param("inboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "入库单号不能为空")
		return
	}

	// 获取入库单基本信息，确认存在性
	ticket, err := c.service.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		response.Fail(ctx, statusCode, fmt.Sprintf("获取入库单失败: %v", err))
		return
	}

	// 获取请求体中的入库明细
	var details []model.InboundDetail
	if err := ctx.ShouldBindJSON(&details); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 验证入库明细的有效性
	for _, detail := range details {
		// 确认明细是属于当前入库单的
		if detail.InboundNo != inboundNo {
			response.Fail(ctx, http.StatusBadRequest, fmt.Sprintf("入库明细(ID=%d)不属于当前入库单", detail.ID))
			return
		}
	}

	// 更新入库明细
	if err := c.service.UpdateInboundDetail(ctx, ticket.InboundType, ticket.InboundReason, details); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新入库明细失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新入库明细成功")
}

// UpdateInboundDetailByImport 通过导入文件更新入库明细
func (c *InboundTicketController) UpdateInboundDetailByImport(ctx *gin.Context) {
	var importForm model.DetailImportReq
	inboundNo := ctx.Param("inboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "入库单号不能为空")
		return
	}
	if err := ctx.ShouldBind(&importForm); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误:"+err.Error())
		return
	}

	// 获取上传文件
	file, err := importForm.File.Open()
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "打开文件失败:"+err.Error())
		return
	}
	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			fmt.Println(err.Error())
		}
	}(file)

	// 保存上传文件到临时文件
	tempFile := filepath.Join("tmp", importForm.File.Filename)
	if err := ctx.SaveUploadedFile(importForm.File, tempFile); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "保存文件失败:"+err.Error())
		return
	}
	defer func(name string) {
		err := os.Remove(name)
		if err != nil {
			// 忽略删除临时文件错误
			fmt.Printf("删除临时文件失败: %v\n", err)
		}
	}(tempFile) // 处理完毕后删除临时文件

	// 处理上传文件
	err = c.service.UpdateInboundDetailByImport(ctx, importForm, inboundNo)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "导入更新入库明细失败:"+err.Error())
		return
	}

	response.Success(ctx, nil, "导入更新入库明细成功")
}

// GetInboundDetailsList 分页获取入库单详情列表
// @Summary 分页获取入库单详情列表
// @Description 根据入库单号分页获取入库单详细信息列表
// @Tags 工单管理-入库单
// @Accept json
// @Produce json
// @Param InboundNo path string true "入库单号"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct{data=gin.H{list=[]model.InboundDetail,total=int64}} "入库单详情列表"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 404 {object} response.ResponseStruct "入库单不存在"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /ticket/inbound-tickets/detail-list/{InboundNo} [get]
func (c *InboundTicketController) GetInboundDetailsList(ctx *gin.Context) {
	inboundNo := ctx.Param("InboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "入库单号不能为空")
		return
	}

	// 获取分页参数
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码")
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	if err != nil || pageSize < 1 {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量")
		return
	}

	// 获取入库单基本信息
	ticket, err := c.service.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		response.Fail(ctx, statusCode, fmt.Sprintf("获取入库单失败: %v", err))
		return
	}

	// 获取入库单详情列表（分页）
	details, total, err := c.service.GetInboundDetailListByNo(ctx, ticket.InboundType, inboundNo, page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, fmt.Sprintf("获取入库单详情列表失败: %v", err))
		return
	}

	response.Success(ctx, gin.H{
		"list":  details,
		"total": total,
	}, "获取入库单详情列表成功")
}
