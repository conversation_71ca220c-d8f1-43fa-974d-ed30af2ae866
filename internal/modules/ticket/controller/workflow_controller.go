package controller

import (
	"backend/internal/modules/cmdb/service/outbound"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/service"
	"backend/internal/modules/ticket/workflow"
	"backend/response"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// WorkflowController 工作流控制器
type WorkflowController struct {
	faultTicketService      service.FaultTicketService
	repairSelectionService  service.RepairSelectionService
	customerApprovalService service.CustomerApprovalService
	verificationService     service.VerificationService
	repairTicketService     service.RepairTicketService
	outboundTicketService   outbound.OutboundTicketService
	entryService            service.EntryPersonService
	userService             service.UserService
	coldMigrationService    service.ColdMigrationService
	logger                  *zap.Logger
}

// NewWorkflowController 创建工作流控制器
func NewWorkflowController(
	faultTicketService service.FaultTicketService,
	repairSelectionService service.RepairSelectionService,
	customerApprovalService service.CustomerApprovalService,
	verificationService service.VerificationService,
	repairTicketService service.RepairTicketService,
	outboundTicketService outbound.OutboundTicketService,
	entryService service.EntryPersonService,
	userService service.UserService,
	coldMigrationService service.ColdMigrationService,
	logger *zap.Logger,
) *WorkflowController {
	return &WorkflowController{
		faultTicketService:      faultTicketService,
		repairSelectionService:  repairSelectionService,
		outboundTicketService:   outboundTicketService,
		customerApprovalService: customerApprovalService,
		verificationService:     verificationService,
		repairTicketService:     repairTicketService,
		entryService:            entryService,
		userService:             userService,
		coldMigrationService:    coldMigrationService,
		logger:                  logger,
	}
}

// RegisterRoutes 注册路由
func (c *WorkflowController) RegisterRoutes(router *gin.RouterGroup) {
	workflowRouter := router.Group("/workflow")
	{
		// 接单
		//workflowRouter.PUT("/tickets/:id/accept", c.AcceptTicket)

		// 维修选择
		//workflowRouter.POST("/tickets/:id/repair-selection", c.CreateRepairSelection)
		workflowRouter.GET("/tickets/:id/repair-selection", c.GetRepairSelection)

		// 客户审批
		//workflowRouter.POST("/tickets/:id/customer-approval", c.CreateCustomerApproval)
		workflowRouter.GET("/tickets/:id/customer-approval", c.GetCustomerApproval)

		// 验证
		//workflowRouter.POST("/tickets/:id/verification", c.CreateVerification)
		workflowRouter.GET("/tickets/:id/verification", c.GetVerification)

		// 入室人员
		//workflowRouter.POST("/tickets/:id/entry-person", c.CreateEntryPerson)
		workflowRouter.GET("/tickets/:id/entry-person", c.GetEntryPerson)

		// 故障总结 (使用已有的CloseFaultTicket接口)

		// 手动触发工作流
		workflowRouter.POST("/trigger/:id", c.TriggerManualAction)
		workflowRouter.GET("/waiting-stages/:id", c.GetWaitingStages)

		// 冷迁移
		workflowRouter.GET("/tickets/:id/cold-migration", c.GetColdMigration)
		workflowRouter.GET("/cold-migrations", c.ListColdMigrations)
	}
}

// AcceptTicket 接单
// @Summary 接单
// @Description 接受报障单，将状态改为排查中，并设置接单人为当前操作用户
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/tickets/{id}/accept [put]
func (c *WorkflowController) AcceptTicket(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 获取当前登录用户信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 检查工单是否存在
	_, err = c.faultTicketService.GetFaultTicketByID(ctx, id)
	if err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "获取工单失败")
		return
	}

	// 更新工单状态为排查中，同时设置操作人信息
	// UpdateFaultTicketStatus会同时处理operatorID和operatorName以及设置接单人信息
	if err := c.faultTicketService.UpdateFaultTicketStatus(ctx, id, common.StatusInvestigating, operatorID, operatorName); err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "接单失败")
		return
	}

	// 不再需要额外设置接单人，因为在UpdateFaultTicketStatus方法中已经处理

	response.Success(ctx, nil, "接单成功")
}

// CreateRepairSelection 创建维修选择
// @Summary 创建维修选择
// @Description 创建维修选择记录，选择维修方式(重启/硬件维修/软件维修/冷迁移)
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Param repair_selection body model.RepairSelection true "维修选择信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/tickets/{id}/repair-selection [post]
func (c *WorkflowController) CreateRepairSelection(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	var repairSelection model.RepairSelection
	if err := ctx.ShouldBindJSON(&repairSelection); err != nil {
		common.HandleError(ctx, err, http.StatusBadRequest, "参数错误")
		return
	}

	// 获取当前登录用户信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 设置关联的报障单ID和操作人信息
	repairSelection.TicketID = id
	repairSelection.OperatorID = operatorID
	repairSelection.OperatorName = operatorName

	// 创建维修选择记录
	if err := c.repairSelectionService.CreateRepairSelection(ctx, &repairSelection); err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "创建维修选择记录失败")
		return
	}

	response.Success(ctx, repairSelection, "创建维修选择记录成功")
}

// GetRepairSelection 获取维修选择
// @Summary 获取维修选择
// @Description 获取报障单关联的维修选择记录
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Success 200 {object} response.ResponseStruct{data=model.RepairSelection}
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/tickets/{id}/repair-selection [get]
func (c *WorkflowController) GetRepairSelection(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 获取维修选择记录
	repairSelection, err := c.repairSelectionService.GetRepairSelectionByTicketID(ctx, id)
	if err != nil {
		// 如果是记录不存在的错误，返回空数据而不是错误
		if err.Error() == "record not found" {
			response.Success(ctx, nil, "未找到维修选择记录")
			return
		}
		common.HandleError(ctx, err, http.StatusInternalServerError, "获取维修选择记录失败")
		return
	}

	response.Success(ctx, repairSelection, "获取维修选择记录成功")
}

// CreateCustomerApproval 创建客户审批
// @Summary 创建客户审批
// @Description 创建客户审批记录，审批维修方案
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Param customer_approval body model.CustomerApproval true "客户审批信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/tickets/{id}/customer-approval [post]
func (c *WorkflowController) CreateCustomerApproval(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	var customerApproval model.CustomerApproval
	if err := ctx.ShouldBindJSON(&customerApproval); err != nil {
		common.HandleError(ctx, err, http.StatusBadRequest, "参数错误")
		return
	}

	// 获取当前工单状态
	ticket, err := c.faultTicketService.GetFaultTicketByID(ctx, id)
	if err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "获取工单失败")
		return
	}

	// 检查工单状态是否为 waiting_approval
	if ticket.Status != common.StatusWaitingApproval {
		response.Fail(ctx, http.StatusBadRequest, "工单状态必须为 waiting_approval 才能进行客户审批")
		return
	}

	// 获取当前登录用户信息
	customerID, customerName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 设置关联的报障单ID和客户信息
	customerApproval.TicketID = id
	customerApproval.CustomerID = customerID
	customerApproval.CustomerName = customerName

	// 如果没有指定响应时间，则使用当前时间
	if customerApproval.ResponseTime.IsZero() {
		customerApproval.ResponseTime = time.Now()
	}

	// 创建客户审批记录
	if err := c.customerApprovalService.CreateCustomerApproval(ctx, &customerApproval); err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "创建客户审批记录失败")
		return
	}

	// 根据审批结果更新工单状态
	var newStatus string
	if customerApproval.Status == "approved" {
		newStatus = common.StatusApprovedWaiting
	} else {
		newStatus = common.StatusCancelled
	}

	// 更新工单状态
	if err := c.faultTicketService.UpdateFaultTicketStatus(ctx, id, newStatus, customerID, customerName); err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "更新工单状态失败")
		return
	}

	// 如果审批通过，更新故障单的客户审批时间
	if customerApproval.Status == "approved" {
		updateFields := map[string]interface{}{
			"customer_approval_time": customerApproval.ResponseTime,
		}
		if err := c.faultTicketService.UpdateFaultTicketFields(ctx, id, updateFields); err != nil {
			c.logger.Warn("更新客户审批时间失败",
				zap.Error(err),
				zap.Uint("ticketID", id))
		}
	}

	response.Success(ctx, customerApproval, "创建客户审批记录成功")
}

// GetCustomerApproval 获取客户审批
// @Summary 获取客户审批
// @Description 获取报障单关联的客户审批记录
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Success 200 {object} response.ResponseStruct{data=model.CustomerApproval}
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/tickets/{id}/customer-approval [get]
func (c *WorkflowController) GetCustomerApproval(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 获取客户审批记录
	customerApproval, err := c.customerApprovalService.GetCustomerApprovalByTicketID(ctx, id)
	if err != nil {
		// 如果是记录不存在的错误，返回空数据而不是错误
		if err.Error() == "record not found" {
			response.Success(ctx, nil, "未找到客户审批记录")
			return
		}
		common.HandleError(ctx, err, http.StatusInternalServerError, "获取客户审批记录失败")
		return
	}

	response.Success(ctx, customerApproval, "获取客户审批记录成功")
}

// CreateVerification 创建验证
// @Summary 创建验证
// @Description 创建验证记录，验证维修结果
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Param verification body model.Verification true "验证信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/tickets/{id}/verification [post]
func (c *WorkflowController) CreateVerification(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	var verification model.Verification
	if err := ctx.ShouldBindJSON(&verification); err != nil {
		common.HandleError(ctx, err, http.StatusBadRequest, "参数错误")
		return
	}

	// 获取当前登录用户信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 设置关联的报障单ID和操作人信息
	verification.TicketID = id
	verification.OperatorID = operatorID
	verification.OperatorName = operatorName
	// 设置验证时间为当前时间
	verification.VerificationTime = time.Now()

	// 获取当前工单的维修选择记录，以获取维修类型信息
	repairSelection, err := c.repairSelectionService.GetRepairSelectionByTicketID(ctx, id)
	if err != nil && err.Error() != "record not found" {
		c.logger.Warn("获取维修选择记录失败，但继续验证流程",
			zap.Error(err),
			zap.Uint("ticketID", id))
	}

	// 创建验证记录
	if err := c.verificationService.CreateVerification(ctx, &verification); err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "创建验证记录失败")
		return
	}

	// 如果验证成功或失败，都需要触发工作流
	// 准备触发工作流的信号数据
	signalData := map[string]interface{}{
		"success": verification.Success,
	}

	// 添加维修类型信息（如果有）
	if repairSelection != nil {
		c.logger.Info("获取到当前工单维修类型",
			zap.Uint("ticketID", id),
			zap.String("repairType", repairSelection.RepairType))
		signalData["repair_type"] = repairSelection.RepairType
	}

	// 创建验证记录成功后，触发工作流
	err = c.faultTicketService.TriggerWorkflowStage(
		ctx,
		id,
		common.StageCompleteVerification,
		operatorID,
		operatorName,
		verification.Comments,
		signalData,
	)

	if err != nil {
		c.logger.Error("触发验证完成工作流失败，但验证记录已创建",
			zap.Error(err),
			zap.Uint("ticketID", id))
		// 不返回错误，因为验证记录已经创建成功
	}

	response.Success(ctx, verification, "创建验证记录成功")
}

// GetVerification 获取验证
// @Summary 获取验证
// @Description 获取报障单关联的验证记录
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Success 200 {object} response.ResponseStruct{data=model.Verification}
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/tickets/{id}/verification [get]
func (c *WorkflowController) GetVerification(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 获取验证记录
	verification, err := c.verificationService.GetVerificationByTicketID(ctx, id)
	if err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "获取验证记录失败")
		return
	}

	// 如果没有找到记录，返回空对象而不是错误
	if verification == nil {
		response.Success(ctx, nil, "未找到验证记录")
		return
	}

	response.Success(ctx, verification, "获取验证记录成功")
}

// TriggerManualAction 手动触发工作流下一步
// @Summary 手动触发工作流下一步
// @Description 当工作流在等待手动触发状态时，通过此API手动触发工作流继续执行
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Param body body object true "包含stage字段，指定触发的阶段"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/trigger/{id} [post]
func (c *WorkflowController) TriggerManualAction(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 解析请求体获取阶段信息
	var requestBody struct {
		Stage    string                 `json:"stage" binding:"required"`
		Comments string                 `json:"comments"`
		Data     map[string]interface{} `json:"data"`
	}
	if err := ctx.ShouldBindJSON(&requestBody); err != nil {
		common.HandleError(ctx, err, http.StatusBadRequest, "参数错误")
		return
	}

	// 获取当前工单状态，确认是否可以手动触发
	ticket, err := c.faultTicketService.GetFaultTicketByID(ctx, id)
	if err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "获取报障单失败")
		return
	}

	// 根据当前状态和要触发的阶段进行验证
	isValid, message := common.ValidateStageTransition(ticket.Status, requestBody.Stage)
	if !isValid {
		response.Fail(ctx, http.StatusBadRequest, "当前状态下不能触发此阶段: "+message)
		return
	}

	// 如果是维修选择阶段，强制设置需要客户审批
	if requestBody.Stage == common.StageRepairSelection {
		if requestBody.Data == nil {
			requestBody.Data = make(map[string]interface{})
		}
		requestBody.Data["require_customer_approval"] = true
	}

	// 获取操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 先记录操作日志
	remark := fmt.Sprintf("用户[%s]手动触发工作流阶段[%s]", operatorName, requestBody.Stage)
	c.logger.Info(remark,
		zap.Uint("ticketID", id),
		zap.String("stage", requestBody.Stage),
		zap.String("operator", operatorName))

	// 更新报障单备注字段
	if ticket.Remarks != "" {
		ticket.Remarks = ticket.Remarks + "\n" + remark
	} else {
		ticket.Remarks = remark
	}

	if err := c.faultTicketService.UpdateFaultTicket(ctx, ticket); err != nil {
		// 仅记录日志，不影响主流程
		c.logger.Warn("记录手动触发操作失败",
			zap.Error(err),
			zap.Uint("ticketID", id))
	}

	// 确定新状态
	var newStatus string
	switch requestBody.Stage {
	case common.StageAcceptTicket:
		newStatus = common.StatusInvestigating
	case common.StageRepairSelection:
		// 根据是否需要客户审批决定状态
		requireApproval := true // 默认需要审批

		// 尝试从请求数据中获取是否需要审批的标志
		if val, ok := requestBody.Data["require_customer_approval"]; ok {
			if boolVal, isBool := val.(bool); isBool {
				requireApproval = boolVal
			}
		}

		// 如果是硬件修复类型，检查是否有具体故障类型
		if repairType, ok := requestBody.Data["repair_type"].(string); ok {
			if repairType == "hardware" || repairType == "hardware_fix" {
				// 确保数据中包含故障详细类型
				if _, hasDetailType := requestBody.Data["fault_detail_type"]; !hasDetailType {
					c.logger.Warn("硬件修复未指定具体故障类型",
						zap.Uint("ticketID", id),
						zap.String("stage", requestBody.Stage))
				}

				// 检查是否包含插槽位置信息
				if _, hasSlotPosition := requestBody.Data["slot_position"]; !hasSlotPosition {
					c.logger.Warn("硬件修复未指定插槽位置，可指定多个位置用逗号分隔，如: 1,2,3",
						zap.Uint("ticketID", id),
						zap.String("stage", requestBody.Stage))
				}
			}
		}

		if requireApproval {
			newStatus = common.StatusWaitingApproval
		} else {
			newStatus = common.StatusApprovedWaiting
		}
	case common.StageCustomerApproval:
		newStatus = common.StatusApprovedWaiting
	case common.StageStartRepair:
		// 检查是否需要返回到排查状态（特别是冷迁移情况）
		if returnToInvestigate, ok := requestBody.Data["return_to_investigate"].(bool); ok && returnToInvestigate {
			c.logger.Info("冷迁移取消，返回排查状态",
				zap.Uint("ticketID", id),
				zap.Bool("returnToInvestigate", returnToInvestigate))
			newStatus = common.StatusInvestigating
		} else if repairType, ok := requestBody.Data["repair_type"].(string); ok {
			switch repairType {
			case "restart":
				newStatus = common.StatusRestarting
			case "cold_migration":
				newStatus = common.StatusMigrating
			case "software_fix":
				newStatus = common.StatusSoftwareFixing
			default:
				newStatus = common.StatusRepairing
			}
		} else {
			newStatus = common.StatusRepairing
		}
	case common.StageCompleteRepair:
		newStatus = common.StatusWaitingVerification
	case common.StageStartVerification:
		newStatus = common.StatusWaitingVerification
	case common.StageCompleteVerification:
		if success, ok := requestBody.Data["success"].(bool); ok && success {
			newStatus = common.StatusSummarizing
		} else {
			// 对于验证失败的情况，根据维修类型决定下一步状态
			repairType := ""
			if requestBody.Data != nil {
				if rt, ok := requestBody.Data["repair_type"].(string); ok {
					repairType = rt
				}
			}

			// 没有在Data中找到维修类型，则尝试从工单记录中获取
			if repairType == "" {
				// 尝试从工单数据中获取维修类型
				repairSelection, err := c.repairSelectionService.GetRepairSelectionByTicketID(ctx, id)
				if err == nil && repairSelection != nil {
					repairType = repairSelection.RepairType
				}
			}

			// 根据维修类型决定验证失败后的状态
			if repairType == "hardware_fix" {
				newStatus = common.StatusRepairing
			} else {
				newStatus = common.StatusInvestigating
				c.logger.Info("非硬件维修验证失败，回到排查中状态",
					zap.Uint("ticketID", id),
					zap.String("repairType", repairType))
			}
		}
	case common.StageSummary:
		newStatus = common.StatusCompleted

		// 检查是否为已取消状态，如果是则保持该状态并确保不计入SLA
		ticket, err := c.faultTicketService.GetFaultTicketByID(ctx, id)
		if err == nil && ticket.Status == common.StatusCancelled {
			newStatus = common.StatusCancelled

			// 确保count_in_sla设置为false
			if requestBody.Data == nil {
				requestBody.Data = make(map[string]interface{})
			}
			requestBody.Data["count_in_sla"] = false

			c.logger.Info("检测到已取消的工单进入summary阶段，保持cancelled状态并确保不计入SLA",
				zap.Uint("ticketID", id))
		}

		// 如果有总结数据，则更新到故障单
		if requestBody.Data != nil {
			// 提取总结相关信息
			updateFields := map[string]interface{}{
				"close_time": time.Now(),
			}

			// 如果状态是cancelled，强制设置count_in_sla为false
			if newStatus == common.StatusCancelled {
				updateFields["count_in_sla"] = false

				// 处理取消原因
				if cancelReason, ok := requestBody.Data["cancel_reason"].(string); ok && cancelReason != "" {
					updateFields["fault_summary"] = fmt.Sprintf("取消原因: %s", cancelReason)
					c.logger.Info("已设置取消原因到故障总结字段",
						zap.Uint("ticketID", id),
						zap.String("cancelReason", cancelReason))
				}
			} else {
				// 处理是否计入SLA，只在非cancelled状态时生效
				if countInSLA, ok := requestBody.Data["count_in_sla"].(bool); ok {
					updateFields["count_in_sla"] = countInSLA
				}
			}

			// 处理故障总结 - 只有在非取消状态或没有取消原因时才使用summary字段
			if summary, ok := requestBody.Data["summary"].(string); ok && summary != "" {
				if newStatus != common.StatusCancelled || updateFields["fault_summary"] == nil {
					updateFields["fault_summary"] = summary
				}
			}

			// 处理维修方法
			if repairMethod, ok := requestBody.Data["repair_method"].(string); ok && repairMethod != "" {
				updateFields["repair_method"] = repairMethod
			}

			// 处理预防措施
			if preventionMeasures, ok := requestBody.Data["prevention_measures"].(string); ok && preventionMeasures != "" {
				updateFields["prevention_measures"] = preventionMeasures
			}

			// 处理故障具体类型
			if faultDetailType, ok := requestBody.Data["fault_detail_type"].(string); ok && faultDetailType != "" {
				updateFields["fault_detail_type"] = faultDetailType
			}

			// 处理业务影响
			if businessImpact, ok := requestBody.Data["business_impact"].(string); ok && businessImpact != "" {
				updateFields["business_impact"] = businessImpact
			}

			// 处理业务影响时长
			if businessImpactTime, ok := requestBody.Data["business_impact_time"].(float64); ok {
				updateFields["business_impact_time"] = int(businessImpactTime)
			}

			// 处理总停机时间
			if totalDowntime, ok := requestBody.Data["total_downtime"].(float64); ok {
				updateFields["total_downtime"] = int(totalDowntime)
			}

			// 更新字段
			if err := c.faultTicketService.UpdateFaultTicketFields(ctx, id, updateFields); err != nil {
				c.logger.Warn("更新故障单总结信息失败",
					zap.Error(err),
					zap.Uint("ticketID", id))
			}
		}
	case common.StageCompleteTicket:
		newStatus = common.StatusCompleted
	default:
		newStatus = ticket.Status // 保持状态不变
	}

	// 先触发工作流
	if err := c.faultTicketService.TriggerWorkflowStage(
		ctx,
		id,
		requestBody.Stage,
		operatorID,
		operatorName,
		requestBody.Comments,
		requestBody.Data,
	); err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "触发工作流失败")
		return
	}

	// 工作流触发成功后更新状态
	if err := c.faultTicketService.UpdateFaultTicketStatus(ctx, id, newStatus, operatorID, operatorName); err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "更新状态失败")
		return
	}

	response.Success(ctx, nil, "已成功触发工作流阶段: "+requestBody.Stage)
}

// GetWaitingStages 获取工单当前等待的工作流阶段
// @Summary 获取工单当前等待的工作流阶段
// @Description 获取工单当前等待手动触发的工作流阶段列表
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Success 200 {object} response.ResponseStruct{data=[]string}
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/waiting-stages/{id} [get]
func (c *WorkflowController) GetWaitingStages(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 获取当前工单状态
	ticket, err := c.faultTicketService.GetFaultTicketByID(ctx, id)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取报障单失败: "+err.Error())
		return
	}

	// 使用非活动版本获取等待的阶段
	stages := workflow.GetWaitingStages(id)

	// 如果工单在数据库中标记为等待手动触发，但内存中没有对应的阶段，则使用数据库中的标记
	if ticket.WaitingManualTrigger && ticket.CurrentWaitingStage != "" {
		// 检查当前阶段是否已经在stages中
		stageExists := false
		for _, stage := range stages {
			if stage == ticket.CurrentWaitingStage {
				stageExists = true
				break
			}
		}
		// 如果阶段不存在，则添加
		if !stageExists {
			stages = append(stages, ticket.CurrentWaitingStage)
		}
	}

	// 记录日志
	c.logger.Info("获取等待阶段",
		zap.Uint("ticketID", id),
		zap.Strings("stages", stages),
		zap.Bool("waitingManualTrigger", ticket.WaitingManualTrigger),
		zap.String("currentWaitingStage", ticket.CurrentWaitingStage))

	response.Success(ctx, stages, "获取等待阶段成功")
}

// GetColdMigration 获取冷迁移信息
// @Summary 获取冷迁移信息
// @Description 获取报障单关联的冷迁移记录
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param id path int true "报障单ID"
// @Success 200 {object} response.ResponseStruct{data=model.ColdMigration}
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/tickets/{id}/cold-migration [get]
func (c *WorkflowController) GetColdMigration(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}
	// 获取冷迁移记录
	coldMigration, err := c.coldMigrationService.GetColdMigrationByTicketID(ctx, id)
	if err != nil {
		// 如果是记录不存在的错误，返回空数据而不是错误
		if err.Error() == "record not found" {
			response.Success(ctx, nil, "未找到冷迁移记录")
			return
		}
		common.HandleError(ctx, err, http.StatusInternalServerError, "获取冷迁移记录失败")
		return
	}

	response.Success(ctx, coldMigration, "获取冷迁移记录成功")
}

// ListColdMigrations 获取冷迁移列表
// @Summary 获取冷迁移列表
// @Description 获取冷迁移记录列表，支持分页和筛选
// @Tags 工单管理-工作流
// @Accept json
// @Produce json
// @Param page query int false "页码, 默认1"
// @Param page_size query int false "每页数量, 默认20"
// @Param fault_device_sn query string false "故障设备SN"
// @Param backup_device_sn query string false "备机设备SN"
// @Param status query string false "状态(success/failed)"
// @Success 200 {object} response.ResponseStruct{data=response.PageResult{list=[]model.ColdMigration}}
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/workflow/cold-migrations [get]
func (c *WorkflowController) ListColdMigrations(ctx *gin.Context) {
	// 获取分页参数
	page, pageSize := common.GetPageParams(ctx)

	// 获取筛选条件
	filters := map[string]interface{}{}

	// 添加可选筛选条件
	if faultDeviceSN := ctx.Query("fault_device_sn"); faultDeviceSN != "" {
		filters["fault_device_sn"] = faultDeviceSN
	}
	if backupDeviceSN := ctx.Query("backup_device_sn"); backupDeviceSN != "" {
		filters["backup_device_sn"] = backupDeviceSN
	}
	if status := ctx.Query("status"); status != "" {
		filters["status"] = status
	}

	// 获取冷迁移列表
	coldMigrations, total, err := c.coldMigrationService.ListColdMigrations(ctx, page, pageSize, filters)
	if err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "获取冷迁移列表失败")
		return
	}

	// 构建分页结果
	result := response.PageResult{
		List:     coldMigrations,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}

	response.Success(ctx, result, "获取冷迁移列表成功")
}

// CreateEntryPerson 创建入室人员
func (c *WorkflowController) CreateEntryPerson(ctx *gin.Context) {
	_, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	var entryPerson []*model.EntryPerson
	if err := ctx.ShouldBindJSON(&entryPerson); err != nil {
		common.HandleError(ctx, err, http.StatusBadRequest, "参数错误")
		return
	}

	// 创建入室人员
	if err := c.entryService.CreateEntryPerson(ctx, entryPerson); err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "创建入室人员记录失败")
		return
	}

	response.Success(ctx, entryPerson, "创建验证记录成功")
}

// GetEntryPerson 获取入室人员
func (c *WorkflowController) GetEntryPerson(ctx *gin.Context) {
	id, err := common.ValidateID(ctx, ctx.Param("id"))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	// 获取入室人员记录
	entryPerson, err := c.entryService.GetEntryPersonByTicketID(ctx, id)
	if err != nil {
		common.HandleError(ctx, err, http.StatusInternalServerError, "获取入室人员记录失败")
		return
	}

	// 如果没有找到记录，返回空对象而不是错误
	if entryPerson == nil {
		response.Success(ctx, nil, "未找到入室人员")
		return
	}

	response.Success(ctx, entryPerson, "获取入室人员成功")
}
