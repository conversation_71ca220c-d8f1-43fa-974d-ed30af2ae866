package model

// FaultTicketStatusUpdate 报障单状态更新请求
type FaultTicketStatusUpdate struct {
	Status       string `json:"status" binding:"required"`
	OperatorID   uint   `json:"operator_id" binding:"required"`
	OperatorName string `json:"operator_name" binding:"required"`
}

// FaultTicketAssignment 报障单分配请求
type FaultTicketAssignment struct {
	EngineerID uint `json:"engineer_id" binding:"required"`
}

// FaultTicketClosure 报障单关闭请求
type FaultTicketClosure struct {
	Summary string `json:"summary" binding:"required"`
}

// RepairTicketCreateRequest 维修单创建请求
type RepairTicketCreateRequest struct {
	FaultTicketID uint   `json:"fault_ticket_id" binding:"required"`
	RepairType    string `json:"repair_type" binding:"required"`
}

// RepairTicketStatusUpdate 维修单状态更新请求
type RepairTicketStatusUpdate struct {
	Status       string `json:"status" binding:"required"`
	OperatorID   uint   `json:"operator_id" binding:"required"`
	OperatorName string `json:"operator_name" binding:"required"`
}

// RepairTicketAssignment 维修单分配请求
type RepairTicketAssignment struct {
	EngineerID uint `json:"engineer_id" binding:"required"`
}

// SpareRequest 备件申请请求
type SpareRequest struct {
	ProductID uint `json:"product_id" binding:"required"`
	Quantity  int  `json:"quantity" binding:"required"`
}

// SpareMachineRequest 备机申请请求
type SpareMachineRequest struct {
	TemplateID uint `json:"template_id" binding:"required"`
}

// HardwareReplaceRequest 硬件更换完成请求
type HardwareReplaceRequest struct {
	SpareID uint `json:"spare_id" binding:"required"`
}

// EntryPersonRequest 入室人员
type EntryPersonRequest struct {
	Name                 string `json:"name" binding:"required"`
	IdentificationNumber string `json:"identification_number" binding:"required"`
	PersonType           string `json:"person_type" binding:"required"`
	Company              string `json:"company" binding:"required"`
	Telephone            string `json:"telephone" binding:"required"`
}
