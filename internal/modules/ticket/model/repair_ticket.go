package model

import (
	"time"

	"gorm.io/gorm"
)

// RepairTicket 维修工单模型
type RepairTicket struct {
	ID                        uint           `json:"id" gorm:"primarykey"`
	CreatedAt                 time.Time      `json:"created_at"`
	UpdatedAt                 time.Time      `json:"updated_at"`
	DeletedAt                 gorm.DeletedAt `json:"-" gorm:"index"`
	TicketNo                  string         `json:"ticket_no" gorm:"type:varchar(50);uniqueIndex;comment:维修工单号"`
	FaultTicketID             uint           `json:"fault_ticket_id" gorm:"comment:关联的报障单ID"`
	FaultTicket               *FaultTicket   `json:"fault_ticket" gorm:"foreignKey:FaultTicketID"`
	Status                    string         `json:"status" gorm:"type:enum('waiting_authorization','waiting_accept','assigned','in_progress','replacing_hardware','hardware_replace_completed','hardware_replace_failed','restarting','restart_completed','restart_failed','migrating','migration_completed','migration_failed','waiting_verification','completed','failed','cancelled');default:waiting_authorization;comment:工单状态"`
	RepairType                string         `json:"repair_type" gorm:"type:enum('restart','hardware_fix','board_repair','cold_migration');comment:维修类型"`
	AssignedEngineerID        uint           `json:"assigned_engineer_id" gorm:"comment:分配的工程师ID"`
	AssignedEngineerName      string         `json:"assigned_engineer_name" gorm:"type:varchar(100);comment:分配的工程师姓名"`
	CreatedTime               time.Time      `json:"created_time" gorm:"comment:创建时间"`
	AuthorizationTime         *time.Time     `json:"authorization_time" gorm:"comment:授权时间"`
	AssignedTime              *time.Time     `json:"assigned_time" gorm:"comment:分配时间"`
	StartTime                 *time.Time     `json:"start_time" gorm:"comment:开始维修时间"`
	ArriveTime                *time.Time     `json:"arrive_time" gorm:"comment:工程师到达时间"`
	HardwareReplaceStart      *time.Time     `json:"hardware_replace_start" gorm:"comment:硬件更换开始时间"`
	HardwareReplaceEnd        *time.Time     `json:"hardware_replace_end" gorm:"comment:硬件更换结束时间"`
	SoftwareConfigStart       *time.Time     `json:"software_config_start" gorm:"comment:软件配置开始时间"`
	SoftwareConfigEnd         *time.Time     `json:"software_config_end" gorm:"comment:软件配置结束时间"`
	TestingStart              *time.Time     `json:"testing_start" gorm:"comment:测试开始时间"`
	TestingEnd                *time.Time     `json:"testing_end" gorm:"comment:测试结束时间"`
	VerificationStartTime     *time.Time     `json:"verification_start_time" gorm:"comment:验证开始时间"`
	CompleteTime              *time.Time     `json:"complete_time" gorm:"comment:完成时间"`
	HardwareOperationDuration int            `json:"hardware_operation_duration" gorm:"comment:硬件操作时长(分钟)"`
	SoftwareOperationDuration int            `json:"software_operation_duration" gorm:"comment:软件操作时长(分钟)"`
	TotalRepairDuration       int            `json:"total_repair_duration" gorm:"comment:总维修时长(分钟)"`
	WaitingDuration           int            `json:"waiting_duration" gorm:"comment:等待响应时长(分钟)"`
	SpareID                   uint           `json:"spare_id,omitempty" gorm:"comment:使用的备件ID;default:null"`
	SpareSN                   string         `json:"spare_sn,omitempty" gorm:"type:varchar(100);comment:备件序列号"`
	SpareType                 string         `json:"spare_type,omitempty" gorm:"type:varchar(50);comment:备件类型"`
	SpareModel                string         `json:"spare_model,omitempty" gorm:"type:varchar(100);comment:备件型号"`
	RepairSteps               string         `json:"repair_steps" gorm:"type:text;comment:维修步骤"`
	Solution                  string         `json:"solution" gorm:"type:text;comment:解决方案"`
	RepairResult              string         `json:"repair_result" gorm:"type:text;comment:维修结果"`
	VerificationResult        string         `json:"verification_result" gorm:"type:enum('pending','passed','failed');default:pending;comment:验证结果"`
	VerificationDescription   string         `json:"verification_description" gorm:"type:text;comment:验证描述"`
	IsFirstAttempt            bool           `json:"is_first_attempt,omitempty" gorm:"default:true;comment:是否首次尝试"`
	AttemptNumber             int            `json:"attempt_number,omitempty" gorm:"default:1;comment:尝试次数"`
}

// TableName 指定表名
func (RepairTicket) TableName() string {
	return "repair_tickets"
}

// RepairTicketStatusHistory 维修单状态历史
type RepairTicketStatusHistory struct {
	ID               uint           `json:"id" gorm:"primarykey"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`
	RepairTicketID   uint           `json:"repair_ticket_id" gorm:"comment:维修单ID"`
	PreviousStatus   string         `json:"previous_status" gorm:"type:varchar(50);comment:先前状态"`
	NewStatus        string         `json:"new_status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID       uint           `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName     string         `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	OperationTime    time.Time      `json:"operation_time" gorm:"comment:操作时间"`
	Duration         int            `json:"duration" gorm:"comment:状态持续时间(分钟)"`
	ActivityCategory string         `json:"activity_category" gorm:"type:varchar(50);comment:活动类别"`
	IsSLAPause       bool           `json:"is_sla_pause" gorm:"comment:是否SLA暂停时间"`
	PauseReason      string         `json:"pause_reason" gorm:"type:text;comment:暂停原因"`
	Remarks          string         `json:"remarks" gorm:"type:text;comment:备注"`
}

// TableName 指定表名
func (RepairTicketStatusHistory) TableName() string {
	return "repair_ticket_status_histories"
}

// RepairTicketQuery 维修单查询条件
type RepairTicketQuery struct {
	TicketNo           string    `json:"ticket_no"`            // 维修单号
	FaultTicketID      uint      `json:"fault_ticket_id"`      // 关联的故障单ID
	Status             string    `json:"status"`               // 状态
	RepairType         string    `json:"repair_type"`          // 维修类型
	AssignedEngineerID uint      `json:"assigned_engineer_id"` // 分配的工程师ID
	StartTime          time.Time `json:"start_time"`           // 开始时间
	EndTime            time.Time `json:"end_time"`             // 结束时间
	SearchKeyword      string    `json:"search_keyword"`       // 搜索关键词
	Page               int64     `json:"page"`                 // 页码
	PageSize           int64     `json:"page_size"`            // 每页数量
	OrderBy            string    `json:"order_by"`             // 排序字段
	OrderDirection     string    `json:"order_direction"`      // 排序方向：asc/desc
}

// 维修单状态常量
const (
	// 基本状态
	RepairTicketStatusWaitingAuthorization = "waiting_authorization" // 等待授权
	RepairTicketStatusWaitingAccept        = "waiting_accept"        // 等待接单
	RepairTicketStatusAssigned             = "assigned"              // 已分配
	RepairTicketStatusAcknowledged         = "acknowledged"          // 已确认接单
	RepairTicketStatusInProgress           = "in_progress"           // 进行中
	RepairTicketStatusWaitingVerification  = "waiting_verification"  // 等待验证
	RepairTicketStatusCompleted            = "completed"             // 已完成
	RepairTicketStatusFailed               = "failed"                // 失败
	RepairTicketStatusCancelled            = "cancelled"             // 已取消

	// 硬件维修状态
	RepairTicketStatusReplacingHardware        = "replacing_hardware"         // 硬件替换中
	RepairTicketStatusHardwareReplaceCompleted = "hardware_replace_completed" // 硬件替换完成
	RepairTicketStatusHardwareReplaceFailed    = "hardware_replace_failed"    // 硬件替换失败

	// 迁移状态
	RepairTicketStatusMigrating          = "migrating"           // 迁移中
	RepairTicketStatusMigrationCompleted = "migration_completed" // 迁移完成
	RepairTicketStatusMigrationFailed    = "migration_failed"    // 迁移失败

	// 重启状态
	RepairTicketStatusRestarting       = "restarting"        // 重启中
	RepairTicketStatusRestartCompleted = "restart_completed" // 重启完成
	RepairTicketStatusRestartFailed    = "restart_failed"    // 重启失败
)

// 维修类型常量
const (
	RepairTypeRestart     = "restart"      // 重启
	RepairTypeHardwareFix = "hardware_fix" // 硬件维修

	// 下面是兼容代码使用的别名，但实际写入数据库时应使用上面的值
	RepairTypeHardwareReplace = "hardware_fix" // 硬件替换 (兼容别名，实际使用hardware_fix)
	RepairTypeSoftwareFix     = "restart"      // 软件修复 (兼容别名，实际使用restart)
)

// RepairTicketTakeRequest 维修单接单请求
type RepairTicketTakeRequest struct {
	EngineerID   uint   `json:"engineer_id" binding:"required"`
	EngineerName string `json:"engineer_name"`
	Comments     string `json:"comments"`
}
