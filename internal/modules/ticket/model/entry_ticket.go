package model

import (
	"time"

	"gorm.io/gorm"
)

// EntryTicket 入室单模型
type EntryTicket struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	TicketNo  string         `json:"ticketNo" gorm:"unique;type:varchar(50);not null;comment:工单号"`
	Status    string         `json:"status" gorm:"type:enum('waiting_approval','waiting_second_approval','approved','rejected');default:waiting_approval;comment:状态"`

	EntryPersons   string     `json:"entryPersons" gorm:"type:varchar(1000);comment:入室人员"`
	ApplicantID    uint       `json:"applicantID" gorm:"comment:申请人ID"`
	ApplicantName  string     `json:"applicantName" gorm:"type:varchar(100);comment:申请人姓名"`
	CreationTime   time.Time  `json:"creationTime" gorm:"not null;comment:申请时间"`
	EntryStartTime *time.Time `json:"entryStartTime" gorm:"comment:入室开始时间"`
	EntryEndTime   *time.Time `json:"entryEndTime" gorm:"comment:入室结束时间"`
	Reason         string     `json:"reason" gorm:"type:text;comment:入室原因"`

	IsOutSource      *bool  `json:"isOutSource" gorm:"default:false;comment:是否包含外派员工"`
	IsCarryEquipment bool   `json:"isCarryEquipment" gorm:"default:false;comment:是否携带设备"`
	CarryEquipment   string `json:"carryEquipment" gorm:"type:varchar(255);comment:携带的设备"`

	ApprovalTime *time.Time `json:"approvalTime" gorm:"comment:审批时间"`
	CloseTime    *time.Time `json:"closeTime" gorm:"comment:关闭时间"`
	// 工作流相关字段
	NeedsWorkflowRetry    bool       `json:"needsWorkflowRetry" gorm:"default:false;comment:是否需要重试工作流"`
	LastWorkflowRetryTime *time.Time `json:"lastWorkflowRetryTime" gorm:"comment:最后工作流重试时间"`
	WorkflowRetryCount    int        `json:"workflow_retry_count" gorm:"default:0;comment:工作流重试次数"`
	// 手动触发相关字段
	WaitingManualTrigger bool       `json:"waitingManualTrigger" gorm:"default:false;comment:是否等待手动触发"`
	CurrentWaitingStage  string     `json:"currentWaitingStage" gorm:"type:varchar(32);comment:当前等待的阶段"`
	LastWaitingTime      *time.Time `json:"lastWaitingTime" gorm:"comment:上次等待的时间"`
}

// TableName 指定表名
func (EntryTicket) TableName() string {
	return "entry_tickets"
}

// EntryTicketStatusHistory 入室单状态历史
type EntryTicketStatusHistory struct {
	ID               uint           `json:"id" gorm:"primarykey"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`
	EntryTicketID    uint           `json:"entry_ticket_id" gorm:"comment:入室单ID"`
	PreviousStatus   string         `json:"previous_status" gorm:"type:varchar(50);comment:先前状态"`
	NewStatus        string         `json:"new_status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID       uint           `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName     string         `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	OperationTime    time.Time      `json:"operation_time" gorm:"comment:操作时间"`
	Duration         int            `json:"duration" gorm:"comment:状态持续时间(分钟)"`
	ActivityCategory string         `json:"activity_category" gorm:"type:varchar(50);comment:活动类别"`
	Remarks          string         `json:"remarks" gorm:"type:text;comment:备注"`
}

// TableName 指定表名
func (EntryTicketStatusHistory) TableName() string {
	return "entry_ticket_status_histories"
}
