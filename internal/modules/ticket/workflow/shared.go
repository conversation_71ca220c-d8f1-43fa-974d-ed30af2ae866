package workflow

import (
	"time"
)

const (
	// 任务队列
	EntryTicketTaskQueue   = "ENTRY_TICKET_TASK_QUEUE"
	FaultTicketTaskQueue   = "FAULT_TICKET_TASK_QUEUE"
	RepairTicketTaskQueue  = "REPAIR_TICKET_TASK_QUEUE"
	InboundTicketTaskQueue = "INBOUND_TICKET_TASK_QUEUE"

	// 工作流ID前缀
	EntryTicketWorkflowIDPrefix   = "entry_ticket"
	FaultTicketWorkflowIDPrefix   = "fault_ticket_"
	RepairTicketWorkflowIDPrefix  = "repair_ticket_"
	InboundTickerWorkflowIDPrefix = "inbound_ticket_"

	// 活动超时设置
	ActivityStartToCloseTimeout = 7 * 24 * time.Hour
	ActivityHeartbeatTimeout    = 12 * time.Hour

	// 重试策略
	MaxAttempts        = 5
	InitialInterval    = 5 * time.Second
	BackoffCoefficient = 2.0
	MaximumInterval    = 2 * time.Hour

	// 工单状态常量
	StatusWaitingAccept       = "waiting_accept"          // 待接单
	StatusInvestigating       = "investigating"           // 排查中
	StatusWaitingApproval     = "waiting_approval"        // 客户待审批
	StatusRejected            = "rejected"                //审核不通过
	StatusApprovedWaiting     = "approved_waiting_action" // 客户已批准待手动触发
	StatusRepairing           = "repairing"               // 维修中
	StatusRestarting          = "restarting"              // 重启中
	StatusMigrating           = "migrating"               // 迁移中
	StatusSoftwareFixing      = "software_fixing"         // 软件修复中
	StatusWaitingVerification = "waiting_verification"    // 验证中
	StatusSummarizing         = "summarizing"             // 故障总结中
	StatusCompleted           = "completed"               // 已完成
	StatusCancelled           = "cancelled"               // 已取消

	// 维修类型常量
	RepairTypeRestart       = "restart"        // 重启
	RepairTypeColdMigration = "cold_migration" // 冷迁移
	RepairTypeSoftwareFix   = "software_fix"   // 软件修复
	RepairTypeHardwareFix   = "hardware_fix"   // 硬件维修

	// 工作流阶段常量 - 用于手动触发
	StageAcceptTicket            = "accept_ticket"             // 接单
	StageRepairSelection         = "repair_selection"          // 选择维修方式
	StageCustomerApproval        = "customer_approval"         // 客户审批
	StageStartRepair             = "start_repair"              // 开始维修
	StageCompleteRepair          = "complete_repair"           // 完成维修
	StageStartVerification       = "start_verification"        // 开始验证
	StageCompleteVerification    = "complete_verification"     // 完成验证
	StageSummary                 = "summary"                   // 总结
	StageCompleteTicket          = "complete_ticket"           // 完成工单
	StageEngineerTake            = "engineer_take"             // 工程师接单
	StageArriveOnSite            = "arrive_on_site"            // 到达现场
	StageStartHardwareReplace    = "start_hardware_replace"    // 开始硬件更换
	StageCompleteHardwareReplace = "complete_hardware_replace" // 完成硬件更换
	StageVerify                  = "verify"                    // 验证

	// 入库阶段常量
	StatusInProgress = "in_progress" // 入库操作中
	StatusCounting   = "counting"    //入库清点中

	// 定义信号名称常量
	WorkflowControlSignalName = "workflow_control_signal" // 工作流控制信号名称

	// 入室
	EntryPersonEnabled  = "enabled"
	EntryPersonDisabled = "disabled"
)

// FaultTicketWorkflowInput 报障单工作流输入
type FaultTicketWorkflowInput struct {
	TicketID         uint   `json:"ticket_id"`
	ReporterID       uint   `json:"reporter_id"`
	ReporterName     string `json:"reporter_name"`
	DeviceID         *uint  `json:"device_id"` // 可能为空的设备ID，使用Resource替代
	ResourceID       *uint  `json:"resource_id"`
	FaultType        string `json:"fault_type"`
	FaultDescription string `json:"fault_description"`
	Symptom          string `json:"symptom"`
	SlotPosition     string `json:"slot_position"`
	Priority         string `json:"priority"`
	Source           string `json:"source"`
	OperatorID       uint   `json:"operator_id"`   // 操作人ID
	OperatorName     string `json:"operator_name"` // 操作人姓名
	Completed        bool   `json:"completed"`     // 是否已完成

	// 扩展字段 - 保存报障单的更多信息
	TicketNo           string `json:"ticket_no"`           // 工单号
	Status             string `json:"status"`              // 当前状态
	Title              string `json:"title"`               // 故障标题
	DeviceSN           string `json:"device_sn"`           // 设备序列号
	ComponentSN        string `json:"component_sn"`        // 组件序列号
	ComponentType      string `json:"component_type"`      // 组件类型
	ResourceIdentifier string `json:"resource_identifier"` // 资源标识符
	RepairMethod       string `json:"repair_method"`       // 维修方法
	AssignedTo         uint   `json:"assigned_to"`         // 分配给的工程师ID
	RequireApproval    bool   `json:"require_approval"`    // 是否需要客户审批
	ImmediateRepair    bool   `json:"immediate_repair"`    // 是否立即维修
}

// DiagnosisResult 诊断结果
type DiagnosisResult struct {
	RepairType              string `json:"repair_type"`
	DiagnosisResult         string `json:"diagnosis_result"`
	RequireCustomerApproval bool   `json:"require_customer_approval"`
	ApprovalType            string `json:"approval_type"`
	EstimatedRepairTime     int    `json:"estimated_repair_time"` // 分钟
}

// CustomerApprovalResult 客户审批结果
type CustomerApprovalResult struct {
	Status       string    `json:"status"`
	ResponseTime time.Time `json:"response_time"`
	Comments     string    `json:"comments"`
}

// VerificationResult 验证结果
type VerificationResult struct {
	Success          bool      `json:"success"`
	VerificationTime time.Time `json:"verification_time"`
	Comments         string    `json:"comments"`
}

// InventoryAvailabilityResult 库存可用性结果
type InventoryAvailabilityResult struct {
	Available   bool   `json:"available"`
	ProductID   uint   `json:"product_id"`
	Quantity    int    `json:"quantity"`
	ProductName string `json:"product_name"`
}

// RepairResult 维修结果
type RepairResult struct {
	RepairResult string `json:"repair_result"`
	Solution     string `json:"solution"`
	RepairSteps  string `json:"repair_steps"`
}

// RepairSelectionInput 维修方式选择输入
type RepairSelectionInput struct {
	TicketID                uint   `json:"ticket_id"`
	RepairType              string `json:"repair_type"`
	Diagnosis               string `json:"diagnosis"`
	Comments                string `json:"comments"`
	OperatorID              uint   `json:"operator_id"`
	OperatorName            string `json:"operator_name"`
	RequireCustomerApproval bool   `json:"require_customer_approval"`
	FaultDetailType         string `json:"fault_detail_type"`
	SlotPosition            string `json:"slot_position"`
}

// WorkflowControlSignal 工作流控制信号
type WorkflowControlSignal struct {
	Stage        string                 `json:"stage"`         // 要进入的阶段
	OperatorID   uint                   `json:"operator_id"`   // 操作人ID
	OperatorName string                 `json:"operator_name"` // 操作人姓名
	Comments     string                 `json:"comments"`      // 操作说明
	Data         map[string]interface{} `json:"data"`          // 可能需要的附加数据
}

// EntryTicketWorkflowInput 入室工作流输入
type EntryTicketWorkflowInput struct {
	TicketID      uint   `json:"ticket_id"`
	ApplicantID   uint   `json:"applicant_id"`
	ApplicantName string `json:"applicant_name"`

	PersonType string `json:"person_type"`
	//Priority         string `json:"priority"`
	//Source           string `json:"source"`
	OperatorID   uint   `json:"operator_id"`   // 操作人ID
	OperatorName string `json:"operator_name"` // 操作人姓名
	Completed    bool   `json:"completed"`     // 是否已完成

	// 扩展字段 - 保存报障单的更多信息
	TicketNo string `json:"ticket_no"` // 工单号
	Status   string `json:"status"`    // 当前状态
	//Title              string `json:"title"`               // 故障标题
	//RequireApproval    bool   `json:"require_approval"`    // 是否需要客户审批
}

// EntryApprovalResult 审批结果
type EntryApprovalResult struct {
	Status        string    `json:"status"`
	ResponseTime  time.Time `json:"response_time"`
	Comments      string    `json:"comments"`
	ApproverCount int       `json:"approver_count"`
}

// 工作流阶段与工单状态的映射关系
var StageToStatusMap = map[string]string{
	StageAcceptTicket:         StatusInvestigating,
	StageRepairSelection:      "", // 不固定状态，由require_customer_approval决定
	StageCustomerApproval:     StatusWaitingApproval,
	StageStartRepair:          StatusRepairing,
	StageCompleteRepair:       StatusWaitingVerification,
	StageStartVerification:    StatusWaitingVerification,
	StageCompleteVerification: StatusSummarizing,
	StageSummary:              StatusSummarizing,
	StageCompleteTicket:       StatusCompleted,
}

// IsValidStatusTransition 检查状态转换是否合法
func IsValidStatusTransition(currentStatus, newStatus string) bool {
	// 定义状态转换映射
	validTransitions := map[string][]string{
		StatusWaitingAccept:       {StatusInvestigating, StatusCancelled},
		StatusInvestigating:       {StatusWaitingApproval, StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing, StatusCompleted, StatusCancelled},
		StatusWaitingApproval:     {StatusApprovedWaiting, StatusCancelled},
		StatusApprovedWaiting:     {StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing, StatusCancelled, StatusInvestigating},
		StatusRepairing:           {StatusWaitingVerification, StatusCancelled},
		StatusRestarting:          {StatusWaitingVerification, StatusCancelled},
		StatusMigrating:           {StatusWaitingVerification, StatusCancelled},
		StatusSoftwareFixing:      {StatusWaitingVerification, StatusCancelled},
		StatusWaitingVerification: {StatusSummarizing, StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing, StatusCancelled, StatusInvestigating},
		StatusSummarizing:         {StatusCompleted, StatusCancelled},
	}

	// 任何状态都可以转换为相同的状态（无变化）
	if currentStatus == newStatus {
		return true
	}

	// 检查目标状态是否在当前状态允许的转换列表中
	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}

	return false
}

// MapStatusToStage 根据状态转换映射到工作流阶段
func MapStatusToStage(currentStatus, newStatus, currentWaitingStage string) string {
	// 如果当前等待阶段与状态转换匹配，则返回该阶段
	// 定义状态到阶段的映射
	statusToStage := map[string]map[string]string{
		StatusInvestigating: {
			StatusWaitingApproval: StageRepairSelection,
		},
		StatusWaitingApproval: {
			StatusApprovedWaiting: StageCustomerApproval,
		},
		StatusApprovedWaiting: {
			StatusRepairing:      StageStartRepair,
			StatusRestarting:     StageStartRepair,
			StatusMigrating:      StageStartRepair,
			StatusSoftwareFixing: StageStartRepair,
		},
		StatusRepairing: {
			StatusWaitingVerification: StageStartVerification,
		},
		StatusRestarting: {
			StatusWaitingVerification: StageStartVerification,
		},
		StatusMigrating: {
			StatusWaitingVerification: StageStartVerification,
		},
		StatusSoftwareFixing: {
			StatusWaitingVerification: StageStartVerification,
		},
		StatusWaitingVerification: {
			StatusSummarizing: StageSummary,
		},
		StatusSummarizing: {
			StatusCompleted: StageCompleteTicket,
		},
	}

	// 检查当前状态到新状态的转换是否有对应的工作流阶段
	if stageMap, exists := statusToStage[currentStatus]; exists {
		if stage, ok := stageMap[newStatus]; ok {
			// 再确认此阶段与当前等待阶段相同
			if stage == currentWaitingStage {
				return stage
			}
		}
	}

	return ""
}
