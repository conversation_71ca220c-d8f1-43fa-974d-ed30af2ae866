package workflow

//
//import (
//	"backend/internal/modules/ticket/common"
//	"errors"
//	"fmt"
//
//	"go.temporal.io/sdk/temporal"
//	"go.temporal.io/sdk/workflow"
//	"go.uber.org/zap"
//)
//
//type newInboundState struct {
//	InboundTicketID uint
//	InboundNo       string
//	CurrentStatus   string
//	CurrentStage    string
//	RequireApproval bool
//	FileID          uint
//	Data            interface{}
//	TryCount        uint
//}
//
//func NewInboundWorkflow(ctx workflow.Context, input common.NewInboundWorkflowInput) error {
//	// 设置工作流选项
//	ao := workflow.ActivityOptions{
//		StartToCloseTimeout: common.ActivityStartToCloseTimeout,
//		HeartbeatTimeout:    common.ActivityHeartbeatTimeout,
//		RetryPolicy: &temporal.RetryPolicy{
//			InitialInterval:        common.InitialInterval,
//			BackoffCoefficient:     common.BackoffCoefficient,
//			MaximumInterval:        common.MaximumInterval,
//			MaximumAttempts:        common.MaxAttempts,
//			NonRetryableErrorTypes: []string{"InvalidInputError"},
//		},
//	}
//	ctx = workflow.WithActivityOptions(ctx, ao)
//	// 创建工作流日志
//	logger := workflow.GetLogger(ctx)
//	logger.Info("初始化NewInboundWorkflow工作流日志成功")
//
//	// 创建工作流状态
//	inboundWorkflowState := newInboundState{
//		InboundTicketID: input.NewInboundTicketID,           // 新购入库单ID
//		InboundNo:       input.InboundNo,                    // 新购入库单号
//		CurrentStatus:   common.StatusWaitingManageApproval, //初始：待审核
//		CurrentStage:    common.StageWaitingManageApproval,  //初始：待审核
//		FileID:          input.FileID,                       // 上传文件的ID
//		RequireApproval: input.RequireApproval,
//		TryCount:        1, // 初始为第一次尝试
//	}
//
//	var isWorkflowComplete bool
//	completeChan := make(chan struct{})
//	// 注册 SetUpdateHandler ，接收更新信号
//	err := workflow.SetUpdateHandler(ctx, common.NewInboundUpdateSignal, func(ctx workflow.Context, signal common.WorkflowControlSignal) error {
//		logger.Info("收到UpdateHandler信号",
//			"NewInboundTicketID", input.NewInboundTicketID,
//			"stage", signal.Stage,
//			"status", signal.Status,
//			"operator", signal.OperatorName)
//		newInboundID := input.NewInboundTicketID
//		ctx = workflow.WithActivityOptions(ctx, ao)
//		// 处理信号
//		switch signal.Stage {
//		case common.StageManageApproval: // 审核通过
//			// 处理入库审核信号
//			err := NewinboundApprovalHandler(ctx, newInboundID, signal, &inboundWorkflowState)
//			logger.Info("manage_approval阶段：", zap.Any("当下数据：", inboundWorkflowState))
//			if err != nil {
//				logger.Error("处理入库审核失败", zap.Any("error", err))
//				return fmt.Errorf("处理入库审核失败：%v", err)
//			}
//			// 清点阶段和入库阶段理论上都可以合并
//		case common.StageCount, common.StageCompleteCount: // 清点阶段
//			err := NewInboundCountingHandler(ctx, newInboundID, signal, &inboundWorkflowState)
//			logger.Info("count阶段：", zap.Any("当下数据：", inboundWorkflowState))
//			if err != nil {
//				logger.Error("入库清点失败", zap.Any("error", err))
//				return fmt.Errorf("处理入库清点失败：%v", err)
//			}
//		case common.StageInbound, common.StageCompleteInbound: // 入库阶段
//			// 处理入库操作信号
//			err := NewInboundHandle(ctx, newInboundID, signal, &inboundWorkflowState)
//			logger.Info("inbound阶段：", zap.Any("当下数据：", inboundWorkflowState))
//			if err != nil {
//				logger.Error("处理入库操作失败", signal.Stage+"-error:", err)
//				return fmt.Errorf("处理入库失败：%v", err)
//			}
//		case common.StageAssetApproval:
//			// 处理资产审核信号
//			err := NewInboundAssetApprovalHandler(ctx, newInboundID, signal, &inboundWorkflowState)
//			logger.Info("asset_approval阶段：", zap.Any("当下数据：", inboundWorkflowState))
//			if err != nil {
//				logger.Error("处理入库资产审核失败", "error", err)
//				return err
//			}
//			// 关闭通道,结束
//			logger.Info("完成维修入库活动")
//			isWorkflowComplete = true
//			close(completeChan)
//		case common.StageCancelled:
//			err := NewInboundCancelHandler(ctx, newInboundID, signal, &inboundWorkflowState)
//			if err != nil {
//				logger.Error("处理入库取消失败", "error", err)
//				return fmt.Errorf("入库取消失败：%v", err)
//			}
//			// 关闭通道,结束
//			logger.Info("维修入库活动取消")
//			isWorkflowComplete = true
//			close(completeChan)
//		case common.StageRejected:
//			err := NewInboundRejectHandle(ctx, newInboundID, signal, &inboundWorkflowState)
//			if err != nil {
//				logger.Error("拒绝工单失败", "error", err)
//				return fmt.Errorf("拒绝工单失败：%v", err)
//			}
//			inboundWorkflowState.TryCount += 1 //重试次数+1
//			// 暂时使用这个逻辑，重新编辑功能需要更改newInboundInfo代码，加入Trycount字段
//			logger.Info("维修入库审核拒绝")
//			isWorkflowComplete = true
//			close(completeChan)
//		case common.StageReEdit: // 更新newIboundInfo的trycount困难，暂时不使用
//			err := NewInboundReEditHandle(ctx, newInboundID, signal, input)
//			if err != nil {
//				logger.Error("重新编辑工单失败", "error", err)
//				return fmt.Errorf("重新编辑工单失败：%v", err)
//			}
//		default:
//			logger.Error("未知的工作流阶段", "stage", signal.Stage)
//			return fmt.Errorf("未知的工作流阶段：%v", signal.Stage)
//		}
//
//		return nil
//	})
//
//	if err != nil {
//		logger.Error("SetUpdateHandler failed.", "error:", err, zap.Error(err))
//	}
//
//	// 使用 workflow.Await 等待条件满足
//	err = workflow.Await(ctx, func() bool {
//		select {
//		case <-completeChan:
//			return true
//		default:
//			return isWorkflowComplete
//		}
//	})
//
//	if err != nil {
//		logger.Error("Workflow await failed.", "error:", err, zap.Error(err))
//		return err
//	}
//	return nil
//}
////
////// NewinboundApprovalHandler 处理入库审核信号
////func NewinboundApprovalHandler(ctx workflow.Context, inboundID uint, signal common.WorkflowControlSignal, state *newInboundState) error {
////	// 检查是否需要审核
////	if !state.RequireApproval {
////		return errors.New("当前入库单不需要审核")
////	}
////
////	input := UpdateInput{
////		InboundID:    inboundID,
////		Stage:        signal.Stage,
////		NextStatus:   signal.Status,
////		OperatorID:   signal.OperatorID,
////		OperatorName: signal.OperatorName,
////		Comments:     signal.Comments,
////		TryCount:     state.TryCount,
////	}
////	// 执行数据库状态更新活动
////	err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", input).Get(ctx, nil)
////	if err != nil {
////		return fmt.Errorf("更新入库状态失败: %v", err)
////	}
////	// 更新工作流内部状态为：已审核、更改阶段为：入库清点阶段
////	state.CurrentStatus = signal.Status
////	state.CurrentStage = signal.Stage
////	return nil
////}
//
////// handleCounting处理入库清点
////func NewInboundCountingHandler(ctx workflow.Context, inboundID uint, signal common.WorkflowControlSignal, state *newInboundState) error {
////	input := UpdateInput{
////		InboundID:    inboundID,
////		CurrentStage: signal.Stage,
////		Stage:        signal.Stage,
////		NextStatus:   signal.Status,
////		OperatorID:   signal.OperatorID,
////		OperatorName: signal.OperatorName,
////		Comments:     signal.Comments,
////		TryCount:     state.TryCount,
////	}
////	// 执行数据库状态更新活动
////	err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", input).Get(ctx, nil)
////
////	if err != nil {
////		return fmt.Errorf("更新入库状态失败: %v", err)
////	}
////
////	// 更新工作流内部状态为：入库中、更改阶段为：入库阶段
////	state.CurrentStatus = signal.Status
////	state.CurrentStage = signal.Stage
////	return nil
////}
////
////// handleInbound 处理入库操作信号
////func NewInboundHandle(ctx workflow.Context, inboundID uint, signal common.WorkflowControlSignal, state *newInboundState) error {
////	input := UpdateInput{
////		InboundID:    inboundID,
////		Stage:        signal.Stage,
////		NextStatus:   signal.Status,
////		OperatorID:   signal.OperatorID,
////		OperatorName: signal.OperatorName,
////		Comments:     signal.Comments,
////		Data:         signal.Data,
////		TryCount:     state.TryCount,
////	}
////	// 执行数据库状态更新活动
////	err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", input).Get(ctx, nil)
////	if err != nil {
////		return fmt.Errorf("更新入库状态失败: %w", err)
////	}
////	// 更新工作流内部状态
////	state.CurrentStatus = signal.Status
////	state.CurrentStage = signal.Stage
////	return nil
////}
//
//type NewInboundCompleteInput struct {
//	Summary      string `json:"summary"`     //总结
//	Operator     string `json:"operator"`    //最终操作人
//	OperatorID   uint   `json:"operator_id"` // 操作人ID
//	NewInboundID uint   `json:"new_inbound_id"`
//	FileID       uint   `json:"file_id"`
//	Status       string `json:"status"` //Active | Archive
//}
//
////func NewInboundAssetApprovalHandler(ctx workflow.Context, inboundID uint, signal common.WorkflowControlSignal, state *newInboundState) error {
////
////	updateInput := UpdateInput{
////		InboundID:    inboundID,
////		Stage:        signal.Stage,
////		NextStatus:   signal.Status,
////		OperatorID:   signal.OperatorID,
////		OperatorName: signal.OperatorName,
////		Comments:     signal.Comments,
////		TryCount:     state.TryCount,
////	}
////	// 携带结束信息
////	completeInput := NewInboundCompleteInput{
////		Operator:   signal.OperatorName,
////		OperatorID: signal.OperatorID,
////		Summary:    signal.Comments,
////		FileID:     state.FileID,
////	}
////	err := workflow.ExecuteActivity(ctx, "CompleteNewTicketActivity", updateInput, completeInput).Get(ctx, nil)
////	if err != nil {
////		return fmt.Errorf("更新入库库存失败: %w", err)
////	}
////
////	// 更新工作流内部状态
////	state.CurrentStatus = signal.Status
////	state.CurrentStage = signal.Stage
////	return nil
////}
////
////func NewInboundCancelHandler(ctx workflow.Context, inboundID uint, signal common.WorkflowControlSignal, state *newInboundState) error {
////	input := UpdateInput{
////		InboundID:    inboundID,
////		NextStatus:   signal.Status,
////		OperatorID:   signal.OperatorID,
////		OperatorName: signal.OperatorName,
////		Comments:     signal.Comments,
////		TryCount:     state.TryCount,
////	}
////	// 执行数据库状态更新活动
////	err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", input).Get(ctx, nil)
////	if err != nil {
////		return fmt.Errorf("取消维修入库失败: %v", err)
////	}
////	state.CurrentStatus = signal.Status
////	state.CurrentStage = signal.Stage
////	return nil
////}
////
////func NewInboundRejectHandle(ctx workflow.Context, inboundID uint, signal common.WorkflowControlSignal, state *newInboundState) error {
////	switch state.CurrentStage {
////	case common.StageWaitingManageApproval: // 采购管理员审核阶段审核失败了
////		input := UpdateInput{
////			InboundID:    inboundID,
////			CurrentStage: state.CurrentStage,
////			NextStatus:   signal.Status,
////			OperatorID:   signal.OperatorID,
////			OperatorName: signal.OperatorName,
////			Comments:     signal.Comments,
////			TryCount:     state.TryCount,
////		}
////		err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", input).Get(ctx, nil)
////		if err != nil {
////			return err
////		}
////	case common.StageCompleteInbound: //资产管理员审核阶段审核失败
////		input := UpdateInput{
////			InboundID:    inboundID,
////			CurrentStage: state.CurrentStage,
////			NextStatus:   signal.Status,
////			OperatorID:   signal.OperatorID,
////			OperatorName: signal.OperatorName,
////			Comments:     signal.Comments,
////			TryCount:     state.TryCount,
////		}
////		err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", input).Get(ctx, nil)
////		if err != nil {
////			return err
////		}
////	}
////	state.CurrentStatus = signal.Status
////	state.CurrentStage = signal.Stage
////	return nil
////}
//
//// 重新编辑新购入库
//func NewInboundReEditHandle(ctx workflow.Context, inboundID uint, signal common.WorkflowControlSignal, input common.NewInboundWorkflowInput) error {
//
//	return nil
//}
