package workflow

import (
	"backend/internal/common/constants"
	"backend/internal/modules/ticket/common"
	"fmt"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

type partInboundState struct {
	InboundTicketID uint
	InboundNo       string
	CurrentStatus   string
	CurrentStage    string
	RequireVerify   bool
	inboundReason   string
}

// PartInboundWorkflow 配件入库工作流
func PartInboundWorkflow(ctx workflow.Context, input common.PartInboundWorkflowInput) error {
	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: common.ActivityStartToCloseTimeout,
		HeartbeatTimeout:    common.ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        common.InitialInterval,
			BackoffCoefficient:     common.BackoffCoefficient,
			MaximumInterval:        common.MaximumInterval,
			MaximumAttempts:        common.MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)
	// 创建工作流日志
	logger := workflow.GetLogger(ctx)
	logger.Info("初始化partInboundWorkflow工作流日志成功")
	logger.Info("工作流输入信息", "partInboundInput", input)

	partState := partInboundState{
		InboundTicketID: input.InboundTicketID,
		InboundNo:       input.InboundNo,
		RequireVerify:   input.RequireVarify,
		inboundReason:   input.InboundReason,
		CurrentStatus:   common.StatusWaitingAssetApproval,
		CurrentStage:    common.StageAssetApproval,
	}

	// 发送飞书通知
	//err := workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
	//if err != nil {
	//	logger.Error("发送飞书通知失败", "err", err)
	//}
	workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo)

	var isWorkflowComplete bool
	completeChan := make(chan struct{})
	// 注册 SetUpdateHandler ，接收更新信号
	err := workflow.SetUpdateHandler(ctx, common.PartInboundUpdateSignal, func(ctx workflow.Context, signal common.PartInboundSignal) error {
		logger.Info("收到UpdateHandler信号",
			"InboundTicketID", input.InboundTicketID,
			"status", signal.Status,
			"operator", signal.OperatorName)
		ctx = workflow.WithActivityOptions(ctx, ao)
		// 处理信号
		switch signal.Status {
		case common.StatusAssetApprovalPass:
			if !partState.RequireVerify { // 不需要验收
				if err := workflow.ExecuteActivity(ctx, "ImportToCMDB", input.InboundNo).Get(ctx, nil); err != nil {
					return fmt.Errorf("UpdateCMDB失败: %w", err)
				}
				// 更新ticket 和history
				updateInput := common.UpdateTicketInput{
					InboundTicketID: input.InboundTicketID,
					InboundNo:       input.InboundNo,
					OperatorID:      signal.OperatorID,
					OperatorName:    signal.OperatorName,
					Comments:        signal.Comments,
					CurrentStage:    common.StageAssetApproval,
					CurrentStatus:   common.StatusAssetApprovalPass,
					Stage:           common.StageCompleteInbound,
					NextStatus:      common.StatusCompleted,
				}
				err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
				if err != nil {
					return err
				}

				// 更新inboundList状态
				err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, common.StageCompleteInbound).Get(ctx, nil)
				if err != nil {
					return err
				}

				// 发送飞书通知
				err = workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
				if err != nil {
					logger.Error("发送飞书通知失败", "err", err)
				}
				//workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo)

				// 保安群通知
				switch input.InboundReason {
				case constants.SourceTypeNewPurchase, constants.SourceTypeReturnRepair: // 新购、返修
					err = workflow.ExecuteActivity(ctx, "SendInboundMsgToSecurityGuard", input.InboundNo).Get(ctx, nil)
					if err != nil {
						logger.Error("发送飞书通知失败", "err", err)
					}
					//workflow.ExecuteActivity(ctx, "SendInboundMsgToSecurityGuard", input.InboundNo)
				}

				// 终止流程
				isWorkflowComplete = true
				close(completeChan)
				break
			}
			// 更新ticket和history
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.InboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageAssetApproval,
				CurrentStatus:   common.StatusAssetApprovalPass,
				Stage:           common.StageVerify,
				NextStatus:      common.StatusWaitingVerify,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 更新inboundList状态
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, common.StageVerify).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			//err = workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
			//if err != nil {
			//	logger.Error("发送飞书通知失败", "err", err)
			//}
			workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo)

			// 更新内部状态
			partState.CurrentStatus = common.StatusWaitingVerify
			partState.CurrentStage = common.StageVerify

		case common.StatusAssetApprovalFail:
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.InboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageAssetApproval,
				CurrentStatus:   common.StatusAssetApprovalFail,
				Stage:           common.StageRejected,
				NextStatus:      common.StatusRejected,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 更新inboundList状态
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, common.StageRejected).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			//workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo)
			// 终止流程
			isWorkflowComplete = true
			close(completeChan)

		case common.StatusVerificationPassed:
			// 更新CMDB
			if err := workflow.ExecuteActivity(ctx, "ImportToCMDB", input.InboundNo).Get(ctx, nil); err != nil {
				return fmt.Errorf("更新CMDB失败: %w", err)
			}

			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.InboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageVerify,
				CurrentStatus:   common.StatusVerificationPassed,
				Stage:           common.StageCompleteInbound,
				NextStatus:      common.StatusCompleted,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 更新inboundList状态
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, common.StageCompleteInbound).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			//workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo)

			// 保安群通知
			switch input.InboundReason {
			case constants.SourceTypeNewPurchase, constants.SourceTypeReturnRepair: // 新购、返修
				err = workflow.ExecuteActivity(ctx, "SendInboundMsgToSecurityGuard", input.InboundNo).Get(ctx, nil)
				if err != nil {
					logger.Error("发送飞书通知失败", "err", err)
				}
				//workflow.ExecuteActivity(ctx, "SendInboundMsgToSecurityGuard", input.InboundNo)
			}

			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		case common.StatusVerificationFailed:
			// 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.InboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageVerify,
				CurrentStatus:   common.StatusVerificationFailed,
				Stage:           common.StageRejected,
				NextStatus:      common.StatusRejected,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 更新inboundList状态
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, common.StageRejected).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			//workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo)
			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		}
		return nil
	})
	if err != nil {
		logger.Error("SetUpdateHandler failed.", "error:", err, zap.Error(err))
	}

	// 使用 workflow.Await 等待条件满足
	err = workflow.Await(ctx, func() bool {
		select {
		case <-completeChan:
			return true
		default:
			return isWorkflowComplete
		}
	})

	if err != nil {
		logger.Error("Workflow await failed.", "error:", err, zap.Error(err))
		return err
	}
	return nil
}
