package activities

import (
	"backend/internal/common/utils/notifier"
	inboundrepo "backend/internal/modules/cmdb/repository/inbound"
	inboundSvc "backend/internal/modules/cmdb/service/inbound"
	fileSvc "backend/internal/modules/file/service"
	importSvc "backend/internal/modules/import/service"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type newInboundActivities struct {
	db                *gorm.DB
	inboundSvc        inboundSvc.InboundService
	inboundRepo       inboundrepo.Repository
	inboundTicketRepo repository.InboundTicketRepository
	file              fileSvc.FileService
	importSvc         importSvc.ImportService
	inboundNotifier   *notifier.InboundNotifier
}

func NewInboundActivities(db *gorm.DB, inboundRepo inboundrepo.Repository, inboundSvc inboundSvc.InboundService, inboundTicketRepo repository.InboundTicketRepository, file fileSvc.FileService, importSvc importSvc.ImportService, inboundNotifier *notifier.InboundNotifier) NewInboundActivity {
	return &newInboundActivities{
		db:                db,
		inboundRepo:       inboundRepo,
		inboundSvc:        inboundSvc,
		inboundTicketRepo: inboundTicketRepo,
		file:              file,
		importSvc:         importSvc,
		inboundNotifier:   inboundNotifier,
	}
}

// 更新工单状态及历史记录
func (n newInboundActivities) UpdateTicketAndHistory(ctx context.Context, input common.UpdateTicketInput) error {
	// 获取入库工单信息
	logger.Info("更新入库工单信息：", zap.Any("UpdateInput", input))
	ticket, err := n.inboundTicketRepo.GetNewInboundTicketByID(ctx, input.InboundTicketID)
	if err != nil {
		return fmt.Errorf("获取入库工单信息失败: %v", err)
	}

	// 更新工单状态
	ticket.PreviousStatus = ticket.Status
	ticket.Status = input.NextStatus
	ticket.UpdatedAt = time.Now()
	ticket.TryCount = input.TryCount
	ticket.Stage = input.Stage

	// 根据状态更新相关字段
	switch input.CurrentStage {
	case common.StageAssetApproval:
		ticket.AssetApprover = input.OperatorName
		ticket.AssetApproverID = input.OperatorID
	//case common.StatusAssetApprovalFail:
	//	ticket.AssetApprover = input.OperatorName
	//	ticket.AssetApproverID = input.OperatorID
	//	ticket.Stage = input.Stage

	case common.StageVerify:
		ticket.EngineerApproverID = input.OperatorID
		ticket.EngineerApprover = input.OperatorName

	//case common.StatusVerificationFailed:
	//	ticket.EngineerApproverID = input.OperatorID
	//	ticket.EngineerApprover = input.OperatorName
	//	ticket.Stage = common.StageCompleteInbound

	/*
		TODO 下方的集体修改成Stage相关
	*/
	case common.StatusCompleted:
		switch input.CurrentStage {
		case common.StageVerify:
			// 验收后才可以结束
			ticket.EngineerApprover = input.OperatorName
			ticket.EngineerApproverID = input.OperatorID
		case common.StageAssetApproval:
			// 不验收直接结束
			ticket.AssetApprover = input.OperatorName
			ticket.AssetApproverID = input.OperatorID
		}

	/*
		废弃的状态信息
	*/
	case common.StatusManageApproval:
		ticket.ApproverID_before = input.OperatorID
		ticket.Approver_before = input.OperatorName
	case common.StatusCounting:
		ticket.CounterID = input.OperatorID
		ticket.Counter = input.OperatorName
	case common.StatusInbounding:
		ticket.OperatorID = input.OperatorID
		ticket.Operator = input.OperatorName
	case common.StatusWaitingAssetApproval:
		ticket.ApproverID_after = input.OperatorID
		ticket.Approver_after = input.OperatorName
		now := time.Now()
		ticket.CompletedAt = &now
	case common.StatusCancelled:
		ticket.Stage = common.StageCancelled
		ticket.Status = common.StatusCancelled
		now := time.Now()
		ticket.CompletedAt = &now
	case common.StatusRejected:
		ticket.Stage = common.StageRejected
		ticket.Status = common.StatusRejected
		switch input.CurrentStage {
		case common.StageWaitingManageApproval:
			ticket.ApproverID_before = input.OperatorID
			ticket.Approver_before = input.OperatorName
		case common.StageCompleteInbound:
			ticket.ApproverID_after = input.OperatorID
			ticket.Approver_after = input.OperatorName
		}

	case common.StageWaitingManageApproval:
	default:
		logger.Info("中间状态", zap.Any("Status", input.NextStatus))
	}
	// 创建历史记录
	history := &model.InboundTicketHistory{
		InboundNo:      ticket.InboundNo,
		PreviousStatus: input.CurrentStatus,
		NewStatus:      input.NextStatus,
		OperatorID:     input.OperatorID,
		OperatorName:   input.OperatorName,
		Comment:        input.Comments,
		Stage:          input.Stage,
		OperationTime:  time.Now(),
	}
	err = n.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新工单
		err = n.inboundTicketRepo.UpdateNewTicket(ctx, input.InboundTicketID, ticket)
		if err != nil {
			return fmt.Errorf("更新入库工单状态失败，事务回滚: %v", err)
		}

		// 创建历史记录
		err = n.inboundTicketRepo.CreateTicketHistory(ctx, history)
		if err != nil {
			return fmt.Errorf("创建入库工单历史记录失败，事务回滚: %v", err)
		}
		return nil
	})
	logger.Info("更新工单及历史记录成功", zap.Any("TicketHistory", history), zap.Any("Ticket", ticket))
	if err != nil {
		return err
	}
	return nil
}

func (n newInboundActivities) UpdateInboundActivity(ctx context.Context, input common.UpdateInput) error {
	//TODO implement me
	panic("implement me")
}

func (n newInboundActivities) ValidateInboundApprovalActivity(ctx context.Context, inboundID uint, operatorID uint) error {
	//TODO implement me
	panic("implement me")
}

func (n newInboundActivities) ValidateInboundCountActivity(ctx context.Context, inboundID uint, operatorID uint) error {
	//TODO implement me
	panic("implement me")
}

func (n newInboundActivities) ProcessInboundActivity(ctx context.Context, inboundID uint, operatorID uint, location string, quantity int, productDetails string) error {
	//TODO implement me
	panic("implement me")
}

func (n newInboundActivities) CompleteNewTicketActivity(ctx context.Context, UpdateInput common.UpdateInput, CompleteInput common.NewInboundCompleteInput) error {
	ticket, err := n.inboundTicketRepo.GetNewInboundTicketByID(ctx, UpdateInput.InboundID)
	if err != nil {
		return err
	}
	ticket.PreviousStatus = ticket.Status
	ticket.Stage = UpdateInput.Stage
	ticket.Status = UpdateInput.NextStatus
	now := time.Now()
	ticket.CompletedAt = &now
	ticket.ApproverID_after = UpdateInput.OperatorID
	ticket.Approver_after = UpdateInput.OperatorName

	// 创建历史记录
	history := &model.InboundTicketHistory{
		InboundNo:      ticket.InboundNo,
		PreviousStatus: ticket.PreviousStatus,
		NewStatus:      UpdateInput.NextStatus,
		OperatorID:     UpdateInput.OperatorID,
		OperatorName:   UpdateInput.OperatorName,
		Stage:          UpdateInput.Stage,
		OperationTime:  time.Now(),
		TryCount:       UpdateInput.TryCount,
	}

	// 进行文件导入
	fileInfo, err := n.file.GetFileByID(CompleteInput.FileID)
	if err != nil {
		return err
	}
	file, err := os.Open(fileInfo.StoragePath)
	if err != nil {
		return err
	}

	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			fmt.Println("关闭文件失败")
		}

	}(file)
	// 导入文件
	_, err = n.importSvc.ImportData(ctx, "spare", file)
	if err != nil {
		return err
	}

	// 启动事务操作
	err = n.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err = tx.WithContext(ctx).Create(&history).Error
		if err != nil {
			return err
		}
		err = tx.WithContext(ctx).Model(&model.NewInboundTicket{}).Where("id = ?", ticket.ID).Updates(ticket).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// ImportToCMDB 将将NewInboundDetails 的数据导入CMDB
func (n newInboundActivities) ImportToCMDB(ctx context.Context, InboundNo string) error {
	newInbound, _, err := n.inboundRepo.GetNewInboundByNo(ctx, InboundNo)
	if err != nil {
		return err
	}
	// 导入CMDB
	err = n.inboundSvc.ImportToCMDB(ctx, newInbound)
	if err != nil {
		return err
	}
	return nil
}

func (n newInboundActivities) UpdateInboundListStage(ctx context.Context, inboundNo, Stage string) error {
	return n.inboundRepo.UpdateInboundListStage(ctx, inboundNo, Stage)
}

// SendInboundMsg 发送入库信息通知
func (n newInboundActivities) SendInboundMsg(ctx context.Context, inboundNo string) error {
	//inbound, ticket, err := n.inboundRepo.GetNewInboundByNo(ctx, inboundNo)
	//if err != nil {
	//	return fmt.Errorf("获取飞书通知前置信息失败: %v", err)
	//}
	//err = n.inboundNotifier.SendInboundNotification(inbound, ticket)
	//if err != nil {
	//	return fmt.Errorf("发送飞书通知失败: %v", err)
	//}
	return nil
}

// SendInboundMsgToSecurityGuard 发送通知到保安群
func (r newInboundActivities) SendInboundMsgToSecurityGuard(ctx context.Context, inboundNo string) error {
	Inbound, ticket, err := r.inboundRepo.GetNewInboundByNo(ctx, inboundNo)
	if err != nil {
		return fmt.Errorf("获取飞书通知前置信息失败: %v", err)
	}
	moduleType := fmt.Sprintf("%s-photo", Inbound.InboundType())
	module, err := r.file.GetFilesByModule(moduleType, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取验收单失败: %v", err)
	}
	err = r.inboundNotifier.SendInboundMsgToSecurityGuard(Inbound, ticket, module[0].URL)
	if err != nil {
		return fmt.Errorf("发送信息到保安群失败: %v", err)
	}
	return nil
}
