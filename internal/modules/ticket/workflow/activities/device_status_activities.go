package activities

import (
	"backend/internal/common/constants"
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	cmdbAsset "backend/internal/modules/cmdb/model/asset"
	cmdbAssetRepo "backend/internal/modules/cmdb/repository/asset"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"backend/pkg/utils"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
)

// 设备状态相关全局变量
var (
	// 设备仓库
	deviceStatusDeviceRepo   cmdbAssetRepo.DeviceRepository
	deviceStatusResourceRepo cmdbAssetRepo.ResourceRepository
	deviceStatusChangeRepo   cmdbAssetRepo.StatusChangeRepository
	// 故障单仓库
	deviceStatusFaultTicketRepo repository.FaultTicketRepository
	// 冷迁移记录仓库
	coldMigrationRepo repository.ColdMigrationRepository
)

// UpdateDeviceHardwareStatusActivity 更新设备硬件状态活动
func UpdateDeviceHardwareStatusActivity(ctx context.Context, deviceSN string, status string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行更新设备硬件状态活动",
		zap.String("deviceSN", deviceSN),
		zap.String("status", status))

	if deviceSN == "" {
		return errors.New("设备SN不能为空")
	}

	// 检查设备仓库依赖是否初始化
	if deviceStatusDeviceRepo == nil || deviceStatusChangeRepo == nil {
		logger.Error("设备仓库或状态变更仓库未初始化")
		return errors.New("设备仓库或状态变更仓库未初始化")
	}

	// 根据SN获取设备
	device, err := deviceStatusDeviceRepo.GetBySN(ctx, deviceSN)
	if err != nil {
		logger.Error("获取设备信息失败",
			zap.Error(err),
			zap.String("deviceSN", deviceSN))
		return fmt.Errorf("获取设备信息失败: %w", err)
	}

	if device == nil {
		logger.Error("未找到设备", zap.String("deviceSN", deviceSN))
		return errors.New("未找到设备")
	}

	// 记录原始状态
	oldStatus := device.HardwareStatus

	// 如果状态没有变化，无需更新
	if oldStatus == status {
		logger.Info("设备硬件状态未变化，无需更新",
			zap.String("deviceSN", deviceSN),
			zap.String("status", status))
		return nil
	}

	// 更新设备硬件状态
	switch status {
	case "faulty":
		device.HardwareStatus = cmdbAsset.HardwareStatusFaulty
	case "normal":
		device.HardwareStatus = cmdbAsset.HardwareStatusNormal
	case "warning":
		device.HardwareStatus = cmdbAsset.HardwareStatusWaning
	default:
		logger.Warn("未知的设备硬件状态", zap.String("status", status))
		return fmt.Errorf("未知的设备硬件状态: %s", status)
	}

	// 获取当前线程信息
	info := activity.GetInfo(ctx)
	var operatorName string
	var operatorID uint

	// 尝试从活动中获取操作人信息
	if info.TaskQueue == "fault-ticket-task-queue" {
		operatorName = "故障管理系统"
	} else {
		operatorName = "维修工单系统"
	}

	// 记录状态变更日志
	err = deviceStatusChangeRepo.ChangeHardwareStatus(ctx, device.ID, status,
		fmt.Sprintf("故障工单流程触发的硬件状态变更: %s -> %s", oldStatus, status),
		operatorID, operatorName, "故障工单系统")

	if err != nil {
		logger.Error("记录硬件状态变更日志失败",
			zap.Error(err),
			zap.String("deviceSN", deviceSN))
		// 继续执行，不中断流程
	}

	// 记录最后状态变更时间
	device.LastHardwareStatusChange = utils.Date(time.Now())

	// 保存设备
	err = deviceStatusDeviceRepo.Update(ctx, device)
	if err != nil {
		logger.Error("更新设备硬件状态失败",
			zap.Error(err),
			zap.String("deviceSN", deviceSN),
			zap.String("status", status))
		return fmt.Errorf("更新设备硬件状态失败: %w", err)
	}

	logger.Info("设备硬件状态已更新",
		zap.String("deviceSN", deviceSN),
		zap.String("oldStatus", oldStatus),
		zap.String("newStatus", device.HardwareStatus))

	return nil
}

// GetDeviceOriginalStatusActivity 获取设备的原始状态活动
func GetDeviceOriginalStatusActivity(ctx context.Context, workflowID string) (*cmdbAsset.StatusChangeLog, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行获取设备原始状态活动", zap.String("workflowID", workflowID))

	if workflowID == "" {
		return nil, errors.New("工作流ID不能为空")
	}

	// 检查状态变更仓库依赖是否初始化
	if deviceStatusChangeRepo == nil {
		logger.Error("状态变更仓库未初始化")
		return nil, errors.New("状态变更仓库未初始化")
	}

	// 获取该工单中设备的原始状态记录
	statusLog, err := deviceStatusChangeRepo.GetDeviceOriginalStatusByWorkflowID(ctx, workflowID)
	if err != nil {
		logger.Error("获取设备原始状态失败",
			zap.Error(err),
			zap.String("workflowID", workflowID))
		return nil, fmt.Errorf("获取设备原始状态失败: %w", err)
	}

	if statusLog == nil {
		logger.Error("未找到设备原始状态记录", zap.String("workflowID", workflowID))
		return nil, errors.New("未找到设备原始状态记录")
	}

	logger.Info("成功获取设备原始状态",
		zap.String("workflowID", workflowID),
		zap.Uint("assetID", statusLog.AssetID),
		zap.String("oldAssetStatus", statusLog.OldAssetStatus),
		zap.String("oldBizStatus", statusLog.OldBizStatus),
		zap.String("oldHardwareStatus", statusLog.OldHardwareStatus))

	return statusLog, nil
}

// GetDeviceOriginalStatusBySNActivity 通过设备SN获取设备的原始状态
func GetDeviceOriginalStatusBySNActivity(ctx context.Context, deviceSN string, workflowID string) (*cmdbAsset.StatusChangeLog, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行通过SN获取设备原始状态活动",
		zap.String("deviceSN", deviceSN),
		zap.String("workflowID", workflowID))

	if deviceSN == "" {
		return nil, errors.New("设备SN不能为空")
	}

	if workflowID == "" {
		return nil, errors.New("工作流ID不能为空")
	}

	// 检查状态变更仓库依赖是否初始化
	if deviceStatusChangeRepo == nil || deviceStatusDeviceRepo == nil {
		logger.Error("仓库未初始化")
		return nil, errors.New("仓库未初始化")
	}

	// 获取该设备在工单中的原始状态记录
	statusLog, err := deviceStatusChangeRepo.GetDeviceOriginalStatusBySN(ctx, deviceSN, workflowID)
	if err != nil {
		logger.Error("获取设备原始状态失败",
			zap.Error(err),
			zap.String("deviceSN", deviceSN),
			zap.String("workflowID", workflowID))
		return nil, fmt.Errorf("获取设备原始状态失败: %w", err)
	}

	if statusLog == nil {
		logger.Error("未找到设备原始状态记录",
			zap.String("deviceSN", deviceSN),
			zap.String("workflowID", workflowID))
		return nil, errors.New("未找到设备原始状态记录")
	}

	logger.Info("成功获取设备原始状态",
		zap.String("deviceSN", deviceSN),
		zap.String("workflowID", workflowID),
		zap.Uint("assetID", statusLog.AssetID),
		zap.String("oldAssetStatus", statusLog.OldAssetStatus),
		zap.String("oldBizStatus", statusLog.OldBizStatus),
		zap.String("oldHardwareStatus", statusLog.OldHardwareStatus))

	return statusLog, nil
}

// UpdateDeviceAssetStatusActivity 更新设备资产状态活动
func UpdateDeviceAssetStatusActivity(ctx context.Context, deviceSN string, status string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行更新设备资产状态活动",
		zap.String("deviceSN", deviceSN),
		zap.String("status", status))

	if deviceSN == "" {
		return errors.New("设备SN不能为空")
	}

	// 检查设备仓库依赖是否初始化
	if deviceStatusDeviceRepo == nil || deviceStatusChangeRepo == nil {
		logger.Error("设备仓库或状态变更仓库未初始化")
		return errors.New("设备仓库或状态变更仓库未初始化")
	}

	// 根据SN获取设备
	device, err := deviceStatusDeviceRepo.GetBySN(ctx, deviceSN)
	if err != nil {
		logger.Error("获取设备信息失败",
			zap.Error(err),
			zap.String("deviceSN", deviceSN))
		return fmt.Errorf("获取设备信息失败: %w", err)
	}

	if device == nil {
		logger.Error("未找到设备", zap.String("deviceSN", deviceSN))
		return errors.New("未找到设备")
	}

	// 记录原始状态
	oldStatus := device.AssetStatus

	// 如果状态没有变化，无需更新
	if oldStatus == status {
		logger.Info("设备资产状态未变化，无需更新",
			zap.String("deviceSN", deviceSN),
			zap.String("status", status))
		return nil
	}

	// 更新设备资产状态
	switch status {
	case "maintaining":
		device.AssetStatus = constants.AssetStatusRepairing
	case "in_use":
		device.AssetStatus = constants.AssetStatusInUse
	case "idle":
		device.AssetStatus = constants.AssetStatusIdle
	default:
		logger.Warn("未知的设备资产状态", zap.String("status", status))
		return fmt.Errorf("未知的设备资产状态: %s", status)
	}

	// 获取当前线程信息
	info := activity.GetInfo(ctx)
	var operatorName string
	var operatorID uint

	// 尝试从活动中获取操作人信息
	if info.TaskQueue == "fault-ticket-task-queue" {
		operatorName = "故障管理系统"
	} else {
		operatorName = "维修工单系统"
	}

	// 记录状态变更日志
	err = deviceStatusChangeRepo.ChangeAssetStatus(ctx, device.ID, status,
		fmt.Sprintf("故障工单流程触发的资产状态变更: %s -> %s", oldStatus, status),
		operatorID, operatorName)

	if err != nil {
		logger.Error("记录资产状态变更日志失败",
			zap.Error(err),
			zap.String("deviceSN", deviceSN))
		// 继续执行，不中断流程
	}

	// 记录最后状态变更时间
	device.LastStatusChange = utils.Date(time.Now())

	// 保存设备
	err = deviceStatusDeviceRepo.Update(ctx, device)
	if err != nil {
		logger.Error("更新设备资产状态失败",
			zap.Error(err),
			zap.String("deviceSN", deviceSN),
			zap.String("status", status))
		return fmt.Errorf("更新设备资产状态失败: %w", err)
	}

	logger.Info("设备资产状态已更新",
		zap.String("deviceSN", deviceSN),
		zap.String("oldStatus", oldStatus),
		zap.String("newStatus", device.AssetStatus))

	return nil
}

// UpdateResourceBizStatusActivity 更新资源业务状态活动
func UpdateResourceBizStatusActivity(ctx context.Context, deviceSN string, status string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行更新资源业务状态活动",
		zap.String("deviceSN", deviceSN),
		zap.String("status", status))

	if deviceSN == "" {
		return errors.New("设备SN不能为空")
	}

	// 检查资源仓库依赖是否初始化
	if deviceStatusResourceRepo == nil || deviceStatusChangeRepo == nil {
		logger.Error("资源仓库或状态变更仓库未初始化")
		return errors.New("资源仓库或状态变更仓库未初始化")
	}

	// 根据SN获取资源
	resource, err := deviceStatusResourceRepo.GetBySN(ctx, deviceSN)
	if err != nil {
		// 查询不到资源不是严重错误，可能设备还没有被分配资源
		logger.Warn("获取资源信息失败，可能设备还未分配资源",
			zap.Error(err),
			zap.String("deviceSN", deviceSN))
		return nil
	}

	if resource == nil {
		logger.Warn("未找到资源记录，可能设备还未分配资源", zap.String("deviceSN", deviceSN))
		return nil
	}

	// 记录原始状态
	oldStatus := resource.BizStatus

	// 如果状态没有变化，无需更新
	if oldStatus == status {
		logger.Info("资源业务状态未变化，无需更新",
			zap.String("deviceSN", deviceSN),
			zap.String("status", status))
		return nil
	}

	// 更新资源业务状态
	switch status {
	case "active":
		resource.BizStatus = cmdbAsset.BizStatusActive
	case "maintaining":
		resource.BizStatus = cmdbAsset.BizStatusMaintaining
	case "outage":
		resource.BizStatus = cmdbAsset.BizStatusOutage
	default:
		logger.Warn("未知的资源业务状态", zap.String("status", status))
		return fmt.Errorf("未知的资源业务状态: %s", status)
	}

	// 获取当前线程信息
	info := activity.GetInfo(ctx)
	var operatorName string
	var operatorID uint

	// 尝试从活动中获取操作人信息
	if info.TaskQueue == "fault-ticket-task-queue" {
		operatorName = "故障管理系统"
	} else {
		operatorName = "维修工单系统"
	}

	// 记录状态变更日志
	err = deviceStatusChangeRepo.ChangeBizStatus(ctx, resource.ID, status,
		fmt.Sprintf("故障工单流程触发的业务状态变更: %s -> %s", oldStatus, status),
		operatorID, operatorName)

	if err != nil {
		logger.Error("记录业务状态变更日志失败",
			zap.Error(err),
			zap.String("deviceSN", deviceSN))
		// 继续执行，不中断流程
	}

	// 记录最后业务状态变更时间
	resource.LastBizStatusChange = utils.Date(time.Now())

	// 保存资源
	err = deviceStatusResourceRepo.Update(ctx, resource)
	if err != nil {
		logger.Error("更新资源业务状态失败",
			zap.Error(err),
			zap.String("deviceSN", deviceSN),
			zap.String("status", status))
		return fmt.Errorf("更新资源业务状态失败: %w", err)
	}

	logger.Info("资源业务状态已更新",
		zap.String("deviceSN", deviceSN),
		zap.String("oldStatus", oldStatus),
		zap.String("newStatus", resource.BizStatus))

	return nil
}

// UpdateDeviceStatusActivity 更新设备状态（综合处理）
func UpdateDeviceStatusActivity(ctx context.Context, deviceSN string, source string, stage string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行综合设备状态更新活动",
		zap.String("deviceSN", deviceSN),
		zap.String("source", source),
		zap.String("stage", stage))

	if deviceSN == "" {
		return errors.New("设备SN不能为空")
	}

	// 获取当前线程信息
	info := activity.GetInfo(ctx)

	// 获取设备和资源的当前状态，以便记录完整的状态变更日志
	var device *cmdbAsset.Device
	var resource *cmdbAsset.Resource
	var oldHardwareStatus, oldAssetStatus, oldBizStatus string
	var newHardwareStatus, newAssetStatus, newBizStatus string
	var logReason string

	// 检查仓库是否初始化
	if deviceStatusDeviceRepo == nil || deviceStatusResourceRepo == nil || deviceStatusChangeRepo == nil {
		logger.Error("仓库未初始化")
		return errors.New("仓库未初始化")
	}

	// 获取当前设备状态
	if d, err := deviceStatusDeviceRepo.GetBySN(ctx, deviceSN); err == nil && d != nil {
		device = d
		oldHardwareStatus = d.HardwareStatus
		oldAssetStatus = d.AssetStatus
	} else {
		logger.Warn("获取设备信息失败，跳过状态更新",
			zap.Error(err),
			zap.String("deviceSN", deviceSN))
		return nil
	}

	// 获取当前资源状态
	if r, err := deviceStatusResourceRepo.GetBySN(ctx, deviceSN); err == nil && r != nil {
		resource = r
		oldBizStatus = r.BizStatus
	}

	// 检查stage值，根据不同的阶段执行不同的状态更新
	switch stage {
	case "accept_to_investigating":
		// 1. 首先更新设备硬件状态为故障
		newHardwareStatus = cmdbAsset.HardwareStatusFaulty
		logReason = "故障单接单至排查阶段，更新硬件状态为故障"

		// 2. 根据来源决定是否立即更新资源业务状态
		if source == "customer" {
			// 如果来源是客户，立即将业务状态设为故障
			newBizStatus = cmdbAsset.BizStatusOutage
			logReason += "，来源为客户，同时更新业务状态为故障"
		}

	case "customer_approved":
		// 客户授权后，如果来源不是客户，此时将业务状态更新为故障
		if source != "customer" {
			newBizStatus = cmdbAsset.BizStatusOutage
			logReason = "客户授权后，更新业务状态为故障"
		}

	case "start_repairing":
		// 开始维修时，将资产状态更新为维修中
		newAssetStatus = cmdbAsset.AssetStatusMaintaining
		logReason = "开始维修，更新资产状态为维修中"

	case "complete_repair":
		// 使用工作流ID
		workflowID := info.WorkflowExecution.ID

		// 尝试获取设备维修前的原始状态
		originalStatus, err := GetDeviceOriginalStatusBySNActivity(ctx, deviceSN, workflowID)
		if err != nil {
			logger.Warn("获取设备原始状态失败，使用默认值恢复状态",
				zap.Error(err),
				zap.String("deviceSN", deviceSN),
				zap.String("workflowID", workflowID))
			// 如果获取原始状态失败，则使用默认值
			newHardwareStatus = cmdbAsset.HardwareStatusNormal
			newAssetStatus = cmdbAsset.AssetStatusInUse
			newBizStatus = cmdbAsset.BizStatusActive
			logReason = "维修完成，无法获取原始状态，重置设备硬件状态为正常，资产状态为使用中，业务状态为ACTIVE"
		} else {
			// 使用原始状态，如果原始状态为空，则使用默认值
			if originalStatus.OldHardwareStatus == "" {
				newHardwareStatus = cmdbAsset.HardwareStatusNormal
			} else {
				newHardwareStatus = originalStatus.OldHardwareStatus
			}

			if originalStatus.OldAssetStatus == "" {
				newAssetStatus = cmdbAsset.AssetStatusInUse
			} else {
				newAssetStatus = originalStatus.OldAssetStatus
			}

			if originalStatus.OldBizStatus == "" {
				newBizStatus = cmdbAsset.BizStatusActive
			} else {
				newBizStatus = originalStatus.OldBizStatus
			}

			logReason = fmt.Sprintf("维修完成，恢复设备到原始状态：硬件状态=%s，资产状态=%s，业务状态=%s",
				newHardwareStatus, newAssetStatus, newBizStatus)
		}

		// 检查设备是否是备机，并判断处理场景
		if resource != nil && resource.IsBackup {
			// 检查是否是冷迁移后的故障备机
			var isColdMigrationTarget = false

			if coldMigrationRepo != nil {
				// 获取工单ID
				var ticketID uint
				if strings.HasPrefix(workflowID, "fault_ticket_") {
					ticketIDStr := strings.TrimPrefix(workflowID, "fault_ticket_")
					if id, err := strconv.ParseUint(ticketIDStr, 10, 32); err == nil {
						ticketID = uint(id)
					}
				}

				// 查询冷迁移记录
				if ticketID > 0 {
					coldMigration, err := coldMigrationRepo.GetByTicketID(ctx, ticketID)
					if err == nil && coldMigration != nil && coldMigration.FaultDeviceSN == deviceSN {
						// 发现是冷迁移中的故障设备
						isColdMigrationTarget = true
					}
				}
			}

			if isColdMigrationTarget {
				// 场景1: 冷迁移后的故障备机，需要保持故障状态
				logger.Info("检测到设备是冷迁移中的故障备机，将硬件状态保持为故障，业务状态保持为MAINTAINING",
					zap.String("deviceSN", deviceSN),
					zap.String("currentHardwareStatus", oldHardwareStatus),
					zap.String("currentBizStatus", oldBizStatus))

				// 将硬件状态强制设为故障
				newHardwareStatus = cmdbAsset.HardwareStatusFaulty

				// 确保业务状态为MAINTAINING
				newBizStatus = cmdbAsset.BizStatusMaintaining

				// 更新日志
				logReason = "维修完成，设备为冷迁移中的故障备机，硬件状态保持为故障，业务状态保持为MAINTAINING"
			} else {
				// 场景2: 备机维修完成，需要设置为正常状态
				logger.Info("检测到设备是已维修完成的备机，将硬件状态设为正常，业务状态保持为MAINTAINING",
					zap.String("deviceSN", deviceSN),
					zap.String("currentHardwareStatus", oldHardwareStatus),
					zap.String("currentBizStatus", oldBizStatus))

				// 将硬件状态设为正常
				newHardwareStatus = cmdbAsset.HardwareStatusNormal

				// 确保业务状态为MAINTAINING
				newBizStatus = cmdbAsset.BizStatusMaintaining

				// 更新日志
				logReason = "维修完成，备机维修已完成，硬件状态更新为正常，业务状态保持为MAINTAINING"
			}
		}

	default:
		logger.Warn("未知的状态更新阶段", zap.String("stage", stage))
		return fmt.Errorf("未知的状态更新阶段: %s", stage)
	}

	// 获取操作人信息
	var (
		operatorName string
		operatorID   uint
		TicketNo     string
	)

	// 尝试获取工作流ID
	workflowID := ""
	// 从故障单ID获取关联的工单号
	// 查询故障单信息
	if deviceStatusFaultTicketRepo != nil {
		// 直接使用当前工作流ID
		workflowID = info.WorkflowExecution.ID

		logger.Info("从活动信息获取工作流ID",
			zap.String("workflowID", workflowID),
			zap.String("deviceSN", deviceSN))

		// 如果从活动信息获取失败，再尝试从故障单中获取
		if workflowID == "" {
			// 使用deviceSN查询故障单
			faultTickets, total, err := deviceStatusFaultTicketRepo.List(ctx, 1, 1, map[string]interface{}{
				"device_sn": deviceSN,
				"status":    "not_closed", // 尝试获取未关闭的工单
			})

			if err == nil && len(faultTickets) > 0 {
				faultTicket := faultTickets[0]
				// 构造工作流ID
				workflowID = fmt.Sprintf("fault_ticket_%d", faultTicket.ID)
				logger.Info("从故障单获取工作流ID",
					zap.String("workflowID", workflowID),
					zap.Uint("ticketID", faultTicket.ID),
					zap.Int64("total", total))
				operatorName = faultTicket.AssignedTo
				TicketNo = faultTicket.TicketNo
			} else {
				logger.Warn("无法通过deviceSN获取故障单",
					zap.Error(err),
					zap.String("deviceSN", deviceSN))
			}
		}
	}

	// 尝试从活动中获取操作人信息
	//if info.TaskQueue == "fault-ticket-task-queue" {
	//	operatorName = "故障管理系统"
	//} else {
	//	operatorName = "维修工单系统"
	//}

	// 只有当有变更时才记录日志
	if newHardwareStatus != "" || newAssetStatus != "" || newBizStatus != "" {
		// 首先记录完整的状态变更日志（如果有变化）
		if device != nil && ((newHardwareStatus != "" && newHardwareStatus != oldHardwareStatus) ||
			(newAssetStatus != "" && newAssetStatus != oldAssetStatus) ||
			(resource != nil && newBizStatus != "" && newBizStatus != oldBizStatus)) {

			// 如果某些状态没有变更，保持原值
			if newHardwareStatus == "" {
				newHardwareStatus = oldHardwareStatus
			}
			if newAssetStatus == "" {
				newAssetStatus = oldAssetStatus
			}
			if newBizStatus == "" && resource != nil {
				newBizStatus = oldBizStatus
			}

			// 记录完整状态变更日志
			//err := deviceStatusChangeRepo.LogFullStatusChange(ctx, device.ID,
			//	newAssetStatus, oldAssetStatus,
			//	newBizStatus, oldBizStatus,
			//	newHardwareStatus, oldHardwareStatus,
			//	logReason, "故障工单系统", operatorID, operatorName, workflowID)

			//  修改这里的代码
			err := deviceStatusChangeRepo.LogStatusChange(ctx, &cmdbAsset.StatusChangeLog{
				AssetID:           device.ID,
				OldAssetStatus:    oldAssetStatus,
				NewAssetStatus:    newAssetStatus,
				OldBizStatus:      oldBizStatus,
				NewBizStatus:      newBizStatus,
				OldHardwareStatus: oldHardwareStatus,
				NewHardwareStatus: newHardwareStatus,
				ChangeReason:      logReason,
				OperatorID:        operatorID,
				OperatorName:      operatorName,
				TicketNo:          TicketNo,
				WorkflowID:        workflowID,
				Source:            constants.SourceTypeOps,
			})
			if err != nil {
				logger.Warn("记录完整状态变更日志失败", zap.Error(err))

				// 如果完整日志记录失败，再尝试记录单独的状态变更
				// 如果设备存在，执行个别更新
				if device != nil {
					if newHardwareStatus != "" && newHardwareStatus != oldHardwareStatus {
						if err := UpdateDeviceHardwareStatusActivity(ctx, deviceSN, newHardwareStatus); err != nil {
							logger.Warn("更新硬件状态失败", zap.Error(err))
						}
					}

					if newAssetStatus != "" && newAssetStatus != oldAssetStatus {
						if err := UpdateDeviceAssetStatusActivity(ctx, deviceSN, newAssetStatus); err != nil {
							logger.Warn("更新资产状态失败", zap.Error(err))
						}
					}
				}

				// 如果资源存在且需要更新业务状态
				if resource != nil && newBizStatus != "" && newBizStatus != oldBizStatus {
					if err := UpdateResourceBizStatusActivity(ctx, deviceSN, newBizStatus); err != nil {
						logger.Warn("更新业务状态失败", zap.Error(err))
					}
				}
			} else {
				// 完整日志记录成功后，直接更新设备状态，但不再记录单独的状态变更日志

				// 更新设备硬件状态
				if device != nil && newHardwareStatus != "" && newHardwareStatus != oldHardwareStatus {
					device.HardwareStatus = newHardwareStatus
					device.LastHardwareStatusChange = utils.Date(time.Now())
				}

				// 更新设备资产状态
				if device != nil && newAssetStatus != "" && newAssetStatus != oldAssetStatus {
					device.AssetStatus = newAssetStatus
					device.LastStatusChange = utils.Date(time.Now())
				}

				// 保存设备更新
				if device != nil &&
					((newHardwareStatus != "" && newHardwareStatus != oldHardwareStatus) ||
						(newAssetStatus != "" && newAssetStatus != oldAssetStatus)) {
					if err := deviceStatusDeviceRepo.Update(ctx, device); err != nil {
						logger.Warn("更新设备状态失败", zap.Error(err))
					}
				}

				// 更新资源业务状态
				if resource != nil && newBizStatus != "" && newBizStatus != oldBizStatus {
					resource.BizStatus = newBizStatus
					resource.LastBizStatusChange = utils.Date(time.Now())

					if err := deviceStatusResourceRepo.Update(ctx, resource); err != nil {
						logger.Warn("更新资源业务状态失败", zap.Error(err))
					}
				}
			}
		} else {
			// 如果设备存在，执行个别更新
			if device != nil {
				if newHardwareStatus != "" && newHardwareStatus != oldHardwareStatus {
					if err := UpdateDeviceHardwareStatusActivity(ctx, deviceSN, newHardwareStatus); err != nil {
						logger.Warn("更新硬件状态失败", zap.Error(err))
					}
				}

				if newAssetStatus != "" && newAssetStatus != oldAssetStatus {
					if err := UpdateDeviceAssetStatusActivity(ctx, deviceSN, newAssetStatus); err != nil {
						logger.Warn("更新资产状态失败", zap.Error(err))
					}
				}
			}

			// 如果资源存在且需要更新业务状态
			if resource != nil && newBizStatus != "" && newBizStatus != oldBizStatus {
				if err := UpdateResourceBizStatusActivity(ctx, deviceSN, newBizStatus); err != nil {
					logger.Warn("更新业务状态失败", zap.Error(err))
				}
			}
		}
	}

	return nil
}

// InitDeviceStatusActivities 初始化设备状态变更活动
func InitDeviceStatusActivities(
	dRepo cmdbAssetRepo.DeviceRepository,
	rRepo cmdbAssetRepo.ResourceRepository,
	sRepo cmdbAssetRepo.StatusChangeRepository,
	ftRepo repository.FaultTicketRepository,
	cmRepo repository.ColdMigrationRepository,
) {
	deviceStatusDeviceRepo = dRepo
	deviceStatusResourceRepo = rRepo
	deviceStatusChangeRepo = sRepo
	deviceStatusFaultTicketRepo = ftRepo
	coldMigrationRepo = cmRepo
}

// HandleColdMigrationActivity 处理冷迁移活动
func HandleColdMigrationActivity(ctx context.Context, faultDeviceSN string, backupDeviceSN string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行冷迁移处理活动",
		zap.String("faultDeviceSN", faultDeviceSN),
		zap.String("backupDeviceSN", backupDeviceSN))

	if faultDeviceSN == "" || backupDeviceSN == "" {
		return errors.New("故障设备SN和备机SN均不能为空")
	}

	// 获取当前时间，用于记录执行时间和计算耗时
	startTime := time.Now()

	// 检查资源仓库依赖是否初始化
	if deviceStatusResourceRepo == nil || deviceStatusDeviceRepo == nil {
		logger.Error("资源仓库或设备仓库未初始化")
		return errors.New("资源仓库或设备仓库未初始化")
	}

	// 获取故障机资源信息
	faultResource, err := deviceStatusResourceRepo.GetBySN(ctx, faultDeviceSN)
	if err != nil {
		logger.Error("获取故障机资源信息失败",
			zap.Error(err),
			zap.String("faultDeviceSN", faultDeviceSN))
		return fmt.Errorf("获取故障机资源信息失败: %w", err)
	}

	if faultResource == nil {
		logger.Error("未找到故障机资源信息", zap.String("faultDeviceSN", faultDeviceSN))
		return errors.New("未找到故障机资源信息")
	}

	// 获取备机资源信息
	backupResource, err := deviceStatusResourceRepo.GetBySN(ctx, backupDeviceSN)
	if err != nil {
		logger.Error("获取备机资源信息失败",
			zap.Error(err),
			zap.String("backupDeviceSN", backupDeviceSN))
		return fmt.Errorf("获取备机资源信息失败: %w", err)
	}

	if backupResource == nil {
		logger.Error("未找到备机资源信息", zap.String("backupDeviceSN", backupDeviceSN))
		return errors.New("未找到备机资源信息")
	}

	// 获取故障机设备信息，确保硬件状态为故障
	faultDevice, err := deviceStatusDeviceRepo.GetBySN(ctx, faultDeviceSN)
	if err != nil {
		logger.Error("获取故障机设备信息失败",
			zap.Error(err),
			zap.String("faultDeviceSN", faultDeviceSN))
		return fmt.Errorf("获取故障机设备信息失败: %w", err)
	}

	if faultDevice == nil {
		logger.Error("未找到故障机设备信息", zap.String("faultDeviceSN", faultDeviceSN))
		return errors.New("未找到故障机设备信息")
	}

	// 确保故障机的硬件状态为故障
	if faultDevice.HardwareStatus != cmdbAsset.HardwareStatusFaulty {
		logger.Info("设置故障机硬件状态为故障",
			zap.String("faultDeviceSN", faultDeviceSN),
			zap.String("oldHardwareStatus", faultDevice.HardwareStatus))

		faultDevice.HardwareStatus = cmdbAsset.HardwareStatusFaulty
		faultDevice.LastHardwareStatusChange = utils.Date(time.Now())

		// 保存硬件状态更新
		err = deviceStatusDeviceRepo.Update(ctx, faultDevice)
		if err != nil {
			logger.Error("更新故障机硬件状态失败",
				zap.Error(err),
				zap.String("faultDeviceSN", faultDeviceSN))
			return fmt.Errorf("更新故障机硬件状态失败: %w", err)
		}
	}

	// 记录租户IP（如果存在）
	tenantIP := faultResource.TenantIP

	// 更新故障机状态 - 设为备机并进入维护状态
	faultResource.IsBackup = true
	faultResource.BizStatus = cmdbAsset.BizStatusMaintaining
	faultResource.LastBizStatusChange = utils.Date(time.Now())

	// 清空故障机的租户IP（如果存在）
	if tenantIP != "" {
		faultResource.TenantIP = ""
		logger.Info("清空故障机租户IP",
			zap.String("faultDeviceSN", faultDeviceSN),
			zap.String("originalTenantIP", tenantIP))
	}

	// 更新备机状态 - 设为非备机并进入活跃状态
	backupResource.IsBackup = false
	backupResource.BizStatus = cmdbAsset.BizStatusActive
	backupResource.LastBizStatusChange = utils.Date(time.Now())

	// 如果原故障机有租户IP，则设置到备机上
	if tenantIP != "" {
		backupResource.TenantIP = tenantIP
		logger.Info("将租户IP转移到备机",
			zap.String("backupDeviceSN", backupDeviceSN),
			zap.String("tenantIP", tenantIP))
	}

	// 保存故障机状态更新
	err = deviceStatusResourceRepo.Update(ctx, faultResource)
	if err != nil {
		logger.Error("更新故障机状态失败",
			zap.Error(err),
			zap.String("faultDeviceSN", faultDeviceSN))
		return fmt.Errorf("更新故障机状态失败: %w", err)
	}

	// 保存备机状态更新
	err = deviceStatusResourceRepo.Update(ctx, backupResource)
	if err != nil {
		logger.Error("更新备机状态失败",
			zap.Error(err),
			zap.String("backupDeviceSN", backupDeviceSN))
		// 尝试回滚故障机状态
		logger.Warn("尝试回滚故障机状态", zap.String("faultDeviceSN", faultDeviceSN))
		// 这里不检查回滚错误，因为主要错误已经在返回值中
		faultResource.IsBackup = false                      // 假设原来不是备机
		faultResource.BizStatus = cmdbAsset.BizStatusOutage // 恢复为故障状态
		faultResource.TenantIP = tenantIP                   // 恢复租户IP
		if err := deviceStatusResourceRepo.Update(ctx, faultResource); err != nil {
			logger.Warn("回滚故障机状态失败", zap.Error(err), zap.String("faultDeviceSN", faultDeviceSN))
		}

		return fmt.Errorf("更新备机状态失败: %w", err)
	}

	// 记录状态变更日志
	operatorName := "冷迁移自动化流程"
	operatorID := uint(0)

	// 记录故障机状态变更日志
	err = deviceStatusChangeRepo.ChangeBizStatus(ctx, faultResource.ID, cmdbAsset.BizStatusMaintaining,
		"冷迁移流程将设备从ACTIVE/OUTAGE变更为MAINTAINING，同时设为备机",
		operatorID, operatorName)
	if err != nil {
		logger.Warn("记录故障机状态变更日志失败",
			zap.Error(err),
			zap.String("faultDeviceSN", faultDeviceSN))
		// 继续流程，不中断
	}

	// 记录备机状态变更日志
	err = deviceStatusChangeRepo.ChangeBizStatus(ctx, backupResource.ID, cmdbAsset.BizStatusActive,
		"冷迁移流程将设备从MAINTAINING变更为ACTIVE，同时解除备机状态",
		operatorID, operatorName)
	if err != nil {
		logger.Warn("记录备机状态变更日志失败",
			zap.Error(err),
			zap.String("backupDeviceSN", backupDeviceSN))
		// 继续流程，不中断
	}

	// 计算冷迁移耗时（秒）
	duration := int(time.Since(startTime).Seconds())

	// 尝试从活动信息中获取工单ID、操作人ID和操作人姓名
	info := activity.GetInfo(ctx)
	var ticketID uint
	var operatorComment string

	// 从工作流ID中提取故障单ID
	if strings.HasPrefix(info.WorkflowExecution.ID, "fault_ticket_") {
		ticketIDStr := strings.TrimPrefix(info.WorkflowExecution.ID, "fault_ticket_")
		if id, err := strconv.ParseUint(ticketIDStr, 10, 32); err == nil {
			ticketID = uint(id)
		}
	}

	// 获取备注信息
	operatorComment = fmt.Sprintf("冷迁移成功: 故障机(%s)→备机(%s)", faultDeviceSN, backupDeviceSN)
	if tenantIP != "" {
		operatorComment += fmt.Sprintf(", 租户IP(%s)已迁移", tenantIP)
	}

	// 记录冷迁移数据
	if coldMigrationRepo != nil && ticketID > 0 {
		coldMigration := &model.ColdMigration{
			TicketID:       ticketID,
			FaultDeviceSN:  faultDeviceSN,
			BackupDeviceSN: backupDeviceSN,
			TenantIP:       tenantIP,
			Status:         "success",
			ExecutionTime:  startTime,
			Duration:       duration,
			OperatorID:     operatorID,
			OperatorName:   operatorName,
			Comments:       operatorComment,
		}

		if err := coldMigrationRepo.Create(ctx, coldMigration); err != nil {
			logger.Warn("记录冷迁移数据失败",
				zap.Error(err),
				zap.Uint("ticketID", ticketID),
				zap.String("faultDeviceSN", faultDeviceSN),
				zap.String("backupDeviceSN", backupDeviceSN))
			// 继续流程，不中断
		} else {
			logger.Info("成功记录冷迁移数据",
				zap.Uint("ticketID", ticketID),
				zap.String("faultDeviceSN", faultDeviceSN),
				zap.String("backupDeviceSN", backupDeviceSN),
				zap.Int("duration", duration))
		}
	} else {
		logger.Warn("未能记录冷迁移数据",
			zap.Bool("coldMigrationRepo存在", coldMigrationRepo != nil),
			zap.Uint("ticketID", ticketID))
	}

	logger.Info("冷迁移处理完成",
		zap.String("faultDeviceSN", faultDeviceSN),
		zap.String("backupDeviceSN", backupDeviceSN),
		zap.Bool("租户IP迁移", tenantIP != ""),
		zap.Int("耗时(秒)", duration))

	return nil
}

// ProcessColdMigrationActivity 处理冷迁移状态变更活动
func ProcessColdMigrationActivity(ctx context.Context, faultDeviceSN string, backupDeviceSN string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行冷迁移状态变更活动",
		zap.String("faultDeviceSN", faultDeviceSN),
		zap.String("backupDeviceSN", backupDeviceSN))

	// 获取当前时间，用于记录执行时间和计算耗时
	startTime := time.Now()

	if faultDeviceSN == "" {
		return errors.New("故障设备SN不能为空")
	}

	if backupDeviceSN == "" {
		return errors.New("备机SN不能为空")
	}

	// 尝试从活动信息中获取工单ID
	info := activity.GetInfo(ctx)
	var ticketID uint

	// 从工作流ID中提取故障单ID
	if strings.HasPrefix(info.WorkflowExecution.ID, "fault_ticket_") {
		ticketIDStr := strings.TrimPrefix(info.WorkflowExecution.ID, "fault_ticket_")
		if id, err := strconv.ParseUint(ticketIDStr, 10, 32); err == nil {
			ticketID = uint(id)
		}
	}

	// 检查故障设备 - 更新硬件状态为故障
	err := UpdateDeviceHardwareStatusActivity(ctx, faultDeviceSN, "faulty")
	if err != nil {
		logger.Error("更新故障设备硬件状态失败",
			zap.Error(err),
			zap.String("faultDeviceSN", faultDeviceSN))

		// 记录冷迁移失败
		recordColdMigrationFailure(ctx, ticketID, faultDeviceSN, backupDeviceSN,
			"更新故障设备硬件状态失败: "+err.Error(), startTime)

		return err
	}

	// 检查故障设备 - 更新资产状态为维修中
	err = UpdateDeviceAssetStatusActivity(ctx, faultDeviceSN, "maintaining")
	if err != nil {
		logger.Error("更新故障设备资产状态失败",
			zap.Error(err),
			zap.String("faultDeviceSN", faultDeviceSN))

		// 记录冷迁移失败
		recordColdMigrationFailure(ctx, ticketID, faultDeviceSN, backupDeviceSN,
			"更新故障设备资产状态失败: "+err.Error(), startTime)

		return err
	}

	// 调用冷迁移处理活动
	err = HandleColdMigrationActivity(ctx, faultDeviceSN, backupDeviceSN)
	if err != nil {
		logger.Error("冷迁移处理失败",
			zap.Error(err),
			zap.String("faultDeviceSN", faultDeviceSN),
			zap.String("backupDeviceSN", backupDeviceSN))

		// 记录冷迁移失败
		recordColdMigrationFailure(ctx, ticketID, faultDeviceSN, backupDeviceSN,
			"冷迁移处理失败: "+err.Error(), startTime)

		return err
	}

	logger.Info("冷迁移状态变更完成",
		zap.String("faultDeviceSN", faultDeviceSN),
		zap.String("backupDeviceSN", backupDeviceSN))

	return nil
}

// recordColdMigrationFailure 记录冷迁移失败信息
func recordColdMigrationFailure(ctx context.Context, ticketID uint, faultDeviceSN, backupDeviceSN, failureReason string, startTime time.Time) {
	logger := activity.GetLogger(ctx)

	// 如果没有冷迁移记录仓库或工单ID，则跳过记录
	if coldMigrationRepo == nil || ticketID == 0 {
		logger.Warn("无法记录冷迁移失败数据",
			zap.Bool("coldMigrationRepo存在", coldMigrationRepo != nil),
			zap.Uint("ticketID", ticketID))
		return
	}

	// 计算冷迁移耗时（秒）
	duration := int(time.Since(startTime).Seconds())

	// 创建冷迁移失败记录
	coldMigration := &model.ColdMigration{
		TicketID:       ticketID,
		FaultDeviceSN:  faultDeviceSN,
		BackupDeviceSN: backupDeviceSN,
		TenantIP:       "", // 失败时可能没有租户IP信息
		Status:         "failed",
		ExecutionTime:  startTime,
		Duration:       duration,
		OperatorID:     0,
		OperatorName:   "冷迁移自动化流程",
		Comments:       fmt.Sprintf("冷迁移失败: %s", failureReason),
	}

	if err := coldMigrationRepo.Create(ctx, coldMigration); err != nil {
		logger.Warn("记录冷迁移失败数据失败",
			zap.Error(err),
			zap.Uint("ticketID", ticketID),
			zap.String("faultDeviceSN", faultDeviceSN),
			zap.String("backupDeviceSN", backupDeviceSN))
	} else {
		logger.Info("成功记录冷迁移失败数据",
			zap.Uint("ticketID", ticketID),
			zap.String("faultDeviceSN", faultDeviceSN),
			zap.String("backupDeviceSN", backupDeviceSN),
			zap.String("failureReason", failureReason),
			zap.Int("duration", duration))
	}
}

// RevertColdMigrationActivity 冷迁移验证失败时回滚状态变更
func RevertColdMigrationActivity(ctx context.Context, faultDeviceSN string, backupDeviceSN string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行冷迁移回滚活动",
		zap.String("faultDeviceSN", faultDeviceSN),
		zap.String("backupDeviceSN", backupDeviceSN))

	if faultDeviceSN == "" {
		return errors.New("故障设备SN不能为空")
	}

	// 如果备机SN为空，尝试从冷迁移记录中获取
	if backupDeviceSN == "" && coldMigrationRepo != nil {
		logger.Info("备机SN为空，尝试从冷迁移记录中获取", zap.String("faultDeviceSN", faultDeviceSN))

		// 尝试从活动信息中获取工单ID
		info := activity.GetInfo(ctx)
		var ticketID uint

		// 从工作流ID中提取故障单ID
		if strings.HasPrefix(info.WorkflowExecution.ID, "fault_ticket_") {
			ticketIDStr := strings.TrimPrefix(info.WorkflowExecution.ID, "fault_ticket_")
			if id, err := strconv.ParseUint(ticketIDStr, 10, 32); err == nil {
				ticketID = uint(id)
				logger.Info("从工作流ID提取故障单ID成功",
					zap.Uint("ticketID", ticketID),
					zap.String("workflowID", info.WorkflowExecution.ID))

				// 优先使用故障单ID获取冷迁移记录
				coldMigration, err := coldMigrationRepo.GetByTicketID(ctx, ticketID)
				if err != nil {
					logger.Error("通过故障单ID查询冷迁移记录失败",
						zap.Error(err),
						zap.Uint("ticketID", ticketID))
				} else if coldMigration != nil {
					backupDeviceSN = coldMigration.BackupDeviceSN
					logger.Info("通过故障单ID成功找到备机SN",
						zap.String("backupDeviceSN", backupDeviceSN),
						zap.Time("migrationTime", coldMigration.ExecutionTime))
				}
			}
		}

	}

	// 再次检查备机SN是否获取到
	if backupDeviceSN == "" {
		return errors.New("备机SN不能为空，且无法从冷迁移记录中获取")
	}

	// 获取当前时间，用于记录执行时间和计算耗时
	startTime := time.Now()

	// 检查资源仓库依赖是否初始化
	if deviceStatusResourceRepo == nil || deviceStatusDeviceRepo == nil {
		logger.Error("资源仓库或设备仓库未初始化")
		return errors.New("资源仓库或设备仓库未初始化")
	}

	// 获取故障机资源信息
	faultResource, err := deviceStatusResourceRepo.GetBySN(ctx, faultDeviceSN)
	if err != nil {
		logger.Error("获取故障机资源信息失败",
			zap.Error(err),
			zap.String("faultDeviceSN", faultDeviceSN))
		return fmt.Errorf("获取故障机资源信息失败: %w", err)
	}

	if faultResource == nil {
		logger.Error("未找到故障机资源信息", zap.String("faultDeviceSN", faultDeviceSN))
		return errors.New("未找到故障机资源信息")
	}

	// 获取备机资源信息
	backupResource, err := deviceStatusResourceRepo.GetBySN(ctx, backupDeviceSN)
	if err != nil {
		logger.Error("获取备机资源信息失败",
			zap.Error(err),
			zap.String("backupDeviceSN", backupDeviceSN))
		return fmt.Errorf("获取备机资源信息失败: %w", err)
	}

	if backupResource == nil {
		logger.Error("未找到备机资源信息", zap.String("backupDeviceSN", backupDeviceSN))
		return errors.New("未找到备机资源信息")
	}

	// 检查并记录租户IP（可能在备机上）
	tenantIP := backupResource.TenantIP

	// 1. 恢复故障机状态 - 设回非备机并回到故障状态
	faultResource.IsBackup = false
	faultResource.BizStatus = cmdbAsset.BizStatusOutage
	faultResource.LastBizStatusChange = utils.Date(time.Now())

	// 如果备机有租户IP，则转移回故障机
	if tenantIP != "" {
		faultResource.TenantIP = tenantIP
		logger.Info("将租户IP从备机转移回故障机",
			zap.String("faultDeviceSN", faultDeviceSN),
			zap.String("tenantIP", tenantIP))
	}

	// 2. 恢复备机状态 - 设回备机并回到维护状态
	backupResource.IsBackup = true
	backupResource.BizStatus = cmdbAsset.BizStatusMaintaining
	backupResource.LastBizStatusChange = utils.Date(time.Now())

	// 如果备机有租户IP，则清除
	if tenantIP != "" {
		backupResource.TenantIP = ""
		logger.Info("清除备机上的租户IP",
			zap.String("backupDeviceSN", backupDeviceSN),
			zap.String("originalTenantIP", tenantIP))
	}

	// 保存故障机状态更新
	err = deviceStatusResourceRepo.Update(ctx, faultResource)
	if err != nil {
		logger.Error("回滚时更新故障机状态失败",
			zap.Error(err),
			zap.String("faultDeviceSN", faultDeviceSN))
		return fmt.Errorf("回滚时更新故障机状态失败: %w", err)
	}

	// 保存备机状态更新
	err = deviceStatusResourceRepo.Update(ctx, backupResource)
	if err != nil {
		logger.Error("回滚时更新备机状态失败",
			zap.Error(err),
			zap.String("backupDeviceSN", backupDeviceSN))
		return fmt.Errorf("回滚时更新备机状态失败: %w", err)
	}

	// 记录状态变更日志
	operatorName := "冷迁移验证失败自动回滚"
	operatorID := uint(0)

	// 记录故障机状态变更日志
	err = deviceStatusChangeRepo.ChangeBizStatus(ctx, faultResource.ID, cmdbAsset.BizStatusOutage,
		"冷迁移验证失败，回滚状态：将故障机从备机状态恢复为非备机，业务状态从MAINTAINING变更为OUTAGE",
		operatorID, operatorName)
	if err != nil {
		logger.Warn("记录故障机状态回滚日志失败",
			zap.Error(err),
			zap.String("faultDeviceSN", faultDeviceSN))
		// 继续流程，不中断
	}

	// 记录备机状态变更日志
	err = deviceStatusChangeRepo.ChangeBizStatus(ctx, backupResource.ID, cmdbAsset.BizStatusMaintaining,
		"冷迁移验证失败，回滚状态：将备机从非备机状态恢复为备机，业务状态从ACTIVE变更为MAINTAINING",
		operatorID, operatorName)
	if err != nil {
		logger.Warn("记录备机状态回滚日志失败",
			zap.Error(err),
			zap.String("backupDeviceSN", backupDeviceSN))
		// 继续流程，不中断
	}

	// 尝试从活动信息中获取工单ID
	info := activity.GetInfo(ctx)
	var ticketID uint

	// 从工作流ID中提取故障单ID
	if strings.HasPrefix(info.WorkflowExecution.ID, "fault_ticket_") {
		ticketIDStr := strings.TrimPrefix(info.WorkflowExecution.ID, "fault_ticket_")
		if id, err := strconv.ParseUint(ticketIDStr, 10, 32); err == nil {
			ticketID = uint(id)
		}
	}

	// 计算操作耗时（秒）
	duration := int(time.Since(startTime).Seconds())

	// 记录冷迁移回滚数据
	if coldMigrationRepo != nil && ticketID > 0 {
		coldMigration := &model.ColdMigration{
			TicketID:       ticketID,
			FaultDeviceSN:  faultDeviceSN,
			BackupDeviceSN: backupDeviceSN,
			TenantIP:       tenantIP,
			Status:         "reverted",
			ExecutionTime:  startTime,
			Duration:       duration,
			OperatorID:     operatorID,
			OperatorName:   operatorName,
			Comments:       "冷迁移验证失败，状态已回滚：故障机和备机已恢复到迁移前状态",
		}

		if err := coldMigrationRepo.Create(ctx, coldMigration); err != nil {
			logger.Warn("记录冷迁移回滚数据失败",
				zap.Error(err),
				zap.Uint("ticketID", ticketID),
				zap.String("faultDeviceSN", faultDeviceSN),
				zap.String("backupDeviceSN", backupDeviceSN))
			// 继续流程，不中断
		} else {
			logger.Info("成功记录冷迁移回滚数据",
				zap.Uint("ticketID", ticketID),
				zap.String("faultDeviceSN", faultDeviceSN),
				zap.String("backupDeviceSN", backupDeviceSN),
				zap.Int("duration", duration))
		}
	}

	logger.Info("冷迁移状态回滚完成",
		zap.String("faultDeviceSN", faultDeviceSN),
		zap.String("backupDeviceSN", backupDeviceSN),
		zap.Bool("租户IP迁移回滚", tenantIP != ""),
		zap.Int("耗时(秒)", duration))

	return nil
}
