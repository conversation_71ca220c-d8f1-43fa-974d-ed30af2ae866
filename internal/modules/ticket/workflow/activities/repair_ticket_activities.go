package activities

import (
	"backend/configs"
	"backend/internal/common/utils/notifier"
	"backend/internal/modules/cmdb/service/asset"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	ticketService "backend/internal/modules/ticket/service"
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

// 全局仓库和服务依赖 - 只声明本文件特有的变量，共享变量不再重复声明
var (
	repairTicketRepoImpl     repository.RepairTicketRepository
	faultTicketRepoForRepair repository.FaultTicketRepository
	zapLogger                *zap.Logger
	feishuNotifier           *notifier.FeishuNotifier
	deviceProjectServiceImpl ticketService.DeviceProjectService
)

// InitRepairActivities 初始化维修单活动
func InitRepairActivities(
	repo repository.RepairTicketRepository,
	repairSvc ticketService.RepairTicketService,
	userSvc ticketService.UserService,
	logger *zap.Logger,
	faultRepo repository.FaultTicketRepository,
	deviceSearchSvc asset.DeviceSearchService,
) {
	repairTicketRepoImpl = repo
	repairTicketService = repairSvc // 使用共享变量
	userService = userSvc           // 修复：使用不同参数名避免自我赋值
	zapLogger = logger
	faultTicketRepoForRepair = faultRepo

	// 初始化设备项目服务
	deviceProjectServiceImpl = ticketService.NewDeviceProjectService(deviceSearchSvc)
}

// InitFeishuNotifier 初始化飞书通知器
func InitFeishuNotifier(webhookURL, secret, detailUrlTemplate string, repairDetailUrlTemplate string, repairProjectWebhooks configs.ProjectWebhookMappings, logger *zap.Logger) {
	// 打印传入的URL模板值，帮助调试
	logger.Info("初始化飞书通知器 - 参数",
		zap.String("detailUrlTemplate", detailUrlTemplate),
		zap.String("repairDetailUrlTemplate", repairDetailUrlTemplate))

	feishuNotifier = notifier.NewFeishuNotifier(webhookURL, secret, detailUrlTemplate, repairDetailUrlTemplate, repairProjectWebhooks, logger)

	// 打印实际使用的URL模板值
	logger.Info("初始化飞书通知器 - 结果",
		zap.String("实际使用的URL模板", feishuNotifier.DetailUrlTemplate),
		zap.String("实际使用的维修单URL模板", feishuNotifier.RepairDetailUrlTemplate))
}

// GetUserInfoForOperator 获取操作人信息
func GetUserInfoForOperator(ctx context.Context, operatorID uint) (string, error) {
	if operatorID == 0 {
		return "系统", nil // 如果操作者ID为0，返回"系统"
	}

	// 尝试从上下文获取用户真实姓名
	// 检查上下文中是否直接包含用户信息
	if userRealName, ok := ctx.Value("userRealName").(string); ok && userRealName != "" {
		return userRealName, nil
	} else if userInfo, ok := ctx.Value("userInfo").(map[string]interface{}); ok {
		if realName, ok := userInfo["realName"].(string); ok && realName != "" {
			return realName, nil
		}
	}

	// 如果上下文中没有找到，尝试使用userService
	if userService == nil {
		if zapLogger != nil {
			zapLogger.Warn("用户服务未初始化，无法获取用户真实姓名",
				zap.Uint("operatorID", operatorID))
		}
		return "", fmt.Errorf("用户服务未初始化")
	}

	// 使用defer-recover防止panic
	var userName string
	var err error

	func() {
		defer func() {
			if r := recover(); r != nil {
				if zapLogger != nil {
					zapLogger.Error("获取用户名时发生panic",
						zap.Any("panic", r),
						zap.Uint("operatorID", operatorID))
				}
				userName = ""
				err = fmt.Errorf("获取用户名时发生错误: %v", r)
			}
		}()

		// 尝试获取用户名
		userName, err = userService.GetUserName(ctx, operatorID)
	}()

	// 如果获取成功且非空，返回获取的用户名
	if err == nil && userName != "" && !strings.HasPrefix(userName, "用户") {
		return userName, nil
	}

	// 失败或空值时返回空字符串
	return "", fmt.Errorf("无法获取用户ID=%d的真实姓名", operatorID)
}

// GetRepairTicketActivity 获取维修单活动
func GetRepairTicketActivity(ctx context.Context, repairTicketID uint) (*model.RepairTicket, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行获取维修单活动", zap.Uint("repairTicketID", repairTicketID))

	if repairTicketRepoImpl == nil {
		return nil, ErrActivityDependenciesNotInitialized
	}

	// 使用仓库方法获取维修单信息
	repairTicket, err := repairTicketRepoImpl.GetByID(ctx, repairTicketID)
	if err != nil {
		logger.Error("获取维修单失败", zap.Error(err))
		return nil, err
	}

	return repairTicket, nil
}

// RepairTicketListResult 维修单列表结果
type RepairTicketListResult struct {
	Tickets []*model.RepairTicket `json:"tickets"`
	Total   int64                 `json:"total"`
}

// ListAvailableRepairTicketsActivity 获取可供接单的维修单列表
func ListAvailableRepairTicketsActivity(ctx context.Context, engineerID uint, limit int, offset int) (*RepairTicketListResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行获取可供接单的维修单列表活动",
		zap.Uint("engineerID", engineerID),
		zap.Int("limit", limit),
		zap.Int("offset", offset))

	if repairTicketRepoImpl == nil {
		return nil, ErrActivityDependenciesNotInitialized
	}

	// 构建查询
	query := &model.RepairTicketQuery{
		Status:         model.RepairTicketStatusWaitingAccept, // 只获取待接单状态的工单
		PageSize:       int64(limit),
		Page:           int64(offset/limit + 1),
		OrderBy:        "created_at",
		OrderDirection: "desc",
	}

	// 获取可供接单的维修单列表
	tickets, total, err := repairTicketRepoImpl.ListByQuery(ctx, query)
	if err != nil {
		logger.Error("获取可供接单的维修单列表失败", zap.Error(err))
		return nil, err
	}

	result := &RepairTicketListResult{
		Tickets: tickets,
		Total:   total,
	}

	logger.Info("获取可供接单的维修单列表成功",
		zap.Int("count", len(tickets)),
		zap.Int64("total", total))
	return result, nil
}

// EngineerTakeRepairTicketActivity 工程师主动接单活动
func EngineerTakeRepairTicketActivity(ctx context.Context, repairTicketID uint, engineerID uint) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行工程师主动接单活动",
		zap.Uint("repairTicketID", repairTicketID),
		zap.Uint("engineerID", engineerID))

	if repairTicketRepoImpl == nil || repairTicketService == nil || userService == nil {
		return ErrActivityDependenciesNotInitialized
	}

	// 获取维修单
	repairTicket, err := repairTicketRepoImpl.GetByID(ctx, repairTicketID)
	if err != nil {
		logger.Error("获取维修单失败", zap.Error(err))
		return err
	}

	// 检查维修单状态
	if repairTicket.Status != model.RepairTicketStatusWaitingAccept {
		logger.Error("维修单状态不正确，无法接单",
			zap.String("currentStatus", repairTicket.Status))
		return fmt.Errorf("维修单状态不是'%s'，无法接单", model.RepairTicketStatusWaitingAccept)
	}

	// 新增：检查关联故障单的状态
	if repairTicket.FaultTicketID > 0 {
		// 确保faultTicketRepoForRepair已经在globals中定义和初始化
		if faultTicketRepoForRepair == nil {
			logger.Error("故障单仓库未初始化", zap.Uint("repairTicketID", repairTicketID))
			return errors.New("故障单仓库未初始化，无法获取故障单状态")
		}

		// 获取关联故障单信息
		faultTicket, err := faultTicketRepoForRepair.GetByID(ctx, repairTicket.FaultTicketID)
		if err != nil {
			logger.Error("获取关联故障单失败",
				zap.Error(err),
				zap.Uint("faultTicketID", repairTicket.FaultTicketID))
			return fmt.Errorf("获取关联故障单失败: %w", err)
		}

		// 检查故障单状态是否为repairing
		if faultTicket.Status != "repairing" {
			logger.Error("关联故障单状态不正确，无法接单",
				zap.String("faultTicketStatus", faultTicket.Status),
				zap.Uint("faultTicketID", faultTicket.ID))
			return fmt.Errorf("关联故障单状态不是'%s'，当前为'%s'，无法接单", "repairing", faultTicket.Status)
		}

		// 检查是否为高频故障设备
		if faultTicket.DeviceSN != "" {
			// 检查最近7天内的故障数
			faults7Days, err := faultTicketRepoForRepair.CountRecentFaultsByDeviceSN(ctx, faultTicket.DeviceSN, 7)
			if err != nil {
				logger.Warn("检查设备最近7天故障记录失败",
					zap.Error(err),
					zap.String("deviceSN", faultTicket.DeviceSN))
			} else if faults7Days >= 2 {
				// 更新IsFrequentFault标记
				fields := map[string]interface{}{
					"is_frequent_fault": true,
				}
				if err := faultTicketRepoForRepair.UpdateFields(ctx, faultTicket.ID, fields); err != nil {
					logger.Warn("更新高频故障标记失败",
						zap.Error(err),
						zap.Uint("faultTicketID", faultTicket.ID))
				} else {
					logger.Info("设备为高频故障(最近7天内>=2次)",
						zap.String("deviceSN", faultTicket.DeviceSN),
						zap.Int64("故障次数", faults7Days))
				}
			} else {
				// 检查最近30天内的故障数
				faults30Days, err := faultTicketRepoForRepair.CountRecentFaultsByDeviceSN(ctx, faultTicket.DeviceSN, 30)
				if err != nil {
					logger.Warn("检查设备最近30天故障记录失败",
						zap.Error(err),
						zap.String("deviceSN", faultTicket.DeviceSN))
				} else if faults30Days >= 3 {
					// 更新IsFrequentFault标记
					fields := map[string]interface{}{
						"is_frequent_fault": true,
					}
					if err := faultTicketRepoForRepair.UpdateFields(ctx, faultTicket.ID, fields); err != nil {
						logger.Warn("更新高频故障标记失败",
							zap.Error(err),
							zap.Uint("faultTicketID", faultTicket.ID))
					} else {
						logger.Info("设备为高频故障(最近30天内>=3次)",
							zap.String("deviceSN", faultTicket.DeviceSN),
							zap.Int64("故障次数", faults30Days))
					}
				}
			}
		}

		logger.Info("关联故障单状态检查通过",
			zap.Uint("faultTicketID", faultTicket.ID),
			zap.String("status", faultTicket.Status))
	}

	// 获取工程师姓名
	engineerName, err := GetUserInfoForOperator(ctx, engineerID)
	if err != nil {
		logger.Warn("获取工程师姓名失败", zap.Error(err))
		// 获取姓名失败时，使用"用户ID"格式作为默认值
		engineerName = fmt.Sprintf("用户%d", engineerID)
	}

	// 更新维修单工程师信息和状态
	now := time.Now()
	repairTicket.AssignedEngineerID = engineerID
	repairTicket.AssignedEngineerName = engineerName
	repairTicket.AssignedTime = &now
	repairTicket.Status = "assigned"

	err = repairTicketRepoImpl.Update(ctx, repairTicket)
	if err != nil {
		logger.Error("更新维修单失败", zap.Error(err))
		return err
	}

	logger.Info("工程师接单成功",
		zap.Uint("repairTicketID", repairTicketID),
		zap.Uint("engineerID", engineerID),
		zap.String("engineerName", engineerName))
	return nil
}

// EngineerAcknowledgeActivity 工程师确认接单活动
func EngineerAcknowledgeActivity(ctx context.Context, repairTicketID uint) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行工程师确认接单活动", zap.Uint("repairTicketID", repairTicketID))

	if repairTicketRepoImpl == nil || repairTicketService == nil {
		return ErrActivityDependenciesNotInitialized
	}

	// 获取维修单
	repairTicket, err := repairTicketRepoImpl.GetByID(ctx, repairTicketID)
	if err != nil {
		logger.Error("获取维修单失败", zap.Error(err))
		return err
	}

	// 检查维修单状态
	if repairTicket.Status != model.RepairTicketStatusAssigned {
		logger.Error("维修单状态不正确，无法确认接单",
			zap.String("currentStatus", repairTicket.Status))
		return fmt.Errorf("维修单状态不是'%s'，无法确认接单", model.RepairTicketStatusAssigned)
	}

	// 确认接单 - 实际可能需要等待工程师确认
	// 这里简化处理，直接更新状态
	err = repairTicketService.UpdateRepairTicketStatus(
		ctx,
		repairTicketID,
		model.RepairTicketStatusAcknowledged,
		repairTicket.AssignedEngineerID,
		"")
	if err != nil {
		logger.Error("更新维修单确认时间失败", zap.Error(err))
		return err
	}

	logger.Info("工程师确认接单成功", zap.Uint("repairTicketID", repairTicketID))
	return nil
}

// UpdateRepairTicketStatusActivity 更新维修单状态活动
func UpdateRepairTicketStatusActivity(ctx context.Context, repairTicketID uint, status string, operatorID uint, comments string, operatorName string) error {
	// 确保logger初始化
	if zapLogger == nil {
		zapLogger = zap.NewNop()
	}
	logger := activity.GetLogger(ctx)
	logger.Info("执行更新维修单状态活动",
		zap.Uint("repairTicketID", repairTicketID),
		zap.String("status", status),
		zap.Uint("operatorID", operatorID),
		zap.String("operatorName", operatorName))

	if repairTicketRepoImpl == nil || repairTicketService == nil {
		return ErrActivityDependenciesNotInitialized
	}

	// 使用传入的操作人名称
	// 如果为空，才尝试获取
	if operatorName == "" && operatorID > 0 {
		var err error
		operatorName, err = GetUserInfoForOperator(ctx, operatorID)
		if err != nil {
			logger.Warn("获取操作人姓名失败", zap.Error(err))
			// 当获取失败时，使用"用户ID"格式作为备用
			operatorName = fmt.Sprintf("用户%d", operatorID)
		}
	}

	// 更新维修单状态
	err := repairTicketService.UpdateRepairTicketStatus(ctx, repairTicketID, status, operatorID, operatorName)
	if err != nil {
		logger.Error("更新维修单状态失败", zap.Error(err))
		return err
	}

	logger.Info("维修单状态更新成功",
		zap.Uint("repairTicketID", repairTicketID),
		zap.String("status", status))
	return nil
}

// RecordHardwareReplaceStartActivity 记录硬件替换开始活动
func RecordHardwareReplaceStartActivity(ctx context.Context, repairTicketID uint) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行记录硬件替换开始活动", zap.Uint("repairTicketID", repairTicketID))

	if repairTicketRepoImpl == nil {
		return ErrActivityDependenciesNotInitialized
	}

	// 获取维修单
	repairTicket, err := repairTicketRepoImpl.GetByID(ctx, repairTicketID)
	if err != nil {
		logger.Error("获取维修单失败", zap.Error(err))
		return err
	}

	// 通过状态更新来记录硬件替换开始
	err = repairTicketService.UpdateRepairTicketStatus(
		ctx,
		repairTicketID,
		model.RepairTicketStatusReplacingHardware,
		repairTicket.AssignedEngineerID,
		"")
	if err != nil {
		logger.Error("更新维修单状态失败", zap.Error(err))
		return err
	}

	logger.Info("硬件替换开始时间已记录", zap.Uint("repairTicketID", repairTicketID))
	return nil
}

// CompleteHardwareReplaceActivity 完成硬件替换活动
func CompleteHardwareReplaceActivity(ctx context.Context, repairTicketID uint, success bool, details string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行完成硬件替换活动",
		zap.Uint("repairTicketID", repairTicketID),
		zap.Bool("success", success))

	if repairTicketRepoImpl == nil || repairTicketService == nil {
		return ErrActivityDependenciesNotInitialized
	}

	// 获取维修单
	repairTicket, err := repairTicketRepoImpl.GetByID(ctx, repairTicketID)
	if err != nil {
		logger.Error("获取维修单失败", zap.Error(err))
		return err
	}

	// 通过状态更新来记录硬件替换完成
	status := model.RepairTicketStatusHardwareReplaceCompleted
	if !success {
		status = model.RepairTicketStatusHardwareReplaceFailed
	}

	err = repairTicketService.UpdateRepairTicketStatus(
		ctx,
		repairTicketID,
		status,
		repairTicket.AssignedEngineerID,
		details)
	if err != nil {
		logger.Error("更新维修单状态失败", zap.Error(err))
		return err
	}

	logger.Info("硬件替换已完成",
		zap.Uint("repairTicketID", repairTicketID),
		zap.Bool("success", success))
	return nil
}

// CompleteRepairActivity 完成维修活动
func CompleteRepairActivity(ctx context.Context, repairTicketID uint, success bool, conclusion string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行完成维修活动",
		zap.Uint("repairTicketID", repairTicketID),
		zap.Bool("success", success))

	if repairTicketRepoImpl == nil || repairTicketService == nil {
		return ErrActivityDependenciesNotInitialized
	}

	// 获取维修单
	repairTicket, err := repairTicketRepoImpl.GetByID(ctx, repairTicketID)
	if err != nil {
		logger.Error("获取维修单失败", zap.Error(err))
		return err
	}

	// 更新维修单状态
	status := model.RepairTicketStatusHardwareReplaceCompleted
	if !success {
		status = model.RepairTicketStatusFailed
	}

	err = repairTicketService.UpdateRepairTicketStatus(
		ctx,
		repairTicketID,
		status,
		repairTicket.AssignedEngineerID,
		conclusion)
	if err != nil {
		logger.Error("更新维修单状态失败", zap.Error(err))
		return err
	}

	// 如果与故障单关联，请在故障单工作流中更新状态，此处不直接操作

	logger.Info("维修已完成",
		zap.Uint("repairTicketID", repairTicketID),
		zap.Bool("success", success))
	return nil
}

// ArriveOnSiteActivity 记录工程师到达现场活动
func ArriveOnSiteActivity(ctx context.Context, repairTicketID uint) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行工程师到达现场活动", zap.Uint("repairTicketID", repairTicketID))

	if repairTicketRepoImpl == nil || repairTicketService == nil {
		return ErrActivityDependenciesNotInitialized
	}

	// 获取维修单
	repairTicket, err := repairTicketRepoImpl.GetByID(ctx, repairTicketID)
	if err != nil {
		logger.Error("获取维修单失败", zap.Error(err))
		return err
	}

	// 检查维修单状态
	if repairTicket.Status != "in_progress" {
		logger.Error("维修单状态不正确，无法记录到达现场",
			zap.String("currentStatus", repairTicket.Status))
		return fmt.Errorf("维修单必须处于进行中状态才能记录到达现场，当前状态: %s", repairTicket.Status)
	}

	// 记录到达现场时间
	now := time.Now()
	repairTicket.ArriveTime = &now

	// 更新维修单
	err = repairTicketRepoImpl.Update(ctx, repairTicket)
	if err != nil {
		logger.Error("更新维修单失败", zap.Error(err))
		return err
	}

	logger.Info("工程师到达现场记录完成",
		zap.Uint("repairTicketID", repairTicketID),
		zap.Time("arriveTime", *repairTicket.ArriveTime),
		zap.Uint("engineerID", repairTicket.AssignedEngineerID))
	return nil
}

// AuthorizeRepairTicketActivity 维修单授权活动
func AuthorizeRepairTicketActivity(ctx context.Context, repairTicketID uint, operatorID uint, operatorName string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行维修单授权活动",
		zap.Uint("repairTicketID", repairTicketID),
		zap.Uint("operatorID", operatorID),
		zap.String("operatorName", operatorName))

	if repairTicketService == nil {
		return ErrActivityDependenciesNotInitialized
	}

	// 如果操作人名称为空，尝试获取
	if operatorName == "" {
		var err error
		operatorName, err = GetUserInfoForOperator(ctx, operatorID)
		if err != nil {
			logger.Warn("获取操作人名称失败，使用默认名称", zap.Error(err))
			// operatorName已经包含"用户ID"格式的默认值，不需要再设置
		}
	}

	// 调用服务执行授权
	err := repairTicketService.AuthorizeRepairTicket(ctx, repairTicketID, operatorID, operatorName)
	if err != nil {
		logger.Error("维修单授权失败", zap.Error(err))
		return err
	}

	logger.Info("维修单授权成功",
		zap.Uint("repairTicketID", repairTicketID))
	return nil
}

// SendRepairTicketWaitingAcceptNotificationActivity 发送维修单待接单通知的活动
func SendRepairTicketWaitingAcceptNotificationActivity(ctx context.Context, repairTicketID uint) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行发送维修单待接单通知活动", zap.Uint("repairTicketID", repairTicketID))

	if repairTicketRepoImpl == nil {
		return ErrActivityDependenciesNotInitialized
	}

	if feishuNotifier == nil {
		logger.Warn("飞书通知器未初始化，无法发送通知")
		return nil // 不阻塞工作流
	}

	// 获取维修单信息
	repairTicket, err := repairTicketRepoImpl.GetByID(ctx, repairTicketID)
	if err != nil {
		logger.Error("获取维修单信息失败", zap.Error(err))
		return err
	}

	// 获取关联的故障单信息
	var faultTicketNo string
	deviceSN := "未知设备"
	// 定义机柜、包间、机房信息的变量
	var cabinetName, roomName, dataCenterName string
	var reporterName = "未知"

	if repairTicket.FaultTicketID > 0 {
		// 获取故障单信息
		faultTicket, err := faultTicketRepoForRepair.GetByID(ctx, repairTicket.FaultTicketID)
		if err != nil {
			logger.Warn("获取关联故障单信息失败，使用默认设备SN",
				zap.Error(err),
				zap.Uint("faultTicketID", repairTicket.FaultTicketID))
		} else if faultTicket != nil {
			faultTicketNo = faultTicket.TicketNo
			deviceSN = faultTicket.DeviceSN
			reporterName = faultTicket.AssignedTo
			logger.Info("成功获取故障单信息",
				zap.String("deviceSN", deviceSN),
				zap.String("faultTicketNo", faultTicketNo),
				zap.Uint("faultTicketID", faultTicket.ID))

			// 获取设备位置信息
			if faultTicket.Resource.ID > 0 {
				// 机柜信息
				if faultTicket.Resource.Cabinet.ID > 0 {
					cabinetName = faultTicket.Resource.Cabinet.Name
					logger.Info("获取到机柜信息", zap.String("cabinetName", cabinetName))
				}

				// 包间信息
				if faultTicket.Resource.Room.ID > 0 {
					roomName = faultTicket.Resource.Room.Name
					logger.Info("获取到包间信息", zap.String("roomName", roomName))

					// 机房信息
					if faultTicket.Resource.Room.DataCenter.ID > 0 {
						dataCenterName = faultTicket.Resource.Room.DataCenter.Name
						logger.Info("获取到机房信息", zap.String("dataCenterName", dataCenterName))
					}
				}
			}
		}
	}

	// 根据设备SN获取项目信息
	var project string
	logger.Info("开始获取设备项目信息",
		zap.String("deviceSN", deviceSN),
		zap.Bool("deviceProjectServiceImpl_exists", deviceProjectServiceImpl != nil))

	if deviceProjectServiceImpl != nil && deviceSN != "未知设备" {
		projectInfo, err := deviceProjectServiceImpl.GetProjectBySN(ctx, deviceSN)
		if err != nil {
			logger.Warn("获取设备项目信息失败，使用默认webhook",
				zap.Error(err),
				zap.String("deviceSN", deviceSN))
		} else {
			project = projectInfo
			logger.Info("成功获取设备项目信息",
				zap.String("deviceSN", deviceSN),
				zap.String("project", project))
		}
	} else {
		logger.Warn("设备项目服务未初始化或设备SN无效",
			zap.Bool("deviceProjectServiceImpl_exists", deviceProjectServiceImpl != nil),
			zap.String("deviceSN", deviceSN))
	}

	// 根据项目获取维修单专用webhook配置
	webhookURL, secret := feishuNotifier.GetRepairProjectWebhookConfig(project)

	logger.Info("使用维修单项目专用webhook配置",
		zap.String("project", project),
		zap.String("webhookURL", webhookURL))

	// 发送飞书通知（使用项目专用的webhook配置）
	err = feishuNotifier.SendRepairTicketWaitingAcceptNotificationWithWebhook(
		fmt.Sprintf("%d", repairTicketID),
		repairTicket.TicketNo,
		deviceSN,
		faultTicketNo,
		reporterName,
		cabinetName,
		roomName,
		dataCenterName,
		webhookURL,
		secret,
	)

	if err != nil {
		logger.Error("发送待接单通知失败", zap.Error(err))
		// 不阻塞工作流
	} else {
		logger.Info("成功发送维修单待接单通知")
	}

	return nil
}

// SendRepairTicketWaitingVerificationNotificationActivity 发送维修单待验证通知的活动
func SendRepairTicketWaitingVerificationNotificationActivity(ctx context.Context, repairTicketID uint) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行发送维修单待验证通知活动", zap.Uint("repairTicketID", repairTicketID))

	if repairTicketRepoImpl == nil {
		return ErrActivityDependenciesNotInitialized
	}

	if feishuNotifier == nil {
		logger.Warn("飞书通知器未初始化，无法发送通知")
		return nil // 不阻塞工作流
	}

	// 获取维修单信息
	repairTicket, err := repairTicketRepoImpl.GetByID(ctx, repairTicketID)
	if err != nil {
		logger.Error("获取维修单信息失败", zap.Error(err))
		return err
	}

	// 获取关联的故障单信息
	var faultTicketNo string
	deviceSN := "未知设备"
	var acceptorID string
	var repairSolution, repairSteps string

	if repairTicket.FaultTicketID > 0 {
		// 获取故障单信息
		faultTicket, err := faultTicketRepoForRepair.GetByID(ctx, repairTicket.FaultTicketID)
		if err != nil {
			logger.Warn("获取关联故障单信息失败，使用默认设备SN",
				zap.Error(err),
				zap.Uint("faultTicketID", repairTicket.FaultTicketID))
		} else if faultTicket != nil {
			faultTicketNo = faultTicket.TicketNo
			deviceSN = faultTicket.DeviceSN
			logger.Info("成功获取故障单信息",
				zap.String("deviceSN", deviceSN),
				zap.String("faultTicketNo", faultTicketNo),
				zap.Uint("faultTicketID", faultTicket.ID))

			// 由于需求要求@报障单的接单人，但我们缺少直接获取接单人ID的方法
			// 最简单的方案是@报障单的创建人（ReporterID）
			if faultTicket.ReporterID > 0 {
				acceptorID = fmt.Sprintf("%d", faultTicket.ReporterID)
				logger.Info("使用故障单创建人作为通知@对象",
					zap.Uint("reporterID", faultTicket.ReporterID),
					zap.String("reporterName", faultTicket.ReporterName))
			} else {
				logger.Info("故障单创建人ID为0，将不包含@提醒")
			}
		}
	}

	// 获取维修结果信息
	repairSolution = repairTicket.Solution
	repairSteps = repairTicket.RepairSteps

	// 发送飞书通知
	var faultTicketIDStr string
	if repairTicket.FaultTicketID > 0 {
		faultTicketIDStr = fmt.Sprintf("%d", repairTicket.FaultTicketID)
	} else {
		// 如果没有关联的故障单ID，则使用维修单ID作为回退
		faultTicketIDStr = fmt.Sprintf("%d", repairTicketID)
		logger.Warn("维修单没有关联的故障单ID，使用维修单ID作为回退",
			zap.Uint("repairTicketID", repairTicketID))
	}

	err = feishuNotifier.SendRepairTicketWaitingVerificationNotification(
		faultTicketIDStr,
		repairTicket.TicketNo,
		deviceSN,
		faultTicketNo,
		acceptorID,
		repairSolution,
		repairSteps,
	)

	if err != nil {
		logger.Error("发送待验证通知失败", zap.Error(err))
		// 不阻塞工作流
	} else {
		logger.Info("成功发送维修单待验证通知")
	}

	return nil
}

// SendVerificationFailedNotification 发送验证不通过通知的活动
func SendVerificationFailedNotification(ctx context.Context, faultTicketID, repairTicketID, repairTicketNo, deviceSN, faultTicketNo, engineerName, verificationComments string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行发送验证不通过通知活动",
		zap.String("faultTicketID", faultTicketID),
		zap.String("repairTicketID", repairTicketID),
		zap.String("repairTicketNo", repairTicketNo),
		zap.String("deviceSN", deviceSN),
		zap.String("engineerName", engineerName))

	if feishuNotifier == nil {
		logger.Warn("飞书通知器未初始化，无法发送通知")
		return nil // 不阻塞工作流
	}

	// 获取机柜、包间、机房信息
	var cabinetName, roomName, dataCenterName string

	// 如果我们有故障单ID并且大于0，则尝试获取位置信息
	faultTicketIDUint, err := strconv.ParseUint(faultTicketID, 10, 32)
	if err == nil && faultTicketIDUint > 0 && faultTicketRepoForRepair != nil {
		faultTicket, err := faultTicketRepoForRepair.GetByID(ctx, uint(faultTicketIDUint))
		if err == nil && faultTicket != nil {
			logger.Info("成功获取故障单信息用于验证不通过通知",
				zap.String("deviceSN", faultTicket.DeviceSN),
				zap.Uint("faultTicketID", faultTicket.ID))

			// 获取设备位置信息
			if faultTicket.Resource.ID > 0 {
				// 机柜信息
				if faultTicket.Resource.Cabinet.ID > 0 {
					cabinetName = faultTicket.Resource.Cabinet.Name
					logger.Info("获取到机柜信息", zap.String("cabinetName", cabinetName))
				}

				// 包间信息
				if faultTicket.Resource.Room.ID > 0 {
					roomName = faultTicket.Resource.Room.Name
					logger.Info("获取到包间信息", zap.String("roomName", roomName))

					// 机房信息
					if faultTicket.Resource.Room.DataCenter.ID > 0 {
						dataCenterName = faultTicket.Resource.Room.DataCenter.Name
						logger.Info("获取到机房信息", zap.String("dataCenterName", dataCenterName))
					}
				}
			}
		}
	}

	// 发送飞书通知
	err = feishuNotifier.SendVerificationFailedNotification(
		faultTicketID,
		repairTicketID,
		repairTicketNo,
		deviceSN,
		faultTicketNo,
		engineerName,
		verificationComments,
		cabinetName,
		roomName,
		dataCenterName,
	)

	if err != nil {
		logger.Error("发送验证不通过通知失败", zap.Error(err))
		// 不阻塞工作流
	} else {
		logger.Info("成功发送验证不通过通知")
	}

	return nil
}

// DescribeWorkflowExecutionActivity 检查工作流实例是否仍在运行
func DescribeWorkflowExecutionActivity(ctx context.Context, workflowID string) (bool, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行检查工作流状态活动", zap.String("workflowID", workflowID))

	// 获取上下文中的temporal客户端
	temporalClient := ctx.Value("temporalClient")
	if temporalClient == nil {
		logger.Error("Temporal客户端未找到")
		// 如果无法获取客户端，假设工作流不存在
		return false, nil
	}

	// 类型断言，获取实际的client.Client
	client, ok := temporalClient.(client.Client)
	if !ok {
		logger.Error("Temporal客户端类型断言失败")
		// 如果类型断言失败，假设工作流不存在
		return false, nil
	}

	// 使用空RunID来获取最新的运行实例
	runID := ""

	// 尝试描述工作流执行状态
	_, err := client.DescribeWorkflowExecution(ctx, workflowID, runID)
	if err != nil {
		// 检查是否是"工作流不存在"错误
		if strings.Contains(err.Error(), "workflow execution not found") ||
			strings.Contains(err.Error(), "unknown external workflow execution") {
			logger.Info("工作流不再运行",
				zap.String("workflowID", workflowID),
				zap.Error(err))
			return false, nil
		}

		// 其他错误
		logger.Error("检查工作流状态失败",
			zap.Error(err),
			zap.String("workflowID", workflowID))
		// 出错时也返回false，表示不应该继续
		return false, nil
	}

	// 工作流存在且正在运行
	logger.Info("工作流正在运行", zap.String("workflowID", workflowID))
	return true, nil
}

// UpdateRepairTicketFields 更新维修单字段活动
func UpdateRepairTicketFields(ctx context.Context, repairTicketID uint, fields map[string]interface{}) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行更新维修单字段活动",
		zap.Uint("repairTicketID", repairTicketID),
		zap.Any("fields", fields))

	if repairTicketRepoImpl == nil {
		return ErrActivityDependenciesNotInitialized
	}

	// 更新维修单字段
	err := repairTicketRepoImpl.UpdateFields(ctx, repairTicketID, fields)
	if err != nil {
		logger.Error("更新维修单字段失败", zap.Error(err))
		return err
	}

	logger.Info("维修单字段更新成功",
		zap.Uint("repairTicketID", repairTicketID))
	return nil
}

// 错误定义已移至共享位置，此处不再重复定义
