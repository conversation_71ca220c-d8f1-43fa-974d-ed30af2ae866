package activities

import (
	"backend/internal/common/utils/notifier"
	inboundrepo "backend/internal/modules/cmdb/repository/inbound"
	inboundSvc "backend/internal/modules/cmdb/service/inbound"
	fileSvc "backend/internal/modules/file/service"
	importSvc "backend/internal/modules/import/service"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/repository"
	"context"
	"gorm.io/gorm"
)

type dismantledInboundActivities struct {
	db                *gorm.DB
	inboundSvc        inboundSvc.InboundService
	inboundRepo       inboundrepo.Repository
	inboundTicketRepo repository.InboundTicketRepository
	file              fileSvc.FileService
	importSvc         importSvc.ImportService
	inboundNotifier   *notifier.InboundNotifier
}

func (d *dismantledInboundActivities) SendInboundMsgToSecurityGuard(ctx context.Context, inboundNo string) error {
	//TODO implement me
	panic("implement me")
}

func (d *dismantledInboundActivities) UpdateInboundActivity(ctx context.Context, input common.UpdateInput) error {
	//TODO implement me
	panic("implement me")
}

func (d *dismantledInboundActivities) UpdateTicketAndHistory(ctx context.Context, input common.UpdateTicketInput) error {
	//TODO implement me
	panic("implement me")
}

func (d *dismantledInboundActivities) ProcessInboundActivity(ctx context.Context, inboundID uint, operatorID uint, location string, quantity int, productDetails string) error {
	//TODO implement me
	panic("implement me")
}

func (d *dismantledInboundActivities) UpdateInboundListStage(ctx context.Context, inboundNo, Stage string) error {
	//TODO implement me
	panic("implement me")
}

func (d *dismantledInboundActivities) SendInboundMsg(ctx context.Context, inboundNo string) error {
	//inbound, ticket, err := d.inboundRepo.GetDismantledInboundByNo(ctx, inboundNo)
	//if err != nil {
	//	return fmt.Errorf("获取飞书通知前置信息失败: %v", err)
	//}
	//err = d.inboundNotifier.SendInboundNotification(inbound, ticket)
	//if err != nil {
	//	return fmt.Errorf("发送飞书通知失败: %v", err)
	//}
	//if ticket.Stage == common.StageCompleteInbound {
	//	moduleType := fmt.Sprintf("%s-photo", inbound.InboundType())
	//	module, err := d.file.GetFilesByModule(moduleType, ticket.ID)
	//	if err != nil {
	//		return fmt.Errorf("获取验收单失败: %v", err)
	//	}
	//	err = d.inboundNotifier.SendInboundMsgToSecurityGuard(inbound, ticket, module[0].URL)
	//	if err != nil {
	//		return fmt.Errorf("发送信息到保安群失败: %v", err)
	//	}
	//	return nil
	//}
	return nil
}

func NewdismantledInboundActivities(db *gorm.DB, inboundRepo inboundrepo.Repository, inboundSvc inboundSvc.InboundService, inboundTicketRepo repository.InboundTicketRepository, file fileSvc.FileService, importSvc importSvc.ImportService, inboundNotifier *notifier.InboundNotifier) DismantleInboundActivity {
	return &dismantledInboundActivities{
		db:                db,
		inboundRepo:       inboundRepo,
		inboundSvc:        inboundSvc,
		inboundTicketRepo: inboundTicketRepo,
		file:              file,
		importSvc:         importSvc,
		inboundNotifier:   inboundNotifier,
	}
}
