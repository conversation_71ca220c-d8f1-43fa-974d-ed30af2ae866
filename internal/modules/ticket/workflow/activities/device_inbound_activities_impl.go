package activities

import (
	"backend/internal/common/utils/notifier"
	inboundrepo "backend/internal/modules/cmdb/repository/inbound"
	fileSvc "backend/internal/modules/file/service"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	ticketService "backend/internal/modules/ticket/service"
	"context"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strings"
)

type DeviceInboundActivities struct {
	db                *gorm.DB
	inboundSvc        ticketService.InboundTicketService
	inboundRepo       inboundrepo.Repository
	inboundTicketRepo repository.InboundTicketRepository
	fileSvc           fileSvc.FileService
	inboundNotifier   *notifier.InboundNotifier
}

func (d DeviceInboundActivities) SendInboundMsgToSecurityGuard(ctx context.Context, inboundNo string) error {
	var photoInfos []string
	ticket, err := d.inboundSvc.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		return err
	}
	details, err := d.inboundSvc.GetInboundDetailByNo(ctx, ticket.InboundType, inboundNo)
	if err != nil {
		return nil
	}
	files, err := d.fileSvc.GetFilesByModule("inbound-ticket-photo", ticket.ID)
	if err != nil {
		return fmt.Errorf("获取出入室图片失败: %v", err)
	}
	for i, file := range files {
		photoInfo := fmt.Sprintf("[图片%d](%s)", i+1, file.URL)
		photoInfos = append(photoInfos, photoInfo)
	}
	photo := strings.Join(photoInfos, "\n")
	err = d.inboundNotifier.SendInboundMsgToSecurityGuardV2(ticket, details, photo)
	if err != nil {
		return err
	}
	return nil
}

func (d DeviceInboundActivities) UpdateCMDB(ctx context.Context, inboundNo string) error {
	ticket, err := d.inboundSvc.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		return err
	}

	details, err := d.inboundSvc.GetInboundDetailByNo(ctx, ticket.InboundType, inboundNo)
	if err != nil {
		return err
	}

	err = d.inboundSvc.UpdateCMDB(ctx, ticket.InboundType, ticket.InboundReason, details)
	if err != nil {
		return err
	}
	return nil
}

func (d DeviceInboundActivities) UpdateInboundActivity(ctx context.Context, input common.UpdateInput) error {
	//TODO implement me
	panic("implement me")
}

func (d DeviceInboundActivities) UpdateTicketAndHistory(ctx context.Context, input common.UpdateTicketInput) error {
	// 获取入库工单信息
	logger.Info("更新入库工单信息：", zap.Any("UpdateInput", input))
	ticket, err := d.inboundTicketRepo.GetInboundTicketByNo(ctx, input.InboundNo)
	if err != nil {
		return fmt.Errorf("获取入库工单信息失败: %v", err)
	}

	// 更新工单状态
	ticket.Status = input.NextStatus
	ticket.Stage = input.Stage

	history := &model.InboundHistory{
		InboundTicketID: ticket.ID,
		InboundNo:       ticket.InboundNo,
		Stage:           input.CurrentStage,
		Status:          input.CurrentStatus,
		OperatorID:      input.OperatorID,
		OperatorName:    input.OperatorName,
		Comment:         input.Comments,
	}
	err = d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新工单
		err := tx.Model(&model.InboundTicket{}).Where("id = ?", input.InboundTicketID).Updates(ticket).Error
		if err != nil {
			return fmt.Errorf("更新整机设备入库工单状态失败，事务回滚: %v", err)
		}

		// 创建历史记录
		err = tx.Create(history).Error
		if err != nil {
			return fmt.Errorf("创建入库工单历史记录失败，事务回滚: %v", err)
		}
		return nil
	})
	logger.Info("更新工单及历史记录成功", zap.Any("TicketHistory", history), zap.Any("Ticket", ticket))
	if err != nil {
		return err
	}
	return nil
}

func (d DeviceInboundActivities) ProcessInboundActivity(ctx context.Context, inboundID uint, operatorID uint, location string, quantity int, productDetails string) error {
	//TODO implement me
	panic("implement me")
}

func (d DeviceInboundActivities) UpdateInboundListStage(ctx context.Context, inboundNo, Stage string) error {
	return d.inboundRepo.UpdateInboundListStage(ctx, inboundNo, Stage)
}

func (d DeviceInboundActivities) SendInboundMsg(ctx context.Context, inboundNo string) error {
	ticket, err := d.inboundSvc.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		return err
	}
	details, err := d.inboundSvc.GetInboundDetailByNo(ctx, ticket.InboundType, inboundNo)
	if err != nil {
		return err
	}
	history, err := d.inboundSvc.GetInboundHistoryByNo(ctx, ticket.InboundNo)
	if err != nil {
		return err
	}
	err = d.inboundNotifier.SendInboundNotificationV2(ticket, details, history)
	if err != nil {
		return err
	}
	return nil
}

func InitDeviceInboundActivities(db *gorm.DB, inboundRepo inboundrepo.Repository, inboundSvc ticketService.InboundTicketService, inboundTicketRepo repository.InboundTicketRepository, fileSvc fileSvc.FileService, inboundNotifier *notifier.InboundNotifier) *DeviceInboundActivities {
	return &DeviceInboundActivities{
		db:                db,
		inboundRepo:       inboundRepo,
		inboundSvc:        inboundSvc,
		inboundTicketRepo: inboundTicketRepo,
		fileSvc:           fileSvc,
		inboundNotifier:   inboundNotifier,
	}

}
