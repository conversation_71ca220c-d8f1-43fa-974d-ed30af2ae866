package activities

import (
	"backend/internal/modules/cmdb/model/inbound"
	"backend/internal/modules/ticket/common"
	"context"

	"gorm.io/gorm"
)

type InboundActivities interface {
	// 更新入库单
	UpdateInboundActivity(ctx context.Context, input common.UpdateInput) error

	// 更新入库工单及创建历史记录
	//UpdateTicketAndHistory(ctx context.Context, input workflow.UpdateInput) error
	UpdateTicketAndHistory(ctx context.Context, input common.UpdateTicketInput) error

	ProcessInboundActivity(ctx context.Context, inboundID uint, operatorID uint, location string, quantity int, productDetails string) error
	UpdateInboundListStage(ctx context.Context, inboundNo, Stage string) error
	SendInboundMsg(ctx context.Context, inboundNo string) error
	SendInboundMsgToSecurityGuard(ctx context.Context, inboundNo string) error
}

type PartiInboundActivity interface {
	// 嵌入 InboundActivities 接口
	InboundActivities
	ImportToCMDB(ctx context.Context, InboundNo string) error
}

type NewInboundActivity interface {
	// 嵌入 InboundActivities 接口
	InboundActivities
	// ImportToCMDB 将NewInboundDetails 的数据导入CMDB
	ImportToCMDB(ctx context.Context, InboundNo string) error
	// CompleteNewTicketActivity 结束新购入库工单
	CompleteNewTicketActivity(ctx context.Context, UpdateInput common.UpdateInput, CompleteInput common.NewInboundCompleteInput) error
}

type RepairInboundActivity interface {
	InboundActivities
	// 更新CMDB
	UpdateCMDBwithRepair(ctx context.Context, inboundNo string) error
	handleNormalRepair(ctx context.Context, repair inbound.RepairInboundDetails) error
	handleRenewRepair(ctx context.Context, repair inbound.RepairInboundDetails) error
	updateStock(tx *gorm.DB, productID uint, warehouseID uint, delta int) error
}

type DismantleInboundActivity interface {
	InboundActivities
}

type DeviceInboundActivity interface {
	InboundActivities
	UpdateCMDB(ctx context.Context, inboundNo string) error
}
