package activities

import (
	"backend/internal/common/utils/notifier"
	inboundrepo "backend/internal/modules/cmdb/repository/inbound"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"
	fileSvc "backend/internal/modules/file/service"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	ticketService "backend/internal/modules/ticket/service"
	"context"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strings"
)

// 过渡状态
//var trans = map[string]string{
//	constants.SourceTypeNewPurchase:    "新购",
//	constants.SourceTypeDismantled:     "拆机",
//	constants.SourceTypeReturnRepaired: "返修",
//}

type PartInboundActivities struct {
	db              *gorm.DB
	inboundSvc      ticketService.InboundTicketService
	inboundRepo     inboundrepo.Repository
	inventorySvc    inventorySvc.InventoryService
	fileSvc         fileSvc.FileService
	inboundNotifier *notifier.InboundNotifier
}

func (p PartInboundActivities) ImportToCMDB(ctx context.Context, InboundNo string) error {
	if InboundNo == "" {
		return fmt.Errorf("入库单号不能为空")
	}
	ticket, err := p.inboundSvc.GetInboundTicketByNo(ctx, InboundNo)
	if err != nil {
		return err
	}
	//info, err := p.inboundSvc.GetInboundInfoByNo(ctx, ticket.InboundType, InboundNo)
	//if err != nil {
	//	return err
	//}
	details, err := p.inboundSvc.GetInboundDetailByNo(ctx, ticket.InboundType, InboundNo)
	if err != nil {
		return err
	}

	err = p.inboundSvc.UpdateCMDB(ctx, ticket.InboundType, ticket.InboundReason, details)
	if err != nil {
		return err
	}
	return nil
}

func (p PartInboundActivities) UpdateInboundActivity(ctx context.Context, input common.UpdateInput) error {
	//TODO implement me
	panic("implement me")
}

func (p PartInboundActivities) UpdateTicketAndHistory(ctx context.Context, input common.UpdateTicketInput) error {
	if input.InboundNo == "" {
		return fmt.Errorf("入库单号不能为空")
	}
	// 获取入库工单信息
	logger.Info("更新入库工单信息：", zap.Any("UpdateInput", input))
	ticket, err := p.inboundSvc.GetInboundTicketByNo(ctx, input.InboundNo)
	if err != nil {
		return fmt.Errorf("获取入库工单信息失败: %v", err)
	}

	// 更新工单状态
	ticket.Status = input.NextStatus
	ticket.Stage = input.Stage

	history := &model.InboundHistory{
		InboundTicketID: ticket.ID,
		InboundNo:       ticket.InboundNo,
		Stage:           input.CurrentStage,
		Status:          input.CurrentStatus,
		OperatorID:      input.OperatorID,
		OperatorName:    input.OperatorName,
		Comment:         input.Comments,
	}
	err = p.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新工单
		err := tx.Model(&model.InboundTicket{}).Where("id = ?", input.InboundTicketID).Updates(ticket).Error
		if err != nil {
			return fmt.Errorf("更新整机设备入库工单状态失败，事务回滚: %v", err)
		}

		// 创建历史记录
		err = tx.Create(history).Error
		if err != nil {
			return fmt.Errorf("创建入库工单历史记录失败，事务回滚: %v", err)
		}
		return nil
	})
	logger.Info("更新工单及历史记录成功", zap.Any("TicketHistory", history), zap.Any("Ticket", ticket))
	if err != nil {
		return err
	}
	return nil
}

func (p PartInboundActivities) ProcessInboundActivity(ctx context.Context, inboundID uint, operatorID uint, location string, quantity int, productDetails string) error {
	//TODO implement me
	panic("implement me")
}

func (p PartInboundActivities) UpdateInboundListStage(ctx context.Context, inboundNo, Stage string) error {
	err := p.inboundRepo.UpdateInboundListStage(ctx, inboundNo, Stage)
	if err != nil {
		return err
	}
	return nil
}

func (p PartInboundActivities) SendInboundMsg(ctx context.Context, inboundNo string) error {
	ticket, err := p.inboundSvc.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		return err
	}
	details, err := p.inboundSvc.GetInboundDetailByNo(ctx, ticket.InboundType, inboundNo)
	if err != nil {
		return err
	}
	history, err := p.inboundSvc.GetInboundHistoryByNo(ctx, ticket.InboundNo)
	if err != nil {
		return err
	}
	err = p.inboundNotifier.SendInboundNotificationV2(ticket, details, history)
	if err != nil {
		return err
	}
	return nil
}

func (p PartInboundActivities) SendInboundMsgToSecurityGuard(ctx context.Context, inboundNo string) error {
	var photoInfos []string
	ticket, err := p.inboundSvc.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		return err
	}
	details, err := p.inboundSvc.GetInboundDetailByNo(ctx, ticket.InboundType, inboundNo)
	if err != nil {
		return nil
	}
	files, err := p.fileSvc.GetFilesByModule("inbound-ticket-photo", ticket.ID)
	if err != nil {
		return fmt.Errorf("获取出入室图片失败: %v", err)
	}
	for i, file := range files {
		photoInfo := fmt.Sprintf("[图片%d](%s)", i+1, file.URL)
		photoInfos = append(photoInfos, photoInfo)
	}
	photo := strings.Join(photoInfos, "\n")
	err = p.inboundNotifier.SendInboundMsgToSecurityGuardV2(ticket, details, photo)
	if err != nil {
		return err
	}
	return nil
}

func InitPartInboundActivities(db *gorm.DB, inboundSvc ticketService.InboundTicketService, inboundRepo inboundrepo.Repository, inventorySvc inventorySvc.InventoryService, fileSvc fileSvc.FileService, inboundNotifier *notifier.InboundNotifier) *PartInboundActivities {
	return &PartInboundActivities{
		db:              db,
		inboundSvc:      inboundSvc,
		inboundRepo:     inboundRepo,
		inventorySvc:    inventorySvc,
		fileSvc:         fileSvc,
		inboundNotifier: inboundNotifier,
	}
}
