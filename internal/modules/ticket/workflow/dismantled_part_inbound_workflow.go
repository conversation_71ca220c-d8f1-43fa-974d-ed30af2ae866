package workflow

import (
	"backend/internal/modules/ticket/common"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

type dismantledPartInboundState struct {
	InboundTicketID uint
	InboundNo       string
	CurrentStatus   string
	CurrentStage    string
	RequireApproval bool
	RequireVerified bool
	TryCount        uint
}

// DismantledPartInboundWorkflow 拆机配件入库单工作流
func DismantledPartInboundWorkflow(ctx workflow.Context, input common.DismantledInboundWorkflowInput) error {
	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: common.ActivityStartToCloseTimeout,
		HeartbeatTimeout:    common.ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        common.InitialInterval,
			BackoffCoefficient:     common.BackoffCoefficient,
			MaximumInterval:        common.MaximumInterval,
			MaximumAttempts:        common.MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)
	// 创建工作流日志
	logger := workflow.GetLogger(ctx)
	logger.Info("初始化DismantledPartInboundWorkflow工作流日志成功")
	logger.Info("输入信息为", "DismantledPartInput", input)

	repairState := dismantledPartInboundState{
		InboundTicketID: input.DismantledInboundTicketID,
		InboundNo:       input.InboundNo,
		RequireVerified: true,
		RequireApproval: true,
		CurrentStatus:   common.StatusWaitingAssetApproval,
		CurrentStage:    common.StageAssetApproval,
	}

	var isWorkflowComplete bool
	completeChan := make(chan struct{})

	// 发送通知
	err := workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
	if err != nil {
		logger.Error("发送飞书通知失败", "err", err)
	}

	// 终止流程
	isWorkflowComplete = true
	close(completeChan)

	// 后续拓展 目前不使用，投入使用需要进行特定配置修改
	// 注册 SetUpdateHandler ，接收更新信号
	err = workflow.SetUpdateHandler(ctx, common.DismantledPartInboundUpdateSignal, func(ctx workflow.Context, signal common.DismantledPartInboundSignal) error {
		logger.Info("收到UpdateHandler信号",
			"DismantledPartInboundTicketID", input.DismantledInboundTicketID,
			"status", signal.Status,
			"operator", signal.OperatorName)
		ctx = workflow.WithActivityOptions(ctx, ao)
		// 处理信号
		switch signal.Status {
		case common.StatusAssetApprovalPass:
			if !signal.RequiredVerify { // 不需要验收
				if err := workflow.ExecuteActivity(ctx, "UpdateCMDBwithRepair", input.InboundNo).Get(ctx, nil); err != nil {
					logger.Error("导入CMDB失败：", zap.Error(err))
				}
				// TODO 更新ticket 和history
				updateInput := common.UpdateTicketInput{
					InboundTicketID: input.DismantledInboundTicketID,
					InboundNo:       input.InboundNo,
					OperatorID:      signal.OperatorID,
					OperatorName:    signal.OperatorName,
					Comments:        signal.Comments,
					CurrentStage:    common.StageAssetApproval,
					CurrentStatus:   common.StatusAssetApprovalPass,
					Stage:           common.StageCompleteInbound,
					NextStatus:      common.StatusCompleted,
				}
				err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
				if err != nil {
					return err
				}

				// TODO 更新inboundList状态
				err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, "Complete").Get(ctx, nil)
				if err != nil {
					return err
				}

				// TODO 飞书通知

				// TODO 更新change_log
				// 终止流程
				isWorkflowComplete = true
				close(completeChan)
				break
			}
			// TODO 更新ticket和history
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.DismantledInboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageAssetApproval,
				CurrentStatus:   common.StatusAssetApprovalPass,
				Stage:           common.StageVerify,
				NextStatus:      common.StatusWaitingVerify,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// TODO 更新内部状态
			repairState.CurrentStatus = common.StatusWaitingVerify
			repairState.CurrentStage = common.StageVerify

		case common.StatusAssetApprovalFail:
			// TODO 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.DismantledInboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageAssetApproval,
				CurrentStatus:   common.StatusAssetApprovalFail,
				Stage:           common.StageRejected,
				NextStatus:      common.StatusCompleted,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// TODO 更新inboundList状态
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, "Fail").Get(ctx, nil)
			if err != nil {
				return err
			}
			// TODO 飞书通知

			// 终止流程
			isWorkflowComplete = true
			close(completeChan)

		case common.StatusVerificationPassed:
			// TODO 更新CMDB
			if err := workflow.ExecuteActivity(ctx, "UpdateCMDBwithRepair", input.InboundNo).Get(ctx, nil); err != nil {
				logger.Error("导入CMDB失败：", zap.Error(err))
			}

			// TODO 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.DismantledInboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageVerify,
				CurrentStatus:   common.StatusVerificationPassed,
				Stage:           common.StageCompleteInbound,
				NextStatus:      common.StatusCompleted,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// TODO 更新inboundList状态
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, "Complete").Get(ctx, nil)
			if err != nil {
				return err
			}

			// TODO 飞书通知

			// TODO 更新change_log

			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		case common.StatusVerificationFailed:
			// TODO 更新ticket 和history
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.DismantledInboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageVerify,
				CurrentStatus:   common.StatusVerificationFailed,
				Stage:           common.StageRejected,
				NextStatus:      common.StatusCompleted,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// TODO 更新inboundList状态
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, "Fail").Get(ctx, nil)
			if err != nil {
				return err
			}
			// TODO 飞书通知

			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		}
		return nil
	})
	if err != nil {
		logger.Error("SetUpdateHandler failed.", "error:", err, zap.Error(err))
	}

	// 使用 workflow.Await 等待条件满足
	err = workflow.Await(ctx, func() bool {
		select {
		case <-completeChan:
			return true
		default:
			return isWorkflowComplete
		}
	})

	if err != nil {
		logger.Error("Workflow await failed.", "error:", zap.Error(err))
		return err
	}
	return nil
}
