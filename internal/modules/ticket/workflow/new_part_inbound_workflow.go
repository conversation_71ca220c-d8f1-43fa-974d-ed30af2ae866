package workflow

import (
	"backend/internal/modules/ticket/common"
	"fmt"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

type newPartInboundState struct {
	InboundTicketID uint
	InboundNo       string
	CurrentStatus   string
	CurrentStage    string
	RequireApproval bool
	TryCount        uint
}

// NewPartInboundWorkflow 新购配件入库工作流
func NewPartInboundWorkflow(ctx workflow.Context, input common.NewPartInboundWorkflowInput) error {
	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: common.ActivityStartToCloseTimeout,
		HeartbeatTimeout:    common.ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        common.InitialInterval,
			BackoffCoefficient:     common.BackoffCoefficient,
			MaximumInterval:        common.MaximumInterval,
			MaximumAttempts:        common.MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)
	// 创建工作流日志
	logger := workflow.GetLogger(ctx)
	logger.Info("初始化NewPartInboundWorkflow工作流日志成功")
	logger.Info("输入信息为", input)

	// 创建工作流状态
	inboundWorkflowState := newPartInboundState{
		InboundTicketID: input.NewInboundTicketID,          // 新购入库单ID
		InboundNo:       input.InboundNo,                   // 新购入库单号
		CurrentStatus:   common.StatusWaitingAssetApproval, //初始：待审核
		CurrentStage:    common.StageAssetApproval,         //初始：资产管理员审核阶段
		TryCount:        1,                                 // 初始为第一次尝试
		RequireApproval: true,                              // 默认需要审核
	}

	// 发送飞书通知
	err := workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
	if err != nil {
		logger.Error("发送飞书通知失败", "err", err)
	}

	var isWorkflowComplete bool
	completeChan := make(chan struct{})
	// 注册 SetUpdateHandler ，接收更新信号
	err = workflow.SetUpdateHandler(ctx, common.NewInboundUpdateSignal, func(ctx workflow.Context, signal common.NewPartInboundSignal) error {
		logger.Info("收到UpdateHandler信号",
			"NewPartInboundTicketID", input.NewInboundTicketID,
			"status", signal.Status,
			"operator", signal.OperatorName)
		ctx = workflow.WithActivityOptions(ctx, ao)
		// 处理信号
		switch signal.Status {
		case common.StatusAssetApprovalPass: // 审核通过

			var updateInput common.UpdateTicketInput
			if !signal.RequiredVerify { //如果不需要验证
				err := workflow.ExecuteActivity(ctx, "ImportToCMDB", input.InboundNo).Get(ctx, nil)
				if err != nil {
					return err
				}

				// 更改流程状态
				updateInput = common.UpdateTicketInput{
					InboundTicketID:  input.NewInboundTicketID,
					InboundNo:        input.InboundNo,
					OperatorID:       signal.OperatorID,
					OperatorName:     signal.OperatorName,
					RequiredApproval: signal.RequiredApproval,
					Comments:         signal.Comments,
					CurrentStage:     common.StageAssetApproval,
					Stage:            common.StageCompleteInbound,
					CurrentStatus:    common.StatusAssetApprovalPass,
					NextStatus:       common.StatusCompleted,
				}
				// 更新 InboundList 的 stage 字段
				err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, common.StageCompleteInbound).Get(ctx, nil)
				if err != nil {
					return err
				}
				err = workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
				if err != nil {
					return err
				}

				// 飞书通知
				err = workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
				if err != nil {
					logger.Error("发送飞书通知失败", "err", err)
				}
				err = workflow.ExecuteActivity(ctx, "SendInboundMsgToSecurityGuard", input.InboundNo).Get(ctx, nil)
				if err != nil {
					logger.Error("发送飞书通知失败", "err", err)
				}

				// 终止流程
				isWorkflowComplete = true
				close(completeChan)
				break
			} else {
				// 更改流程状态
				updateInput = common.UpdateTicketInput{
					InboundTicketID:  input.NewInboundTicketID,
					InboundNo:        input.InboundNo,
					OperatorID:       signal.OperatorID,
					OperatorName:     signal.OperatorName,
					RequiredApproval: signal.RequiredApproval,
					Comments:         signal.Comments,
					CurrentStage:     common.StageAssetApproval,
					Stage:            common.StageVerify,
					CurrentStatus:    common.StatusAssetApprovalPass,
					NextStatus:       common.StatusWaitingVerify,
				}
			}

			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 通知现场工程师
			err = workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			// 更新 InboundList 的 stage 字段
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, common.StageVerify).Get(ctx, nil)
			if err != nil {
				return err
			}

			inboundWorkflowState.CurrentStatus = updateInput.NextStatus
			inboundWorkflowState.CurrentStage = updateInput.Stage
			logger.Info(input.InboundNo + "资产管理员审核通过")
		case common.StatusAssetApprovalFail: // 审核不通过
			// 修改流程状态
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.NewInboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageAssetApproval,
				CurrentStatus:   common.StatusAssetApprovalFail,
				Stage:           common.StageRejected,
				NextStatus:      common.StatusRejected,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 更新 InboundList 的 stage 字段
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, common.StageRejected).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 终止流程
			logger.Info(input.InboundNo + "资产管理员审核不通过")
			isWorkflowComplete = true
			close(completeChan)
		case common.StatusVerificationPassed: // 验证通过

			// 更新到库存
			err := workflow.ExecuteActivity(ctx, "ImportToCMDB", input.InboundNo).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 更改流程状态
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.NewInboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageVerify,
				Stage:           common.StageCompleteInbound,
				CurrentStatus:   common.StatusVerificationPassed,
				NextStatus:      common.StatusCompleted,
			}
			err = workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 更新 InboundList 的 stage 字段
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, common.StageCompleteInbound).Get(ctx, nil)
			if err != nil {
				return err
			}

			//  通知老板
			err = workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			err = workflow.ExecuteActivity(ctx, "SendInboundMsgToSecurityGuard", input.InboundNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}

			// 终止流程
			logger.Info(input.InboundNo + "验收通过")
			isWorkflowComplete = true
			close(completeChan)
		case common.StatusVerificationFailed: // 验证不通过
			// TODO 更改流程状态
			updateInput := common.UpdateTicketInput{
				InboundTicketID: input.NewInboundTicketID,
				InboundNo:       input.InboundNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common.StageVerify,
				Stage:           common.StageRejected,
				CurrentStatus:   common.StatusVerificationFailed,
				NextStatus:      common.StatusRejected,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 更新 InboundList 的 stage 字段
			err = workflow.ExecuteActivity(ctx, "UpdateInboundListStage", input.InboundNo, common.StageRejected).Get(ctx, nil)
			if err != nil {
				return err
			}
			logger.Info(input.InboundNo + "验收不通过")

			// 发送飞书通知
			err = workflow.ExecuteActivity(ctx, "SendInboundMsg", input.InboundNo).Get(ctx, nil)
			if err != nil {
				logger.Error("发送飞书通知失败", "err", err)
			}
			isWorkflowComplete = true
			close(completeChan)
		default:
			logger.Error("未知的工作流状态", "Status", signal.Status)
			return fmt.Errorf("未知的工作流阶段：%v", signal.Status)
		}

		return nil
	})

	if err != nil {
		logger.Error("SetUpdateHandler failed.", "error:", err, zap.Error(err))
	}

	// 使用 workflow.Await 等待条件满足
	err = workflow.Await(ctx, func() bool {
		select {
		case <-completeChan:
			return true
		default:
			return isWorkflowComplete
		}
	})

	if err != nil {
		logger.Error("Workflow await failed.", "error:", err, zap.Error(err))
		return err
	}
	return nil
}
