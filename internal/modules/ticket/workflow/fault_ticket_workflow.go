package workflow

import (
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"fmt"
	"strings"
	"time"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// FaultTicketWorkflow 报障单工作流
func FaultTicketWorkflow(ctx workflow.Context, input FaultTicketWorkflowInput) error {
	// 如果工作流已完成，直接返回
	if input.Completed {
		logger := workflow.GetLogger(ctx)
		logger.Info("工作流已完成，直接返回", "ticketID", input.TicketID)
		return nil
	}

	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: common.ActivityStartToCloseTimeout,
		HeartbeatTimeout:    common.ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        common.InitialInterval,
			BackoffCoefficient:     common.BackoffCoefficient,
			MaximumInterval:        common.MaximumInterval,
			MaximumAttempts:        common.MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	logger := workflow.GetLogger(ctx)
	logger.Info("FaultTicket workflow started", "ticketID", input.TicketID)

	// 创建工作流状态
	workflowState := struct {
		TicketID              uint
		CurrentStatus         string
		CurrentStage          string
		RepairType            string
		RepairTicketID        uint
		NeedsCustomerApproval bool
		DiagnosisResult       string
		ChildWorkflowRunning  bool
		ChildWorkflowSuccess  *bool
	}{
		TicketID:      input.TicketID,
		CurrentStatus: input.Status, // 使用输入中的状态
		CurrentStage:  "",           // 初始没有阶段
	}

	// 创建信号通道 - 这将是我们接收所有信号的主要通道
	signalChan := workflow.GetSignalChannel(ctx, common.WorkflowControlSignalName)

	// 根据初始状态设置当前阶段
	switch workflowState.CurrentStatus {
	case common.StatusWaitingAccept:
		workflowState.CurrentStage = common.StageAcceptTicket
	case common.StatusInvestigating:
		workflowState.CurrentStage = common.StageRepairSelection
	case common.StatusWaitingApproval:
		workflowState.CurrentStage = common.StageCustomerApproval
	case common.StatusApprovedWaiting:
		workflowState.CurrentStage = common.StageStartRepair
	case common.StatusRepairing, common.StatusRestarting, common.StatusMigrating, common.StatusSoftwareFixing:
		workflowState.CurrentStage = common.StageCompleteRepair
	case common.StatusWaitingVerification:
		workflowState.CurrentStage = common.StageCompleteVerification
	case common.StatusSummarizing:
		workflowState.CurrentStage = common.StageSummary
	}

	// 工作流主循环 - 完全基于信号驱动
	for {
		// 如果当前状态是cancelled，确保count_in_sla为false并结束工作流
		if workflowState.CurrentStatus == common.StatusCancelled {
			logger.Info("工单已处于已取消状态，确保不计入SLA并结束工作流",
				"ticketID", input.TicketID,
				"currentStatus", workflowState.CurrentStatus)

			// 调用专门的活动确保count_in_sla设置为false
			err := workflow.ExecuteActivity(ctx, "UpdateCancelledTicketSLAActivity", input.TicketID).Get(ctx, nil)
			if err != nil {
				logger.Error("确保已取消工单不计入SLA失败", "error", err)
				// 错误不阻止工作流结束
			}

			return nil
		}

		// 等待下一个信号
		logger.Info("等待工作流控制信号",
			"ticketID", input.TicketID,
			"currentStage", workflowState.CurrentStage,
			"currentStatus", workflowState.CurrentStatus)

		// 无限期等待信号
		var signal common.WorkflowControlSignal
		signalChan.Receive(ctx, &signal)

		logger.Info("收到工作流控制信号",
			"ticketID", input.TicketID,
			"stage", signal.Stage,
			"operator", signal.OperatorName)

		// 处理信号
		switch signal.Stage {
		case common.StageAcceptTicket:
			// 处理接单信号
			err := handleAcceptTicket(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理接单失败", "error", err)
				continue // 继续等待下一个信号
			}

		case common.StageRepairSelection:
			// 处理维修选择信号
			err := handleRepairSelection(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理维修选择失败", "error", err)
				continue
			}

		case common.StageCustomerApproval:
			// 处理客户审批信号
			err := handleCustomerApproval(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理客户审批失败", "error", err)
				continue
			}

		case common.StageStartRepair:
			// 处理开始维修信号
			err := handleStartRepair(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理开始维修失败", "error", err)
				continue
			}

		case common.StageCompleteRepair:
			// 处理完成维修信号
			err := handleCompleteRepair(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理完成维修失败", "error", err)
				continue
			}

		case common.StageStartVerification:
			// 处理开始验证信号
			err := handleStartVerification(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理开始验证失败", "error", err)
				continue
			}

		case common.StageCompleteVerification:
			// 处理完成验证信号
			err := handleCompleteVerification(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理完成验证失败", "error", err)
				continue
			}

		case common.StageSummary:
			// 处理总结信号
			err := handleSummary(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理总结失败", "error", err)
				continue
			}
			// 工单已完成，结束工作流主循环
			logger.Info("工单已完成(总结)，结束工作流", "ticketID", input.TicketID)
			return nil

		case common.StageCompleteTicket:
			// 处理完成工单信号
			err := handleCompleteTicket(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理完成工单失败", "error", err)
				continue
			}
			// 工单已完成，结束工作流主循环
			logger.Info("工单已完成，结束工作流", "ticketID", input.TicketID)
			return nil

		case common.StageCancelTicket:
			// 处理取消工单信号
			err := handleCancelTicket(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理取消工单失败", "error", err)
				continue
			}
			// 工单已取消，结束工作流主循环
			logger.Info("工单已取消，结束工作流", "ticketID", input.TicketID)
			return nil

		case common.StatusRepairFailed:
			workflowState.CurrentStage = common.StageRepairSelection

		default:
			logger.Error("未知的工作流阶段", "stage", signal.Stage)
		}

		// 检查工作流是否已完成，如果状态是completed或cancelled，则结束工作流
		if workflowState.CurrentStatus == common.StatusCompleted ||
			workflowState.CurrentStatus == common.StatusCancelled {
			logger.Info("工单状态已变更为已完成或已取消，结束工作流",
				"ticketID", input.TicketID,
				"status", workflowState.CurrentStatus)
			return nil
		}

	}

}

// handleAcceptTicket 处理接单信号
func handleAcceptTicket(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)

	// 先更新工作流内部状态为排查中
	state.CurrentStatus = common.StatusInvestigating
	state.CurrentStage = common.StageRepairSelection

	// 获取故障单信息，找到设备SN和故障来源
	var faultTicket *model.FaultTicket
	if err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket); err != nil {
		logger.Error("获取故障单信息失败", "error", err)
		// 获取故障单失败不应阻塞工作流主流程
	} else if faultTicket != nil && faultTicket.DeviceSN != "" {
		// 更新设备状态
		err := workflow.ExecuteActivity(ctx, "UpdateDeviceStatusActivity",
			faultTicket.DeviceSN,
			faultTicket.Source,
			"accept_to_investigating").Get(ctx, nil)

		if err != nil {
			logger.Error("更新设备状态失败", "error", err, "deviceSN", faultTicket.DeviceSN)
			// 更新设备状态失败不应阻塞工作流主流程
		} else {
			logger.Info("已更新设备状态", "deviceSN", faultTicket.DeviceSN, "source", faultTicket.Source)
		}
	}

	// 再执行数据库状态更新活动
	err := workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
		ticketID, common.StatusInvestigating, signal.OperatorID, signal.Comments).Get(ctx, nil)
	//err := workflow.ExecuteActivity(ctx, activities.UpdateFaultTicketStatusActivity,
	//	ticketID, common.StatusInvestigating, signal.OperatorID, signal.Comments).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	return nil
}

// handleRepairSelection 处理维修选择信号
func handleRepairSelection(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)

	// 解析维修选择数据
	var repairSelection common.RepairSelectionInput
	if err := workflow.ExecuteActivity(ctx, "GetRepairSelectionActivity", ticketID).Get(ctx, &repairSelection); err != nil {
		return fmt.Errorf("获取维修选择失败: %w", err)
	}

	// 先只更新工作流内部状态，不更新数据库
	state.RepairType = repairSelection.RepairType
	state.DiagnosisResult = repairSelection.Diagnosis

	// 从信号数据中获取是否需要客户审批
	state.NeedsCustomerApproval = true // 默认为true
	if signal.Data != nil {
		if val, ok := signal.Data["require_customer_approval"]; ok {
			if boolVal, isBool := val.(bool); isBool {
				state.NeedsCustomerApproval = boolVal
				logger.Info("从信号数据获取到客户审批标志",
					"requireApproval", state.NeedsCustomerApproval,
					"ticketID", ticketID)
			}
		}
	}

	// 委托给CreateRepairTicketActivity处理维修单创建
	// 它会根据维修类型决定是否创建维修单，并会更新故障单的维修方法
	var repairTicketID uint
	err := workflow.ExecuteActivity(ctx, "CreateRepairTicketActivity",
		ticketID, repairSelection.RepairType).Get(ctx, &repairTicketID)
	if err != nil {
		logger.Error("维修单处理失败，工作流将保持当前状态", "error", err)
		return fmt.Errorf("维修单处理失败: %w", err)
	}

	// 如果返回了维修单ID（不为0），则需要启动维修子工作流
	if repairTicketID > 0 {
		logger.Info("成功创建维修单，准备启动维修子工作流",
			"repairTicketID", repairTicketID,
			"faultTicketID", ticketID)

		// 启动维修子工作流
		cwo := workflow.ChildWorkflowOptions{
			WorkflowID: fmt.Sprintf("repair_ticket_%d", repairTicketID),
			TaskQueue:  common.RepairTicketTaskQueue,
		}
		childCtx := workflow.WithChildOptions(ctx, cwo)

		// 更新状态显示子工作流正在运行
		state.ChildWorkflowRunning = true
		state.RepairTicketID = repairTicketID

		// 执行子工作流
		childFuture := workflow.ExecuteChildWorkflow(childCtx, "RepairWorkflow", repairTicketID)

		logger.Info("启动维修工作流",
			"faultTicketID", ticketID,
			"repairTicketID", repairTicketID,
			"workflowID", fmt.Sprintf("repair_ticket_%d", repairTicketID))

		// 注册回调处理子工作流完成
		workflow.Go(ctx, func(ctx workflow.Context) {
			err := childFuture.Get(ctx, nil)
			success := err == nil
			state.ChildWorkflowSuccess = &success
			state.ChildWorkflowRunning = false

			logger.Info("子工作流执行完成",
				"faultTicketID", ticketID,
				"repairTicketID", repairTicketID,
				"success", success,
				"error", err)
		})
	} else {
		logger.Info("当前维修类型不需要创建维修单，继续故障工单流程",
			"ticketID", ticketID,
			"repairType", repairSelection.RepairType)
	}

	// 修改工作流状态
	if state.NeedsCustomerApproval {
		state.CurrentStage = common.StageCustomerApproval
		state.CurrentStatus = common.StatusWaitingApproval

		logger.Info("需要客户审批，设置工单状态为等待审批",
			"ticketID", ticketID,
			"requireApproval", state.NeedsCustomerApproval,
			"newStatus", common.StatusWaitingApproval)

		// 更新数据库状态
		err := workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
			ticketID, common.StatusWaitingApproval, signal.OperatorID, signal.Comments).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		// 发送确认通知
		err = workflow.ExecuteActivity(ctx, "SendNotificationActivity",
			ticketID, "pending_approval", state.DiagnosisResult).Get(ctx, nil)
		if err != nil {
			logger.Warn("发送通知失败", "error", err)
			// 继续执行，通知失败不阻塞工作流
		}
	} else {
		// 如果不需要客户审批，直接进入已批准等待状态
		state.CurrentStage = common.StageStartRepair
		state.CurrentStatus = common.StatusApprovedWaiting

		// 获取故障单信息，检查来源
		var faultTicket *model.FaultTicket
		var shouldSetCountInSLAFalse = true // 默认设为false

		if err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket); err != nil {
			logger.Error("获取故障单信息失败", "error", err)
			// 出错时仍设置count_in_sla为false
		} else if faultTicket != nil {
			// 如果来源为客户报障，则保持count_in_sla为true（无论是否需要客户审批）
			if faultTicket.Source == "customer" {
				shouldSetCountInSLAFalse = false
				logger.Info("客户报障来源，保持count_in_sla为true",
					"ticketID", ticketID,
					"source", faultTicket.Source,
					"requireApproval", faultTicket.RequireApproval)
			}
		}

		logger.Info("无需客户审批，设置工单状态为已批准等待操作",
			"ticketID", ticketID,
			"requireApproval", state.NeedsCustomerApproval,
			"setCountInSLAFalse", shouldSetCountInSLAFalse,
			"newStatus", common.StatusApprovedWaiting)

		// 根据判断结果决定是否更新count_in_sla
		if shouldSetCountInSLAFalse {
			err := workflow.ExecuteActivity(ctx, "UpdateFieldsActivity",
				ticketID, map[string]interface{}{"count_in_sla": false}).Get(ctx, nil)
			if err != nil {
				logger.Warn("更新count_in_sla字段失败，但继续流程", "error", err)
				// 继续执行，不中断工作流
			}
		}

		// 更新数据库状态
		err = workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
			ticketID, common.StatusApprovedWaiting, signal.OperatorID, signal.Comments).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		logger.Info("工单无需客户审批，直接进入已批准等待状态",
			"ticketID", ticketID,
			"operatorID", signal.OperatorID,
			"operatorName", signal.OperatorName)
	}

	return nil
}

// handleCustomerApproval 处理客户审批信号
func handleCustomerApproval(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)

	// 解析审批结果
	var approvalResult common.CustomerApprovalResult
	err := workflow.ExecuteActivity(ctx, "GetCustomerApprovalActivity", ticketID).Get(ctx, &approvalResult)
	if err != nil {
		logger.Error("获取客户审批结果失败", "error", err)
		return fmt.Errorf("获取客户审批结果失败: %w", err)
	}

	// 确保使用有效的ENUM类型
	// 数据库支持的枚举值: restart, hardware_fix, board_repair, cold_migration
	var validEnumType string
	normalizedType := strings.ToLower(strings.TrimSpace(state.RepairType))

	// 映射到有效的枚举值
	enumMapping := map[string]string{
		// 数据库支持的标准枚举值
		"restart":        "restart",
		"hardware_fix":   "hardware_fix",
		"cold_migration": "cold_migration",

		// 常见映射
		"hardware_replace": "hardware_fix",
		"software_fix":     "software_fix",
	}

	// 查找映射值
	if mappedType, exists := enumMapping[normalizedType]; exists {
		validEnumType = mappedType
	} else {
		// 关键词匹配
		if strings.Contains(normalizedType, "hardware") {
			validEnumType = "hardware_fix"
		} else if strings.Contains(normalizedType, "migrat") {
			validEnumType = "cold_migration"
		} else {
			// 默认安全值
			validEnumType = "hardware_fix"
		}
	}

	// 使用硬编码字符串确保绝对匹配
	var finalEnumValue string
	switch validEnumType {
	case "restart":
		finalEnumValue = "restart"
	case "hardware_fix":
		finalEnumValue = "hardware_fix"
	case "board_repair":
		finalEnumValue = "board_repair"
	case "cold_migration":
		finalEnumValue = "cold_migration"
	default:
		finalEnumValue = "hardware_fix"
	}

	// 检查审批状态
	switch approvalResult.Status {
	case "approved":
		logger.Info("客户已批准维修方案", "ticketID", ticketID, "维修类型", finalEnumValue)

		// 获取故障单信息
		var faultTicket *model.FaultTicket
		if err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket); err != nil {
			logger.Error("获取故障单信息失败", "error", err)
			// 获取故障单失败不应阻塞工作流主流程
		} else if faultTicket != nil && faultTicket.DeviceSN != "" && faultTicket.Source != "customer" {
			// 更新设备状态 - 非客户来源的故障，在审批通过后更新业务状态
			err := workflow.ExecuteActivity(ctx, "UpdateDeviceStatusActivity",
				faultTicket.DeviceSN,
				faultTicket.Source,
				"customer_approved").Get(ctx, nil)

			if err != nil {
				logger.Error("更新设备状态失败", "error", err, "deviceSN", faultTicket.DeviceSN)
				// 更新设备状态失败不应阻塞工作流主流程
			} else {
				logger.Info("客户已授权，已更新设备状态", "deviceSN", faultTicket.DeviceSN)
			}
		}

		// 根据维修类型选择不同的路径
		// 注意：如果已经创建了维修单，就不需要再创建了
		if state.RepairTicketID == 0 {
			logger.Info("在客户审批后检查是否需要创建维修单", "ticketID", ticketID, "维修类型", state.RepairType)

			// 使用原始的维修类型检查是否需要创建维修单，而不是硬编码检查硬件类型
			// CreateRepairTicketActivity函数内部会根据维修类型决定是否创建维修单
			var repairTicketID uint
			err := workflow.ExecuteActivity(ctx, "CreateRepairTicketActivity", ticketID, state.RepairType).Get(ctx, &repairTicketID)
			if err != nil {
				logger.Error("处理维修单失败", "error", err)
				// 记录错误但不阻止流程继续进行
				repairTicketID = 0
			}

			if repairTicketID > 0 {
				state.RepairTicketID = repairTicketID
				logger.Info("成功创建维修单",
					"repairTicketID", repairTicketID,
					"类型", state.RepairType)

				// 注意：不在此处启动维修子工作流，等待StartRepair信号后再启动
			}
		}

		// 明确设置为等待操作状态
		state.CurrentStage = common.StageStartRepair
		state.CurrentStatus = common.StatusApprovedWaiting

		// 日志记录此状态变更
		logger.Info("客户已批准，工单状态变更为等待手动启动维修",
			"ticketID", ticketID,
			"newStatus", common.StatusApprovedWaiting)

		// 更新数据库状态
		err = workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
			ticketID, common.StatusApprovedWaiting, signal.OperatorID, "客户已批准维修方案，等待开始维修").Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		return nil
	case "rejected":
		// 客户拒绝，回到维修选择阶段
		logger.Info("客户拒绝维修方案，回到维修选择阶段", "ticketID", ticketID)
		state.CurrentStage = common.StageRepairSelection
		state.CurrentStatus = common.StatusInvestigating

		// 更新数据库状态
		err = workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
			ticketID, state.CurrentStatus, signal.OperatorID, signal.Comments).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		return nil
	default:
		// 审批状态未知，保持当前状态
		logger.Warn("未知的审批状态", "status", approvalResult.Status)
		return fmt.Errorf("未知的审批状态: %s", approvalResult.Status)
	}
}

// handleStartRepair 处理开始维修信号
func handleStartRepair(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)

	// 如果子工作流已经完成，则直接更新状态
	if state.ChildWorkflowSuccess != nil {
		if *state.ChildWorkflowSuccess {
			logger.Info("子工作流已完成，设置状态为等待验证")
			state.CurrentStatus = common.StatusWaitingVerification
			state.CurrentStage = common.StageStartVerification
			err := workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
				ticketID, state.CurrentStatus, signal.OperatorID, "维修完成，等待验证").Get(ctx, nil)
			if err != nil {
				return fmt.Errorf("更新工单状态失败: %w", err)
			}
		} else {
			logger.Info("子工作流执行失败，重新进入维修选择阶段")
			state.CurrentStatus = common.StatusRepairFailed
			state.CurrentStage = common.StageRepairSelection
			err := workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
				ticketID, state.CurrentStatus, signal.OperatorID, "维修失败，需重新选择维修方式").Get(ctx, nil)
			if err != nil {
				return fmt.Errorf("更新工单状态失败: %w", err)
			}
		}
		return nil
	}

	// 检查当前状态是否为approved_waiting_action
	if state.CurrentStatus != common.StatusApprovedWaiting {
		logger.Warn("当前状态不是等待开始维修，无法启动维修流程",
			"currentStatus", state.CurrentStatus)
		return fmt.Errorf("当前状态不是%s，无法开始维修", common.StatusApprovedWaiting)
	}

	// 检查是否需要返回到排查中状态（特别是冷迁移情况）
	if state.RepairType == "cold_migration" && signal.Data != nil {
		// 检查信号数据中是否有返回排查标志
		if returnToInvestigate, ok := signal.Data["return_to_investigate"].(bool); ok && returnToInvestigate {
			logger.Info("冷迁移情况下返回到排查中状态",
				"ticketID", ticketID,
				"from", state.CurrentStatus,
				"to", common.StatusInvestigating)

			state.CurrentStatus = common.StatusInvestigating
			state.CurrentStage = common.StageRepairSelection

			err := workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
				ticketID, state.CurrentStatus, signal.OperatorID, "冷迁移取消，返回排查状态").Get(ctx, nil)
			if err != nil {
				return fmt.Errorf("更新工单状态失败: %w", err)
			}

			return nil
		}
	}

	// 先更新工作流内部状态
	switch state.RepairType {
	case "restart", "reboot":
		state.CurrentStatus = common.StatusRestarting
	case "hardware_fix", "hardware_replace", "software_fix", "hardware":
		state.CurrentStatus = common.StatusRepairing
	case "cold_migration":
		state.CurrentStatus = common.StatusMigrating
	default:
		// 默认使用维修中状态
		state.CurrentStatus = common.StatusRepairing
	}
	state.CurrentStage = common.StageCompleteRepair

	// 获取故障单信息
	var faultTicket *model.FaultTicket
	if err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket); err != nil {
		logger.Error("获取故障单信息失败", "error", err)
		// 获取故障单失败不应阻塞工作流主流程
	} else if faultTicket != nil && faultTicket.DeviceSN != "" {
		// 检查维修类型是否为冷迁移，如果是，则获取备机SN并进行处理
		if state.RepairType == "cold_migration" {
			// 从信号数据中获取备机SN
			var backupDeviceSN string
			if signal.Data != nil {
				if bdsn, ok := signal.Data["backup_device_sn"].(string); ok && bdsn != "" {
					backupDeviceSN = bdsn
					logger.Info("冷迁移：从信号中获取到备机SN",
						"backupDeviceSN", backupDeviceSN,
						"faultDeviceSN", faultTicket.DeviceSN)
				}
			}

			// 如果获取到备机SN，调用冷迁移处理活动
			if backupDeviceSN != "" {
				err := workflow.ExecuteActivity(ctx, "ProcessColdMigrationActivity",
					faultTicket.DeviceSN, backupDeviceSN).Get(ctx, nil)
				if err != nil {
					logger.Error("冷迁移处理失败",
						"error", err,
						"faultDeviceSN", faultTicket.DeviceSN,
						"backupDeviceSN", backupDeviceSN)
					// 处理失败不阻断工作流
				} else {
					logger.Info("冷迁移处理成功",
						"faultDeviceSN", faultTicket.DeviceSN,
						"backupDeviceSN", backupDeviceSN)
				}
			} else {
				logger.Warn("冷迁移：未提供备机SN，将跳过冷迁移处理",
					"faultDeviceSN", faultTicket.DeviceSN)
			}
		} else {
			// 非冷迁移情况，执行标准的设备状态更新
			// 开始维修时，将资产状态更新为维修中
			err := workflow.ExecuteActivity(ctx, "UpdateDeviceStatusActivity",
				faultTicket.DeviceSN,
				faultTicket.Source,
				"start_repairing").Get(ctx, nil)

			if err != nil {
				logger.Error("更新设备资产状态失败", "error", err, "deviceSN", faultTicket.DeviceSN)
				// 更新设备状态失败不应阻塞工作流主流程
			} else {
				logger.Info("维修开始，已更新设备资产状态为维修中", "deviceSN", faultTicket.DeviceSN)
			}
		}
	}

	// 如果存在关联的维修单ID，向维修单工作流发送授权信号
	if state.RepairTicketID > 0 {
		logger.Info("向维修单工作流发送授权信号",
			"faultTicketID", ticketID,
			"repairTicketID", state.RepairTicketID)

		// 构造维修单工作流ID
		repairWorkflowID := fmt.Sprintf("%s%d", common.RepairTicketWorkflowIDPrefix, state.RepairTicketID)

		// 准备操作人信息
		operatorName := signal.OperatorName

		// 如果操作人姓名为空或是默认格式（如"用户5"），设置为特定标识
		if operatorName == "" || strings.HasPrefix(operatorName, "用户") {
			// 使用特定标识，清晰表明这是从故障单发起的授权
			operatorName = fmt.Sprintf("故障单授权人(ID:%d)", signal.OperatorID)
		}

		// 发送授权信号到维修单工作流
		signalErr := workflow.SignalExternalWorkflow(ctx, repairWorkflowID, "", common.WorkflowControlSignalName, common.WorkflowControlSignal{
			Stage:        common.StageAuthorize,
			OperatorID:   signal.OperatorID,
			OperatorName: operatorName,
			Comments:     "故障单客户已批准维修方案，自动授权维修单",
			Data: map[string]interface{}{
				"fault_ticket_id":   ticketID,
				"from_fault_ticket": true, // 添加标记，表明是从故障单发起的操作
			},
		}).Get(ctx, nil)

		if signalErr != nil {
			logger.Error("向维修单工作流发送授权信号失败",
				"error", signalErr,
				"repairTicketID", state.RepairTicketID)
			// 不阻断主工作流，仅记录错误
		} else {
			logger.Info("成功向维修单工作流发送授权信号",
				"repairTicketID", state.RepairTicketID,
				"operatorName", operatorName)
		}
	}

	// 更新数据库状态
	err := workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
		ticketID, state.CurrentStatus, signal.OperatorID, signal.Comments).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	if state.RepairTicketID > 0 {
		// 在开始维修阶段发送维修单待接单通知
		notifyErr := workflow.ExecuteActivity(ctx, "SendRepairTicketWaitingAcceptNotificationActivity", state.RepairTicketID).Get(ctx, nil)
		if notifyErr != nil {
			logger.Warn("发送维修单待接单通知失败",
				"error", notifyErr,
				"repairTicketID", state.RepairTicketID)
			// 通知失败不影响主工作流
		} else {
			logger.Info("成功发送维修单待接单通知",
				"repairTicketID", state.RepairTicketID)
		}

		if !state.ChildWorkflowRunning {
			logger.Info("开始启动维修单子工作流",
				"ticketID", ticketID,
				"repairTicketID", state.RepairTicketID,
				"repairType", state.RepairType)

			// 启动维修子工作流
			cwo := workflow.ChildWorkflowOptions{
				WorkflowID: fmt.Sprintf("repair_ticket_%d", state.RepairTicketID),
				TaskQueue:  common.RepairTicketTaskQueue,
			}
			childCtx := workflow.WithChildOptions(ctx, cwo)

			// 更新状态显示子工作流正在运行
			state.ChildWorkflowRunning = true

			// 执行子工作流
			childFuture := workflow.ExecuteChildWorkflow(childCtx, "RepairWorkflow", state.RepairTicketID)

			logger.Info("启动维修工作流",
				"faultTicketID", ticketID,
				"repairTicketID", state.RepairTicketID,
				"workflowID", fmt.Sprintf("repair_ticket_%d", state.RepairTicketID))

			// 注册回调处理子工作流完成
			workflow.Go(ctx, func(ctx workflow.Context) {
				err := childFuture.Get(ctx, nil)
				success := err == nil
				state.ChildWorkflowSuccess = &success
				state.ChildWorkflowRunning = false

				logger.Info("子工作流执行完成",
					"faultTicketID", ticketID,
					"repairTicketID", state.RepairTicketID,
					"success", success,
					"error", err)

				// 子工作流结束后不自动流转状态，由子工作流通过信号触发状态流转
			})
		} else if state.RepairTicketID == 0 {
			logger.Warn("没有关联的维修单ID，无法启动子工作流", "ticketID", ticketID)
		} else if state.ChildWorkflowRunning {
			logger.Info("子工作流已经在运行中", "ticketID", ticketID, "repairTicketID", state.RepairTicketID)
		}
	} else if state.RepairTicketID == 0 {
		logger.Warn("没有关联的维修单ID，无法启动子工作流", "ticketID", ticketID)
	}

	return nil
}

// handleCompleteRepair 处理完成维修信号
func handleCompleteRepair(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("收到维修完成信号",
		"ticketID", ticketID,
		"signal", signal.Stage,
		"data", fmt.Sprintf("%+v", signal.Data))

	// 从信号数据中提取维修结果
	success := true // 默认成功
	var solution, repairSteps string

	if signal.Data != nil {
		// 检查是否成功
		if successVal, ok := signal.Data["success"].(bool); ok {
			success = successVal
		}

		// 提取解决方案
		if sol, ok := signal.Data["solution"].(string); ok {
			solution = sol
		}

		// 提取维修步骤
		if steps, ok := signal.Data["repair_steps"].(string); ok {
			repairSteps = steps
		}

		// 记录子工作流的ID
		if repairID, ok := signal.Data["repair_ticket_id"].(float64); ok {
			state.RepairTicketID = uint(repairID)
		}

		// 确保维修类型信息正确传递，如果信号中包含则更新状态中的维修类型
		if repairType, ok := signal.Data["repair_type"].(string); ok && repairType != "" {
			state.RepairType = repairType
			logger.Info("从信号中更新维修类型",
				"ticketID", ticketID,
				"repairType", repairType)
		}

		// 如果是冷迁移类型，保存备机SN到验证阶段
		if state.RepairType == "cold_migration" {
			// 从信号数据中获取备机SN
			if backupSN, ok := signal.Data["backup_device_sn"].(string); ok && backupSN != "" {
				// 保持备机SN在信号数据中，确保在验证阶段可以使用
				logger.Info("冷迁移完成: 保存备机SN到验证阶段",
					"ticketID", ticketID,
					"backupDeviceSN", backupSN)
			} else {
				// 尝试从故障单获取相关信息
				var faultTicket *model.FaultTicket
				if err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket); err == nil && faultTicket != nil {
					// 从故障单关联的冷迁移记录中获取备机SN
					if faultTicket.DeviceSN != "" {
						logger.Info("冷迁移完成: 尝试查找冷迁移记录获取备机SN",
							"ticketID", ticketID,
							"faultDeviceSN", faultTicket.DeviceSN)
						// TODO: 这里需要考虑如何获取备机SN，可能需要扩展Activity支持
					}
				}
			}
		}
	}

	// 更新子工作流状态
	state.ChildWorkflowRunning = false
	childSuccess := success
	state.ChildWorkflowSuccess = &childSuccess

	// 构建维修结果描述
	resultDescription := "维修失败: "
	if success {
		resultDescription = "维修成功: "
	}
	resultDescription += solution

	// 先更新工作流内部状态
	state.CurrentStatus = common.StatusWaitingVerification
	state.CurrentStage = common.StageStartVerification

	// 记录维修结果到活动中
	err := workflow.ExecuteActivity(ctx, "SummarizeFaultTicketActivity",
		ticketID,
		resultDescription,
		state.RepairType,
		repairSteps,
		common.StatusWaitingVerification,
		"").Get(ctx, nil)

	if err != nil {
		logger.Error("记录维修结果失败", "error", err)
		// 不中断流程
	}

	// 再更新数据库工单状态
	err = workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
		ticketID, state.CurrentStatus, signal.OperatorID, signal.Comments).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	return nil
}

// handleStartVerification 处理开始验证信号
func handleStartVerification(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("处理开始验证信号",
		"ticketID", ticketID,
		"currentStatus", state.CurrentStatus,
		"signal", signal.Stage)

	// 解析信号数据
	var solution, repairSteps string
	var repairTicketID uint
	success := true

	if signal.Data != nil {
		// 提取解决方案
		if sol, ok := signal.Data["solution"].(string); ok {
			solution = sol
		}

		// 提取维修步骤
		if steps, ok := signal.Data["repair_steps"].(string); ok {
			repairSteps = steps
		}

		// 记录维修单ID
		if repairID, ok := signal.Data["repair_ticket_id"].(float64); ok {
			repairTicketID = uint(repairID)
			state.RepairTicketID = repairTicketID
		}

		// 提取成功标志
		if successFlag, ok := signal.Data["success"].(bool); ok {
			success = successFlag
		}

		// 确保维修类型信息正确传递，如果信号中包含则更新状态中的维修类型
		if repairType, ok := signal.Data["repair_type"].(string); ok && repairType != "" {
			state.RepairType = repairType
			logger.Info("从信号中更新维修类型",
				"ticketID", ticketID,
				"repairType", repairType)

			// 如果是冷迁移类型，保存备机SN信息到工作流状态
			if repairType == "cold_migration" {
				if backupSN, ok := signal.Data["backup_device_sn"].(string); ok && backupSN != "" {
					// 将备机SN添加到信号数据中，确保在验证阶段可以使用
					if signal.Data == nil {
						signal.Data = make(map[string]interface{})
					}
					signal.Data["backup_device_sn"] = backupSN
					logger.Info("冷迁移: 保存备机SN到验证阶段",
						"ticketID", ticketID,
						"backupDeviceSN", backupSN)
				}
			}
		}
	}

	logger.Info("从维修单收到验证信号数据",
		"repairTicketID", repairTicketID,
		"solution", solution,
		"repairType", state.RepairType,
		"success", success)

	// 更新工作流内部状态
	state.CurrentStatus = common.StatusWaitingVerification
	state.CurrentStage = common.StageCompleteVerification

	// 记录维修结果到活动中
	resultDescription := "维修完成，等待验证: "
	if !success {
		resultDescription = "维修失败，等待验证: "
	}
	resultDescription += solution

	// 调用活动记录维修结果 - 使用waiting_verification状态
	err := workflow.ExecuteActivity(ctx, "SummarizeFaultTicketActivity",
		ticketID,
		resultDescription,
		state.RepairType,
		repairSteps,
		common.StatusWaitingVerification,
		"").Get(ctx, nil)

	if err != nil {
		logger.Error("记录维修结果失败", "error", err)
		// 不中断流程
	}

	// 更新数据库工单状态 - 关键是这里要把报障单状态更新为waiting_verification
	err = workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
		ticketID, common.StatusWaitingVerification, signal.OperatorID, "维修完成，等待验证").Get(ctx, nil)
	if err != nil {
		logger.Error("更新工单状态失败", "error", err)
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	logger.Info("报障单已进入验证状态",
		"ticketID", ticketID,
		"newStatus", common.StatusWaitingVerification)

	return nil
}

// handleCompleteVerification 处理完成验证信号
func handleCompleteVerification(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("处理完成验证信号",
		"ticketID", ticketID,
		"repairTicketID", state.RepairTicketID,
		"currentStage", state.CurrentStage,
		"currentStatus", state.CurrentStatus,
		"signalData", signal.Data)

	// 解析验证结果
	var verificationResult common.VerificationResult

	// 优先使用信号中的验证结果数据，而不是从数据库获取
	if signal.Data != nil && signal.Data["success"] != nil {
		success, ok := signal.Data["success"].(bool)
		if ok {
			// 直接使用信号中的数据
			verificationResult.Success = success
			verificationResult.Comments = signal.Comments

			logger.Info("使用信号中的验证结果数据",
				"success", verificationResult.Success,
				"comments", verificationResult.Comments)
		} else {
			// 类型转换失败，从数据库获取
			if err := workflow.ExecuteActivity(ctx, "GetVerificationActivity", ticketID).Get(ctx, &verificationResult); err != nil {
				return fmt.Errorf("获取验证结果失败: %w", err)
			}
		}
	} else {
		// 信号中没有验证结果数据，从数据库获取
		if err := workflow.ExecuteActivity(ctx, "GetVerificationActivity", ticketID).Get(ctx, &verificationResult); err != nil {
			return fmt.Errorf("获取验证结果失败: %w", err)
		}
	}

	// 记录验证信息
	logger.Info("最终使用的验证结果",
		"success", verificationResult.Success,
		"comments", verificationResult.Comments)

	// 查找当前关联的维修单ID - 可能来自状态或信号数据
	repairTicketID := state.RepairTicketID
	if repairTicketID == 0 && signal.Data != nil {
		if rtID, ok := signal.Data["repair_ticket_id"].(float64); ok {
			repairTicketID = uint(rtID)
			state.RepairTicketID = repairTicketID
			logger.Info("从信号数据中获取维修单ID",
				"repairTicketID", repairTicketID)
		}
	}

	// 根据验证结果确定下一步状态
	// 验证失败时，始终将状态设置为investigating，而不是summarizing
	if !verificationResult.Success {
		// 验证失败：根据维修类型确定不同的处理逻辑
		switch state.RepairType {
		case "hardware_fix":
			// 硬件维修失败，回到维修中状态
			state.CurrentStatus = common.StatusRepairing
			state.CurrentStage = common.StageCompleteRepair

			logger.Info("硬件维修验证失败，回到维修中状态",
				"ticketID", ticketID,
				"repairTicketID", repairTicketID)
		case "cold_migration":
			// 获取故障单信息，用于恢复设备状态
			var faultTicket *model.FaultTicket
			err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket)
			if err != nil {
				logger.Error("获取故障单信息失败，无法恢复设备状态",
					"error", err,
					"ticketID", ticketID)
				// 继续执行，不中断流程
			} else if faultTicket != nil {
				// 尝试从设备冷迁移记录中获取备用设备SN
				backupDeviceSN := ""

				// 获取冷迁移记录中的备用设备SN
				if signal.Data != nil && signal.Data["backup_device_sn"] != nil {
					if bdsn, ok := signal.Data["backup_device_sn"].(string); ok {
						backupDeviceSN = bdsn
						logger.Info("从信号数据获取备用设备SN",
							"backupDeviceSN", backupDeviceSN)
					}
				}

				// 检查是否有足够的数据进行恢复操作
				if faultTicket.DeviceSN != "" && backupDeviceSN != "" {
					// 恢复设备状态
					err := workflow.ExecuteActivity(ctx, "RevertDeviceStatusFromColdMigration",
						faultTicket.DeviceSN,
						backupDeviceSN).Get(ctx, nil)

					if err != nil {
						logger.Error("恢复设备状态失败",
							"error", err,
							"faultDeviceSN", faultTicket.DeviceSN,
							"backupDeviceSN", backupDeviceSN)
						// 处理失败不阻断工作流
					} else {
						logger.Info("冷迁移状态回滚成功",
							"faultDeviceSN", faultTicket.DeviceSN,
							"backupDeviceSN", backupDeviceSN)
					}
				}

				// 回到排查中状态，允许重新选择维修方法
				state.CurrentStatus = common.StatusInvestigating
				state.CurrentStage = common.StageRepairSelection

				// 记录旧的维修单ID
				oldRepairTicketID := state.RepairTicketID

				// 清除旧的维修单关联
				state.RepairTicketID = 0 // 清除工作流状态中的维修单关联
				state.ChildWorkflowRunning = false
				state.ChildWorkflowSuccess = nil

				// 如果前面获取故障单失败，这里重新尝试获取
				if faultTicket == nil {
					err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket)
					if err != nil {
						logger.Error("获取故障单信息失败，无法清除维修单关联",
							"error", err,
							"ticketID", ticketID)
						// 继续执行，不中断流程
					}
				}

				// 更新故障单，清除与维修单的关联
				if faultTicket != nil {
					faultTicket.RepairTicketID = 0
					faultTicket.Status = common.StatusInvestigating
					faultTicket.CurrentWaitingStage = common.StageRepairSelection

					// 在更新状态时会同时更新工单其他信息，无需额外调用活动
					logger.Info("清除故障单维修单关联关系",
						"ticketID", ticketID,
						"oldRepairTicketID", oldRepairTicketID)
				}

				logger.Info("冷迁移验证失败，已回滚设备状态并返回排查中状态",
					"ticketID", ticketID,
					"repairType", state.RepairType)
			} else {
				// 其他维修类型回到排查中状态，允许重新选择维修方法
				state.CurrentStatus = common.StatusInvestigating
				state.CurrentStage = common.StageRepairSelection

				// 记录旧的维修单ID
				oldRepairTicketID := state.RepairTicketID

				// 清除旧的维修单关联
				state.RepairTicketID = 0 // 清除工作流状态中的维修单关联
				state.ChildWorkflowRunning = false
				state.ChildWorkflowSuccess = nil

				// 获取故障单信息
				var faultTicket *model.FaultTicket
				err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket)
				if err != nil {
					logger.Error("获取故障单信息失败，无法清除维修单关联",
						"error", err,
						"ticketID", ticketID)
					// 继续执行，不中断流程
				} else if faultTicket != nil {
					// 更新故障单，清除与维修单的关联
					faultTicket.RepairTicketID = 0
					faultTicket.Status = common.StatusInvestigating
					faultTicket.CurrentWaitingStage = common.StageRepairSelection

					// 在更新状态时会同时更新工单其他信息，无需额外调用活动
					logger.Info("清除故障单维修单关联关系",
						"ticketID", ticketID,
						"oldRepairTicketID", oldRepairTicketID)
				}

				logger.Info("非硬件维修验证失败，回到排查中状态，允许重新选择维修方法",
					"ticketID", ticketID,
					"repairType", state.RepairType)
			}
		default:
			// 所有其他维修类型回到排查中状态，允许重新选择维修方法
			state.CurrentStatus = common.StatusInvestigating
			state.CurrentStage = common.StageRepairSelection

			// 记录旧的维修单ID
			oldRepairTicketID := state.RepairTicketID

			// 清除旧的维修单关联
			state.RepairTicketID = 0 // 清除工作流状态中的维修单关联
			state.ChildWorkflowRunning = false
			state.ChildWorkflowSuccess = nil

			// 获取故障单信息
			var faultTicket *model.FaultTicket
			err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket)
			if err != nil {
				logger.Error("获取故障单信息失败，无法清除维修单关联",
					"error", err,
					"ticketID", ticketID)
				// 继续执行，不中断流程
			} else if faultTicket != nil {
				// 更新故障单，清除与维修单的关联
				faultTicket.RepairTicketID = 0
				faultTicket.Status = common.StatusInvestigating
				faultTicket.CurrentWaitingStage = common.StageRepairSelection

				// 在更新状态时会同时更新工单其他信息，无需额外调用活动
				logger.Info("清除故障单维修单关联关系",
					"ticketID", ticketID,
					"oldRepairTicketID", oldRepairTicketID)
			}

			logger.Info("验证失败，回到排查中状态，允许重新选择维修方法",
				"ticketID", ticketID,
				"repairType", state.RepairType)
		}
	} else {
		// 验证成功，进入总结阶段
		state.CurrentStatus = common.StatusSummarizing
		state.CurrentStage = common.StageSummary
	}

	// 更新数据库工单状态，同时包含工单其他信息的更新
	comments := verificationResult.Comments
	if comments == "" {
		comments = signal.Comments
	}
	if comments == "" {
		if verificationResult.Success {
			comments = "验证通过，进入总结阶段"
		} else if state.RepairType == "hardware_fix" {
			comments = "验证失败，需要返回维修状态重新维修"
		} else if state.RepairType == "cold_migration" {
			comments = "冷迁移验证失败，已回滚设备状态，需要重新选择维修方法"
		} else {
			comments = "验证失败，需要重新选择维修方法"
		}
	}

	err := workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
		ticketID, state.CurrentStatus, signal.OperatorID, comments).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	// 记录debug日志 - 验证前后状态和信号数据
	logger.Info("验证完成，状态已更新",
		"ticketID", ticketID,
		"newStatus", state.CurrentStatus,
		"newStage", state.CurrentStage,
		"verificationSuccess", verificationResult.Success,
		"repairTicketID", repairTicketID)

	// 向维修单发送验证结果信号 - 只有当关联了维修单时才尝试发送
	if repairTicketID > 0 {
		// 构建维修单工作流ID
		repairWorkflowID := fmt.Sprintf("%s%d", common.RepairTicketWorkflowIDPrefix, repairTicketID)
		logger.Info("准备向维修单发送验证结果信号",
			"repairTicketID", repairTicketID,
			"repairWorkflowID", repairWorkflowID,
			"verificationSuccess", verificationResult.Success)

		// 创建信号内容
		signalStage := common.StageVerify
		var targetStatus string
		var comments string

		if verificationResult.Success {
			// 验证通过，让维修单完成
			targetStatus = "completed"
			comments = "验证通过，维修成功完成"
		} else {
			// 验证失败，让维修单继续维修
			targetStatus = "in_progress"
			comments = "验证失败，需要继续维修：" + verificationResult.Comments
		}

		// 准备验证结果值
		verificationResultValue := "failed"
		if verificationResult.Success {
			verificationResultValue = "passed"
		}

		// 发送信号到维修单工作流 - 采用乐观发送策略，处理可能的错误
		signalErr := workflow.SignalExternalWorkflow(ctx, repairWorkflowID, "", common.WorkflowControlSignalName, common.WorkflowControlSignal{
			Stage:        signalStage,
			OperatorID:   signal.OperatorID,
			OperatorName: signal.OperatorName,
			Comments:     comments,
			Data: map[string]interface{}{
				"success":             verificationResult.Success,
				"status":              targetStatus,
				"target_status":       targetStatus,
				"comments":            verificationResult.Comments,
				"fault_ticket_id":     ticketID,
				"second_verification": true,                    // 标记这是第二次验证
				"verification_result": verificationResultValue, // 添加验证结果字段
			},
		}).Get(ctx, nil)

		// 优化错误处理
		if signalErr != nil {
			// 检查是否是"工作流不存在"错误，这种情况是正常的，不应视为错误
			if strings.Contains(signalErr.Error(), "workflow execution not found") ||
				strings.Contains(signalErr.Error(), "unknown external workflow execution") {
				// 维修单工作流已经结束，这是预期的行为
				logger.Info("维修单工作流已结束，跳过发送验证信号",
					"repairTicketID", repairTicketID)
			} else {
				// 记录其他类型的错误，但不阻断工作流
				logger.Error("向维修单发送验证信号失败",
					"error", signalErr,
					"repairTicketID", repairTicketID)
			}
		} else {
			logger.Info("成功向维修单工作流发送验证结果信号",
				"repairTicketID", repairTicketID,
				"verificationSuccess", verificationResult.Success)
		}
	} else {
		logger.Warn("没有关联的维修单，无法发送验证结果信号", "ticketID", ticketID)
	}

	return nil
}

// handleSummary 处理总结信号
func handleSummary(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)

	// 如果当前状态是已取消，不允许修改状态
	if state.CurrentStatus == common.StatusCancelled {
		logger.Info("工单已处于已取消状态，保持当前状态",
			"ticketID", ticketID,
			"currentStatus", state.CurrentStatus)

		// 明确调用专门的活动，确保count_in_sla设置为false
		err := workflow.ExecuteActivity(ctx, "UpdateCancelledTicketSLAActivity", ticketID).Get(ctx, nil)
		if err != nil {
			logger.Error("确保已取消工单不计入SLA失败", "error", err)
			// 错误不阻止继续处理
		} else {
			logger.Info("已确保已取消工单不计入SLA", "ticketID", ticketID)
		}

		// 只允许更新总结信息，不更改状态
		var summary, preventionMeasures, faultType string
		var isFalseAlarm, isDuplicateFault bool

		if signal.Data != nil {
			logger.Info("取消处理收到的信号数据",
				"ticketID", ticketID,
				"signalData", fmt.Sprintf("%+v", signal.Data))

			// 检查是否为误报或重复故障
			if val, ok := signal.Data["isFalseAlarm"].(bool); ok && val {
				isFalseAlarm = true
				logger.Info("从信号数据中检测到isFalseAlarm=true",
					"ticketID", ticketID)
			}

			if val, ok := signal.Data["isDuplicateFault"].(bool); ok && val {
				isDuplicateFault = true
				logger.Info("从信号数据中检测到isDuplicateFault=true",
					"ticketID", ticketID)
			}

			// 优先使用cancel_reason作为取消原因
			if cancelReason, ok := signal.Data["cancel_reason"].(string); ok && cancelReason != "" {
				summary = cancelReason
				logger.Info("使用cancel_reason作为故障总结",
					"ticketID", ticketID,
					"cancelReason", cancelReason,
					"summary", summary)

				// 从取消原因自动判断是否误报
				if strings.Contains(strings.ToLower(cancelReason), "误报") {
					isFalseAlarm = true
					logger.Info("取消原因包含\"误报\"，自动设置isFalseAlarm=true",
						"ticketID", ticketID)
				}

				// 从取消原因自动判断是否重复故障
				if strings.Contains(strings.ToLower(cancelReason), "重复") {
					isDuplicateFault = true
					logger.Info("取消原因包含\"重复\"，自动设置isDuplicateFault=true",
						"ticketID", ticketID)
				}
			} else if sum, ok := signal.Data["summary"].(string); ok && sum != "" {
				// 如果没有cancel_reason但有summary，仍使用summary
				summary = sum
			}

			if measures, ok := signal.Data["prevention_measures"].(string); ok {
				preventionMeasures = measures
			}
			if ft, ok := signal.Data["fault_type"].(string); ok && ft != "" {
				faultType = ft
			} else if ft, ok := signal.Data["faultType"].(string); ok && ft != "" {
				faultType = ft
			}
		}

		// 在调用总结活动前，添加更多日志
		logger.Info("准备调用SummarizeFaultTicketActivity",
			"ticketID", ticketID,
			"summary", summary,
			"isFalseAlarm", isFalseAlarm,
			"isDuplicateFault", isDuplicateFault,
			"status", common.StatusCancelled)

		// 首先将工单正确标记为已取消和误报/重复故障
		updateFields := map[string]interface{}{
			"status":       common.StatusCancelled,
			"count_in_sla": false,
		}

		if isFalseAlarm {
			updateFields["is_false_alarm"] = true
		}

		if isDuplicateFault {
			updateFields["is_duplicate_fault"] = true
		}

		// 直接更新数据库字段
		errUpdate := workflow.ExecuteActivity(ctx, "UpdateFieldsActivity", ticketID, updateFields).Get(ctx, nil)
		if errUpdate != nil {
			logger.Error("直接更新取消字段失败", "error", errUpdate)
			// 错误不阻止继续处理
		} else {
			logger.Info("直接更新取消字段成功",
				"ticketID", ticketID,
				"isFalseAlarm", isFalseAlarm,
				"isDuplicateFault", isDuplicateFault)
		}

		// 直接更新字段
		errSLA := workflow.ExecuteActivity(ctx, "UpdateCancelledTicketSLAActivity", ticketID).Get(ctx, nil)
		if errSLA != nil {
			logger.Error("确保已取消工单不计入SLA失败", "error", errSLA)
			// 错误不阻止继续处理
		}

		// 从信号中获取关联工单ID
		var relatedTicketID uint
		if val, ok := signal.Data["relatedTicketID"].(float64); ok {
			relatedTicketID = uint(val)
			logger.Info("从信号中获取关联工单ID",
				"ticketID", ticketID,
				"relatedTicketID", relatedTicketID)
		}

		// 调用总结活动，但保持cancelled状态
		err = workflow.ExecuteActivity(ctx, "SummarizeFaultTicketActivity",
			ticketID,
			summary,
			state.RepairType,
			preventionMeasures,
			common.StatusCancelled, // 明确指定保持cancelled状态
			faultType,
			isFalseAlarm,                  // 传递isFalseAlarm参数
			isDuplicateFault,              // 传递isDuplicateFault参数
			relatedTicketID).Get(ctx, nil) // 传递关联工单ID
		if err != nil {
			return fmt.Errorf("记录总结失败: %w", err)
		}

		// 跳过后续的状态更新操作
		return nil
	}

	// 先更新工作流内部状态
	state.CurrentStatus = common.StatusCompleted
	state.CurrentStage = common.StageCompleteTicket

	// 获取总结数据
	var summary, preventionMeasures, faultType string
	if signal.Data != nil {
		if sum, ok := signal.Data["summary"].(string); ok {
			summary = sum
		}
		if measures, ok := signal.Data["prevention_measures"].(string); ok {
			preventionMeasures = measures
		}
		// 从信号中获取故障类型 - 检查两种可能的键名(fault_type和faultType)
		if ft, ok := signal.Data["fault_type"].(string); ok && ft != "" {
			faultType = ft
			logger.Info("在总结阶段更新故障类型(fault_type)", "ticketID", ticketID, "faultType", faultType)
		} else if ft, ok := signal.Data["faultType"].(string); ok && ft != "" {
			// 兼容前端可能使用的大写键名
			faultType = ft
			logger.Info("在总结阶段更新故障类型(faultType)", "ticketID", ticketID, "faultType", faultType)
		}
	}

	// 调用总结活动
	err := workflow.ExecuteActivity(ctx, "SummarizeFaultTicketActivity",
		ticketID,
		summary,
		state.RepairType,
		preventionMeasures,
		common.StatusCompleted,
		faultType,
		false,           // 非取消情况下，isFalseAlarm设为false
		false,           // 非取消情况下，isDuplicateFault设为false
		0).Get(ctx, nil) // 非取消情况下，relatedTicketID设为0
	if err != nil {
		return fmt.Errorf("记录总结失败: %w", err)
	}

	// 计算时间指标
	err = workflow.ExecuteActivity(ctx, "CalculateDurationsActivity", ticketID).Get(ctx, nil)
	if err != nil {
		logger.Error("计算时间指标失败", "error", err)
		// 继续执行，不中断流程
	} else {
		logger.Info("已计算工单时间指标", "ticketID", ticketID)
	}

	// 获取故障单信息
	var faultTicket *model.FaultTicket
	if err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket); err != nil {
		logger.Error("获取故障单信息失败", "error", err)
		// 获取故障单失败不应阻塞工作流主流程
	} else if faultTicket != nil && faultTicket.DeviceSN != "" {
		// 维修完成时，重置设备和资源状态
		err := workflow.ExecuteActivity(ctx, "UpdateDeviceStatusActivity",
			faultTicket.DeviceSN,
			faultTicket.Source,
			"complete_repair").Get(ctx, nil)

		if err != nil {
			logger.Error("重置设备状态失败", "error", err, "deviceSN", faultTicket.DeviceSN)
			// 更新设备状态失败不应阻塞工作流主流程
		} else {
			logger.Info("维修完成，已重置设备和资源状态", "deviceSN", faultTicket.DeviceSN)
		}

		// 检查是否为冷迁移且已完成，如果是则创建新的报障单
		if state.RepairType == "cold_migration" {
			logger.Info("检测到已完成的冷迁移报障单，准备创建新报障单",
				"原报障单ID", ticketID,
				"设备SN", faultTicket.DeviceSN)

			// 创建新报障单的输入
			newTicketInput := model.FaultTicket{
				Source:           "manual",
				Title:            fmt.Sprintf("冷迁移后备机维修 - %s-%s", faultTicket.DeviceSN, faultTicket.FaultDetailType),
				Priority:         faultTicket.Priority,
				Status:           common.StatusWaitingAccept,   // 初始状态为等待接单
				DeviceSN:         faultTicket.DeviceSN,         // 使用原故障机SN
				ComponentSN:      faultTicket.ComponentSN,      // 使用原组件SN
				ComponentType:    faultTicket.ComponentType,    // 使用原组件类型
				FaultType:        faultTicket.FaultType,        // 使用原故障类型
				FaultDetailType:  faultTicket.FaultDetailType,  // 使用原故障详细类型
				FaultDescription: faultTicket.FaultDescription, // 使用原故障描述
				Symptom:          faultTicket.Symptom,          // 使用原故障现象
				SlotPosition:     faultTicket.SlotPosition,     // 使用原插槽位置
				ReporterID:       0,                            // 使用原报障人ID
				ReporterName:     "系统",                         // 使用原报障人姓名
				CreationTime:     time.Now(),                   // 当前时间
				RelatedTicketID:  ticketID,                     // 关联到原报障单
				RequireApproval:  false,                        // 不需要客户审批
				ImmediateRepair:  false,                        // 不需要立即维修
				CountInSLA:       false,                        // 不计入SLA
				Remarks:          fmt.Sprintf("此报障单由冷迁移报障单(ID:%d)自动创建，用于设备后续维修", ticketID),
			}

			// 执行活动创建新报障单
			var newTicketID uint
			err := workflow.ExecuteActivity(ctx, "CreateFaultTicketAfterColdMigrationActivity", newTicketInput).Get(ctx, &newTicketID)
			if err != nil {
				logger.Error("冷迁移后创建新报障单失败", "error", err)
				// 创建失败不阻塞主流程
			} else {
				logger.Info("冷迁移后成功创建新报障单",
					"原报障单ID", ticketID,
					"新报障单ID", newTicketID,
					"设备SN", faultTicket.DeviceSN)
			}
		}
	}

	// 再更新数据库工单状态
	err = workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
		ticketID, state.CurrentStatus, signal.OperatorID, signal.Comments).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	return nil
}

// handleCompleteTicket 处理完成工单信号
func handleCompleteTicket(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)

	// 如果当前状态是已取消，不允许修改状态
	if state.CurrentStatus == common.StatusCancelled {
		logger.Info("工单已处于已取消状态，完成操作将保持当前状态",
			"ticketID", ticketID,
			"currentStatus", state.CurrentStatus)
		return nil
	}

	// 先更新工作流内部状态
	state.CurrentStatus = common.StatusCompleted

	// 再更新数据库工单状态
	err := workflow.ExecuteActivity(ctx, "UpdateFaultTicketStatusActivity",
		ticketID, state.CurrentStatus, signal.OperatorID, signal.Comments).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	return nil
}

// handleCancelTicket 处理取消工单信号
func handleCancelTicket(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("处理取消工单信号",
		"ticketID", ticketID,
		"operatorName", signal.OperatorName)

	// 从信号中获取取消原因和类型
	var reason string
	var cancellationType string
	var relatedTicketID uint

	if signal.Data != nil {
		// 获取取消原因
		if r, ok := signal.Data["reason"].(string); ok {
			reason = r
		} else if r, ok := signal.Data["comments"].(string); ok {
			reason = r
		} else if r, ok := signal.Data["cancel_reason"].(string); ok {
			reason = r
		} else {
			reason = signal.Comments
		}

		// 获取取消类型：误报/重复故障
		if ct, ok := signal.Data["cancellation_type"].(string); ok {
			cancellationType = ct
		} else {
			// 新的判断逻辑：根据新字段判断取消类型
			isFalseAlarm, hasFalseAlarm := signal.Data["isFalseAlarm"].(bool)
			isDuplicateFault, hasDuplicateFault := signal.Data["isDuplicateFault"].(bool)

			if hasFalseAlarm && isFalseAlarm {
				cancellationType = "false_alarm"
				logger.Info("根据isFalseAlarm字段判断为误报",
					"ticketID", ticketID)
			} else if hasDuplicateFault && isDuplicateFault {
				cancellationType = "duplicate_fault"
				logger.Info("根据isDuplicateFault字段判断为重复故障",
					"ticketID", ticketID)
			}
		}

		// 获取关联的故障单ID
		if cancellationType == "duplicate_fault" {
			if rtID, ok := signal.Data["related_ticket_id"].(float64); ok {
				relatedTicketID = uint(rtID)
			} else if rtID, ok := signal.Data["relatedTicketID"].(float64); ok {
				// 兼容前端camelCase格式
				relatedTicketID = uint(rtID)
				logger.Info("从relatedTicketID获取关联工单ID",
					"ticketID", ticketID,
					"relatedTicketID", relatedTicketID)
			}
		}
	}

	// 如果没有取消原因，使用信号评论
	if reason == "" {
		reason = signal.Comments
	}

	logger.Info("取消工单详情",
		"ticketID", ticketID,
		"reason", reason,
		"cancellationType", cancellationType,
		"relatedTicketID", relatedTicketID)

	// 从信号数据中提取isFalseAlarm和isDuplicateFault值
	isFalseAlarm := false
	isDuplicateFault := false
	if signal.Data != nil {
		if val, ok := signal.Data["isFalseAlarm"].(bool); ok {
			isFalseAlarm = val
			logger.Info("从信号数据中获取到isFalseAlarm值",
				"ticketID", ticketID,
				"isFalseAlarm", isFalseAlarm)
		}
		if val, ok := signal.Data["isDuplicateFault"].(bool); ok {
			isDuplicateFault = val
			logger.Info("从信号数据中获取到isDuplicateFault值",
				"ticketID", ticketID,
				"isDuplicateFault", isDuplicateFault)
		}
	}

	// 基于cancellationType自动设置标记
	if cancellationType == "false_alarm" && !isFalseAlarm {
		isFalseAlarm = true
		logger.Info("根据cancellationType='false_alarm'自动设置isFalseAlarm=true",
			"ticketID", ticketID)
	} else if cancellationType == "duplicate_fault" && !isDuplicateFault {
		isDuplicateFault = true
		logger.Info("根据cancellationType='duplicate_fault'自动设置isDuplicateFault=true",
			"ticketID", ticketID)
	}

	// 调用活动取消报障单，包含新增的参数
	err := workflow.ExecuteActivity(ctx, "CancelFaultTicketActivity",
		ticketID, reason, cancellationType, relatedTicketID, isFalseAlarm, isDuplicateFault).Get(ctx, nil)
	if err != nil {
		logger.Error("取消报障单失败", "error", err)
		return fmt.Errorf("取消报障单失败: %w", err)
	}

	// 更新工作流状态
	state.CurrentStatus = common.StatusCancelled
	state.CurrentStage = "cancelled"

	// 获取故障单信息
	var faultTicket *model.FaultTicket
	if err := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketID).Get(ctx, &faultTicket); err != nil {
		logger.Error("获取故障单信息失败", "error", err)
		// 获取故障单失败不应阻塞工作流主流程
	} else if faultTicket != nil && faultTicket.DeviceSN != "" {
		// 维修取消时，重置设备和资源状态
		err := workflow.ExecuteActivity(ctx, "UpdateDeviceStatusActivity",
			faultTicket.DeviceSN,
			faultTicket.Source,
			"cancel_repair").Get(ctx, nil)

		if err != nil {
			logger.Error("重置设备状态失败", "error", err, "deviceSN", faultTicket.DeviceSN)
			// 更新设备状态失败不应阻塞工作流主流程
		} else {
			logger.Info("维修取消，已重置设备和资源状态", "deviceSN", faultTicket.DeviceSN)
		}
	}

	// 调用活动确保count_in_sla设置为false
	err = workflow.ExecuteActivity(ctx, "UpdateCancelledTicketSLAActivity", ticketID).Get(ctx, nil)
	if err != nil {
		logger.Error("更新取消工单SLA状态失败", "error", err)
		// 错误不阻止工作流结束
	}

	logger.Info("工单取消完成", "ticketID", ticketID)
	return nil
}
