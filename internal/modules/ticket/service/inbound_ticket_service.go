package service

import (
	"backend/internal/common/constants"
	cmdbCommon "backend/internal/modules/cmdb/common"
	"backend/internal/modules/cmdb/model/inbound"
	"backend/internal/modules/cmdb/service/asset"
	inboundSvc "backend/internal/modules/cmdb/service/inbound"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"
	"backend/internal/modules/cmdb/service/product"
	exportModel "backend/internal/modules/export/model"
	purchasemodel "backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/service"
	purchaseService "backend/internal/modules/purchase_old/service"
	"backend/internal/modules/ticket/common"
	ticketmodel "backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"backend/internal/modules/ticket/workflow"
	"context"
	cryptorand "crypto/rand"
	"encoding/binary"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"

	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

// InboundTicketService 入库工单服务接口
type InboundTicketService interface {
	// 维修入库
	GetPartInboundTicketByID(ctx context.Context, id uint) (*ticketmodel.PartInboundTicket, error)
	GetPartInboundTicketByRepairTicketID(ctx context.Context, repairTicketID uint) (*ticketmodel.PartInboundTicket, error)
	ListPartInboundTickets(ctx context.Context, query *ticketmodel.ListParams) ([]*ticketmodel.PartInboundTicket, int64, error)
	GetHistoryByInboundNo(ctx context.Context, inboundNo string) ([]*ticketmodel.InboundTicketHistory, error)
	UpdatePartInboundTicketStatus(ctx context.Context, ticketID uint, status string, operatorID uint, operatorName string) error
	GetPartInboundTicketByNo(ctx context.Context, inboundNo string) (*ticketmodel.PartInboundTicket, error)
	//StartPartInboundWorkflow(ctx context.Context, input common.PartInboundWorkflowInput) error

	// 新购入库
	CreateNewInboundTicket(ctx context.Context, ticket *ticketmodel.NewInboundTicket) error
	InitNewNewInbound(ctx context.Context, ticket *ticketmodel.NewInboundTicket, history *ticketmodel.InboundTicketHistory) error
	GetNewInboundTicketByID(ctx context.Context, id uint) (*ticketmodel.NewInboundTicket, error)
	//StartNewInboundWorkflow(ctx context.Context, input common.NewInboundWorkflowInput) error
	GenerateNewInboundNo() string
	GetNewInboundTicketByNo(ctx context.Context, inboundNo string) (*ticketmodel.NewInboundTicket, error)
	ListNewInboundTicketsByNo(ctx context.Context, query *ticketmodel.ListParams) ([]*ticketmodel.NewInboundTicket, int64, error)
	ListNewInboundTickets(ctx context.Context, query *ticketmodel.ListParams) ([]*ticketmodel.NewInboundTicket, int64, error)

	/*返修入库*/
	InitRepairInboundTicket(ctx context.Context, ticket *ticketmodel.RepairPartInboundTicket) error
	GetRepairPartInboundTicketByNo(ctx context.Context, inboundNo string) (*ticketmodel.RepairPartInboundTicket, error)

	/*拆机配件入库*/
	InitDismantledInboundTicket(ctx context.Context, ticket *ticketmodel.DismantledPartInboundTicket) error
	GetDismantledPartInboundTicketByNo(ctx context.Context, inboundNo string) (*ticketmodel.DismantledPartInboundTicket, error)

	/* 整机入库 */
	CreateDeviceInboundByInput(ctx context.Context, deviceInbound *inbound.DeviceInbound) error
	GetDeviceInboundTicketByNo(ctx context.Context, deviceNo string) (*ticketmodel.DeviceInboundTicket, error)
	CreateDeviceInbound(ctx context.Context, deviceInbound *inbound.DeviceInbound, inboundList *inbound.InboundList, deviceInboundTicket *ticketmodel.DeviceInboundTicket, deviceInboundHistory *ticketmodel.DeviceInboundTicketHistory) error
	// 公用
	CreateTicketHistory(ctx context.Context, history *ticketmodel.InboundTicketHistory) error
	TriggerWorkflowStage(ctx context.Context, ticketID uint, inboundNo string, triggers ticketmodel.InboundTrigger) error

	// 公共的启动工作流接口
	StartInboundWorkflow(ctx context.Context, StartInput common.StartInboundWorkflowInput) error
	CreateInboundTicket(ctx context.Context, ticket ticketmodel.InboundTicketInterface) error

	// TODO 公共初始化接口
	InitInboundTicketAndHistory(ctx context.Context, inboundTicket ticketmodel.InboundTicketInterface, inboundHistory *ticketmodel.InboundTicketHistory) (common.StartInboundWorkflowInput, error)

	/** 重构后的入库工单接口*/

	// 公用创建入库工单接口
	CreateInboundTicketV2(ctx context.Context, Inboundreq ticketmodel.InboundTicket) (string, error)
	// 工作流接口
	StartInboundWorkflowV2(ctx context.Context, ticketID uint, req ticketmodel.WorkflowReq) error
	TriggerWorkflowStageV2(ctx context.Context, ticketID uint, inboundType string, triggers ticketmodel.InboundTrigger) error
	// 全局No搜索接口
	GetInboundTicketByNo(ctx context.Context, ticketNo string) (*ticketmodel.InboundTicket, error)
	GetInboundInfoByNo(ctx context.Context, inboundType, ticketNo string) ([]ticketmodel.InboundInfo, error)
	GetInboundDetailByNo(ctx context.Context, inboundType, ticketNo string) ([]ticketmodel.InboundDetail, error)
	// 分页获取入库单详情列表
	GetInboundDetailListByNo(ctx context.Context, inboundType, ticketNo string, page, pageSize int) ([]ticketmodel.InboundDetail, int64, error)
	GetInboundHistoryByNo(ctx context.Context, inboundNo string) ([]ticketmodel.InboundHistory, error)
	// 更新入库明细
	UpdateInboundDetail(ctx context.Context, inboundType, inboundReason string, details []ticketmodel.InboundDetail) error
	UpdateInboundDetailByImport(ctx context.Context, importReq ticketmodel.DetailImportReq, inboundNo string) error
	// 获取已存在的SN
	CheckExitingSNs(ctx context.Context, inboundType string, SNs []string) ([]string, error)
	// 更新CMDB
	UpdateCMDB(ctx context.Context, inboundType, inboundReason string, details []ticketmodel.InboundDetail) error
	// 校验入库参数
	validateInboundParams(ctx context.Context, inboundReq ticketmodel.InboundTicket) error
}

// inboundTicketService 入库工单服务实现
type inboundTicketService struct {
	repo           repository.InboundTicketRepository
	temporalClient client.Client
	logger         *zap.Logger
	inboundSvc     inboundSvc.InboundService
	purchaseSvc    purchaseService.PurchaseService
	productSvc     product.ProductService
	spareSvc       asset.SpareService
	deviceSvc      asset.DeviceService
	purchaseReqSvc service.PurchaseRequestService
	purchaseCrtSvc service.PurchaseContractService
	purchaseInqSvc service.PurchaseInquiryService
	inventorySvc   inventorySvc.InventoryService
}

func (s *inboundTicketService) UpdateCMDB(ctx context.Context, inboundType, inboundReason string, details []ticketmodel.InboundDetail) error {
	// 根据入库类型和原因调用不同的CMDB更新逻辑
	switch inboundReason {
	case constants.SourceTypeNewPurchase:
		return s.updateCMDBWithNewPurchase(ctx, inboundType, details)
	case constants.SourceTypeReturnRepair:
		return s.updateCMDBWithReturnRepaired(ctx, inboundType, details)
	case constants.SourceTypeDismantled:
		return s.updateCMDBWithDismantled(ctx, details)
	default:
		return fmt.Errorf("不支持的入库原因:%s-%s", inboundType, inboundReason)
	}
}

func (s *inboundTicketService) updateCMDBWithNewPurchase(ctx context.Context, inboundType string, details []ticketmodel.InboundDetail) error {
	var (
		SNs []string
	)
	for _, detail := range details {
		if inboundType == inbound.TypePartInbound {
			SNs = append(SNs, detail.ComponentSN)
		} else {
			SNs = append(SNs, detail.DeviceSN)
		}
	}

	exitSNs, err := s.CheckExitingSNs(ctx, inboundType, SNs)
	if err != nil {
		return err
	}
	if len(exitSNs) > 0 {
		return fmt.Errorf("入库单中存在已存在的SN: %v", exitSNs)
	}

	// 获取ticket数据
	ticket, err := s.repo.GetInboundTicketByNo(ctx, details[0].InboundNo)
	if err != nil {
		return err
	}

	db, err := s.repo.GetDB()
	if err != nil {
		return err
	}

	err = db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新入库相关数据
		ctx = context.WithValue(ctx, constants.ContextKeyTX, tx)
		fmt.Printf("开始插入相关数据")
		err = s.repo.UpdateCMDBWithNewPurchase(ctx, inboundType, details)
		if err != nil {
			return err
		}

		// 更新采购相关asset_status_change_log
		var (
			purchaseCrt *purchasemodel.PurchaseContract
			purchaseInq *purchasemodel.PurchaseInquiry
			purchaseReq *purchasemodel.PurchaseRequest
		)
		if inboundType == inbound.TypeDeviceInbound {
			purchaseCrtDto, err := s.purchaseCrtSvc.GetPurchaseContractByContractNo(ctx, ticket.PurchaseNo)
			if err != nil {
				return err
			}
			purchaseCrt = purchaseCrtDto.PurchaseContract

			if purchaseCrt.InquiryID != nil {
				purchaseInq, err = s.purchaseInqSvc.GetPurchaseInquiryByID(ctx, *purchaseCrt.InquiryID)
				if err != nil {
					return err
				}
				if purchaseInq.RequestID != nil {
					purchaseReq, err = s.purchaseReqSvc.GetPurchaseRequestByID(ctx, *purchaseInq.RequestID)
					if err != nil {
						return err
					}
				}
			}

			err = s.repo.CreatePurchaseAssetStatusChangeLog(ctx, SNs, purchaseCrt, purchaseInq, purchaseReq)
			if err != nil {
				return err
			}

		}
		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (s *inboundTicketService) updateCMDBWithReturnRepaired(ctx context.Context, inboundType string, details []ticketmodel.InboundDetail) error {
	switch inboundType {
	case inbound.TypePartInbound:
		err := s.repo.UpdateCMDBWithReturnRepaired(ctx, inboundType, details)
		if err != nil {
			return err
		}

	case inbound.TypeDeviceInbound:
		// TODO 目前还没有需求
	}
	return nil
}

func (s *inboundTicketService) updateCMDBWithDismantled(ctx context.Context, details []ticketmodel.InboundDetail) error {
	// 处理拆机配件入库的CMDB更新逻辑
	var (
		SNs            []string
		UpdatedDetails []ticketmodel.InboundDetail
		CreatedDetails []ticketmodel.InboundDetail
	)
	ticket, err := s.GetInboundTicketByNo(ctx, details[0].InboundNo)
	if err != nil {
		return err
	}
	infos, err := s.GetInboundInfoByNo(ctx, ticket.InboundType, ticket.InboundNo)
	if err != nil {
		return err
	}
	for _, detail := range details {
		SNs = append(SNs, detail.ComponentSN)
	}
	existingSNs, err := s.CheckExitingSNs(ctx, ticket.InboundType, SNs)
	if err != nil {
		return err
	}
	for _, detail := range details {
		if slices.Contains(existingSNs, detail.ComponentSN) {
			// SN 已存在，更新入库明细
			UpdatedDetails = append(UpdatedDetails, detail)
		} else {
			// SN 不存在，创建新的入库明细
			CreatedDetails = append(CreatedDetails, detail)
		}
	}
	err = s.repo.UpdateCMDBWithDismantled(ctx, UpdatedDetails, CreatedDetails, ticket, infos)
	if err != nil {
		return err
	}
	return nil
}

func (s *inboundTicketService) CheckExitingSNs(ctx context.Context, inboundType string, SNs []string) ([]string, error) {
	return s.repo.CheckExistingSNs(ctx, inboundType, SNs)
}

// TriggerWorkflowStageV2 更新工作流 重构版
func (s *inboundTicketService) TriggerWorkflowStageV2(ctx context.Context, ticketID uint, inboundType string, triggers ticketmodel.InboundTrigger) error {
	// 验证工作流阶段参数
	if triggers.Status == "" {
		return fmt.Errorf("工作流状态不能为空")
	}

	var (
		ticketType string
		updateName string
		signal     common.WorkflowSignalInterface
	)

	switch inboundType {
	case inbound.TypePartInbound:
		ticketType = "Part"
		updateName = common.PartInboundUpdateSignal
		signal = common.PartInboundSignal{
			Status:        triggers.Status,
			OperatorID:    triggers.OperatorID,
			OperatorName:  triggers.OperatorName,
			RequireVerify: triggers.RequireVerified,
			Comments:      triggers.Comments,
		}
	case inbound.TypeDeviceInbound:
		ticketType = "Device"
		updateName = common.DeviceInboundUpdateSignal
		signal = common.DeviceInboundSignal{
			Status:        triggers.Status,
			OperatorID:    triggers.OperatorID,
			OperatorName:  triggers.OperatorName,
			RequireVerify: triggers.RequireVerified,
			Comments:      triggers.Comments,
		}
	default:
		return fmt.Errorf("不支持的出库类型：%s", inboundType)
	}
	// 构造工作流ID
	workflowID := fmt.Sprintf("%s_Inbound_ticket_%d", ticketType, ticketID)

	s.logger.Info("准备更新工作流状态",
		zap.String("workflowID", workflowID),
		zap.String("stage", triggers.Stage),
		zap.String("status", triggers.Status),
		zap.String("operatorName", triggers.OperatorName),
		zap.Uint("ticketID", ticketID))
	// 更新工作流状态
	updateOptions := client.UpdateWorkflowOptions{
		WorkflowID:   workflowID,
		UpdateName:   updateName,
		WaitForStage: client.WorkflowUpdateStageCompleted,
		Args:         []interface{}{signal},
	}
	updateHandle, err := s.temporalClient.UpdateWorkflow(ctx, updateOptions)
	if err != nil {
		s.logger.Error("更新工作流状态失败",
			zap.Error(err),
			zap.String("workflowID", workflowID),
			zap.String("stage", triggers.Stage),
			zap.String("status", triggers.Status),
			zap.String("operatorName", triggers.OperatorName),
			zap.Uint("ticketID", ticketID))
		return fmt.Errorf("更新工作流状态失败: %w", err)
	}

	err = updateHandle.Get(ctx, nil)
	if err != nil {
		s.logger.Error("Unable to get update result", zap.Error(err))
		return err
	}

	s.logger.Info("成功更新入库工作流",
		zap.String("workflowID", workflowID),
		zap.String("stage", triggers.Stage),
		zap.String("status", triggers.Status),
		zap.Uint("ticketID", ticketID))

	return nil
}

func (s *inboundTicketService) GetInboundInfoByNo(ctx context.Context, inboundType, ticketNo string) ([]ticketmodel.InboundInfo, error) {
	return s.repo.GetInboundInfosByNo(ctx, inboundType, ticketNo)
}

func (s *inboundTicketService) GetInboundDetailByNo(ctx context.Context, inboundType, ticketNo string) ([]ticketmodel.InboundDetail, error) {
	return s.repo.GetInboundDetailsByNo(ctx, inboundType, ticketNo)
}

func (s *inboundTicketService) GetInboundTicketByNo(ctx context.Context, ticketNo string) (*ticketmodel.InboundTicket, error) {
	return s.repo.GetInboundTicketByNo(ctx, ticketNo)
}

// GenerateInboundNo 生成入库工单号
func (s *inboundTicketService) GenerateInboundNo(inboundType, inboundReason string) (string, error) {
	// 使用时间戳和安全随机数生成工单号
	timestamp := time.Now().Format("20060102150405")
	// 生成安全的随机数
	var random int
	randomBytes := make([]byte, 4)

	// 尝试使用crypto/rand包获取随机字节
	if _, err := cryptorand.Read(randomBytes); err != nil {
		// 如果安全随机数生成失败，使用时间纳秒作为备选
		s.logger.Warn("使用加密随机数失败，使用时间纳秒代替", zap.Error(err))
		random = int(time.Now().UnixNano() % 1000)
	} else {
		// 将随机字节转换为 0-999 范围内的随机数
		random = int(binary.BigEndian.Uint32(randomBytes) % 1000)
	}
	switch inboundType {
	case inbound.TypePartInbound:
		switch inboundReason {
		case constants.SourceTypeNewPurchase:
			return fmt.Sprintf("NPIT%s%03d", timestamp, random), nil
		case constants.SourceTypeReturnRepair:
			return fmt.Sprintf("RPIT%s%03d", timestamp, random), nil
		case constants.SourceTypeDismantled:
			return fmt.Sprintf("DPIT%s%03d", timestamp, random), nil
		default:
			return "", fmt.Errorf("不支持的入库原因: %s", inboundReason)
		}
	case inbound.TypeDeviceInbound:
		switch inboundReason {
		case constants.SourceTypeNewPurchase:
			return fmt.Sprintf("NDIT%s%03d", timestamp, random), nil
		default:
			return "", fmt.Errorf("不支持的入库原因: %s", inboundReason)
		}
	default:
		return "", fmt.Errorf("不支持的入库类型: %s", inboundType)
	}
}

func (s *inboundTicketService) GetDeviceInboundTicketByNo(ctx context.Context, deviceNo string) (*ticketmodel.DeviceInboundTicket, error) {
	return s.repo.GetDeviceInboundTicketByNo(ctx, deviceNo)
}

func (s *inboundTicketService) GetNewInboundTicketByNo(ctx context.Context, inboundNo string) (*ticketmodel.NewInboundTicket, error) {
	return s.repo.GetNewTicketByNo(ctx, inboundNo)
}

func (s *inboundTicketService) GetPartInboundTicketByID(ctx context.Context, id uint) (*ticketmodel.PartInboundTicket, error) {
	return s.repo.GetPartTicketByID(ctx, id)
}

func (s *inboundTicketService) CreateInboundTicket(ctx context.Context, ticket ticketmodel.InboundTicketInterface) error {
	switch t := ticket.(type) {
	case *ticketmodel.NewInboundTicket:
		err := s.CreateNewInboundTicket(ctx, t)
		if err != nil {
			return err
		}
	case *ticketmodel.RepairPartInboundTicket:
		err := s.InitRepairInboundTicket(ctx, t)
		if err != nil {
			return err
		}
	case *ticketmodel.DismantledPartInboundTicket:
		err := s.InitDismantledInboundTicket(ctx, t)
		if err != nil {
			return err
		}
	default:
		return fmt.Errorf("不存在的工单类型: %s", ticket.GetTicketType())
	}
	return nil
}

// CreateInboundTicketV2 创建入库单
func (s *inboundTicketService) CreateInboundTicketV2(ctx context.Context, InboundReq ticketmodel.InboundTicket) (string, error) {
	var (
		details []ticketmodel.InboundDetail
	)
	// 参数校验
	fmt.Println("needReturn：", InboundReq.NeedReturn)
	if err := s.validateInboundParams(ctx, InboundReq); err != nil {
		return "", err
	}
	// 生成工单号
	inboundNo, err := s.GenerateInboundNo(InboundReq.InboundType, InboundReq.InboundReason)
	if err != nil {
		return "", err
	}
	// 更新 info 和 detail
	InboundReq.InboundNo = inboundNo
	for index := range InboundReq.Info {
		InboundReq.Info[index].InboundNo = inboundNo
		for i := 0; i < InboundReq.Info[index].Amount; i++ {
			detail := ticketmodel.InboundDetail{
				InboundNo: inboundNo,
				ProductID: InboundReq.Info[index].ProductID,
			}
			details = append(details, detail)
		}
	}
	InboundReq.Detail = details
	InboundReq.Stage = common.StageAssetApproval
	InboundReq.Status = common.StatusWaitingAssetApproval

	// 创建历史记录
	history := ticketmodel.InboundHistory{
		InboundNo:    InboundReq.InboundNo,
		Stage:        common.StageSubmitApply,
		Status:       common.StatusSubmitingApply,
		OperatorID:   InboundReq.CreateID,
		OperatorName: InboundReq.CreateBy,
	}

	// 创建列表数据
	list := inbound.InboundList{
		InboundNo:     inboundNo,
		Project:       InboundReq.Project,
		InboundType:   InboundReq.InboundType,
		InboundReason: InboundReq.InboundReason,
		InboundTitle:  InboundReq.InboundTitle,
		CreateBy:      InboundReq.CreateBy,
		CreaterID:     InboundReq.CreateID,
		Stage:         common.StageAssetApproval,
	}
	err = s.repo.CreateInboundTicket(ctx, &InboundReq, &history, &list)
	if err != nil {
		return "", err
	}
	return InboundReq.InboundNo, nil
}

func (s *inboundTicketService) UpdatePartInboundTicketStatus(ctx context.Context, ticketID uint, status string, operatorID uint, operatorName string) error {
	// 获取工单
	ticket, err := s.repo.GetPartTicketByID(ctx, ticketID)
	if err != nil {
		return err
	}

	// 记录状态历史
	history := &ticketmodel.InboundTicketHistory{
		InboundNo:      ticket.InboundNo,
		Stage:          common.StatusWaitingApproval,
		PreviousStatus: ticket.Status,
		NewStatus:      status,
		OperatorID:     operatorID,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
	}
	err = s.repo.CreateTicketHistory(ctx, history)
	if err != nil {
		return err
	}
	return nil
}

func (s *inboundTicketService) TriggerWorkflowStage(ctx context.Context, ticketID uint, inboundNo string, triggers ticketmodel.InboundTrigger) error {
	// 验证工作流阶段参数
	if triggers.Status == "" {
		return fmt.Errorf("工作流状态不能为空")
	}
	var (
		ticketType string
		updateName string
		signal     common.WorkflowSignalInterface
	)
	// 决定工作流 ID 编号
	switch {
	case strings.HasPrefix(inboundNo, "PT"):
		ticketType = "Part"
		updateName = common.PartInboundUpdateSignal
	case strings.HasPrefix(inboundNo, "NPIT"):
		ticketType = "New"
		updateName = common.NewInboundUpdateSignal
		// 构造工作流控制信号
		signal = common.NewPartInboundSignal{
			Status:           triggers.Status,
			OperatorID:       triggers.OperatorID,
			OperatorName:     triggers.OperatorName,
			RequiredApproval: triggers.RequiredApproval,
			RequiredVerify:   triggers.RequireVerified,
			Comments:         triggers.Comments,
		}
	case strings.HasPrefix(inboundNo, "RPIT"):
		ticketType = "Repair"
		updateName = common.RepairPartInboundUpdateSignal
		// 构造工作流控制信号
		signal = common.RepairPartInboundSignal{
			Status:           triggers.Status,
			OperatorID:       triggers.OperatorID,
			OperatorName:     triggers.OperatorName,
			RequiredApproval: triggers.RequiredApproval,
			RequiredVerify:   triggers.RequireVerified,
			Comments:         triggers.Comments,
		}
	case strings.HasPrefix(inboundNo, "GSIT"), strings.HasPrefix(inboundNo, "SVIT"), strings.HasPrefix(inboundNo, "SWIT"):
		ticketType = "device"
		updateName = common.DeviceInboundUpdateSignal
		// 构建工作流控制信号
		signal = common.DeviceInboundSignal{
			Status:        triggers.Status,
			OperatorID:    triggers.OperatorID,
			OperatorName:  triggers.OperatorName,
			RequireVerify: triggers.RequireVerified,
			Comments:      triggers.Comments,
		}

	default:
		return errors.New("工单编号错误，请联系管理员进行系统确认！")
	}

	// 构造工作流ID
	workflowID := fmt.Sprintf("%s_Inbound_ticket_%d", ticketType, ticketID)

	s.logger.Info("准备更新工作流状态",
		zap.String("workflowID", workflowID),
		zap.String("stage", triggers.Stage),
		zap.String("status", triggers.Status),
		zap.String("operatorName", triggers.OperatorName),
		zap.Uint("ticketID", ticketID))
	// 更新工作流状态
	updateOptions := client.UpdateWorkflowOptions{
		WorkflowID:   workflowID,
		UpdateName:   updateName,
		WaitForStage: client.WorkflowUpdateStageCompleted,
		Args:         []interface{}{signal},
	}
	updateHandle, err := s.temporalClient.UpdateWorkflow(ctx, updateOptions)
	if err != nil {
		s.logger.Error("更新工作流状态失败",
			zap.Error(err),
			zap.String("workflowID", workflowID),
			zap.String("stage", triggers.Stage),
			zap.String("status", triggers.Status),
			zap.String("operatorName", triggers.OperatorName),
			zap.Uint("ticketID", ticketID))
		return fmt.Errorf("更新工作流状态失败: %w", err)
	}

	err = updateHandle.Get(ctx, nil)
	if err != nil {
		s.logger.Error("Unable to get update result", zap.Error(err))
		return err
	}

	s.logger.Info("成功更新入库工作流",
		zap.String("workflowID", workflowID),
		zap.String("stage", triggers.Stage),
		zap.String("status", triggers.Status),
		zap.Uint("ticketID", ticketID))

	return nil
}

// StartPartInboundWorkflow 启动维修入库工作流
//func (s *inboundTicketService) StartPartInboundWorkflow(ctx context.Context, StartInput common.PartInboundWorkflowInput) error {
//	// 构造工作流输入参数
//	input := common.PartInboundWorkflowInput{
//		SN:                 StartInput.SN,
//		RepairTicketID:     StartInput.RepairTicketID,
//		HandlingSuggestion: StartInput.HandlingSuggestion,
//		EngineerID:         StartInput.EngineerID,
//		EngineerName:       StartInput.EngineerName,
//		RecipientID:        StartInput.RecipientID,
//		RecipientName:      StartInput.RecipientName,
//		InboundNotes:       StartInput.InboundNotes,
//	}
//
//	// 设置工作流选项
//	options := client.StartWorkflowOptions{
//		ID:        fmt.Sprintf("Part_Inbound_ticket_%d", input.RepairTicketID),
//		TaskQueue: common.PartInboundTicketTask,
//	}
//
//	// 启动工作流
//	_, err := s.temporalClient.ExecuteWorkflow(ctx, options, workflow.PartInboundWorkflow, input)
//	if err != nil {
//		s.logger.Error("启动维修入库工作流失败", zap.Error(err))
//		return fmt.Errorf("启动维修入库工作流失败: %w", err)
//	}
//	s.logger.Info("成功启动维修入库工作流",
//		zap.String("workflowID", options.ID))
//
//	return nil
//}
//
//// StartNewInboundWorkflow 启动新购入库工作流
//func (s *inboundTicketService) StartNewInboundWorkflow(ctx context.Context, StartInput common.NewInboundWorkflowInput) error {
//	// 设置工作流选项
//	options := client.StartWorkflowOptions{
//		ID:        fmt.Sprintf("New_Inbound_ticket_%d", StartInput.NewInboundTicketID),
//		TaskQueue: common.NewInboundTaskQueue,
//	}
//
//	// 启动工作流
//	_, err := s.temporalClient.ExecuteWorkflow(ctx, options, workflow.NewInboundWorkflow, StartInput)
//	if err != nil {
//		s.logger.Error("启动新购入库工作流失败", zap.Error(err))
//		return fmt.Errorf("启动新购入库工作流失败: %w", err)
//	}
//	s.logger.Info("成功启动新购入库工作流",
//		zap.String("workflowID", options.ID))
//
//	return nil
//}

// StartInboundWorkflow 启动入库工作流
func (s *inboundTicketService) StartInboundWorkflow(ctx context.Context, StartInput common.StartInboundWorkflowInput) error {
	var (
		options       client.StartWorkflowOptions
		workflowFunc  interface{}
		workflowInput interface{}
	)

	switch StartInput.GetInboundType() {
	case inbound.TypeNewPartInbound: // 新购入库
		// 进行类型断言
		newInput, ok := StartInput.(*common.NewPartInboundWorkflowInput)
		if !ok {
			return fmt.Errorf("new_part 类型断言失败")
		}
		workflowInput = *newInput
		// 设置工作流选项
		options = client.StartWorkflowOptions{
			ID:        fmt.Sprintf("New_Inbound_ticket_%d", newInput.NewInboundTicketID),
			TaskQueue: common.NewInboundTaskQueue,
		}
		//workflowFunc = workflow.NewInboundWorkflow
		workflowFunc = workflow.NewPartInboundWorkflow
	case inbound.TypeRepairedPartInbound: //返修入库
		// 进行类型断言
		repairInput, ok := StartInput.(*common.RepairInboundWorkflowInput)
		if !ok {
			return fmt.Errorf("repair_part 类型断言失败")
		}
		workflowInput = *repairInput
		// 设置工作流选项
		options = client.StartWorkflowOptions{
			ID:        fmt.Sprintf("Repair_Inbound_ticket_%d", repairInput.RepairInboundTicketID),
			TaskQueue: common.RepairInboundTaskQueue,
		}
		// TODO 增加返修入库工作流
		workflowFunc = workflow.RepairPartInboundWorkflow
	case inbound.TypeDismantledPartInbound: // 拆机入库
		// 进行类型断言
		dismantledInput, ok := StartInput.(*common.DismantledInboundWorkflowInput)
		if !ok {
			return fmt.Errorf("dismantled_part 类型断言失败")
		}
		workflowInput = *dismantledInput
		// 设置工作流选项
		options = client.StartWorkflowOptions{
			ID:        fmt.Sprintf("Dismantled_Inbound_ticket_%d", dismantledInput.DismantledInboundTicketID),
			TaskQueue: common.DismantledInboundTaskQueue,
		}
		workflowFunc = workflow.DismantledPartInboundWorkflow
	case inbound.TypeDeviceInbound:
		deviceInput, ok := StartInput.(*common.DeviceInboundWorkflowInput)
		if !ok {
			return fmt.Errorf("device_inbound 类型断言失败")
		}
		workflowInput = *deviceInput
		// 设置工作流选项
		options = client.StartWorkflowOptions{
			ID:        fmt.Sprintf("device_Inbound_ticket_%d", deviceInput.InboundTicketID),
			TaskQueue: common.DeviceInboundTaskQueue,
		}
		workflowFunc = workflow.DeviceInboundWorkflow
	default:
		return fmt.Errorf("不支持该入库类型")
	}
	// 启动工作流
	//fmt.Println(workflowInput)
	_, err := s.temporalClient.ExecuteWorkflow(ctx, options, workflowFunc, workflowInput)
	if err != nil {
		s.logger.Error("启动入库工作流失败", zap.Error(err))
		return fmt.Errorf("启动入库工作流失败: %w", err)
	}
	s.logger.Info("成功启动入库工作流",
		zap.String("workflowID", options.ID))

	return nil
}

// StartInboundWorkflow 启动入库工作流
func (s *inboundTicketService) StartInboundWorkflowV2(ctx context.Context, ticketID uint, req ticketmodel.WorkflowReq) error {
	var (
		options       client.StartWorkflowOptions
		workflowID    string
		TaskQueueName string
		workflowFunc  interface{}
		workflowInput interface{}
	)
	ticket, err := s.repo.GetInboundTicketByNo(ctx, req.InboundNo)
	if err != nil {
		return err
	}
	switch req.InboundType {
	case inbound.TypePartInbound:
		workflowID = fmt.Sprintf("Part_Inbound_ticket_%d", ticketID)
		TaskQueueName = common.PartInboundTaskQueue
		workflowFunc = workflow.PartInboundWorkflow
		workflowInput = common.PartInboundWorkflowInput{
			InboundTicketID:  ticketID,
			InboundNo:        req.InboundNo,
			RequireVarify:    req.Verify,
			InboundReason:    ticket.InboundReason,
			RepairTicketID:   ticket.RepairTicketID,
			OutboundTicketID: ticket.OutboundTicketID,
		}
	case inbound.TypeDeviceInbound:
		workflowID = fmt.Sprintf("Device_Inbound_ticket_%d", ticketID)
		TaskQueueName = common.DeviceInboundTaskQueue
		workflowInput = common.DeviceInboundWorkflowInput{
			InboundTicketID:  ticketID,
			InboundNo:        req.InboundNo,
			RequireVarify:    req.Verify,
			InboundReason:    ticket.InboundReason,
			RepairTicketID:   ticket.RepairTicketID,
			OutboundTicketID: ticket.OutboundTicketID,
		}
		workflowFunc = workflow.DeviceInboundWorkflow
	default:
		return fmt.Errorf("不支持该入库类型")
	}
	options = client.StartWorkflowOptions{
		ID:        workflowID,
		TaskQueue: TaskQueueName,
	}
	// 启动工作流
	_, err = s.temporalClient.ExecuteWorkflow(ctx, options, workflowFunc, workflowInput)
	if err != nil {
		s.logger.Error("启动入库工作流失败", zap.Error(err))
		return fmt.Errorf("启动入库工作流失败: %w", err)
	}
	s.logger.Info("成功启动入库工作流",
		zap.String("workflowID", options.ID))

	return nil
}

// GetPartInboundTicketByNo 根据入库单号获取入库工单
func (s *inboundTicketService) GetPartInboundTicketByNo(ctx context.Context, inboundNo string) (*ticketmodel.PartInboundTicket, error) {
	// 记录日志
	s.logger.Info("开始查询入库工单",
		zap.String("inboundNo", inboundNo))

	// 调用repository层方法
	ticket, err := s.repo.GetPartTicketByNo(ctx, inboundNo)
	if err != nil {
		s.logger.Error("查询入库工单失败",
			zap.String("inboundNo", inboundNo),
			zap.Error(err))
		return nil, fmt.Errorf("查询入库工单失败: %w", err)
	}

	// 记录成功日志
	s.logger.Info("成功查询入库工单",
		zap.String("inboundNo", inboundNo),
		zap.Uint("ticketID", ticket.ID))

	return ticket, nil
}

func (s *inboundTicketService) GetPartInboundTicketByRepairTicketID(ctx context.Context, repairTicketID uint) (*ticketmodel.PartInboundTicket, error) {
	return s.repo.GetPartTicketByRepairTicketID(ctx, repairTicketID)
}

// ListPartInboundTickets 获取入库工单列表
func (s *inboundTicketService) ListPartInboundTickets(ctx context.Context, query *ticketmodel.ListParams) ([]*ticketmodel.PartInboundTicket, int64, error) {
	s.logger.Info("开始获取维修入库工单列表",
		zap.Int("page", query.Page),
		zap.Int("pageSize", query.PageSize),
		zap.String("status", query.InboundStatus),
	)
	tickets, total, err := s.repo.ListPartTickets(ctx, query)
	if err != nil {
		s.logger.Error("获取维修入库工单列表失败",
			zap.Error(err),
			zap.Int("page", query.Page),
			zap.Int("pageSize", query.PageSize))
		return nil, 0, fmt.Errorf("获取入库工单列表失败: %w", err)
	}

	s.logger.Info("成功获取维修入库工单列表",
		zap.Int64("total", total),
		zap.Int("count", len(tickets)))

	return tickets, total, nil
}

// GetHistoryByInboundNo 获取入库工单历史记录
func (s *inboundTicketService) GetHistoryByInboundNo(ctx context.Context, inboundNo string) ([]*ticketmodel.InboundTicketHistory, error) {
	s.logger.Info("开始获取入库工单历史记录",
		zap.String("inboundNo", inboundNo))

	// 先检查工单是否存在
	_, err := s.GetPartInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		s.logger.Error("获取工单信息失败",
			zap.Error(err),
			zap.String("inboundNo", inboundNo))
		return nil, fmt.Errorf("获取工单信息失败: %w", err)
	}

	// 获取历史记录
	histories, err := s.repo.GetHistoryByInboundNo(ctx, inboundNo)
	if err != nil {
		s.logger.Error("获取工单历史记录失败",
			zap.Error(err),
			zap.String("inboundNo", inboundNo))
		return nil, fmt.Errorf("获取工单历史记录失败: %w", err)
	}

	s.logger.Info("成功获取入库工单历史记录",
		zap.String("inboundNo", inboundNo),
		zap.Int("historyCount", len(histories)))

	return histories, nil
}

func (s *inboundTicketService) GenerateNewInboundNo() string {
	// 使用时间戳和随机数生成工单号
	now := time.Now()
	return fmt.Sprintf("NPIT%s%d", now.Format("20060102"), now.UnixNano()%10000)
}

func (s *inboundTicketService) CreateTicketHistory(ctx context.Context, history *ticketmodel.InboundTicketHistory) error {
	return s.repo.CreateTicketHistory(ctx, history)
}

func (s *inboundTicketService) GetNewInboundTicketByID(ctx context.Context, id uint) (*ticketmodel.NewInboundTicket, error) {
	return s.repo.GetNewInboundTicketByID(ctx, id)
}

func (s *inboundTicketService) InitNewNewInbound(ctx context.Context, ticket *ticketmodel.NewInboundTicket, history *ticketmodel.InboundTicketHistory) error {
	ticket.InboundNo = s.GenerateNewInboundNo()
	history.InboundNo = ticket.InboundNo
	return s.repo.InitNewInbound(ctx, ticket, history)
}

func (s *inboundTicketService) CreateNewInboundTicket(ctx context.Context, ticket *ticketmodel.NewInboundTicket) error {
	ticket.InboundNo = s.GenerateNewInboundNo()
	return s.repo.CreateNewInboundTicket(ctx, ticket)
}

func (s *inboundTicketService) ListNewInboundTicketsByNo(ctx context.Context, query *ticketmodel.ListParams) ([]*ticketmodel.NewInboundTicket, int64, error) {
	tickets, totals, err := s.repo.ListNewTickets(ctx, query)
	if err != nil {
		return nil, 0, err
	}
	return tickets, totals, nil
}

func (s *inboundTicketService) ListNewInboundTickets(ctx context.Context, query *ticketmodel.ListParams) ([]*ticketmodel.NewInboundTicket, int64, error) {
	tickets, totals, err := s.repo.ListNewTickets(ctx, query)
	if err != nil {
		return nil, 0, err
	}
	return tickets, totals, nil
}

// InitInboundTicketAndHistory 初始化工单和历史记录
func (s *inboundTicketService) InitInboundTicketAndHistory(ctx context.Context, inboundTicket ticketmodel.InboundTicketInterface, inboundHistory *ticketmodel.InboundTicketHistory) (common.StartInboundWorkflowInput, error) {
	var (
		input common.StartInboundWorkflowInput
	)
	switch inboundTicket.GetTicketType() {
	case inbound.TypeNewPartInbound:
		// 初始化新购入库工单及历史记录
		newInboundTicket, ok := inboundTicket.(*ticketmodel.NewInboundTicket)
		if !ok {
			return nil, fmt.Errorf("类型转换失败：不是有效的新购入库工单")
		}

		err := s.repo.InitNewInbound(ctx, newInboundTicket, inboundHistory)
		if err != nil {
			return nil, err
		}

		newInput := &common.NewPartInboundWorkflowInput{
			NewInboundTicketID: newInboundTicket.ID,
			InboundNo:          newInboundTicket.InboundNo,
			RequireApproval:    true,
		}
		input = newInput
	case inbound.TypeRepairedPartInbound:
		repairInboundTicket, ok := inboundTicket.(*ticketmodel.RepairPartInboundTicket)
		if !ok {
			return nil, fmt.Errorf("类型转换失败：不是有效的维修入库工单")
		}

		history := &ticketmodel.RepairPartTicketHistory{
			InboundNo:     inboundHistory.InboundNo,
			NewStatus:     inboundHistory.NewStatus,
			OperationTime: inboundHistory.OperationTime,
			OperatorID:    inboundHistory.OperatorID,
			OperatorName:  inboundHistory.OperatorName,
			Stage:         inboundHistory.Stage,
		}
		err := s.repo.InitRepairInboundTicket(ctx, repairInboundTicket, history)
		if err != nil {
			return nil, err
		}

		repairInput := &common.RepairInboundWorkflowInput{
			RepairInboundTicketID: repairInboundTicket.ID,
			InboundNo:             repairInboundTicket.InboundNo,
			RequireVerification:   true, //默认需要验证
		}
		input = repairInput
	case inbound.TypeDismantledPartInbound:
		dismantledInboundTicket, ok := inboundTicket.(*ticketmodel.DismantledPartInboundTicket)
		if !ok {
			return nil, fmt.Errorf("类型转换失败：不是有效的拆机配件入库工单")
		}

		history := &ticketmodel.DismantledPartTicketHistory{
			InboundHistoryTemplate: ticketmodel.InboundHistoryTemplate{
				InboundNo:     inboundHistory.InboundNo,
				NewStatus:     inboundHistory.NewStatus,
				OperationTime: inboundHistory.OperationTime,
				OperatorID:    inboundHistory.OperatorID,
				OperatorName:  inboundHistory.OperatorName,
				Stage:         inboundHistory.Stage,
			},
		}
		err := s.repo.InitDismantledInboundTicket(ctx, dismantledInboundTicket, history)
		if err != nil {
			return nil, err
		}

		dismantledInput := &common.DismantledInboundWorkflowInput{
			DismantledInboundTicketID: dismantledInboundTicket.ID,
			InboundNo:                 dismantledInboundTicket.InboundNo,
		}
		input = dismantledInput
	default:
		return nil, fmt.Errorf("维修类型错误")
	}
	return input, nil
}

// CreateRepairInboundTicket 创建维修配件入库工单
func (s *inboundTicketService) InitRepairInboundTicket(ctx context.Context, ticket *ticketmodel.RepairPartInboundTicket) error {
	history := &ticketmodel.InboundTicketHistory{
		InboundNo:     ticket.InboundNo,
		NewStatus:     ticket.Status,
		OperatorID:    ticket.SubmitterID,
		OperatorName:  ticket.Submitter,
		OperationTime: time.Now(),
		Stage:         ticket.Stage,
	}

	StartWorkflowInput, err := s.InitInboundTicketAndHistory(ctx, ticket, history)
	if err != nil {
		return err
	}
	if err := s.StartInboundWorkflow(ctx, StartWorkflowInput); err != nil {
		return err
	}
	return nil
}

func (s *inboundTicketService) GetRepairPartInboundTicketByNo(ctx context.Context, inboundNo string) (*ticketmodel.RepairPartInboundTicket, error) {
	return s.repo.GetRepairPartInboundTicketByNo(ctx, inboundNo)
}

// InitDismantledInboundTicket 创建拆机配件入库工单
func (s *inboundTicketService) InitDismantledInboundTicket(ctx context.Context, ticket *ticketmodel.DismantledPartInboundTicket) error {
	history := &ticketmodel.InboundTicketHistory{
		InboundNo:     ticket.InboundNo,
		NewStatus:     ticket.Status,
		OperatorID:    ticket.SubmitterID,
		OperatorName:  ticket.Submitter,
		OperationTime: time.Now(),
		Stage:         ticket.Stage,
	}

	StartWorkflowInput, err := s.InitInboundTicketAndHistory(ctx, ticket, history)
	if err != nil {
		return err
	}
	if err := s.StartInboundWorkflow(ctx, StartWorkflowInput); err != nil {
		return err
	}
	return nil
}

func (s *inboundTicketService) GetDismantledPartInboundTicketByNo(ctx context.Context, inboundNo string) (*ticketmodel.DismantledPartInboundTicket, error) {
	return s.repo.GetDismantledPartInboundTicketByNo(ctx, inboundNo)
}

// CreateDeviceInboundTicket 创建整机入库工单
func (s *inboundTicketService) CreateDeviceInboundByInput(ctx context.Context, deviceInbound *inbound.DeviceInbound) error {
	var (
		inboundReason string
		details       []inbound.DeviceInboundDetail
	)
	// 获取入库原因
	switch deviceInbound.DeviceType {
	case constants.AssetTypeServer: // 普通服务器
		inboundReason = inbound.TypeServerInbound
	case constants.AssetTypeGPUServer: // GPU服务器
		inboundReason = inbound.TypeGPUServerInbound
	case constants.AssetTypeSwitch: // 交换机
		inboundReason = inbound.TypeSwitchInbound

	default:
		return fmt.Errorf("非法的入库类型��%v", deviceInbound.DeviceType)
	}

	// 获取采购单号
	purchase, err := s.purchaseSvc.GetPurchaseOrderByPurchaseOrderNo(ctx, deviceInbound.PurchaseOrderNo)
	if err != nil {
		return err
	}
	deviceInbound.PurchaseOrderID = purchase.ID

	// 生成入库单号
	inboundNo, err := s.inboundSvc.GenerateTicketNoByInboundType(inbound.TypeDeviceInbound, inboundReason)
	if err != nil {
		return err
	}

	deviceInbound.InboundNo = inboundNo

	// 入库详情
	for _, info := range deviceInbound.DeviceInfo {
		for i := uint(0); i < info.Amount; i++ {
			detail := inbound.DeviceInboundDetail{
				TemplateID:  info.TemplateID,
				WarehouseID: deviceInbound.WarehouseID,
			}
			details = append(details, detail)
		}
	}
	deviceInbound.DeviceDetails = details

	// 创建列表信息
	inboundList := &inbound.InboundList{
		InboundNo:     deviceInbound.InboundNo,
		Project:       deviceInbound.Project,
		InboundType:   inbound.TypeDeviceInbound,
		InboundReason: inboundReason,
		InboundTitle:  deviceInbound.InboundTitle,
		AssetType:     deviceInbound.DeviceType,
		CreateBy:      deviceInbound.CreateBy,
		CreaterID:     deviceInbound.CreateID,
		Stage:         "Processing",
	}

	// 入库工单
	deviceInboundTicket := &ticketmodel.DeviceInboundTicket{
		InboundTicketTemplate: ticketmodel.InboundTicketTemplate{
			InboundNo:   deviceInbound.InboundNo,
			SubmitterID: deviceInbound.CreateID,
			Submitter:   deviceInbound.CreateBy,
			Stage:       common.StageAssetApproval,
			Status:      common.StatusWaitingAssetApproval,
		},
	}
	// 入库历史
	deviceInboundHistory := &ticketmodel.DeviceInboundTicketHistory{
		InboundHistoryTemplate: ticketmodel.InboundHistoryTemplate{
			InboundNo:     deviceInbound.InboundNo,
			OperationTime: time.Now(),
			OperatorID:    deviceInbound.CreateID,
			OperatorName:  deviceInbound.CreateBy,
			Stage:         common.StageAssetApproval,
			NewStatus:     common.StatusWaitingAssetApproval,
		},
	}
	err = s.CreateDeviceInbound(ctx, deviceInbound, inboundList, deviceInboundTicket, deviceInboundHistory)
	if err != nil {
		return err
	}
	return nil
}

func (s *inboundTicketService) CreateDeviceInbound(ctx context.Context, deviceInbound *inbound.DeviceInbound, inboundList *inbound.InboundList, deviceInboundTicket *ticketmodel.DeviceInboundTicket, deviceInboundHistory *ticketmodel.DeviceInboundTicketHistory) error {
	err := s.repo.InitDeviceInbound(ctx, deviceInbound, inboundList, deviceInboundTicket, deviceInboundHistory)
	if err != nil {
		return err
	}
	fmt.Println("第三次出现：", deviceInbound.RequireVarify)
	// 启动工作流
	startInput := &common.DeviceInboundWorkflowInput{
		InboundTicketID: deviceInboundTicket.ID,
		InboundNo:       deviceInbound.InboundNo,
		RequireVarify:   deviceInbound.RequireVarify,
	}
	err = s.StartInboundWorkflow(ctx, startInput)
	if err != nil {
		return err
	}
	return nil
}

// validateInboundParams 校验入库单参数
func (s *inboundTicketService) validateInboundParams(ctx context.Context, inboundReq ticketmodel.InboundTicket) error {
	if inboundReq.Project == "" {
		return fmt.Errorf("项目不能为空")
	}
	if inboundReq.InboundTitle == "" {
		return fmt.Errorf("入库标题不能为空")
	}
	if inboundReq.InboundType == "" {
		return fmt.Errorf("入库类型不能为空")
	}
	if inboundReq.InboundReason == "" {
		return fmt.Errorf("入库原因不能为空")
	}

	// 校验入库原因是否为有效的预定义常量
	validInboundReasons := []string{
		constants.SourceTypeNewPurchase,  // 新购
		constants.SourceTypeDismantled,   // 拆机
		constants.SourceTypeReturnRepair, // 返厂维修
		constants.SourceTypeReturn,       // 退回
		constants.SourceTypeOther,        // 其他
		constants.SourceTypeAllocate,     // 调拨
	}

	isValidReason := false
	for _, validReason := range validInboundReasons {
		if inboundReq.InboundReason == validReason {
			isValidReason = true
			break
		}
	}

	if !isValidReason {
		return fmt.Errorf("无效的入库原因：%s", inboundReq.InboundReason)
	}

	if inboundReq.WarehouseID == 0 {
		return fmt.Errorf("目标仓库ID不能为空")
	}
	if len(inboundReq.Info) == 0 {
		return fmt.Errorf("入库信息不能为空")
	}

	// 新购入库特殊校验
	if inboundReq.InboundType == "new_purchase" {
		if inboundReq.TrackingInfo == "" {
			return fmt.Errorf("新购入库: 物流信息不能为空")
		}
		if inboundReq.MayArriveAt.IsZero() {
			return fmt.Errorf("新购入库: 预计到达时间不能为空")
		}
		if inboundReq.PurchaseNo == "" {
			return fmt.Errorf("新购入库: 采购单号不能为空")
		}
	}

	// 校验Info信息
	for i, info := range inboundReq.Info {
		if info.ProductID == 0 {
			return fmt.Errorf("入库信息第%d项: 规格ID不能为空", i+1)
		}
		if info.Amount <= 0 {
			return fmt.Errorf("入库信息第%d项: 数量必须大于0", i+1)
		}
		Product, err := s.productSvc.GetByID(ctx, info.ProductID)
		if err != nil {
			return err
		}
		assetType := cmdbCommon.MapMaterialTypeToAssetType[Product.MaterialType]
		if inboundReq.InboundType == inbound.TypeDeviceInbound {
			if assetType == "" {
				return fmt.Errorf("入库信息第 %d 项: 物料类型不符合规范，设备入库不能选择 %s ", i+1, Product.MaterialType)
			}
		} else {
			if assetType != "" {
				return fmt.Errorf("入库信息第 %d 项: 物料类型不符合规范，配件入库不能选择 %s", i+1, Product.MaterialType)
			}
		}
	}
	return nil
}

// GetInboundHistoryByNo 获取入库单历史记录
func (s *inboundTicketService) GetInboundHistoryByNo(ctx context.Context, inboundNo string) ([]ticketmodel.InboundHistory, error) {
	return s.repo.GetInboundHistoryByNo(ctx, inboundNo)
}

// UpdateInboundDetail 更新入库明细
func (s *inboundTicketService) UpdateInboundDetail(ctx context.Context, inboundType, inboundReason string, details []ticketmodel.InboundDetail) error {
	var (
		componentSNs        []string
		deviceSNs           []string
		unExitSNs           []string // 用于存储不存在的SN
		assetStatusNotMatch []string // 用于存储状态不匹配的SN
		snUnfitPn           []string // 用于存储SN与产品PN不匹配的SN
		projectNoMatch      []string // 用于存储项目不匹配的SN
	)
	ticket, err := s.GetInboundTicketByNo(ctx, details[0].InboundNo)
	if err != nil {
		return fmt.Errorf("获取入库工单失败: %w", err)
	}

	if len(details) == 0 {
		return fmt.Errorf("入库明细不能为空")
	}

	switch inboundType {
	case inbound.TypePartInbound:
		// 创建一个map用于检查当前请求中的SN重复
		snMap := make(map[string]bool)
		for i, detail := range details {
			if detail.ComponentSN == "" {
				return fmt.Errorf("入库明细中第 %d 行的组件SN不能为空", i+1)
			}
			// 检查当前请求中SN是否重复
			if snMap[detail.ComponentSN] {
				return fmt.Errorf("入库明细中第 %d 行存在重复的组件SN: %s", i+1, detail.ComponentSN)
			}
			snMap[detail.ComponentSN] = true
			// 收集组件SN
			componentSNs = append(componentSNs, detail.ComponentSN)
		}
		switch inboundReason {
		case constants.SourceTypeNewPurchase:
			// 检查数据库中是否存在重复的组件SN
			existingSNs, err := s.repo.CheckExistingSNs(ctx, inboundType, componentSNs)
			if err != nil {
				return fmt.Errorf("检查组件SN是否存在时发生错误: %w", err)
			}
			if len(existingSNs) > 0 {
				return fmt.Errorf("以下组件SN在系统中已存在: %s", strings.Join(existingSNs, ", "))
			}
		case constants.SourceTypeDismantled:
			// 检查入库明细中每个设备SN和配件状态
			for i, detail := range details {
				if detail.DeviceSN == "" {
					return fmt.Errorf("入库明细中第 %d 行的设备SN不能为空", i+1)
				}
				if detail.ComponentState == "" {
					return fmt.Errorf("入库明细中第 %d 行的配件状态不能为空", i+1)
				}
				deviceSNs = append(deviceSNs, detail.DeviceSN)
				if detail.ComponentSN != "" {
					componentSNs = append(componentSNs, detail.ComponentSN)
				}
			}

			// 仅校验查得到的信息
			spares, err := s.spareSvc.GetSpareBySNs(ctx, componentSNs)
			if err != nil {
				return err
			}
			for i, spare := range spares {
				if spare.AssetStatus == constants.AssetStatusIdle {
					assetStatusNotMatch = append(assetStatusNotMatch, spare.SN)
				}
				if snMap[spare.SN] {
					if spare.ProductID != details[i].ProductID {
						snUnfitPn = append(snUnfitPn, spare.SN)
					}
					if spare.WarehouseID != ticket.WarehouseID {
						projectNoMatch = append(projectNoMatch, spare.SN)
					}
				}
			}
			if len(assetStatusNotMatch) > 0 {
				return fmt.Errorf("以下配件处于闲置状态，已在仓库中: %v", assetStatusNotMatch)
			}
			if len(snUnfitPn) > 0 {
				return fmt.Errorf("以下配件SN与入库明细中的产品PN不匹配: %v", snUnfitPn)
			}
			if len(projectNoMatch) > 0 {
				return fmt.Errorf("无法跨项目拆机以下配件SN: %v", projectNoMatch)
			}

			// 检查主设备SN是否属于此项目
			devices, _, err := s.deviceSvc.ListBySNsV2(ctx, deviceSNs, 2)
			if err != nil {
				return fmt.Errorf("获取主设备信息失败")
			}
			for _, device := range devices {
				if device.Resource.Project != ticket.Project {
					projectNoMatch = append(projectNoMatch, device.SN)
				}
			}
			if len(projectNoMatch) > 0 {
				return fmt.Errorf("以下设备不属于%s项目: %v", ticket.Project, projectNoMatch)
			}

			// 检查主设备SN是否存在
			existingSNs, err := s.repo.CheckExistingSNs(ctx, inbound.TypeDeviceInbound, deviceSNs)
			if err != nil {
				return err
			}
			for _, deviceSN := range deviceSNs {
				if !slices.Contains(existingSNs, deviceSN) {
					unExitSNs = append(unExitSNs, deviceSN)
				}
			}
			if len(unExitSNs) > 0 {
				return fmt.Errorf("以下设备SN不存在: %v", unExitSNs)
			}
		case constants.SourceTypeReturnRepair:
			// 检查入库明细中每个设备SN和配件状态
			for i, detail := range details {
				if detail.ReturnRepairType == "replace" && detail.NewComponentSN == "" {
					return fmt.Errorf("入库明细中第 %d 行换新设备新SN不能为空", i+1)
				}
				if detail.ComponentState == "" {
					return fmt.Errorf("入库明细中第 %d 行的配件状态不能为空", i+1)
				}
				if detail.ReturnRepairType == "repair" {
					componentSNs = append(componentSNs, detail.ComponentSN)
				}
			}
			spares, err := s.spareSvc.GetSpareBySNs(ctx, componentSNs)
			if err != nil {
				return err
			}
			for i, spare := range spares {
				if spare.AssetStatus != constants.AssetStatusRepairing {
					assetStatusNotMatch = append(assetStatusNotMatch, spare.SN)
				}
				if snMap[spare.SN] {
					if spare.ProductID != details[i].ProductID {
						snUnfitPn = append(snUnfitPn, spare.SN)
					}
					if spare.WarehouseID != ticket.WarehouseID {
						projectNoMatch = append(projectNoMatch, spare.SN)
					}
				}
			}
			if len(assetStatusNotMatch) > 0 {
				return fmt.Errorf("以下配件不处于维修中状态: %v", assetStatusNotMatch)
			}
			if len(snUnfitPn) > 0 {
				return fmt.Errorf("以下配件SN与入库明细中的产品PN不匹配: %v", snUnfitPn)
			}
			if len(projectNoMatch) > 0 {
				return fmt.Errorf("无法跨项目返修入库以下配件SN: %v", projectNoMatch)
			}
		}
	case inbound.TypeDeviceInbound:
		// 创建一个map用于检查当前请求中的SN重复
		snMap := make(map[string]bool)
		for i, detail := range details {
			if detail.DeviceSN == "" {
				return fmt.Errorf("入库明细中第 %d 行的设备SN不能为空", i+1)
			}
			// 检查当前请求中SN是否重复
			if snMap[detail.DeviceSN] {
				return fmt.Errorf("入库明细第 %d 行中存在重复的设备SN: %s", i+1, detail.DeviceSN)
			}
			snMap[detail.DeviceSN] = true
			// 收集设备SN
			deviceSNs = append(deviceSNs, detail.DeviceSN)
		}
		switch inboundReason {
		case constants.SourceTypeNewPurchase:
			// 检查数据库中是否存在重复的设备SN
			existingSNs, err := s.repo.CheckExistingSNs(ctx, inboundType, deviceSNs)
			if err != nil {
				return fmt.Errorf("检查设备SN是否存在时发生错误: %w", err)
			}
			if len(existingSNs) > 0 {
				return fmt.Errorf("以下设备SN在系统中已存在: %s", strings.Join(existingSNs, ", "))
			}
		}
	default:
		return fmt.Errorf("不支持的入库类型: %s", inboundType)
	}

	return s.repo.UpdateInboundDetails(ctx, details)
}

// UpdateInboundDetailByImport 通过导入更新入库明细
// 只负责读取Excel信息，数据校验交给 UpdateInboundDetail 方法
func (s *inboundTicketService) UpdateInboundDetailByImport(ctx context.Context, importReq ticketmodel.DetailImportReq, inboundNo string) error {
	var (
		details []ticketmodel.InboundDetail
	)
	// 检查入库单是否存在
	ticket, err := s.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		return fmt.Errorf("获取入库单信息失败: %w", err)
	}

	// 打开文件
	src, err := importReq.File.Open()
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer func(src multipart.File) {
		err := src.Close()
		if err != nil {
			fmt.Printf("关闭文件失败：%v", err)
		}
	}(src)

	// 构建临时文件路径
	timestamp := time.Now().UnixNano()
	fileName := fmt.Sprintf("%s_%d.%s", ticket.InboundNo, timestamp, filepath.Ext(importReq.File.Filename))

	// 安全地构建文件路径
	basePath := "storage/imports"
	if err := os.MkdirAll(basePath, 0750); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	// 验证文件名安全性
	//if !isFileNameSafe(fileName) {
	//	return nil, fmt.Errorf("文件名不安全: %s", fileName)
	//}

	filePath := filepath.Join(basePath, fileName)

	// 确保路径没有超出基础目录
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return fmt.Errorf("获取绝对路径失败: %w", err)
	}
	absBasePath, err := filepath.Abs(basePath)
	if err != nil {
		return fmt.Errorf("获取基础目录绝对路径失败: %w", err)
	}
	if !strings.HasPrefix(absPath, absBasePath) {
		return fmt.Errorf("文件路径超出允许范围")
	}

	// 使用安全的文件操作
	// #nosec G304 -- 文件路径已经过验证，确保在安全的目录中
	dst, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_EXCL, 0600)
	if err != nil {
		return fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer func(dst *os.File) {
		err := dst.Close()
		if err != nil {
			fmt.Printf("关闭文件失败：%v", err)
		}
		// 删除临时文件
		err = os.Remove(filePath)
		if err != nil {
			fmt.Printf("删除临时文件失败：%v", err)
		}
	}(dst)

	// 保存文件到临时目录
	if _, err := io.Copy(dst, src); err != nil {
		return fmt.Errorf("保存文件失败: %w", err)
	}

	// 根据入库单类型选择相应的导入处理方法
	switch ticket.InboundType {
	case inbound.TypePartInbound:
		switch ticket.InboundReason {
		case constants.SourceTypeNewPurchase:
			details, err = s.readPartNewPurchaseExcel(filePath)
			if err != nil {
				return err
			}
		case constants.SourceTypeDismantled:
			details, err = s.readPartDismantledExcel(filePath)
			if err != nil {
				return err
			}
		case constants.SourceTypeReturnRepair:
			details, err = s.readPartReturnRepairedExcel(filePath)
			if err != nil {
				return err
			}
		default:
			return fmt.Errorf("不支持的入库: %s-%s", ticket.InboundType, ticket.InboundReason)
		}
	case inbound.TypeDeviceInbound:
		switch ticket.InboundReason {
		case constants.SourceTypeNewPurchase: // 新购入库
			// 处理新购入库的设备上架模板导入
			details, err = s.readDeviceNewPurchaseExcel(filePath)
			if err != nil {
				return err
			}
		default:
			return fmt.Errorf("不支持的入库: %s-%s", ticket.InboundType, ticket.InboundReason)
		}
	default:
		return fmt.Errorf("不支持的入库单类型: %s", ticket.InboundType)
	}
	details[0].InboundNo = inboundNo
	err = s.UpdateInboundDetail(ctx, ticket.InboundType, ticket.InboundReason, details)
	if err != nil {
		return err
	}
	return nil
}

// readDeviceNewPurchaseExcel 读取设备新购入库模板
func (s *inboundTicketService) readDeviceNewPurchaseExcel(filePath string) ([]ticketmodel.InboundDetail, error) {
	var (
		details []ticketmodel.InboundDetail
	)
	// 打开文件
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取入库详情发生错误，打开文件失败: %w", err)
	}
	defer func(file *excelize.File) {
		err := file.Close()
		if err != nil {
			fmt.Printf("关闭文件失败: %v\n", err)
		}
	}(file)
	// 获取每一行数据
	rows, err := file.GetRows(exportModel.NewDeviceSheet)
	if err != nil {
		return nil, fmt.Errorf("获取%v信息失败: %w", exportModel.NewDeviceSheet, err)
	}

	if len(rows) < 2 {
		return nil, fmt.Errorf("%v中没有数据", exportModel.NewDeviceSheet)
	}

	// 验证表头
	if err := validateHeaders(rows[0], exportModel.NewDeviceHeader); err != nil {
		return nil, fmt.Errorf("表头验证失败: %w", err)
	}

	for i := 1; i < len(rows); i++ {
		row := rows[i]
		if row[1] == "" {
			return nil, fmt.Errorf("第 %d 行的SN不能为空", i+1)
		}

		detailID, err := strconv.ParseUint(row[9], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("入库详情ID转换失败: %w", err)
		}
		productID, err := strconv.ParseUint(row[8], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("规格ID转换失败: %w", err)
		}

		item := ticketmodel.InboundDetail{
			DeviceSN:  row[1],
			ProductID: uint(productID),
			ID:        uint(detailID),
		}
		details = append(details, item)
	}
	return details, nil
}

// readPartDismantledExcel 读取拆机配件入库模板
func (s *inboundTicketService) readPartDismantledExcel(filePath string) ([]ticketmodel.InboundDetail, error) {
	var (
		details []ticketmodel.InboundDetail
	)
	// 打开文件
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取入库详情发生错误，打开文件失败: %w", err)
	}
	defer func(file *excelize.File) {
		err := file.Close()
		if err != nil {
			fmt.Printf("关闭文件失败: %v\n", err)
		}
	}(file)

	// 获取每一行数据
	rows, err := file.GetRows(exportModel.DismantlePartdSheet)
	if err != nil {
		return nil, fmt.Errorf("获取%v信息失败: %w", exportModel.DismantlePartdSheet, err)
	}

	if len(rows) < 2 {
		return nil, fmt.Errorf("%v中没有数据", exportModel.DismantlePartdSheet)
	}

	// 验证表头
	if err := validateHeaders(rows[0], exportModel.DismantledPartHeader); err != nil {
		return nil, fmt.Errorf("表头验证失败: %w", err)
	}

	for i := 1; i < len(rows); i++ {
		row := rows[i]
		// 验证必填字段
		if row[1] == "" {
			return nil, fmt.Errorf("第 %d 行的配件SN不能为空", i+1)
		}
		if row[2] == "" {
			return nil, fmt.Errorf("第 %d 行的主设备SN不能为空", i+1)
		}
		if row[3] == "" {
			return nil, fmt.Errorf("第 %d 行的配件状态不能为空", i+1)
		}

		// 转换ID字段
		detailID, err := strconv.ParseUint(row[12], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第 %d 行详情ID转换失败: %w", i+1, err)
		}

		productID, err := strconv.ParseUint(row[10], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第 %d 行产品ID转换失败: %w", i+1, err)
		}

		// 构建数据模型
		item := ticketmodel.InboundDetail{
			ID:             uint(detailID),
			ComponentSN:    row[1],
			DeviceSN:       row[2],
			ComponentState: row[11], // 使用映射后的状态值
			ProductID:      uint(productID),
		}
		details = append(details, item)
	}
	return details, nil
}

// readPartNewPurchaseExcel 读取新购配件入库模板
func (s *inboundTicketService) readPartNewPurchaseExcel(filePath string) ([]ticketmodel.InboundDetail, error) {
	var (
		details []ticketmodel.InboundDetail
	)
	// 打开文件
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取入库详情发生错误，打开文件失败: %w", err)
	}
	defer func(file *excelize.File) {
		err := file.Close()
		if err != nil {
			fmt.Printf("关闭文件失败: %v\n", err)
		}
	}(file)

	// 获取每一行数据
	rows, err := file.GetRows(exportModel.NewPartSheet)
	if err != nil {
		return nil, fmt.Errorf("获取%v信息失败: %w", exportModel.NewPartSheet, err)
	}

	if len(rows) < 2 {
		return nil, fmt.Errorf("%v中没有数据", exportModel.NewPartSheet)
	}

	// 验证表头
	if err := validateHeaders(rows[0], exportModel.NewPartHeader); err != nil {
		return nil, fmt.Errorf("表头验证失败: %w", err)
	}

	for i := 1; i < len(rows); i++ {
		row := rows[i]
		// 验证必填字段
		if row[1] == "" {
			return nil, fmt.Errorf("第 %d 行的配件SN不能为空", i+1)
		}

		// 转换ID字段
		detailID, err := strconv.ParseUint(row[9], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第 %d 行详情ID转换失败: %w", i+1, err)
		}

		productID, err := strconv.ParseUint(row[8], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第 %d 行产品ID转换失败: %w", i+1, err)
		}

		// 构建数据模型
		item := ticketmodel.InboundDetail{
			ID:          uint(detailID),
			ComponentSN: row[1],
			ProductID:   uint(productID),
		}
		details = append(details, item)
	}
	return details, nil
}

// readPartReturnRepairedExcel 读取返修配件入库模板
func (s *inboundTicketService) readPartReturnRepairedExcel(filePath string) ([]ticketmodel.InboundDetail, error) {
	var (
		details []ticketmodel.InboundDetail
	)
	// 打开文件
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取入库详情发生错误，打开文件失败: %w", err)
	}
	defer func(file *excelize.File) {
		err := file.Close()
		if err != nil {
			fmt.Printf("关闭文件失败: %v\n", err)
		}
	}(file)

	// 获取每一行数据
	rows, err := file.GetRows(exportModel.ReturnRepairPartSheet)
	if err != nil {
		return nil, fmt.Errorf("获取%v信息失败: %w", exportModel.ReturnRepairPartSheet, err)
	}

	if len(rows) < 2 {
		return nil, fmt.Errorf("%v中没有数据", exportModel.ReturnRepairPartSheet)
	}

	// 验证表头
	if err := validateHeaders(rows[0], exportModel.ReturnRepairPartHeader); err != nil {
		return nil, fmt.Errorf("表头验证失败: %w", err)
	}

	for i := 1; i < len(rows); i++ {
		row := rows[i]
		// 验证必填字段
		if row[1] == "" {
			return nil, fmt.Errorf("第 %d 行的原SN不能为空", i+1)
		}
		if row[2] == "" {
			return nil, fmt.Errorf("第 %d 行的维修类型不能为空", i+1)
		}
		if row[4] == "" {
			return nil, fmt.Errorf("第 %d 行的配件状态不能为空", i+1)
		}

		// 转换ID字段
		detailID, err := strconv.ParseUint(row[14], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第 %d 行详情ID转换失败: %w", i+1, err)
		}

		productID, err := strconv.ParseUint(row[11], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第 %d 行产品ID转换失败: %w", i+1, err)
		}

		// 构建数据模型
		item := ticketmodel.InboundDetail{
			ID:               uint(detailID),
			ComponentSN:      row[1],  // 原SN
			NewComponentSN:   row[3],  // 换新SN
			ReturnRepairType: row[12], // 使用映射后的维修类型值
			ComponentState:   row[13], // 使用映射后的配件状态值
			ProductID:        uint(productID),
		}
		details = append(details, item)
	}
	return details, nil
}

// validateHeaders 验证表头是否符合预期格式
func validateHeaders(actual []string, expected []string) error {
	// 检查长度
	if len(actual) < len(expected) {
		return fmt.Errorf("表头列数不足，实际: %d，期望: %d", len(actual), len(expected))
	}

	// 检查每一列的标题是否匹配
	for i, expectedHeader := range expected {
		if i >= len(actual) || !strings.Contains(actual[i], expectedHeader) {
			return fmt.Errorf("第 %d 列表头不匹配，实际: %s，期望包含: %s", i+1, actual[i], expectedHeader)
		}
	}

	return nil
}

// InitInboundTicketService 创建入库工单服务
func InitInboundTicketService(repo repository.InboundTicketRepository, inboundSvc inboundSvc.InboundService, purchaseSvc purchaseService.PurchaseService, inventorySvc inventorySvc.InventoryService, productSvc product.ProductService, spareSvc asset.SpareService, deviceSvc asset.DeviceService, purchaseReqSvc service.PurchaseRequestService, purchaseInqSvc service.PurchaseInquiryService, purchaseCrtSvc service.PurchaseContractService, temporalClient client.Client, logger *zap.Logger) InboundTicketService {
	return &inboundTicketService{
		repo:           repo,
		inboundSvc:     inboundSvc,
		temporalClient: temporalClient,
		inventorySvc:   inventorySvc,
		productSvc:     productSvc,
		spareSvc:       spareSvc,
		deviceSvc:      deviceSvc,
		purchaseReqSvc: purchaseReqSvc,
		purchaseCrtSvc: purchaseCrtSvc,
		purchaseInqSvc: purchaseInqSvc,
		logger:         logger,
		purchaseSvc:    purchaseSvc,
	}
}

// GetInboundDetailListByNo 分页获取入库单详情列表
func (s *inboundTicketService) GetInboundDetailListByNo(ctx context.Context, inboundType, ticketNo string, page, pageSize int) ([]ticketmodel.InboundDetail, int64, error) {
	return s.repo.GetInboundDetailListByNo(ctx, inboundType, ticketNo, page, pageSize)
}
