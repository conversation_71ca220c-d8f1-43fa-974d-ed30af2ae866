package service

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"
)

// ColdMigrationServiceImpl 冷迁移服务实现
type ColdMigrationServiceImpl struct {
	coldMigrationRepo repository.ColdMigrationRepository
}

// NewColdMigrationService 创建冷迁移服务
func NewColdMigrationService(coldMigrationRepo repository.ColdMigrationRepository) ColdMigrationService {
	return &ColdMigrationServiceImpl{
		coldMigrationRepo: coldMigrationRepo,
	}
}

// GetColdMigrationByTicketID 根据故障单ID获取冷迁移记录
func (s *ColdMigrationServiceImpl) GetColdMigrationByTicketID(ctx context.Context, ticketID uint) (*model.ColdMigration, error) {
	return s.coldMigrationRepo.GetByTicketID(ctx, ticketID)
}

// ListColdMigrations 获取冷迁移记录列表
func (s *ColdMigrationServiceImpl) ListColdMigrations(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.ColdMigration, int64, error) {
	return s.coldMigrationRepo.List(ctx, page, pageSize, filters)
}
