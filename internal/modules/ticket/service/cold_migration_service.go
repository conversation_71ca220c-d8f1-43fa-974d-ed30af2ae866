package service

import (
	"backend/internal/modules/ticket/model"
	"context"
)

// ColdMigrationService 冷迁移服务接口
type ColdMigrationService interface {
	// GetColdMigrationByTicketID 根据故障单ID获取冷迁移记录
	GetColdMigrationByTicketID(ctx context.Context, ticketID uint) (*model.ColdMigration, error)

	// ListColdMigrations 获取冷迁移记录列表
	ListColdMigrations(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.ColdMigration, int64, error)
}
