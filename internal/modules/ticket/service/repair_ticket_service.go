package service

import (
	inventoryService "backend/internal/modules/inventory/service"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"
	"crypto/rand"
	"encoding/binary"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"go.temporal.io/sdk/client"
)

// RepairResult 维修结果结构体
type RepairResult struct {
	RepairResult string `json:"repair_result"`
	Solution     string `json:"solution"`
	RepairSteps  string `json:"repair_steps"`
}

// RepairTicketService 维修单服务接口
type RepairTicketService interface {
	// 基础CRUD操作
	CreateRepairTicket(ctx context.Context, faultTicketID uint, repairType string) (*model.RepairTicket, error)
	GetRepairTicketByID(ctx context.Context, id uint) (*model.RepairTicket, error)
	UpdateRepairTicket(ctx context.Context, ticket *model.RepairTicket) error
	UpdateRepairTicketStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error

	// 维修工单流程操作
	AssignRepairTicket(ctx context.Context, id uint, engineerID uint) error
	TakeRepairTicket(ctx context.Context, id uint, engineerID uint) error
	StartRepair(ctx context.Context, id uint) error
	ArriveOnSite(ctx context.Context, id uint) error
	StartHardwareReplace(ctx context.Context, id uint) error
	CompleteHardwareReplace(ctx context.Context, id uint, spareID uint) error
	CompleteRepair(ctx context.Context, id uint, result *RepairResult) error

	// 工作流触发方法
	TriggerWorkflowStage(ctx context.Context, ticketID uint, stage string, operatorID uint, operatorName string, comments string, data map[string]interface{}) error

	// 查询操作
	ListRepairTickets(ctx context.Context, page, pageSize int, query, status string, engineerID uint) ([]*model.RepairTicket, int64, error)
	ListAvailableRepairTickets(ctx context.Context, page, pageSize int) ([]*model.RepairTicket, int64, error)

	// 状态历史记录
	GetRepairTicketStatusHistory(ctx context.Context, ticketID uint) ([]*model.RepairTicketStatusHistory, error)

	// 资源申请操作
	RequestSpare(ctx context.Context, repairTicketID uint, productID uint, quantity int) (interface{}, error)
	RequestSpareMachine(ctx context.Context, repairTicketID uint, templateID uint) (interface{}, error)

	// 工作流相关
	SetTemporalClient(c client.Client)

	// 工作流恢复操作
	RecoverInconsistentWorkflows(ctx context.Context) error

	// 新增方法
	VerifyRepair(ctx context.Context, id uint, success bool, comments string) error

	// 新增方法
	AuthorizeRepairTicket(ctx context.Context, id uint, operatorID uint, operatorName string) error

	// 获取维修单历史记录
	GetRepairTicketHistory(ctx context.Context, repairTicketID uint) ([]model.RepairTicketStatusHistory, error)

	// 记录工程师变更历史
	RecordEngineerChange(ctx context.Context, id uint, oldEngineerName, newEngineerName string, operatorID uint, operatorName string) error
}

// repairTicketService 维修单服务实现
type repairTicketService struct {
	repairTicketRepo repository.RepairTicketRepository
	faultTicketRepo  repository.FaultTicketRepository
	spareService     inventoryService.SpareService
	temporalClient   client.Client
	userService      UserService // 使用已有的UserService接口
}

// NewRepairTicketService 创建维修单服务
func NewRepairTicketService(
	repairTicketRepo repository.RepairTicketRepository,
	faultTicketRepo repository.FaultTicketRepository,
	spareService inventoryService.SpareService,
	userService UserService, // 使用已有的UserService接口
) RepairTicketService {
	return &repairTicketService{
		repairTicketRepo: repairTicketRepo,
		faultTicketRepo:  faultTicketRepo,
		spareService:     spareService,
		userService:      userService, // 设置用户服务
	}
}

// SetTemporalClient 设置Temporal客户端
func (s *repairTicketService) SetTemporalClient(c client.Client) {
	s.temporalClient = c
}

// CreateRepairTicket 创建维修单
func (s *repairTicketService) CreateRepairTicket(ctx context.Context, faultTicketID uint, repairType string) (*model.RepairTicket, error) {
	// 生成工单号
	ticketNo := generateTicketNo()
	now := time.Now()

	// ===================== 强化输入清理和检查 =====================
	// 1. 记录原始输入类型
	originalInputType := repairType

	// 2. 基础清理：去除空格、转小写、移除不可见字符
	repairType = strings.TrimSpace(repairType)
	repairType = strings.ToLower(repairType)

	// 3. 记录清理后的类型值
	cleanedType := repairType

	// 4. 硬编码支持的枚举值 (与数据库保持绝对一致)
	validEnumMap := map[string]bool{
		"restart":      true,
		"hardware_fix": true,
	}

	// 5. 检查是否直接匹配
	var finalEnumValue string
	if validEnumMap[repairType] {
		finalEnumValue = repairType
		fmt.Printf("直接匹配有效枚举值: '%s'\n", finalEnumValue)
	} else {
		// 6. 不是有效枚举，尝试映射
		switch {
		case strings.Contains(repairType, "hardware") && (strings.Contains(repairType, "replace") || strings.Contains(repairType, "fix")):
			finalEnumValue = "hardware_fix"
		case strings.Contains(repairType, "software") || strings.Contains(repairType, "restart") || strings.Contains(repairType, "reboot"):
			finalEnumValue = "restart"
		case strings.Contains(repairType, "board") && strings.Contains(repairType, "repair"):
			finalEnumValue = "board_repair"
		case strings.Contains(repairType, "cold") && strings.Contains(repairType, "migrat"):
			finalEnumValue = "cold_migration"
		default:
			// 7. 都不匹配，使用安全默认值
			finalEnumValue = "hardware_fix" // 默认安全值
			fmt.Printf("无法映射类型'%s'，使用默认安全值'hardware_fix'\n", repairType)
		}
	}

	// 8. 特殊调试：确保结果值绝对正确
	var cleanEnumValue string
	switch finalEnumValue {
	case "restart":
		cleanEnumValue = "restart"
	case "hardware_fix":
		cleanEnumValue = "hardware_fix"
	default:
		cleanEnumValue = "hardware_fix" // 最终安全值
	}

	// 创建维修单对象 - 使用正确的字段
	ticket := &model.RepairTicket{
		TicketNo:      ticketNo,
		Status:        model.RepairTicketStatusWaitingAuthorization,
		CreatedTime:   now,
		FaultTicketID: faultTicketID,
		RepairType:    cleanEnumValue, // 使用清理和验证后的值
	}

	if err := s.repairTicketRepo.Create(ctx, ticket); err != nil {
		return nil, fmt.Errorf("创建维修单失败: %w (原始类型=%s, 清理类型=%s, 最终类型=%s)",
			err, originalInputType, cleanedType, cleanEnumValue)
	}

	// 移除自动启动工作流的代码，确保维修工作流只在故障单状态为approved_waiting_action时才会启动

	return ticket, nil
}

// GetRepairTicketByID 根据ID获取维修单
func (s *repairTicketService) GetRepairTicketByID(ctx context.Context, id uint) (*model.RepairTicket, error) {
	ticket, err := s.repairTicketRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 如果需要，手动预加载FaultTicket的Resource关联
	if ticket.FaultTicket != nil && ticket.FaultTicket.Resource.ID == 0 {
		faultTicket, err := s.faultTicketRepo.GetByID(ctx, ticket.FaultTicketID)
		if err == nil && faultTicket != nil {
			ticket.FaultTicket = faultTicket
		}
	}

	return ticket, nil
}

// UpdateRepairTicket 更新维修单
func (s *repairTicketService) UpdateRepairTicket(ctx context.Context, ticket *model.RepairTicket) error {
	// 先获取现有记录
	existingTicket, err := s.repairTicketRepo.GetByID(ctx, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取维修单失败: %w", err)
	}

	// 保留不应被用户更新的字段
	ticket.CreatedAt = existingTicket.CreatedAt
	ticket.CreatedTime = existingTicket.CreatedTime

	// 更新维修单
	return s.repairTicketRepo.Update(ctx, ticket)
}

// UpdateRepairTicketStatus 更新维修单状态
func (s *repairTicketService) UpdateRepairTicketStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error {
	// 使用事务处理状态更新
	return s.repairTicketRepo.WithTransaction(ctx, func(txCtx context.Context, repo repository.RepairTicketRepository) error {
		// 获取当前维修单
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			return fmt.Errorf("获取维修单失败: %w", err)
		}

		// 如果状态没有变化，则直接返回
		if ticket.Status == status {
			return nil
		}

		// 检查状态转换是否有效
		if !isValidStatusTransition(ticket.Status, status) {
			return fmt.Errorf("无效的状态转换: %s -> %s", ticket.Status, status)
		}

		previousStatus := ticket.Status
		now := time.Now()

		// 根据新状态更新维修单的时间字段
		switch status {
		case model.RepairTicketStatusWaitingAuthorization:
			// 如果是重新回到待授权状态，清空授权时间
			ticket.AuthorizationTime = nil
		case model.RepairTicketStatusWaitingAccept:
			// 如果是从待授权转为待接单，设置授权时间
			if previousStatus == model.RepairTicketStatusWaitingAuthorization {
				ticket.AuthorizationTime = &now
			}
		case model.RepairTicketStatusAssigned:
			ticket.AssignedTime = &now
			if ticket.AuthorizationTime != nil {
				ticket.WaitingDuration = int(now.Sub(*ticket.AuthorizationTime).Minutes())
			} else {
				// If AuthorizationTime is nil, calculate from creation time instead
				ticket.WaitingDuration = int(now.Sub(ticket.CreatedTime).Minutes())
				log.Printf("警告: 维修单ID=%d的AuthorizationTime为空，使用CreatedTime计算等待时长", ticket.ID)
			}
		case model.RepairTicketStatusInProgress:
			ticket.StartTime = &now
		case model.RepairTicketStatusReplacingHardware:
			ticket.HardwareReplaceStart = &now
		case model.RepairTicketStatusHardwareReplaceCompleted, model.RepairTicketStatusHardwareReplaceFailed:
			ticket.HardwareReplaceEnd = &now
		case model.RepairTicketStatusWaitingVerification:
			// 更新验证开始时间
			ticket.VerificationStartTime = &now
			// 计算总维修时长，使用验证开始时间减去授权时间
			if ticket.AuthorizationTime != nil {
				ticket.TotalRepairDuration = int(now.Sub(*ticket.AuthorizationTime).Minutes())
			}
		case model.RepairTicketStatusCompleted, model.RepairTicketStatusFailed:
			ticket.CompleteTime = &now
			// 保持总维修时长计算，已经在waiting_verification状态更新了
		}

		// 更新状态
		ticket.Status = status

		// 更新维修单
		if err := repo.Update(txCtx, ticket); err != nil {
			return fmt.Errorf("更新维修单状态失败: %w", err)
		}

		// 处理操作人姓名 - 确保与报障单行为一致
		recordOperatorName := operatorName

		// 系统操作特殊处理
		if operatorID == 0 {
			recordOperatorName = "系统"
		} else if (recordOperatorName == "" || strings.HasPrefix(recordOperatorName, "用户")) && s.userService != nil {
			// 如果操作人姓名为空或者是默认格式，且用户服务可用，则尝试获取真实姓名
			realName, err := s.userService.GetUserName(ctx, operatorID)
			if err == nil && realName != "" {
				// 获取成功，使用真实姓名
				recordOperatorName = realName
				log.Printf("成功获取用户ID=%d的真实姓名: %s", operatorID, recordOperatorName)
			} else {
				// 获取失败，使用默认格式 - 应该已经由GetUserName处理为"用户ID"格式
				recordOperatorName = fmt.Sprintf("用户%d", operatorID)
				log.Printf("无法获取用户ID=%d的真实姓名: %v", operatorID, err)
			}
		} else if recordOperatorName == "" {
			// 如果获取不到操作人姓名且用户服务不可用，使用默认格式
			recordOperatorName = fmt.Sprintf("用户%d", operatorID)
			log.Printf("用户服务不可用或未初始化，使用默认名称: 用户%d", operatorID)
		}

		// 记录状态历史
		history := &model.RepairTicketStatusHistory{
			RepairTicketID:   id,
			PreviousStatus:   previousStatus,
			NewStatus:        status,
			OperatorID:       operatorID,
			OperatorName:     recordOperatorName,
			OperationTime:    now,
			ActivityCategory: getRepairActivityCategory(status),
			IsSLAPause:       isRepairSLAPauseStatus(status),
			PauseReason:      getRepairPauseReason(status),
			Remarks:          fmt.Sprintf("状态从 %s 变更为 %s", previousStatus, status),
		}

		// 如果是系统操作，修改备注信息
		if operatorID == 0 {
			history.Remarks = fmt.Sprintf("系统自动将状态从 %s 变更为 %s", previousStatus, status)
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			// 仅记录警告，不影响主流程
			log.Printf("记录维修单状态历史失败: %v\n", err)
		}

		return nil
	})
}

// getRepairActivityCategory 根据状态获取活动类别
func getRepairActivityCategory(status string) string {
	switch status {
	case model.RepairTicketStatusWaitingAuthorization:
		return "authorization"
	case model.RepairTicketStatusWaitingAccept:
		return "ticket_acceptance"
	case model.RepairTicketStatusAssigned:
		return "assignment"
	case model.RepairTicketStatusInProgress:
		return "repair_execution"
	case model.RepairTicketStatusReplacingHardware:
		return "hardware_replacement"
	case model.RepairTicketStatusHardwareReplaceCompleted:
		return "hardware_replacement_completed"
	case model.RepairTicketStatusHardwareReplaceFailed:
		return "hardware_replacement_failed"
	case "waiting_verification":
		return "verification"
	case model.RepairTicketStatusCompleted:
		return "completion"
	case model.RepairTicketStatusFailed:
		return "failure"
	case model.RepairTicketStatusCancelled:
		return "cancellation"
	default:
		return "status_change"
	}
}

// isRepairSLAPauseStatus 判断是否为SLA暂停状态
func isRepairSLAPauseStatus(status string) bool {
	switch status {
	case model.RepairTicketStatusWaitingAuthorization, "waiting_verification":
		return true
	default:
		return false
	}
}

// getRepairPauseReason 获取暂停原因
func getRepairPauseReason(status string) string {
	switch status {
	case model.RepairTicketStatusWaitingAuthorization:
		return "等待授权"
	case "waiting_verification":
		return "等待验证"
	default:
		return ""
	}
}

// AssignRepairTicket 分配维修单
func (s *repairTicketService) AssignRepairTicket(ctx context.Context, id uint, engineerID uint) error {
	ticket, err := s.repairTicketRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if ticket.Status != "waiting" {
		return errors.New("只有等待中的维修单才能分配")
	}

	ticket.AssignedEngineerID = engineerID
	now := time.Now()
	ticket.AssignedTime = &now
	ticket.Status = "assigned"

	return s.repairTicketRepo.Update(ctx, ticket)
}

// TakeRepairTicket 工程师接单处理
func (s *repairTicketService) TakeRepairTicket(ctx context.Context, id uint, engineerID uint) error {
	// 使用事务处理状态更新
	return s.repairTicketRepo.WithTransaction(ctx, func(txCtx context.Context, repo repository.RepairTicketRepository) error {
		// 获取维修单信息
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			return fmt.Errorf("获取维修单失败: %w", err)
		}

		if ticket == nil {
			return errors.New("维修单不存在")
		}

		// 验证状态
		if ticket.Status != "waiting_accept" {
			return fmt.Errorf("当前状态(%s)不允许接单，只有待接单状态可以接单", ticket.Status)
		}

		// 检查关联故障单状态
		if ticket.FaultTicketID > 0 {
			// 确保故障单仓库已初始化
			if s.faultTicketRepo == nil {
				return errors.New("故障单仓库未初始化，无法检查故障单状态")
			}

			faultTicket, err := s.faultTicketRepo.GetByID(ctx, ticket.FaultTicketID)
			if err != nil {
				return fmt.Errorf("获取关联故障单失败: %w", err)
			}

			// 检查故障单状态是否为repairing
			if faultTicket.Status != "repairing" {
				return fmt.Errorf("关联故障单状态不是'%s'，当前为'%s'，无法接单",
					"repairing", faultTicket.Status)
			}
		}

		// 获取工程师姓名
		engineerName := fmt.Sprintf("工程师%d", engineerID)

		// 尝试从上下文获取用户真实姓名
		if userRealName, exists := ctx.Value("userRealName").(string); exists && userRealName != "" {
			engineerName = userRealName
		} else if userInfo, exists := ctx.Value("userInfo").(map[string]interface{}); exists {
			if realName, ok := userInfo["realName"].(string); ok && realName != "" {
				engineerName = realName
			}
		}

		// 保存原状态
		previousStatus := ticket.Status

		// 更新维修单，保存工程师ID和姓名
		ticket.AssignedEngineerID = engineerID
		ticket.AssignedEngineerName = engineerName
		ticket.Status = model.RepairTicketStatusAssigned
		now := time.Now()
		ticket.AssignedTime = &now

		// 计算等待响应时长
		if ticket.AuthorizationTime != nil {
			ticket.WaitingDuration = int(now.Sub(*ticket.AuthorizationTime).Minutes())
		}

		// 更新维修单
		if err := repo.Update(txCtx, ticket); err != nil {
			return fmt.Errorf("更新维修单信息失败: %w", err)
		}

		// 记录状态历史
		history := &model.RepairTicketStatusHistory{
			RepairTicketID:   id,
			PreviousStatus:   previousStatus,
			NewStatus:        model.RepairTicketStatusAssigned,
			OperatorID:       engineerID,
			OperatorName:     engineerName,
			OperationTime:    now,
			ActivityCategory: "assignment",
			IsSLAPause:       false,
			PauseReason:      "",
			Remarks:          "工程师接单",
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			// 仅记录警告，不影响主流程
			log.Printf("记录维修单状态历史失败: %v\n", err)
		}

		// 触发工作流状态转换
		err = s.TriggerWorkflowStage(
			ctx,
			id,
			common.StageEngineerTake,
			engineerID,
			engineerName,
			"工程师主动接单",
			map[string]interface{}{
				"engineer_id": engineerID,
				"take_time":   now.Format("2006-01-02 15:04:05"),
			},
		)
		if err != nil {
			return fmt.Errorf("触发工作流状态转换失败: %w", err)
		}

		return nil
	})
}

// StartRepair 开始维修，触发工作流状态转换
func (s *repairTicketService) StartRepair(ctx context.Context, id uint) error {
	// 使用事务处理状态更新
	return s.repairTicketRepo.WithTransaction(ctx, func(txCtx context.Context, repo repository.RepairTicketRepository) error {
		// 获取维修单信息
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			return fmt.Errorf("获取维修单失败: %w", err)
		}

		if ticket == nil {
			return errors.New("维修单不存在")
		}

		// 验证状态
		if ticket.Status != model.RepairTicketStatusAssigned &&
			ticket.Status != model.RepairTicketStatusAcknowledged {
			return fmt.Errorf("当前状态(%s)不允许开始维修", ticket.Status)
		}

		// 获取操作人信息(工程师)
		engineerID := ticket.AssignedEngineerID
		engineerName := ticket.AssignedEngineerName

		if engineerName == "" {
			engineerName = fmt.Sprintf("工程师%d", engineerID)
		}

		// 保存原状态
		previousStatus := ticket.Status

		// 更新维修单状态为进行中
		ticket.Status = model.RepairTicketStatusInProgress
		now := time.Now()
		ticket.StartTime = &now

		// 更新维修单
		if err := repo.Update(txCtx, ticket); err != nil {
			return fmt.Errorf("更新维修单状态失败: %w", err)
		}

		// 记录状态历史
		history := &model.RepairTicketStatusHistory{
			RepairTicketID:   id,
			PreviousStatus:   previousStatus,
			NewStatus:        model.RepairTicketStatusInProgress,
			OperatorID:       engineerID,
			OperatorName:     engineerName,
			OperationTime:    now,
			ActivityCategory: "repair_execution",
			IsSLAPause:       false,
			PauseReason:      "",
			Remarks:          "开始维修",
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			// 仅记录警告，不影响主流程
			log.Printf("记录维修单状态历史失败: %v\n", err)
		}

		// 触发工作流状态转换
		err = s.TriggerWorkflowStage(
			ctx,
			id,
			common.StageStartRepair,
			engineerID,
			engineerName,
			"开始维修",
			map[string]interface{}{
				"engineer_id": engineerID,
				"start_time":  now.Format("2006-01-02 15:04:05"),
			},
		)
		if err != nil {
			return fmt.Errorf("触发工作流状态转换失败: %w", err)
		}

		return nil
	})
}

// ArriveOnSite 到达现场，触发工作流状态转换
func (s *repairTicketService) ArriveOnSite(ctx context.Context, id uint) error {
	// 获取维修单信息
	ticket, err := s.GetRepairTicketByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取维修单失败: %w", err)
	}

	if ticket == nil {
		return errors.New("维修单不存在")
	}

	// 验证状态
	if ticket.Status != model.RepairTicketStatusInProgress {
		return fmt.Errorf("当前状态(%s)不允许记录到达现场", ticket.Status)
	}

	// 获取操作人信息(工程师)
	engineerID := ticket.AssignedEngineerID
	engineerName := ticket.AssignedEngineerName

	if engineerName == "" {
		engineerName = fmt.Sprintf("工程师%d", engineerID)
	}

	// 触发工作流状态转换
	err = s.TriggerWorkflowStage(
		ctx,
		id,
		common.StageArriveOnSite,
		engineerID,
		engineerName,
		"工程师已到达现场",
		map[string]interface{}{
			"arrive_time": time.Now().Format("2006-01-02 15:04:05"),
		},
	)
	if err != nil {
		return fmt.Errorf("触发工作流状态转换失败: %w", err)
	}

	return nil
}

// StartHardwareReplace 开始硬件更换，触发工作流状态转换
func (s *repairTicketService) StartHardwareReplace(ctx context.Context, id uint) error {
	// 使用事务处理状态更新
	return s.repairTicketRepo.WithTransaction(ctx, func(txCtx context.Context, repo repository.RepairTicketRepository) error {
		// 获取维修单信息
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			return fmt.Errorf("获取维修单失败: %w", err)
		}

		if ticket == nil {
			return errors.New("维修单不存在")
		}

		// 验证状态
		if ticket.Status != model.RepairTicketStatusInProgress {
			return fmt.Errorf("当前状态(%s)不允许开始硬件更换", ticket.Status)
		}

		// 获取操作人信息(工程师)
		engineerID := ticket.AssignedEngineerID
		engineerName := ticket.AssignedEngineerName

		if engineerName == "" {
			engineerName = fmt.Sprintf("工程师%d", engineerID)
		}

		// 保存原状态
		previousStatus := ticket.Status

		// 更新维修单状态为硬件替换中
		ticket.Status = model.RepairTicketStatusReplacingHardware
		now := time.Now()
		ticket.HardwareReplaceStart = &now

		// 更新维修单
		if err := repo.Update(txCtx, ticket); err != nil {
			return fmt.Errorf("更新维修单状态失败: %w", err)
		}

		// 记录状态历史
		history := &model.RepairTicketStatusHistory{
			RepairTicketID:   id,
			PreviousStatus:   previousStatus,
			NewStatus:        model.RepairTicketStatusReplacingHardware,
			OperatorID:       engineerID,
			OperatorName:     engineerName,
			OperationTime:    now,
			ActivityCategory: "hardware_replacement",
			IsSLAPause:       false,
			PauseReason:      "",
			Remarks:          "开始硬件更换",
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			// 仅记录警告，不影响主流程
			log.Printf("记录维修单状态历史失败: %v\n", err)
		}

		// 触发工作流状态转换
		err = s.TriggerWorkflowStage(
			ctx,
			id,
			common.StageStartHardwareReplace,
			engineerID,
			engineerName,
			"开始更换硬件",
			map[string]interface{}{
				"start_time": now.Format("2006-01-02 15:04:05"),
			},
		)
		if err != nil {
			return fmt.Errorf("触发工作流状态转换失败: %w", err)
		}

		return nil
	})
}

// CompleteHardwareReplace 完成硬件更换，触发工作流状态转换
func (s *repairTicketService) CompleteHardwareReplace(ctx context.Context, id uint, spareID uint) error {
	// 使用事务处理状态更新
	return s.repairTicketRepo.WithTransaction(ctx, func(txCtx context.Context, repo repository.RepairTicketRepository) error {
		// 获取维修单信息
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			return fmt.Errorf("获取维修单失败: %w", err)
		}

		if ticket == nil {
			return errors.New("维修单不存在")
		}

		// 验证状态
		if ticket.Status != model.RepairTicketStatusReplacingHardware {
			return fmt.Errorf("当前状态(%s)不允许完成硬件更换", ticket.Status)
		}

		// 获取操作人信息(工程师)
		engineerID := ticket.AssignedEngineerID
		engineerName := ticket.AssignedEngineerName

		if engineerName == "" {
			engineerName = fmt.Sprintf("工程师%d", engineerID)
		}

		// 保存原状态
		previousStatus := ticket.Status

		// 更新维修单状态为硬件替换完成
		ticket.Status = model.RepairTicketStatusHardwareReplaceCompleted
		now := time.Now()
		ticket.HardwareReplaceEnd = &now
		ticket.SpareID = spareID

		// 如果有开始时间，计算硬件操作时长
		if ticket.HardwareReplaceStart != nil {
			ticket.HardwareOperationDuration = int(now.Sub(*ticket.HardwareReplaceStart).Minutes())
		}

		// 更新维修单
		if err := repo.Update(txCtx, ticket); err != nil {
			return fmt.Errorf("更新维修单状态失败: %w", err)
		}

		// 记录状态历史
		history := &model.RepairTicketStatusHistory{
			RepairTicketID:   id,
			PreviousStatus:   previousStatus,
			NewStatus:        model.RepairTicketStatusHardwareReplaceCompleted,
			OperatorID:       engineerID,
			OperatorName:     engineerName,
			OperationTime:    now,
			ActivityCategory: "hardware_replacement_completed",
			IsSLAPause:       false,
			PauseReason:      "",
			Remarks:          fmt.Sprintf("硬件更换完成，使用备件ID: %d", spareID),
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			// 仅记录警告，不影响主流程
			log.Printf("记录维修单状态历史失败: %v\n", err)
		}

		// 触发工作流状态转换
		err = s.TriggerWorkflowStage(
			ctx,
			id,
			common.StageCompleteHardwareReplace,
			engineerID,
			engineerName,
			"已完成硬件更换",
			map[string]interface{}{
				"spare_id":      spareID,
				"complete_time": now.Format("2006-01-02 15:04:05"),
				"success":       true,
			},
		)
		if err != nil {
			return fmt.Errorf("触发工作流状态转换失败: %w", err)
		}

		return nil
	})
}

// CompleteRepair 完成维修，触发工作流状态转换
func (s *repairTicketService) CompleteRepair(ctx context.Context, id uint, result *RepairResult) error {
	// 使用事务处理状态更新
	return s.repairTicketRepo.WithTransaction(ctx, func(txCtx context.Context, repo repository.RepairTicketRepository) error {
		// 获取维修单信息
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			return fmt.Errorf("获取维修单失败: %w", err)
		}

		if ticket == nil {
			return errors.New("维修单不存在")
		}

		// 验证状态
		allowedStatuses := []string{
			model.RepairTicketStatusInProgress,
			model.RepairTicketStatusReplacingHardware,
			model.RepairTicketStatusHardwareReplaceCompleted,
		}

		statusAllowed := false
		for _, status := range allowedStatuses {
			if ticket.Status == status {
				statusAllowed = true
				break
			}
		}

		if !statusAllowed {
			return fmt.Errorf("当前状态(%s)不允许完成维修", ticket.Status)
		}

		// 获取操作人信息(工程师)
		engineerID := ticket.AssignedEngineerID
		engineerName := ticket.AssignedEngineerName

		if engineerName == "" {
			engineerName = fmt.Sprintf("工程师%d", engineerID)
		}

		// 保存原状态
		previousStatus := ticket.Status

		// 更新维修单状态为硬件替换完成
		ticket.Status = model.RepairTicketStatusHardwareReplaceCompleted
		now := time.Now()

		// 更新验证开始时间
		ticket.VerificationStartTime = &now

		// 更新维修结果字段
		ticket.RepairResult = result.RepairResult
		ticket.Solution = result.Solution
		ticket.RepairSteps = result.RepairSteps

		// 计算总维修时长，使用验证开始时间减去授权时间
		if ticket.AuthorizationTime != nil {
			ticket.TotalRepairDuration = int(now.Sub(*ticket.AuthorizationTime).Minutes())
		}

		// 更新维修单
		if err := repo.Update(txCtx, ticket); err != nil {
			return fmt.Errorf("更新维修单状态失败: %w", err)
		}

		// 记录状态历史
		history := &model.RepairTicketStatusHistory{
			RepairTicketID:   id,
			PreviousStatus:   previousStatus,
			NewStatus:        model.RepairTicketStatusHardwareReplaceCompleted,
			OperatorID:       engineerID,
			OperatorName:     engineerName,
			OperationTime:    now,
			ActivityCategory: "hardware_replacement_completed",
			IsSLAPause:       false,
			PauseReason:      "",
			Remarks:          "维修已完成，等待验证",
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			// 仅记录警告，不影响主流程
			log.Printf("记录维修单状态历史失败: %v\n", err)
		}

		// 构建数据对象
		data := map[string]interface{}{
			"repair_result": result.RepairResult,
			"solution":      result.Solution,
			"repair_steps":  result.RepairSteps,
			"complete_time": now.Format("2006-01-02 15:04:05"),
			"success":       true,
		}

		// 触发工作流状态转换
		err = s.TriggerWorkflowStage(
			ctx,
			id,
			common.StageCompleteHardwareReplace,
			engineerID,
			engineerName,
			"维修已完成",
			data,
		)
		if err != nil {
			return fmt.Errorf("触发工作流状态转换失败: %w", err)
		}

		return nil
	})
}

// ListRepairTickets 获取维修单列表
func (s *repairTicketService) ListRepairTickets(ctx context.Context, page, pageSize int, query, status string, engineerID uint) ([]*model.RepairTicket, int64, error) {
	return s.repairTicketRepo.List(ctx, page, pageSize, query, status, engineerID)
}

// ListAvailableRepairTickets 获取可接单的维修单列表
func (s *repairTicketService) ListAvailableRepairTickets(ctx context.Context, page, pageSize int) ([]*model.RepairTicket, int64, error) {
	query := &model.RepairTicketQuery{
		Status:         "waiting_accept",
		Page:           int64(page),
		PageSize:       int64(pageSize),
		OrderBy:        "created_at",
		OrderDirection: "desc",
	}

	return s.repairTicketRepo.ListByQuery(ctx, query)
}

// RequestSpare 申请备件
func (s *repairTicketService) RequestSpare(ctx context.Context, repairTicketID uint, productID uint, quantity int) (interface{}, error) {
	// 应该调用库存服务申请备件
	// 这里简单模拟返回
	result := map[string]interface{}{
		"request_id": 12345,
		"status":     "processing",
	}
	return result, nil
}

// RequestSpareMachine 申请备机
func (s *repairTicketService) RequestSpareMachine(ctx context.Context, repairTicketID uint, templateID uint) (interface{}, error) {
	// 应该调用资产服务申请备机
	// 这里简单模拟返回
	result := map[string]interface{}{
		"assignment_id": 67890,
		"status":        "processing",
	}
	return result, nil
}

// RecoverInconsistentWorkflows 恢复不一致的工作流
func (s *repairTicketService) RecoverInconsistentWorkflows(ctx context.Context) error {
	// 检查Temporal客户端是否存在
	if s.temporalClient == nil {
		return errors.New("Temporal客户端未初始化")
	}

	// 查询所有非终态状态的维修单
	nonTerminalStatuses := []string{"waiting_accept", "assigned", "in_progress", "replacing_hardware", "hardware_replace_completed",
		"hardware_replace_failed", "restarting", "restart_completed", "restart_failed", "migrating", "migration_completed",
		"migration_failed", "waiting_verification"}

	for _, status := range nonTerminalStatuses {
		tickets, _, err := s.repairTicketRepo.List(ctx, 1, 1000, "", status, 0)
		if err != nil {
			fmt.Printf("查询状态为%s的维修单失败: %v\n", status, err)
			continue
		}

		for _, ticket := range tickets {
			// 使用直接的字符串格式（与截图中一致）
			workflowID := fmt.Sprintf("repair_ticket_%d", ticket.ID)

			// 检查工作流是否存在
			_, err := s.temporalClient.DescribeWorkflowExecution(ctx, workflowID, "")
			if err != nil {
				// 工作流不存在或已关闭，需要重新启动
				fmt.Printf("维修单ID=%d的工作流不存在或已关闭，尝试重新启动\n", ticket.ID)

				workflow := "RepairWorkflow"
				taskQueue := "REPAIR_TICKET_TASK_QUEUE"

				// 启动新工作流
				_, err = s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
					ID:        workflowID,
					TaskQueue: taskQueue,
				}, workflow, ticket.ID)

				if err != nil {
					fmt.Printf("为维修单ID=%d重新启动工作流失败: %v\n", ticket.ID, err)
				} else {
					fmt.Printf("成功为维修单ID=%d恢复工作流\n", ticket.ID)

					// 恢复工作流时使用系统用户名
					engineerName := "系统恢复"

					// 如果维修单已有工程师，发送接单信号
					if ticket.AssignedEngineerID > 0 {
						signal := common.WorkflowControlSignal{
							Stage:        common.StageEngineerTake,
							OperatorID:   ticket.AssignedEngineerID,
							OperatorName: engineerName,
							Comments:     "工作流恢复: 工程师接单",
						}

						if err := s.temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal); err != nil {
							log.Printf("向工作流发送工程师接单信号失败: %v", err)
							// 继续执行，不中断恢复流程
						}
					}

					// 根据当前状态发送额外的信号
					switch ticket.Status {
					case "in_progress":
						signal := common.WorkflowControlSignal{
							Stage:        common.StageStartRepair,
							OperatorID:   ticket.AssignedEngineerID,
							OperatorName: engineerName,
							Comments:     "工作流恢复: 开始维修",
						}
						if err := s.temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal); err != nil {
							log.Printf("向工作流发送开始维修信号失败: %v", err)
							// 继续执行，不中断恢复流程
						}

					case "replacing_hardware":
						signal := common.WorkflowControlSignal{
							Stage:        common.StageStartHardwareReplace,
							OperatorID:   ticket.AssignedEngineerID,
							OperatorName: engineerName,
							Comments:     "工作流恢复: 开始硬件更换",
						}
						if err := s.temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal); err != nil {
							log.Printf("向工作流发送开始硬件更换信号失败: %v", err)
							// 继续执行，不中断恢复流程
						}

					case "hardware_replace_completed":
						signal := common.WorkflowControlSignal{
							Stage:        common.StageCompleteHardwareReplace,
							OperatorID:   ticket.AssignedEngineerID,
							OperatorName: engineerName,
							Comments:     "工作流恢复: 完成硬件更换",
						}
						if err := s.temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal); err != nil {
							log.Printf("向工作流发送完成硬件更换信号失败: %v", err)
							// 继续执行，不中断恢复流程
						}

					case "waiting_verification":
						signal := common.WorkflowControlSignal{
							Stage:        common.StageCompleteRepair,
							OperatorID:   ticket.AssignedEngineerID,
							OperatorName: engineerName,
							Comments:     "工作流恢复: 等待验证",
						}
						if err := s.temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal); err != nil {
							log.Printf("向工作流发送等待验证信号失败: %v", err)
							// 继续执行，不中断恢复流程
						}
					}
				}
			}
		}
	}

	return nil
}

// TriggerWorkflowStage 触发维修单工作流的阶段转换
// 参数:
// - ctx: 上下文
// - ticketID: 维修单ID
// - stage: 工作流阶段名称
// - operatorID: 操作者ID
// - operatorName: 操作者名称
// - comments: 备注
// - data: 附加数据
func (s *repairTicketService) TriggerWorkflowStage(
	ctx context.Context,
	ticketID uint,
	stage string,
	operatorID uint,
	operatorName string,
	comments string,
	data map[string]interface{},
) error {
	// 检查Temporal客户端是否已初始化
	if s.temporalClient == nil {
		// 添加日志记录
		log.Printf("警告: 尝试触发工作流阶段 %s (维修单ID=%d)，但Temporal客户端未初始化", stage, ticketID)

		return errors.New("temporal client not initialized")
	}

	// 获取维修单信息，确认存在并处于合理状态
	ticket, err := s.GetRepairTicketByID(ctx, ticketID)
	if err != nil {
		return fmt.Errorf("获取维修单信息失败: %w", err)
	}

	if ticket == nil {
		return fmt.Errorf("维修单不存在: %d", ticketID)
	}

	// 构建工作流ID
	workflowID := fmt.Sprintf("repair_ticket_%d", ticketID)

	// 构建信号数据
	signal := common.WorkflowControlSignal{
		Stage:        stage,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     comments,
		Data:         data,
		Timestamp:    time.Now().Unix(),
	}

	// 使用Temporal客户端向工作流发送信号
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal)
	if err != nil {
		// 检查工作流是否不存在
		if strings.Contains(err.Error(), "workflow execution not found") ||
			strings.Contains(err.Error(), "workflow not found") {
			// 工作流不存在，需要启动新工作流
			log.Printf("维修单工作流不存在，正在启动新工作流: %d", ticketID)

			// 启动工作流
			workflowOptions := client.StartWorkflowOptions{
				ID:        workflowID,
				TaskQueue: common.RepairTicketTaskQueue,
			}

			_, err := s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, "RepairWorkflow", ticketID)
			if err != nil {
				return fmt.Errorf("启动维修单工作流失败: %w", err)
			}

			// 启动后等待短暂时间，确保工作流已初始化
			time.Sleep(500 * time.Millisecond)

			// 再次发送信号
			err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal)
			if err != nil {
				return fmt.Errorf("向新启动的工作流发送信号失败: %w", err)
			}
		} else {
			return fmt.Errorf("向工作流发送信号失败: %w", err)
		}
	}

	// 记录日志 - 这里应该替换为你的日志系统
	log.Printf("已成功向维修单工作流发送信号: ID=%d, Stage=%s, Operator=%s",
		ticketID, stage, operatorName)

	return nil
}

// 辅助函数

// generateTicketNo 生成工单号
func generateTicketNo() string {
	now := time.Now()

	// 使用crypto/rand代替math/rand
	randomBytes := make([]byte, 4)
	var randomPart int
	if _, err := rand.Read(randomBytes); err != nil {
		// 如果加密随机数生成失败，使用时间纳秒作为备选
		randomPart = int(time.Now().UnixNano() % 10000)
		log.Printf("使用加密随机数生成失败，使用时间纳秒替代: %v", err)
	} else {
		// 使用加密随机数，取模确保在0-9999范围内
		randomPart = int(binary.BigEndian.Uint32(randomBytes) % 10000)
	}

	return fmt.Sprintf("RT%s%04d%04d", now.Format("20060102"), now.UnixNano()%10000, randomPart)
}

// isValidStatusTransition 检查状态转换是否有效
func isValidStatusTransition(from, to string) bool {
	// 简单实现，实际应该根据工作流规则检查
	validTransitions := map[string][]string{
		"waiting_authorization":      {"waiting_accept", "cancelled"},
		"waiting_accept":             {"assigned", "cancelled"},
		"assigned":                   {"in_progress", "cancelled"},
		"in_progress":                {"waiting_verification", "failed", "cancelled", "replacing_hardware", "restarting", "migrating"},
		"replacing_hardware":         {"hardware_replace_completed", "hardware_replace_failed", "in_progress", "failed"},
		"hardware_replace_completed": {"in_progress", "waiting_verification", "completed"},
		"hardware_replace_failed":    {"in_progress", "failed"},
		"restarting":                 {"restart_completed", "restart_failed", "in_progress"},
		"restart_completed":          {"in_progress", "waiting_verification", "completed"},
		"restart_failed":             {"in_progress", "failed"},
		"migrating":                  {"migration_completed", "migration_failed", "in_progress"},
		"migration_completed":        {"in_progress", "waiting_verification", "completed"},
		"migration_failed":           {"in_progress", "failed"},
		"waiting_verification":       {"completed", "in_progress", "failed"},
		"completed":                  {},
		"failed":                     {"waiting_accept", "cancelled"},
		"cancelled":                  {},
	}

	transitions, ok := validTransitions[from]
	if !ok {
		return false
	}

	for _, validTo := range transitions {
		if validTo == to {
			return true
		}
	}

	return false
}

// VerifyRepair 验证维修结果，触发工作流状态转换
func (s *repairTicketService) VerifyRepair(ctx context.Context, id uint, success bool, comments string) error {
	// 使用事务处理状态更新
	return s.repairTicketRepo.WithTransaction(ctx, func(txCtx context.Context, repo repository.RepairTicketRepository) error {
		// 获取维修单信息
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			return fmt.Errorf("获取维修单失败: %w", err)
		}

		if ticket == nil {
			return errors.New("维修单不存在")
		}

		// 验证状态
		if ticket.Status != "waiting_verification" {
			return fmt.Errorf("当前状态(%s)不允许验证维修结果", ticket.Status)
		}

		// 获取验证人信息
		// 尝试从上下文获取用户真实姓名和ID
		var operatorID uint
		var operatorName string

		// 从上下文获取用户ID
		if userID, exists := ctx.Value("userID").(uint); exists && userID > 0 {
			operatorID = userID
		} else {
			// 如果没有获取到，则使用工程师ID作为备选
			operatorID = ticket.AssignedEngineerID
		}

		// 从上下文获取用户真实姓名
		if userRealName, exists := ctx.Value("userRealName").(string); exists && userRealName != "" {
			operatorName = userRealName
		} else if userInfo, exists := ctx.Value("userInfo").(map[string]interface{}); exists {
			if realName, ok := userInfo["realName"].(string); ok && realName != "" {
				operatorName = realName
			} else {
				operatorName = fmt.Sprintf("验证人%d", operatorID)
			}
		} else {
			operatorName = fmt.Sprintf("验证人%d", operatorID)
		}

		// 保存原状态
		previousStatus := ticket.Status

		// 根据验证结果确定新状态
		newStatus := model.RepairTicketStatusCompleted
		statusDescription := "验证通过，维修单完成"

		if !success {
			newStatus = model.RepairTicketStatusFailed
			statusDescription = "验证失败，维修未成功"
		}

		// 更新维修单状态
		ticket.Status = newStatus
		now := time.Now()

		// 如果是完成状态，更新完成时间
		if newStatus == model.RepairTicketStatusCompleted {
			ticket.CompleteTime = &now
		}

		// 注意：不再重新计算维修时长，使用waiting_verification状态时计算的值

		// 更新维修单
		if err := repo.Update(txCtx, ticket); err != nil {
			return fmt.Errorf("更新维修单状态失败: %w", err)
		}

		// 记录状态历史
		activityCategory := "verification"
		switch newStatus {
		case model.RepairTicketStatusCompleted:
			activityCategory = "completion"
		case model.RepairTicketStatusFailed:
			activityCategory = "failure"
		}

		history := &model.RepairTicketStatusHistory{
			RepairTicketID:   id,
			PreviousStatus:   previousStatus,
			NewStatus:        newStatus,
			OperatorID:       operatorID,
			OperatorName:     operatorName,
			OperationTime:    now,
			ActivityCategory: activityCategory,
			IsSLAPause:       false,
			PauseReason:      "",
			Remarks:          comments,
		}

		if comments == "" {
			history.Remarks = statusDescription
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			// 仅记录警告，不影响主流程
			log.Printf("记录维修单状态历史失败: %v\n", err)
		}

		// 触发工作流状态转换
		err = s.TriggerWorkflowStage(
			ctx,
			id,
			common.StageVerify,
			operatorID,
			operatorName,
			comments,
			map[string]interface{}{
				"success":     success,
				"verify_time": now.Format("2006-01-02 15:04:05"),
			},
		)
		if err != nil {
			return fmt.Errorf("触发工作流状态转换失败: %w", err)
		}

		return nil
	})
}

// AuthorizeRepairTicket 授权维修单
func (s *repairTicketService) AuthorizeRepairTicket(ctx context.Context, id uint, operatorID uint, operatorName string) error {
	// 使用事务处理状态更新
	return s.repairTicketRepo.WithTransaction(ctx, func(txCtx context.Context, repo repository.RepairTicketRepository) error {
		// 获取维修单
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			return fmt.Errorf("获取维修单失败: %w", err)
		}

		// 检查当前状态是否为等待授权
		if ticket.Status != model.RepairTicketStatusWaitingAuthorization {
			return fmt.Errorf("当前状态(%s)不允许授权，只有等待授权状态可以授权", ticket.Status)
		}

		// 如果未提供操作人姓名，尝试从上下文获取
		if operatorName == "" {
			if userRealName, exists := ctx.Value("userRealName").(string); exists && userRealName != "" {
				operatorName = userRealName
			} else if userInfo, exists := ctx.Value("userInfo").(map[string]interface{}); exists {
				if realName, ok := userInfo["realName"].(string); ok && realName != "" {
					operatorName = realName
				} else {
					operatorName = fmt.Sprintf("授权人%d", operatorID)
				}
			} else {
				operatorName = fmt.Sprintf("授权人%d", operatorID)
			}
		}

		previousStatus := ticket.Status
		// 更新状态为等待接单
		now := time.Now()
		ticket.Status = model.RepairTicketStatusWaitingAccept
		ticket.AuthorizationTime = &now

		// 更新维修单
		err = repo.Update(txCtx, ticket)
		if err != nil {
			return fmt.Errorf("更新维修单状态失败: %w", err)
		}

		// 记录状态历史
		history := &model.RepairTicketStatusHistory{
			RepairTicketID:   id,
			PreviousStatus:   previousStatus,
			NewStatus:        model.RepairTicketStatusWaitingAccept,
			OperatorID:       operatorID,
			OperatorName:     operatorName,
			OperationTime:    now,
			ActivityCategory: "authorization",
			IsSLAPause:       false,
			PauseReason:      "",
			Remarks:          "授权完成，等待工程师接单",
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			// 仅记录警告，不影响主流程
			log.Printf("记录维修单状态历史失败: %v\n", err)
		}

		// 触发工作流
		if s.temporalClient != nil {
			// 构建工作流ID
			workflowID := fmt.Sprintf("repair_ticket_%d", ticket.ID)

			// 检查工作流是否存在
			_, err := s.temporalClient.DescribeWorkflowExecution(ctx, workflowID, "")
			if err != nil {
				// 工作流不存在，需要启动
				workflow := "RepairWorkflow"
				taskQueue := common.RepairTicketTaskQueue

				_, err = s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
					ID:        workflowID,
					TaskQueue: taskQueue,
				}, workflow, ticket.ID)

				if err != nil {
					log.Printf("为维修单ID=%d启动工作流失败: %v\n", ticket.ID, err)
					// 工作流启动失败，但状态已更新，所以返回nil
					return nil
				}

				// 短暂等待确保工作流已初始化
				time.Sleep(500 * time.Millisecond)
			}

			// 发送授权完成信号
			signal := common.WorkflowControlSignal{
				Stage:        common.StageAuthorize,
				OperatorID:   operatorID,
				OperatorName: operatorName,
				Comments:     "授权完成，等待工程师接单",
				Data: map[string]interface{}{
					"authorized_at": now.Format("2006-01-02 15:04:05"),
				},
			}

			err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal)
			if err != nil {
				log.Printf("向维修单工作流发送授权信号失败: %v\n", err)
				// 工作流信号发送失败，但状态已更新，所以返回nil
			}
		}

		return nil
	})
}

// GetRepairTicketStatusHistory 获取维修单状态历史记录
func (s *repairTicketService) GetRepairTicketStatusHistory(ctx context.Context, ticketID uint) ([]*model.RepairTicketStatusHistory, error) {
	return s.repairTicketRepo.GetStatusHistory(ctx, ticketID)
}

// GetRepairTicketHistory 获取维修单历史记录
func (s *repairTicketService) GetRepairTicketHistory(ctx context.Context, repairTicketID uint) ([]model.RepairTicketStatusHistory, error) {
	return s.repairTicketRepo.GetRepairTicketHistory(ctx, repairTicketID)
}

// RecordEngineerChange 记录工程师变更历史
func (s *repairTicketService) RecordEngineerChange(ctx context.Context, id uint, oldEngineerName, newEngineerName string, operatorID uint, operatorName string) error {
	return s.repairTicketRepo.WithTransaction(ctx, func(txCtx context.Context, repo repository.RepairTicketRepository) error {
		// 获取当前维修单
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			return fmt.Errorf("获取维修单失败: %w", err)
		}

		// 处理操作人姓名 - 确保与报障单行为一致
		recordOperatorName := operatorName

		// 系统操作特殊处理
		if operatorID == 0 {
			recordOperatorName = "系统"
		} else if (recordOperatorName == "" || strings.HasPrefix(recordOperatorName, "用户")) && s.userService != nil {
			// 如果操作人姓名为空或者是默认格式，且用户服务可用，则尝试获取真实姓名
			realName, err := s.userService.GetUserName(ctx, operatorID)
			if err == nil && realName != "" {
				// 获取成功，使用真实姓名
				recordOperatorName = realName
				log.Printf("成功获取用户ID=%d的真实姓名: %s", operatorID, recordOperatorName)
			} else {
				// 获取失败，使用默认格式 - 应该已经由GetUserName处理为"用户ID"格式
				recordOperatorName = fmt.Sprintf("用户%d", operatorID)
				log.Printf("无法获取用户ID=%d的真实姓名: %v", operatorID, err)
			}
		} else if recordOperatorName == "" {
			// 如果获取不到操作人姓名且用户服务不可用，使用默认格式
			recordOperatorName = fmt.Sprintf("用户%d", operatorID)
			log.Printf("用户服务不可用或未初始化，使用默认名称: 用户%d", operatorID)
		}

		now := time.Now()

		// 记录状态历史（状态不变，但记录工程师变更）
		history := &model.RepairTicketStatusHistory{
			RepairTicketID:   id,
			PreviousStatus:   ticket.Status,
			NewStatus:        ticket.Status,
			OperatorID:       operatorID,
			OperatorName:     recordOperatorName,
			OperationTime:    now,
			ActivityCategory: "engineer_assignment", // 使用专门的活动类别
			IsSLAPause:       false,
			PauseReason:      "",
			Remarks:          fmt.Sprintf("接单人信息从 %s 更新为 %s", oldEngineerName, newEngineerName),
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			return fmt.Errorf("记录工程师变更历史失败: %w", err)
		}

		return nil
	})
}
