package repository

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"backend/internal/modules/schedule/model"
)

// HardScheduleRepository 排班表仓库接口
type HardScheduleRepository interface {
	GetScheduleByMonth(ctx context.Context, startDate, endDate time.Time) ([]model.HardSchedule, error)
	Create(ctx context.Context, schedule *model.HardSchedule) error
	GetByDate(ctx context.Context, date time.Time) (*model.HardSchedule, error)
	Update(ctx context.Context, schedule *model.HardSchedule) error
}

// scheduleRepository 排班表仓库实现
type hardScheduleRepository struct {
	db *gorm.DB
}

// NewHardScheduleRepository 创建排班表仓库
func NewHardScheduleRepository(db *gorm.DB) HardScheduleRepository {
	return &hardScheduleRepository{db: db}
}

// GetScheduleByMonth 通过Month获取主副值班列表
func (r *hardScheduleRepository) GetScheduleByMonth(ctx context.Context, startDate, endDate time.Time) ([]model.HardSchedule, error) {
	// 将时间转换为字符串格式，用于查询
	startDateStr := startDate.Format("2006-01-02")
	endDateStr := endDate.Format("2006-01-02")
	//startDateStr := startDate.Format("2006-01-02T00:00:00") // 当月1号 00:00:00（完整时间）
	//endDateStr := endDate.Format("2006-01-02T23:59:59")     // 当月最后一天 23:59:59（完整时间）

	var schedules []model.HardSchedule
	err := r.db.WithContext(ctx).
		Select("id,date,user_name").
		Where("date >= ? AND date <= ?", startDateStr, endDateStr).
		Order("date ASC"). // 按日期升序排列
		Find(&schedules).Error
	return schedules, err
}

// Create 创建排班表
func (r *hardScheduleRepository) Create(ctx context.Context, schedule *model.HardSchedule) error {

	return r.db.WithContext(ctx).Create(schedule).Error
}

// GetByDate  通过Date获取主副值班人
func (r *hardScheduleRepository) GetByDate(ctx context.Context, date time.Time) (*model.HardSchedule, error) {
	var schedule model.HardSchedule
	if err := r.db.WithContext(ctx).
		Model(&model.HardSchedule{}). //Select("primary_username", "second_username").
		Where("date = ?", date).First(&schedule).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("值班人不存在")
		}
		return nil, err
	}
	return &schedule, nil
}

// Update 更新排班表
func (r *hardScheduleRepository) Update(ctx context.Context, schedule *model.HardSchedule) error {
	return r.db.WithContext(ctx).Model(&model.HardSchedule{}).
		Where("date = ?", schedule.Date).
		Updates(map[string]interface{}{
			"user_name": schedule.UserName,
		}).Error
}
