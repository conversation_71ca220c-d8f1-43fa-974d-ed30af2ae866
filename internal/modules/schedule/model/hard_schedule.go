package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type HardSchedule struct {
	gorm.Model
	ID       uint           `gorm:"primarykey;autoIncrement" example:"1"`
	Date     time.Time      `json:"date" gorm:"unique;not null;comment:排班日期" example:"2025-05-16T00:00:00+08:00"` // 按日期查询，加索引
	UserName datatypes.JSON `json:"username" gorm:"column:user_name;type:json;comment:值班人数组"`                     // 主值班用户姓名（直接存储）
}

func (HardSchedule) TableName() string {
	return "hard_schedules"
}
