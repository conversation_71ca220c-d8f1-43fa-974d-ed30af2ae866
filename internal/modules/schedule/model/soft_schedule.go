package model

import (
	"gorm.io/gorm"
)

type SoftSchedule struct {
	gorm.Model
	ID              uint   `gorm:"primarykey;autoIncrement" example:"1"`
	Date            string `json:"date" gorm:"unique;not null;comment:排班日期" example:"2023-01-01"`                    // 按日期查询，加索引
	PrimaryUserName string `json:"primary_username" gorm:"column:primary_username;type:varchar(20);comment:主值班用户姓名"` // 主值班用户姓名（直接存储）
	SecondUserName  string `json:"second_username" gorm:"column:second_username;type:varchar(20);comment:副值班用户姓名"`
}

func (SoftSchedule) TableName() string {
	return "soft_schedules"
}
