package service

import (
	"backend/internal/common/utils/notifier"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"backend/internal/modules/schedule/model"
	repo "backend/internal/modules/schedule/repository"
)

// HardScheduleService 排班表服务接口
type HardScheduleService interface {
	GetByMonth(ctx context.Context, startDate, endDate time.Time) ([]model.HardSchedule, error)
	Create(ctx context.Context, hardSchedule *model.HardSchedule) error
	GetByDate(ctx context.Context, date time.Time) (*model.HardSchedule, error)
	Update(ctx context.Context, hardSchedule *model.HardSchedule) error
	SendHardScheMsg(ctx context.Context, dateTime time.Time) error
	TriggerNotification(ctx context.Context, hour int) error
}

// hardScheduleService 排班表服务实现
type hardScheduleService struct {
	repo             repo.HardScheduleRepository
	hardScheNotifier *notifier.HardScheNotifier
}

// NewHardScheService 创建排班表服务
func NewHardScheService(repo repo.HardScheduleRepository, hardScheNotifier ...*notifier.HardScheNotifier) HardScheduleService {
	var notifier *notifier.HardScheNotifier
	if len(hardScheNotifier) > 0 {
		notifier = hardScheNotifier[0]
	}

	return &hardScheduleService{repo: repo, hardScheNotifier: notifier}
}

// GetByMonth 通过Month获取排班表服务
func (h *hardScheduleService) GetByMonth(ctx context.Context, startDate, endDate time.Time) ([]model.HardSchedule, error) {
	return h.repo.GetScheduleByMonth(ctx, startDate, endDate)
}

// 创建排班表服务
func (h *hardScheduleService) Create(ctx context.Context, schedule *model.HardSchedule) error {
	return h.repo.Create(ctx, schedule)
}

// GetByDate 通过Date获取排班表服务
func (h *hardScheduleService) GetByDate(ctx context.Context, date time.Time) (*model.HardSchedule, error) {
	return h.repo.GetByDate(ctx, date)
}

// Update 更新排班表服务
func (h *hardScheduleService) Update(ctx context.Context, schedule *model.HardSchedule) error {
	return h.repo.Update(ctx, schedule)
}

// SendScheMsg 发送排班通知
func (h *hardScheduleService) SendHardScheMsg(ctx context.Context, dateTime time.Time) error {
	//// 获取指定时间点的排班信息
	//sche, err := h.repo.GetByDate(ctx, dateTime)
	//if err != nil {
	//	return fmt.Errorf("获取硬件排班通知前置信息失败: %v", err)
	//}
	//
	//// 将time.Time格式化为字符串
	//dateStr := dateTime.Format("2006-01-02 15:04")
	//users := sche.UserName
	//
	//// 根据时间点选择不同的消息模板
	//if dateTime.Hour() == 9 {
	//	err = h.hardScheNotifier.SendHardScheMorningNotification(dateStr, users)
	//	if err != nil {
	//		return fmt.Errorf("发送飞书通知失败: %v", err)
	//	}
	//	return nil
	//} else {
	//	err = h.hardScheNotifier.SendHardScheEveningNotification(dateStr, users)
	//	if err != nil {
	//		return fmt.Errorf("发送飞书通知失败: %v", err)
	//	}
	//	return nil
	//
	//}
	sche, err := h.repo.GetByDate(ctx, dateTime)
	if err != nil {
		return fmt.Errorf("获取硬件排班通知前置信息失败: %v", err)
	}
	dateStr := dateTime.Format("2006-01-02 15:04")
	var users []string
	if err := json.Unmarshal(sche.UserName, &users); err != nil {
		return fmt.Errorf("解析值班人数组失败: %v", err)
	}
	fmt.Printf("值班人员: %v\n", users)
	if dateTime.Hour() == 9 {
		err = h.hardScheNotifier.SendHardScheMorningNotification(dateStr, users)
		if err != nil {
			return fmt.Errorf("发送飞书通知失败: %v", err)
		}
		return nil
	} else {
		err = h.hardScheNotifier.SendHardScheEveningNotification(dateStr, users)
		if err != nil {
			return fmt.Errorf("发送飞书通知失败: %v", err)
		}
		return nil
	}

}

// TriggerNotification 手动触发排班通知
func (h *hardScheduleService) TriggerNotification(ctx context.Context, hour int) error {
	// 获取当前时间
	now := time.Now()

	// 根据传入的小时数设置时间
	notificationTime := time.Date(now.Year(), now.Month(), now.Day(), hour, 0, 0, 0, now.Location())

	// 调用发送通知的方法
	return h.SendHardScheMsg(ctx, notificationTime)
}
