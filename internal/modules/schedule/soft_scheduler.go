package schedule

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/robfig/cron"

	"backend/internal/modules/schedule/service"
)

// 添加一个互斥锁和发送记录映射，用于防止重复发送
var (
	SendMutex   sync.Mutex
	SendRecords = make(map[string]time.Time)
	// 设置防重复发送的时间窗口（5分钟内不重复发送同一天的通知）
	DedupeWindow = 5 * time.Minute
)

type SoftScheduler struct {
	cron            *cron.Cron
	scheduleService service.SoftScheduleService
	isRunning       bool
}

func NewSoftScheduler(scheduleService service.SoftScheduleService) *SoftScheduler {
	// 创建一个定时任务管理器（使用东八区时间）
	c := cron.NewWithLocation(time.FixedZone("CST", 8*60*60))

	return &SoftScheduler{
		cron:            c,
		scheduleService: scheduleService,
		isRunning:       false,
	}
}

func (s *SoftScheduler) Start() {
	if s.isRunning {
		fmt.Printf("定时任务调度器已经在运行中，忽略此次启动请求\n")
		return
	}
	// 在添加任务前清空现有任务
	s.cron.Stop()
	s.cron = cron.NewWithLocation(time.FixedZone("CST", 8*60*60))

	//每天上午9点执行
	err := s.cron.AddFunc("0 0 9 * * *", func() {
		s.sendSoftScheduleNotification()
	})
	if err != nil {
		fmt.Printf("添加排班通知定时任务失败: %v\n", err)
		return
	}
	fmt.Printf("启动定时任务调度器\n")
	s.cron.Start()
	s.isRunning = true
}

func (s *SoftScheduler) Stop() {
	if !s.isRunning {
		fmt.Printf("定时任务调度器已经停止，忽略此次停止请求\n")
		return
	}
	s.cron.Stop()
	s.isRunning = false
	fmt.Printf("停止定时任务调度器\n")
}

func (s *SoftScheduler) sendSoftScheduleNotification() {
	// 获取今天的日期
	today := time.Now().Format("2006-01-02")

	// 检查是否在短时间内已经发送过通知
	SendMutex.Lock()
	lastSendTime, exists := SendRecords[today]
	now := time.Now()
	if exists && now.Sub(lastSendTime) < DedupeWindow {
		fmt.Printf("跳过重复发送，上次发送时间: %v, 当前时间: %v\n", lastSendTime.Format("15:04:05"), now.Format("15:04:05"))
		SendMutex.Unlock()
		return
	}

	// 记录本次发送时间
	SendRecords[today] = now
	SendMutex.Unlock()

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 调用服务发送通知
	err := s.scheduleService.SendSoftScheMsg(ctx, today)
	fmt.Println("今天的日期是", today)
	if err != nil {
		fmt.Printf("发送今日排班通知失败: %v\n", err)
	} else {
		fmt.Printf("成功发送今日排班通知\n")
	}
}
