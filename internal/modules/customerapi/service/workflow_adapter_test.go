package service

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"
)

// 创建模拟仓库
type MockFaultTicketRepository struct {
	mock.Mock
}

func (m *MockFaultTicketRepository) Create(ctx context.Context, ticket *model.FaultTicket) error {
	args := m.Called(ctx, ticket)
	// 模拟自动生成ID
	ticket.ID = 1
	return args.Error(0)
}

func (m *MockFaultTicketRepository) GetByID(ctx context.Context, id uint) (*model.FaultTicket, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.FaultTicket), args.Error(1)
}

func (m *MockFaultTicketRepository) GetByTicketNo(ctx context.Context, ticketNo string) (*model.FaultTicket, error) {
	args := m.Called(ctx, ticketNo)
	return args.Get(0).(*model.FaultTicket), args.Error(1)
}

func (m *MockFaultTicketRepository) Update(ctx context.Context, ticket *model.FaultTicket) error {
	args := m.Called(ctx, ticket)
	return args.Error(0)
}

func (m *MockFaultTicketRepository) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockFaultTicketRepository) List(ctx context.Context, page, pageSize int, conditions map[string]interface{}) ([]*model.FaultTicket, int64, error) {
	args := m.Called(ctx, page, pageSize, conditions)
	return args.Get(0).([]*model.FaultTicket), args.Get(1).(int64), args.Error(2)
}

func (m *MockFaultTicketRepository) CreateStatusHistory(ctx context.Context, history *model.FaultTicketStatusHistory) error {
	args := m.Called(ctx, history)
	return args.Error(0)
}

func (m *MockFaultTicketRepository) GetStatusHistory(ctx context.Context, ticketID uint) ([]*model.FaultTicketStatusHistory, error) {
	args := m.Called(ctx, ticketID)
	return args.Get(0).([]*model.FaultTicketStatusHistory), args.Error(1)
}

func (m *MockFaultTicketRepository) UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	args := m.Called(ctx, id, fields)
	return args.Error(0)
}

func (m *MockFaultTicketRepository) WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo repository.FaultTicketRepository) error) error {
	args := m.Called(ctx, fn)
	return args.Error(0)
}

func (m *MockFaultTicketRepository) FindTicketsForRetry(ctx context.Context) ([]*model.FaultTicket, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*model.FaultTicket), args.Error(1)
}

func (m *MockFaultTicketRepository) CountRecentFaultsByDeviceSN(ctx context.Context, deviceSN string, days int) (int64, error) {
	args := m.Called(ctx, deviceSN, days)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockFaultTicketRepository) CountMonthlyFaultsByDeviceSN(ctx context.Context, deviceSN string, startTime, endTime time.Time) (int64, error) {
	args := m.Called(ctx, deviceSN, startTime, endTime)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockFaultTicketRepository) FindActiveTicketsByResourceIdentifier(ctx context.Context, resourceIdentifier string, excludeID uint) ([]*model.FaultTicket, error) {
	args := m.Called(ctx, resourceIdentifier, excludeID)
	return args.Get(0).([]*model.FaultTicket), args.Error(1)
}

// TestCreateFaultTicket_CreatesStatusHistory 测试CreateFaultTicket方法是否正确创建了状态历史记录
func TestCreateFaultTicket_CreatesStatusHistory(t *testing.T) {
	// 创建模拟仓库
	mockRepo := new(MockFaultTicketRepository)

	// 创建logger
	logger, _ := zap.NewDevelopment()

	// 创建服务
	service := &workflowEnabledFaultTicketService{
		db:     nil, // 不需要实际的DB
		repo:   mockRepo,
		logger: logger,
	}

	// 创建测试工单
	ticket := &model.FaultTicket{
		Status:       "waiting_accept",
		ReporterName: "测试用户",
		TicketNo:     "i20230101000001",
	}

	// 设置模拟行为
	mockRepo.On("Create", mock.Anything, ticket).Return(nil)
	mockRepo.On("CreateStatusHistory", mock.Anything, mock.MatchedBy(func(history *model.FaultTicketStatusHistory) bool {
		return history.FaultTicketID == 1 && // ID应该是1，由Create方法设置
			history.NewStatus == "waiting_accept" &&
			history.PreviousStatus == "" &&
			history.OperatorName == "测试用户" &&
			history.Remarks == "创建报障单"
	})).Return(nil)

	// 执行测试
	err := service.CreateFaultTicket(context.Background(), ticket)

	// 验证结果
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
	mockRepo.AssertCalled(t, "Create", mock.Anything, ticket)
	mockRepo.AssertCalled(t, "CreateStatusHistory", mock.Anything, mock.MatchedBy(func(history *model.FaultTicketStatusHistory) bool {
		return history.FaultTicketID == 1 &&
			history.NewStatus == "waiting_accept" &&
			history.PreviousStatus == "" &&
			history.OperatorName == "测试用户" &&
			history.Remarks == "创建报障单"
	}))
}
