package service

import (
	"backend/internal/common/utils/notifier"
	"backend/internal/modules/customerapi/model"
	ticketModel "backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/service"
	"context"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	zapLog "backend/internal/infrastructure/logger"

	"go.uber.org/zap"
)

// CustomerTicketService 客户工单服务接口
type CustomerTicketService interface {
	// CreateTicket 创建工单
	CreateTicket(ctx context.Context, request *model.CustomerTicketRequest) (*model.CustomerTicketResponse, error)

	// QueryTickets 查询工单
	QueryTickets(ctx context.Context, query *model.CustomerTicketQueryRequest) (*model.CustomerTicketQueryResponse, error)

	// SetAuthority 设置授权
	SetAuthority(ctx context.Context, request *model.CustomerTicketAuthorityRequest) error

	// BatchCreateTickets 批量创建工单
	BatchCreateTickets(ctx context.Context, request *model.BatchCreateTicketRequest) (*model.BatchCreateTicketResponse, error)
}

// customerTicketService 客户工单服务实现
type customerTicketService struct {
	faultTicketService service.FaultTicketService // 内部工单服务
	logger             *zap.Logger                // 日志记录器
	feishuNotifier     *notifier.FeishuNotifier   // 飞书通知器
}

// NewCustomerTicketService 创建客户工单服务
func NewCustomerTicketService(faultTicketService service.FaultTicketService, feishuWebhookURL, feishuSecret, detailUrlTemplate string) CustomerTicketService {
	// 创建生产环境日志记录器
	logger, err := zapLog.NewProductionLogger()
	if err != nil {
		// 如果创建失败，使用默认配置
		defaultConfig := zap.NewProductionConfig()
		defaultConfig.Level = zap.NewAtomicLevelAt(zap.InfoLevel)
		defaultLogger, defaultErr := defaultConfig.Build()
		if defaultErr != nil {
			// 如果还是失败，使用标准库日志记录错误
			log.Printf("无法创建日志记录器: %v, 使用标准日志", defaultErr)
			defaultLogger = zap.NewNop() // 使用空日志记录器
		}
		logger = defaultLogger
	}

	// 创建飞书通知器 - 对于客户API，维修单的详情URL与普通工单相同
	feishuNotifier := notifier.NewFeishuNotifier(
		feishuWebhookURL,
		feishuSecret,
		detailUrlTemplate,
		detailUrlTemplate, // 维修单详情URL与普通工单相同
		nil,               // 客户API不使用项目webhook映射
		logger,
	)

	return &customerTicketService{
		faultTicketService: faultTicketService,
		logger:             logger,
		feishuNotifier:     feishuNotifier,
	}
}

// CreateTicket 创建工单
func (s *customerTicketService) CreateTicket(ctx context.Context, request *model.CustomerTicketRequest) (*model.CustomerTicketResponse, error) {
	// 尝试从上下文获取客户端IP（如果日志中间件已设置）
	clientIP := ""
	if ip, exists := ctx.Value("client_ip").(string); exists && ip != "" {
		clientIP = ip
		s.logger.Info("收到客户端IP信息", zap.String("ip", clientIP))
	}

	// 获取客户ID和名称
	customerID := ""
	customerName := "客户API" // 默认报障人

	if id, exists := ctx.Value("customerID").(string); exists && id != "" {
		customerID = id
	}

	if name, exists := ctx.Value("customerName").(string); exists && name != "" {
		customerName = name // 使用客户名称作为报障人
	}

	s.logger.Info("开始创建工单",
		zap.String("ip", request.TicketVmIP),
		zap.String("content", request.TicketContent),
		zap.String("client_ip", clientIP),
		zap.String("customer_id", customerID),
		zap.String("reporter_name", customerName),
	)

	// IP地址格式验证
	ip := strings.TrimSpace(request.TicketVmIP)
	if ip == "" {
		s.logger.Error("创建工单失败: IP地址为空")
		return nil, fmt.Errorf("IP地址不能为空")
	}

	// 基本的IP格式验证 - 检查是否包含非法字符
	if strings.ContainsAny(ip, " \t\n\"'`;<>()&|") {
		s.logger.Error("创建工单失败: IP地址包含非法字符",
			zap.String("ip", ip),
		)
		return nil, fmt.Errorf("IP地址格式不正确，包含非法字符")
	}

	// 简单的IPv4格式验证
	ipParts := strings.Split(ip, ".")
	if len(ipParts) != 4 {
		return nil, fmt.Errorf("IP地址格式不正确，应为标准IPv4格式(如*************)")
	}

	for _, part := range ipParts {
		if part == "" {
			return nil, fmt.Errorf("IP地址格式不正确，包含空段")
		}

		// 检查每个部分是否是有效的数字
		num, err := strconv.Atoi(part)
		if err != nil {
			return nil, fmt.Errorf("IP地址格式不正确，包含非数字字符: %s", part)
		}

		// 检查数值范围是否在0-255之间
		if num < 0 || num > 255 {
			return nil, fmt.Errorf("IP地址格式不正确，数值范围应在0-255之间: %s", part)
		}
	}

	// 检查是否已经存在相同IP的未完成工单
	existingTickets, _, err := s.faultTicketService.ListFaultTickets(ctx, 1, 10, map[string]interface{}{
		"resource_identifier": ip,
	})

	if err != nil {
		s.logger.Error("查询已有工单失败",
			zap.String("ip", ip),
			zap.Error(err),
		)
		return nil, fmt.Errorf("查询已有工单失败: %w", err)
	}

	// 过滤未完成的工单（状态不是"completed"和"cancelled"的工单）
	for _, ticket := range existingTickets {
		if ticket.Status != "completed" && ticket.Status != "cancelled" {
			s.logger.Warn("存在相同IP的未完成工单",
				zap.String("ip", ip),
				zap.String("existing_ticket_no", ticket.TicketNo),
				zap.String("existing_ticket_status", ticket.Status),
			)

			// 返回包含已存在工单ID的响应
			return &model.CustomerTicketResponse{
				TicketID:       ticket.TicketNo, // 返回已存在的工单号
				ExistingTicket: true,            // 标记为已存在工单
			}, fmt.Errorf("已存在IP为 %s 的重复工单", ip)
		}
	}

	// 内容验证
	content := strings.TrimSpace(request.TicketContent)
	if content == "" {
		s.logger.Error("创建工单失败: 故障描述为空")
		return nil, fmt.Errorf("故障描述不能为空")
	}

	// 检查故障描述长度
	contentLen := len(content)
	if contentLen > 10000 {
		s.logger.Error("创建工单失败: 故障描述过长",
			zap.Int("length", contentLen),
			zap.Int("max_length", 10000),
		)
		return nil, fmt.Errorf("故障描述过长，不能超过10000个字符，当前长度：%d", contentLen)
	}

	// 将客户请求转换为内部工单格式
	faultTicket := &ticketModel.FaultTicket{
		Source:             "customer",   // 来源：客户
		Title:              "客户报障：" + ip, // 标题
		FaultDescription:   content,      // 故障描述
		Priority:           "high",       // 默认优先级
		ResourceIdentifier: ip,           // 资源标识(虚机IP)

		// 设置默认值
		Status:          "waiting_accept", // 状态：待接单
		ReporterName:    customerName,     // 报障人使用客户名称
		RequireApproval: false,            // 不需要客户审批（文档说明一旦提交成功默认客户已授权）
	}

	// 调用内部服务创建工单
	if err := s.faultTicketService.CreateFaultTicket(ctx, faultTicket); err != nil {
		s.logger.Error("创建工单失败",
			zap.Error(err),
		)
		return nil, fmt.Errorf("创建报障单失败: %w", err)
	}

	s.logger.Info("工单创建成功",
		zap.String("ticket_no", faultTicket.TicketNo),
	)

	// 记录工作流启动状态
	workflowStarted := true
	var workflowErr error

	// 在工单创建成功后，启动工作流
	if workflowErr = s.faultTicketService.StartFaultTicketWorkflow(ctx, faultTicket.ID); workflowErr != nil {
		// 记录错误但不影响工单创建结果
		workflowStarted = false
		s.logger.Error("启动工单工作流失败，但工单已创建成功",
			zap.Uint("ticket_id", faultTicket.ID),
			zap.String("ticket_no", faultTicket.TicketNo),
			zap.String("resource_ip", faultTicket.ResourceIdentifier),
			zap.String("client_ip", clientIP),
			zap.Error(workflowErr),
		)
		// 这里不返回错误，因为工单已经创建成功
	} else {
		s.logger.Info("工单工作流启动成功",
			zap.Uint("ticket_id", faultTicket.ID),
			zap.String("ticket_no", faultTicket.TicketNo),
			zap.String("resource_ip", faultTicket.ResourceIdentifier),
			zap.String("client_ip", clientIP),
		)
	}

	// 记录完整的创建成功信息
	s.logger.Info("工单创建完成",
		zap.String("ticket_no", faultTicket.TicketNo),
		zap.Uint("ticket_id", faultTicket.ID),
		zap.String("resource_ip", faultTicket.ResourceIdentifier),
		zap.String("client_ip", clientIP),
		zap.String("customer_id", customerID),
		zap.String("status", faultTicket.Status),
		zap.Bool("workflow_started", workflowStarted),
	)

	// 发送飞书通知
	if s.feishuNotifier != nil {
		// 异步发送通知，不阻塞主流程
		go func() {
			if err := s.feishuNotifier.SendTicketCreatedNotification(
				fmt.Sprintf("%d", faultTicket.ID),
				faultTicket.TicketNo,
				faultTicket.ResourceIdentifier,
				faultTicket.FaultDescription,
			); err != nil {
				s.logger.Error("发送飞书通知失败",
					zap.Error(err),
					zap.String("ticket_no", faultTicket.TicketNo),
				)
			}
		}()
	}

	// 返回结果，只返回工单号作为ticketId
	return &model.CustomerTicketResponse{
		TicketID: faultTicket.TicketNo,
	}, nil
}

// QueryTickets 查询工单
func (s *customerTicketService) QueryTickets(ctx context.Context, query *model.CustomerTicketQueryRequest) (*model.CustomerTicketQueryResponse, error) {
	// 尝试从上下文获取客户端IP（如果日志中间件已设置）
	clientIP := ""
	if ip, exists := ctx.Value("client_ip").(string); exists && ip != "" {
		clientIP = ip
	}

	// 获取客户ID
	customerID := ""
	if id, exists := ctx.Value("customerID").(string); exists && id != "" {
		customerID = id
	}

	s.logger.Info("开始查询工单",
		zap.Int("page", query.Page),
		zap.Int("page_size", query.PageSize),
		zap.String("status", string(query.TicketStatus)),
		zap.String("ip", query.TicketVmIP),
		zap.String("create_at", query.TicketCreateAt),
		zap.String("ticket_id", query.TicketID),
		zap.String("client_ip", clientIP),
		zap.String("customer_id", customerID),
	)

	// 设置默认值和检查异常值
	if query.Page < 0 {
		s.logger.Warn("收到负数页码",
			zap.Int("received_page", query.Page),
			zap.Int("default_page", 1),
		)
		query.Page = 1
	} else if query.Page == 0 {
		query.Page = 1
	} else if query.Page > 10000 {
		s.logger.Warn("页码过大",
			zap.Int("received_page", query.Page),
			zap.Int("max_page", 10000),
		)
		query.Page = 10000
	}

	if query.PageSize < 0 {
		s.logger.Warn("收到负数每页条数",
			zap.Int("received_page_size", query.PageSize),
			zap.Int("default_page_size", 20),
		)
		query.PageSize = 20
	} else if query.PageSize == 0 {
		query.PageSize = 20
	} else if query.PageSize > 100 {
		s.logger.Warn("每页条数过大",
			zap.Int("received_page_size", query.PageSize),
			zap.Int("max_page_size", 100),
		)
		query.PageSize = 100
	}

	// 组装查询条件
	status := ""
	from := ""
	// 创建时间范围
	var startTime, endTime time.Time
	var hasTimeFilter bool
	// 多IP查询支持
	var ipList []string
	var hasIPFilter bool

	// 处理IP查询条件
	if query.TicketVmIP != "" {
		// 支持逗号分隔的多个IP地址
		ipList = strings.Split(query.TicketVmIP, ",")
		// 清理每个IP地址
		for i, ip := range ipList {
			ipList[i] = strings.TrimSpace(ip)
		}
		// 过滤掉空IP
		var validIPList []string
		for _, ip := range ipList {
			if ip != "" {
				validIPList = append(validIPList, ip)
			}
		}

		ipList = validIPList
		hasIPFilter = len(ipList) > 0

		// 记录多IP查询日志
		if len(ipList) > 1 {
			s.logMultiIPSearch(ctx, ipList)
		}
	}

	// 处理状态
	if query.TicketStatus != "" && query.TicketStatus != model.StatusAll {
		status = mapStatusToInternal(query.TicketStatus)
	}

	// 处理来源
	if query.TicketFrom != "" && query.TicketFrom != model.FromAll {
		from = string(query.TicketFrom)
	}

	// 处理创建时间
	if query.TicketCreateAt != "" {
		hasTimeFilter = true
		now := time.Now()
		switch strings.ToLower(query.TicketCreateAt) {
		case "today":
			// 今天 00:00:00 到 23:59:59
			startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			endTime = startTime.Add(24 * time.Hour).Add(-time.Second)
		case "yesterday":
			// 昨天 00:00:00 到 23:59:59
			startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Add(-24 * time.Hour)
			endTime = startTime.Add(24 * time.Hour).Add(-time.Second)
		case "thisweek":
			// 本周，从周一 00:00:00 到下周一 00:00:00
			weekday := now.Weekday()
			if weekday == time.Sunday {
				weekday = 7 // 使周日为一周的第7天
			}
			daysFromMonday := int(weekday - 1)
			startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).AddDate(0, 0, -daysFromMonday)
			endTime = startTime.AddDate(0, 0, 7).Add(-time.Second)
		case "lastweek":
			// 上周，从上周一 00:00:00 到本周一 00:00:00
			weekday := now.Weekday()
			if weekday == time.Sunday {
				weekday = 7
			}
			daysFromMonday := int(weekday - 1)
			thisMonday := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).AddDate(0, 0, -daysFromMonday)
			startTime = thisMonday.AddDate(0, 0, -7)
			endTime = thisMonday.Add(-time.Second)
		case "thismonth":
			// 本月，从本月1日 00:00:00 到下月1日 00:00:00
			startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
			endTime = time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location()).Add(-time.Second)
		case "lastmonth":
			// 上月，从上月1日 00:00:00 到本月1日 00:00:00
			startTime = time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, now.Location())
			endTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location()).Add(-time.Second)
		case "last7days":
			// 最近7天，从7天前的00:00:00到今天的23:59:59
			startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).AddDate(0, 0, -6)
			endTime = time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
			s.logger.Info("查询最近7天的工单",
				zap.Time("start_time", startTime),
				zap.Time("end_time", endTime),
			)
		default:
			// 尝试解析具体日期，格式如: "2023-03-01"
			var parseErr error
			// 定义要支持的时间格式
			timeFormats := []string{
				"2006-01-02",          // 年-月-日（双数）
				"2006/01/02",          // 年/月/日（双数）
				"2006-1-2",            // 年-月-日（单数）
				"2006/1/2",            // 年/月/日（单数）
				"2006-01-2",           // 年-月(双)-日(单)
				"2006-1-02",           // 年-月(单)-日(双)
				"2006/01/2",           // 年/月(双)/日(单)
				"2006/1/02",           // 年/月(单)/日(双)
				"2006-01-02 15:04",    // 年-月-日 时:分（双数）
				"2006/01/02 15:04",    // 年/月/日 时:分（双数）
				"2006-1-2 15:04",      // 年-月-日 时:分（单数）
				"2006/1/2 15:04",      // 年/月/日 时:分（单数）
				"2006-01-02 15:04:05", // 年-月-日 时:分:秒（双数）
				"2006/01/02 15:04:05", // 年/月/日 时:分:秒（双数）
				"2006-1-2 15:04:05",   // 年-月-日 时:分:秒（单数）
				"2006/1/2 15:04:05",   // 年/月/日 时:分:秒（单数）
			}

			// 标记当前格式是否包含时间部分
			isDateOnly := true
			isMinuteOnly := false

			// 尝试解析日期
			for _, format := range timeFormats {
				startTime, parseErr = time.ParseInLocation(format, query.TicketCreateAt, time.Local)
				if parseErr == nil {
					// 解析成功
					// 根据格式判断是否包含时间部分
					if strings.Contains(format, ":") {
						isDateOnly = false
						if !strings.Contains(format, ":05") {
							isMinuteOnly = true
						}
					}
					break
				}
			}

			// 所有格式都解析失败
			if parseErr != nil {
				return nil, fmt.Errorf("无效的创建时间格式: %s", query.TicketCreateAt)
			}

			// 设置结束时间
			if isDateOnly {
				// 只指定了日期，结束时间为当天结束(23:59:59)
				endTime = time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 23, 59, 59, 0, startTime.Location())
			} else if isMinuteOnly {
				// 指定了时和分，结束时间为指定分钟的结束(xx:xx:59)
				endTime = time.Date(startTime.Year(), startTime.Month(), startTime.Day(),
					startTime.Hour(), startTime.Minute(), 59, 0, startTime.Location())
			} else {
				// 指定了时分秒，结束时间就是开始时间
				endTime = startTime
			}
		}

		s.logger.Info("根据时间查询",
			zap.String("create_at", query.TicketCreateAt),
			zap.Time("start_time", startTime),
			zap.Time("end_time", endTime),
			zap.Bool("is_last7days", query.TicketCreateAt == "last7days"),
		)
	}

	var tickets []*ticketModel.FaultTicket
	var total int64
	var err error

	// 创建多条件查询的收集器
	var allTickets []*ticketModel.FaultTicket
	var ticketMap = make(map[uint]*ticketModel.FaultTicket) // 用于去重

	// 如果提供了工单ID，优先使用工单号直接查询
	if query.TicketID != "" {
		// 直接使用工单号查询
		ticketIDs := strings.Split(query.TicketID, ",")
		var matchedTickets []*ticketModel.FaultTicket

		for _, ticketID := range ticketIDs {
			// 处理带i前缀的工单号
			ticketNo := strings.TrimSpace(ticketID)
			if ticketNo == "" {
				continue
			}

			// 尝试使用工单号查询
			ticket, err := s.faultTicketService.GetFaultTicketByTicketNo(ctx, ticketNo)
			if err != nil {
				s.logger.Error("查询工单号失败",
					zap.String("ticket_no", ticketNo),
					zap.Error(err),
				)
				continue
			}
			if ticket != nil {
				// 如果设置了时间过滤，需要检查工单创建时间是否在范围内
				if hasTimeFilter {
					if ticket.CreatedAt.Before(startTime) || ticket.CreatedAt.After(endTime) {
						continue
					}
				}

				// 如果设置了来源过滤，需要检查来源是否匹配
				if from != "" && ticket.Source != from {
					continue
				}

				// 记录找到的工单
				if _, exists := ticketMap[ticket.ID]; !exists {
					ticketMap[ticket.ID] = ticket
					matchedTickets = append(matchedTickets, ticket)
				}
			}
		}

		// 如果找到了匹配的工单，返回结果
		if len(matchedTickets) > 0 {
			// 转换为客户API返回格式
			details := make([]model.TicketDetail, 0, len(matchedTickets))

			// 保存符合条件的总工单数
			totalMatchedTickets := int64(len(matchedTickets))

			// 对结果集进行分页
			startIndex := (query.Page - 1) * query.PageSize
			endIndex := startIndex + query.PageSize

			// 确保索引不越界
			if startIndex < len(matchedTickets) {
				if endIndex > len(matchedTickets) {
					endIndex = len(matchedTickets)
				}
				matchedTickets = matchedTickets[startIndex:endIndex]

				// 生成details
				for _, ticket := range matchedTickets {
					// 转换为客户API格式
					detail := model.TicketDetail{
						TicketID:        ticket.TicketNo,
						TicketContent:   ticket.FaultDescription,
						TicketVmIP:      ticket.ResourceIdentifier,
						TicketFrom:      ticket.Source,
						TicketStatus:    mapStatusToExternal(ticket.Status),
						TicketCreateAt:  ticket.CreatedAt,
						TicketUpdatedAt: ticket.UpdatedAt,
						TicketCategory:  ticket.FaultType,
						IsColdmigration: ticket.RepairMethod == "cold_migration",
						IsAuthorized:    !ticket.RequireApproval || (ticket.RequireApproval && ticket.Status != "waiting_approval" && ticket.Status != "waiting_accept" && ticket.Status != "investigating"),
						RequireApproval: ticket.RequireApproval,
					}

					// 处理可选字段
					if ticket.FaultType != "" {
						detail.TicketSubcategory = ticket.FaultType + " - " + ticket.Symptom
					}
					if ticket.FaultSummary != "" {
						detail.TicketRepairComment = "维修完成"
					}
					if ticket.Status == "completed" && ticket.CloseTime != nil {
						detail.TicketClosedAt = ticket.CloseTime
					}

					details = append(details, detail)
				}
			} else {
				// 如果起始索引超出范围，返回空数组
				matchedTickets = []*ticketModel.FaultTicket{}
				details = []model.TicketDetail{}
			}

			s.logger.Info("查询工单完成",
				zap.Int64("total", totalMatchedTickets),
				zap.Int("result_count", len(matchedTickets)),
				zap.Int("page", query.Page),
				zap.Int("page_size", query.PageSize),
				zap.String("query_period", query.TicketCreateAt),
				zap.String("status_filter", string(query.TicketStatus)),
				zap.String("client_ip", clientIP),
				zap.String("customer_id", customerID),
			)

			return &model.CustomerTicketQueryResponse{
				Total:    totalMatchedTickets,
				Page:     query.Page,
				PageSize: query.PageSize,
				Data:     details,
			}, nil
		}

		// 如果没有找到匹配的工单，返回空结果
		s.logger.Info("未找到匹配的工单",
			zap.String("ticket_id", query.TicketID),
		)
		return &model.CustomerTicketQueryResponse{
			Total:    0,
			Page:     query.Page,
			PageSize: query.PageSize,
			Data:     []model.TicketDetail{},
		}, nil
	}

	// 如果提供了多个IP，且没有提供工单ID，或者提供了工单ID但没有找到工单，则通过IP查询
	if hasIPFilter && len(allTickets) == 0 {
		// 设置数据库上下文
		dbCtx := ctx
		if hasTimeFilter {
			// 创建超时上下文以防查询时间过长
			var cancel context.CancelFunc
			dbCtx, cancel = context.WithTimeout(ctx, 30*time.Second)
			defer cancel()
		}

		// 对每个IP单独查询，然后合并结果
		for _, ip := range ipList {
			// 查询工单列表，使用精确匹配
			ipTickets, _, err := s.faultTicketService.ListFaultTickets(dbCtx, 1, 100, map[string]interface{}{
				"status":              status,
				"resource_identifier": ip,
			})
			if err != nil {
				s.logger.Error("查询IP失败",
					zap.String("ip", ip),
					zap.Error(err),
				)
				continue
			}

			// 过滤出匹配的IP的工单
			for _, ticket := range ipTickets {
				if ticket.ResourceIdentifier == ip {
					// 如果设置了时间过滤，需要检查工单创建时间是否在范围内
					if hasTimeFilter {
						if ticket.CreatedAt.Before(startTime) || ticket.CreatedAt.After(endTime) {
							continue
						}
					}

					// 检查来源过滤
					if from == "" || ticket.Source == from {
						// 添加到结果集（去重）
						if _, exists := ticketMap[ticket.ID]; !exists {
							ticketMap[ticket.ID] = ticket
							allTickets = append(allTickets, ticket)
						}
					}
				}
			}
		}
	} else if !hasIPFilter && len(allTickets) == 0 {
		// 没有IP过滤且没有工单ID，或者有工单ID但没有找到工单，则进行普通查询
		// 处理状态查询，并添加时间过滤条件
		dbCtx := ctx
		if hasTimeFilter {
			// 创建超时上下文以防查询时间过长
			var cancel context.CancelFunc
			dbCtx, cancel = context.WithTimeout(ctx, 30*time.Second)
			defer cancel()
		}

		// 查询工单列表
		tickets, total, err = s.faultTicketService.ListFaultTickets(dbCtx, query.Page, query.PageSize, map[string]interface{}{
			"status": status,
		})
		if err != nil {
			return nil, fmt.Errorf("查询报障单失败: %w", err)
		}

		// 如果时间过滤条件无法传递给底层服务，则在内存中筛选
		if hasTimeFilter {
			// 记录原始查询结果数量
			originalTotal := total
			s.logger.Info("执行时间过滤前的查询结果",
				zap.Int64("original_total", originalTotal),
				zap.Int("result_count", len(tickets)),
			)

			// 创建一个新的工单列表，只包含符合时间条件的工单
			var filteredTickets []*ticketModel.FaultTicket
			for _, ticket := range tickets {
				if !ticket.CreatedAt.Before(startTime) && !ticket.CreatedAt.After(endTime) {
					// 检查来源过滤
					if from == "" || ticket.Source == from {
						filteredTickets = append(filteredTickets, ticket)
					}
				}
			}

			// 总是获取符合时间条件的总数，无论过滤结果如何
			allRecordsCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
			defer cancel()

			// 查询所有符合状态条件的工单
			allStatusTickets, _, err := s.faultTicketService.ListFaultTickets(allRecordsCtx, 1, 1000, map[string]interface{}{
				"status": status,
			})
			if err == nil {
				// 遍历所有工单并应用时间和来源过滤
				var filteredTotal int64
				for _, ticket := range allStatusTickets {
					if !ticket.CreatedAt.Before(startTime) && !ticket.CreatedAt.After(endTime) {
						if from == "" || ticket.Source == from {
							filteredTotal++
						}
					}
				}
				total = filteredTotal

				s.logger.Info("计算符合时间和来源过滤条件的总数",
					zap.Int64("filtered_total", total),
					zap.Int("all_status_tickets", len(allStatusTickets)),
					zap.Int("max_records", 5000),
				)
			} else {
				// 如果无法获取所有工单，则使用过滤后的数量作为总数
				total = int64(len(filteredTickets))
				s.logger.Warn("无法获取所有符合条件的工单总数，使用当前页过滤后的数量",
					zap.Error(err),
					zap.Int64("estimated_total", total),
				)
			}

			tickets = filteredTickets
			s.logger.Info("执行时间过滤后的查询结果",
				zap.Int64("filtered_total", total),
				zap.Int("result_count", len(filteredTickets)),
			)
		} else {
			// 无时间过滤，直接检查来源过滤
			var filteredTickets []*ticketModel.FaultTicket
			for _, ticket := range tickets {
				if from == "" || ticket.Source == from {
					filteredTickets = append(filteredTickets, ticket)
				}
			}

			// 如果进行了来源过滤，需要调整总数
			if len(filteredTickets) < len(tickets) && len(tickets) > 0 {
				// 获取所有符合状态条件的工单
				allRecordsCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
				defer cancel()

				allStatusTickets, _, err := s.faultTicketService.ListFaultTickets(allRecordsCtx, 1, 1000, map[string]interface{}{
					"status": status,
				})
				if err == nil {
					// 遍历所有工单并应用来源过滤
					var filteredTotal int64
					for _, ticket := range allStatusTickets {
						if from == "" || ticket.Source == from {
							filteredTotal++
						}
					}
					total = filteredTotal
				} else {
					// 如果无法获取所有工单，则使用过滤比例估算
					filterRatio := float64(len(filteredTickets)) / float64(len(tickets))
					total = int64(float64(total) * filterRatio)
				}
			}

			tickets = filteredTickets
		}
	}

	// 如果使用了组合条件查询，将allTickets赋值给tickets
	if len(allTickets) > 0 {
		// 保存总工单数
		totalAllTickets := int64(len(allTickets))

		// 对结果集进行分页
		startIndex := (query.Page - 1) * query.PageSize
		endIndex := startIndex + query.PageSize

		// 确保索引不越界
		if startIndex < len(allTickets) {
			if endIndex > len(allTickets) {
				endIndex = len(allTickets)
			}
			tickets = allTickets[startIndex:endIndex]
		} else {
			tickets = []*ticketModel.FaultTicket{}
		}

		// 使用总数而不是分页后的数量
		total = totalAllTickets
	}

	// 转换为客户API返回格式
	details := make([]model.TicketDetail, 0, len(tickets))
	for _, ticket := range tickets {
		// 转换为客户API格式
		detail := model.TicketDetail{
			TicketID:        ticket.TicketNo,
			TicketContent:   ticket.FaultDescription,
			TicketVmIP:      ticket.ResourceIdentifier,
			TicketFrom:      ticket.Source,
			TicketStatus:    mapStatusToExternal(ticket.Status),
			TicketCreateAt:  ticket.CreatedAt,
			TicketUpdatedAt: ticket.UpdatedAt,
			TicketCategory:  ticket.FaultType,
			IsColdmigration: ticket.RepairMethod == "cold_migration",
			IsAuthorized:    !ticket.RequireApproval || (ticket.RequireApproval && ticket.Status != "waiting_approval" && ticket.Status != "waiting_accept" && ticket.Status != "investigating"),
			RequireApproval: ticket.RequireApproval,
		}

		// 处理可选字段
		if ticket.FaultType != "" {
			detail.TicketSubcategory = ticket.FaultType + " - " + ticket.Symptom
		}
		if ticket.FaultSummary != "" {
			detail.TicketRepairComment = "维修完成"
		}
		if ticket.Status == "completed" && ticket.CloseTime != nil {
			detail.TicketClosedAt = ticket.CloseTime
		}

		details = append(details, detail)
	}

	s.logger.Info("查询工单完成",
		zap.Int64("total", total),
		zap.Int("result_count", len(tickets)),
		zap.String("query_period", query.TicketCreateAt),
		zap.String("status_filter", string(query.TicketStatus)),
		zap.String("client_ip", clientIP),
		zap.String("customer_id", customerID),
	)

	// 如果结果集为空，记录一个警告，可能有助于识别潜在的查询问题
	if len(tickets) == 0 && total == 0 {
		s.logger.Warn("查询工单结果为空",
			zap.String("filter_ip", query.TicketVmIP),
			zap.String("filter_id", query.TicketID),
			zap.String("filter_time", query.TicketCreateAt),
			zap.String("filter_status", string(query.TicketStatus)),
			zap.String("client_ip", clientIP),
		)
	}

	return &model.CustomerTicketQueryResponse{
		Total:    total,
		Page:     query.Page,
		PageSize: query.PageSize,
		Data:     details,
	}, nil
}

// logMultiIPSearch 记录多IP查询日志
func (s *customerTicketService) logMultiIPSearch(ctx context.Context, ipList []string) {
	// 获取客户端IP和客户ID
	clientIP := ""
	if ip, exists := ctx.Value("client_ip").(string); exists && ip != "" {
		clientIP = ip
	}

	customerID := ""
	if id, exists := ctx.Value("customerID").(string); exists && id != "" {
		customerID = id
	}

	// 记录查询信息
	ipCount := len(ipList)
	s.logger.Info("执行多IP查询",
		zap.Int("ip_count", ipCount),
		zap.Strings("ip_list", ipList),
		zap.String("client_ip", clientIP),
		zap.String("customer_id", customerID),
	)

	// 如果IP数量超过10个，记录更详细的日志
	if ipCount > 10 {
		s.logger.Info("大批量IP查询",
			zap.Int("total_count", ipCount),
			zap.Strings("first_10_ips", ipList[:10]),
			zap.String("client_ip", clientIP),
			zap.String("customer_id", customerID),
		)
	}

	// 如果IP数量超过50个，记录一个警告
	if ipCount > 50 {
		s.logger.Warn("超大规模IP查询",
			zap.Int("total_count", ipCount),
			zap.String("client_ip", clientIP),
			zap.String("customer_id", customerID),
		)
	}
}

// SetAuthority 设置授权
func (s *customerTicketService) SetAuthority(ctx context.Context, request *model.CustomerTicketAuthorityRequest) error {
	// 尝试从上下文获取客户端IP和客户ID
	clientIP := ""
	if ip, exists := ctx.Value("client_ip").(string); exists && ip != "" {
		clientIP = ip
	}

	customerID := ""
	if id, exists := ctx.Value("customerID").(string); exists && id != "" {
		customerID = id
	}

	s.logger.Info("开始设置工单授权",
		zap.String("ticket_id", request.TicketID),
		zap.String("ticket_vm_ip", request.TicketVmIP),
		zap.String("client_ip", clientIP),
		zap.String("customer_id", customerID),
	)

	// 判断请求参数有效性
	if request.TicketID == "" && request.TicketVmIP == "" {
		s.logger.Error("设置授权失败: TicketID和TicketVmIP都为空")
		return fmt.Errorf("TicketID和TicketVmIP至少需要提供一个")
	}

	if request.TicketID != "" {
		// 通过工单号直接查询
		ticketNo := request.TicketID
		s.logger.Info("通过工单号查询工单",
			zap.String("ticket_no", ticketNo),
		)

		// 获取工单
		ticket, err := s.faultTicketService.GetFaultTicketByTicketNo(ctx, ticketNo)
		if err != nil {
			s.logger.Error("获取工单失败",
				zap.Error(err),
			)
			return fmt.Errorf("获取工单失败: %w", err)
		}

		// 判断工单状态，只有待授权的工单才能授权
		if ticket.Status != "waiting_approval" {
			s.logger.Error("工单状态不正确",
				zap.String("current_status", ticket.Status),
				zap.String("required_status", "waiting_approval"),
			)
			return fmt.Errorf("工单状态不是待授权状态，无法授权")
		}

		prevStatus := ticket.Status

		// 更新工单状态和授权信息
		ticket.RequireApproval = false
		ticket.Status = "approved_waiting_action" // 更新状态为已授权待处理
		now := time.Now()
		ticket.CustomerApprovalTime = &now
		ticket.Remarks = ticket.Remarks + "\n客户API授权: " + time.Now().Format("2006-01-02 15:04:05")

		s.logger.Info("更新工单状态",
			zap.Uint("ticket_id", ticket.ID),
			zap.String("new_status", ticket.Status),
		)

		// 保存工单
		if err := s.faultTicketService.UpdateFaultTicket(ctx, ticket); err != nil {
			s.logger.Error("更新工单失败",
				zap.Error(err),
			)
			return fmt.Errorf("更新工单失败: %w", err)
		}

		// 确保客户审批时间字段被更新
		err = s.faultTicketService.UpdateFaultTicketFields(ctx, ticket.ID, map[string]interface{}{
			"customer_approval_time": now,
		})
		if err != nil {
			s.logger.Warn("更新客户审批时间失败，但不影响主流程",
				zap.Error(err),
				zap.Uint("ticket_id", ticket.ID),
			)
		}

		// 创建状态历史记录
		now = time.Now()
		customerIDUint := uint(0)
		if customerID != "" {
			customerIDUint64, err := strconv.ParseUint(customerID, 10, 64)
			if err == nil {
				customerIDUint = uint(customerIDUint64)
			}
		}
		customerName := "客户API"
		if customerID != "" {
			customerName = "客户API-" + customerID
		}

		statusHistory := &ticketModel.FaultTicketStatusHistory{
			FaultTicketID:    ticket.ID,
			PreviousStatus:   prevStatus,
			NewStatus:        ticket.Status,
			OperatorID:       customerIDUint,
			OperatorName:     customerName,
			OperationTime:    now,
			ActivityCategory: "customer_approval", // 客户审批类别
			IsSLAPause:       false,               // 授权后不再暂停SLA
			Remarks:          "客户通过API接口授权",
		}

		// 尝试创建状态历史记录
		if _, err := s.faultTicketService.GetFaultTicketStatusHistory(ctx, ticket.ID); err == nil {
			// 能够获取状态历史，说明可以创建新记录
			// 使用底层 repo 直接创建状态历史记录
			err = s.faultTicketService.UpdateFaultTicketStatus(ctx, ticket.ID, ticket.Status, statusHistory.OperatorID, statusHistory.OperatorName)
			if err != nil {
				// 只记录错误，不影响主流程
				s.logger.Error("创建状态历史记录失败，但不影响授权结果",
					zap.Error(err),
					zap.Uint("ticket_id", ticket.ID),
				)
			} else {
				s.logger.Info("成功创建状态历史记录",
					zap.Uint("ticket_id", ticket.ID),
					zap.String("previous_status", prevStatus),
					zap.String("new_status", ticket.Status),
				)
			}
		}

		// 创建客户审批记录
		now = time.Now()
		customerApproval := &ticketModel.CustomerApproval{
			TicketID:     ticket.ID,
			Status:       "approved", // 状态设置为已批准
			ResponseTime: now,
			Comments:     "客户通过API接口授权",
			CustomerID:   customerIDUint,
			CustomerName: customerName,
		}

		// 确保客户审批时间字段被更新
		err = s.faultTicketService.UpdateFaultTicketFields(ctx, ticket.ID, map[string]interface{}{
			"customer_approval_time": now,
		})
		if err != nil {
			s.logger.Warn("更新客户审批时间失败，但不影响主流程",
				zap.Error(err),
				zap.Uint("ticket_id", ticket.ID),
				zap.String("ticket_no", ticket.TicketNo),
			)
		}

		// 创建审批记录
		if err := s.faultTicketService.CreateCustomerApproval(ctx, customerApproval); err != nil {
			s.logger.Error("创建客户审批记录失败，但不影响授权结果",
				zap.Error(err),
				zap.Uint("ticket_id", ticket.ID),
			)
			// 记录错误但继续流程
		} else {
			s.logger.Info("成功创建客户审批记录",
				zap.Uint("ticket_id", ticket.ID),
				zap.String("ticket_no", ticket.TicketNo),
				zap.String("status", "approved"),
			)
		}

		s.logger.Info("工单授权成功",
			zap.String("ticket_no", ticket.TicketNo),
			zap.String("previous_status", "waiting_approval"),
			zap.String("new_status", ticket.Status),
			zap.String("resource_ip", ticket.ResourceIdentifier),
			zap.String("client_ip", clientIP),
			zap.String("customer_id", customerID),
		)

		// 发送飞书通知
		if s.feishuNotifier != nil {
			// 异步发送通知，不阻塞主流程
			go func() {
				if err := s.feishuNotifier.SendTicketAuthorizedNotification(
					fmt.Sprintf("%d", ticket.ID),
					ticket.TicketNo,
					ticket.ResourceIdentifier,
					ticket.ReporterName,
					ticket.FaultDescription,
				); err != nil {
					s.logger.Error("发送飞书通知失败",
						zap.Error(err),
						zap.String("ticket_no", ticket.TicketNo),
					)
				}
			}()
		}

		// 触发工作流信号，进入客户审批阶段
		err = s.faultTicketService.TriggerWorkflowStage(
			ctx,
			ticket.ID,
			"customer_approval",           // 客户审批阶段
			customerApproval.CustomerID,   // 使用审批记录中的客户ID
			customerApproval.CustomerName, // 使用审批记录中的客户名称
			customerApproval.Comments,     // 使用审批记录中的备注
			map[string]interface{}{
				"approved": true,
				"comments": customerApproval.Comments,
			},
		)
		if err != nil {
			// 记录错误，但不影响主流程
			s.logger.Error("触发工作流信号失败，但不影响授权结果",
				zap.Error(err),
				zap.Uint("ticket_id", ticket.ID),
				zap.String("ticket_no", ticket.TicketNo),
			)
		} else {
			s.logger.Info("成功触发工作流信号",
				zap.Uint("ticket_id", ticket.ID),
				zap.String("ticket_no", ticket.TicketNo),
				zap.String("stage", "customer_approval"),
			)
		}

		return nil
	} else {
		// 根据IP授权
		// 直接通过底层服务查询相关IP的工单，不使用QueryTickets方法
		// 这样可以避免复杂的过滤逻辑导致的问题
		s.logger.Info("通过IP直接查询待授权工单",
			zap.String("ip", request.TicketVmIP),
		)

		// 使用faultTicketService直接查询所有工单
		allTickets, _, err := s.faultTicketService.ListFaultTickets(ctx, 1, 100, map[string]interface{}{})
		if err != nil {
			s.logger.Error("查询工单失败",
				zap.String("ip", request.TicketVmIP),
				zap.Error(err),
			)
			return fmt.Errorf("查询工单失败: %w", err)
		}

		// 手动筛选出IP匹配且状态为waiting_approval的工单
		var matchedTickets []*ticketModel.FaultTicket
		for _, ticket := range allTickets {
			if ticket.ResourceIdentifier == request.TicketVmIP && ticket.Status == "waiting_approval" {
				matchedTickets = append(matchedTickets, ticket)
			}
		}

		// 如果没有找到工单
		if len(matchedTickets) == 0 {
			// 添加日志，记录所有匹配IP但状态不是waiting_approval的工单
			var otherStatusTickets []string
			for _, ticket := range allTickets {
				if ticket.ResourceIdentifier == request.TicketVmIP {
					otherStatusTickets = append(otherStatusTickets,
						fmt.Sprintf("工单号:%s,状态:%s", ticket.TicketNo, ticket.Status))
				}
			}

			if len(otherStatusTickets) > 0 {
				s.logger.Warn("找到匹配IP的工单，但状态不是待授权",
					zap.String("resource_ip", request.TicketVmIP),
					zap.Strings("tickets", otherStatusTickets),
					zap.String("client_ip", clientIP),
					zap.String("customer_id", customerID),
				)
				return fmt.Errorf("找到匹配IP的工单，但状态不是待授权，无法授权")
			}

			s.logger.Warn("未找到需要授权的工单",
				zap.String("resource_ip", request.TicketVmIP),
				zap.String("client_ip", clientIP),
				zap.String("customer_id", customerID),
			)
			return fmt.Errorf("未找到需要授权的工单")
		}

		// 记录找到的需要授权的工单数量
		s.logger.Info("找到需要授权的工单",
			zap.Int("ticket_count", len(matchedTickets)),
			zap.String("resource_ip", request.TicketVmIP),
			zap.String("client_ip", clientIP),
			zap.String("customer_id", customerID),
		)

		// 处理客户ID
		customerIDUint := uint(0)
		if customerID != "" {
			customerIDUint64, err := strconv.ParseUint(customerID, 10, 64)
			if err == nil {
				customerIDUint = uint(customerIDUint64)
			}
		}
		customerName := "客户API" + customerID // 使用客户ID作为名称标识

		// 遍历并授权
		var successCount, failCount int
		var approvalSuccessCount, approvalFailCount int
		var workflowSignalSuccess, workflowSignalFail int
		var historySuccessCount, historyFailCount int

		for _, ticket := range matchedTickets {
			// 保存原始状态，用于记录历史
			prevStatus := ticket.Status

			// 更新工单状态和授权信息
			ticket.RequireApproval = false
			ticket.Status = "approved_waiting_action" // 更新状态为已授权待处理
			ticket.Remarks = ticket.Remarks + "\n客户API授权: " + time.Now().Format("2006-01-02 15:04:05")

			s.logger.Info("更新工单状态",
				zap.Uint("ticket_id", ticket.ID),
				zap.String("ticket_no", ticket.TicketNo),
				zap.String("new_status", ticket.Status),
			)

			// 保存工单
			if err := s.faultTicketService.UpdateFaultTicket(ctx, ticket); err != nil {
				// 只记录错误，不中断流程
				failCount++
				s.logger.Error("更新工单失败",
					zap.String("ticket_no", ticket.TicketNo),
					zap.String("resource_ip", ticket.ResourceIdentifier),
					zap.String("client_ip", clientIP),
					zap.Error(err),
				)
				continue // 更新失败，跳过触发工作流信号
			} else {
				successCount++
				s.logger.Info("工单授权成功",
					zap.String("ticket_no", ticket.TicketNo),
					zap.String("previous_status", "waiting_approval"),
					zap.String("new_status", ticket.Status),
					zap.String("resource_ip", ticket.ResourceIdentifier),
					zap.String("client_ip", clientIP),
					zap.String("customer_id", customerID),
				)

				// 确保客户审批时间字段被更新
				updateTimeErr := s.faultTicketService.UpdateFaultTicketFields(ctx, ticket.ID, map[string]interface{}{
					"customer_approval_time": time.Now(),
				})
				if updateTimeErr != nil {
					s.logger.Warn("更新客户审批时间失败，但不影响主流程",
						zap.Error(updateTimeErr),
						zap.Uint("ticket_id", ticket.ID),
						zap.String("ticket_no", ticket.TicketNo),
					)
				}
			}

			// 创建状态历史记录
			err = s.faultTicketService.UpdateFaultTicketStatus(ctx, ticket.ID, ticket.Status, customerIDUint, customerName)
			if err != nil {
				// 只记录错误，不影响主流程
				historyFailCount++
				s.logger.Error("创建状态历史记录失败，但不影响授权结果",
					zap.Error(err),
					zap.Uint("ticket_id", ticket.ID),
					zap.String("ticket_no", ticket.TicketNo),
				)
			} else {
				historySuccessCount++
				s.logger.Info("成功创建状态历史记录",
					zap.Uint("ticket_id", ticket.ID),
					zap.String("ticket_no", ticket.TicketNo),
					zap.String("previous_status", prevStatus),
					zap.String("new_status", ticket.Status),
				)
			}

			// 创建客户审批记录
			now := time.Now()
			customerApproval := &ticketModel.CustomerApproval{
				TicketID:     ticket.ID,
				Status:       "approved", // 状态设置为已批准
				ResponseTime: now,
				Comments:     "客户通过API接口授权",
				CustomerID:   customerIDUint,
				CustomerName: customerName,
			}

			// 创建审批记录
			if err := s.faultTicketService.CreateCustomerApproval(ctx, customerApproval); err != nil {
				approvalFailCount++
				s.logger.Error("创建客户审批记录失败，但不影响授权结果",
					zap.Error(err),
					zap.Uint("ticket_id", ticket.ID),
					zap.String("ticket_no", ticket.TicketNo),
				)
				// 记录错误但继续流程
			} else {
				approvalSuccessCount++
				s.logger.Info("成功创建客户审批记录",
					zap.Uint("ticket_id", ticket.ID),
					zap.String("ticket_no", ticket.TicketNo),
					zap.String("status", "approved"),
				)
			}

			// 触发工作流信号，进入客户审批阶段
			err = s.faultTicketService.TriggerWorkflowStage(
				ctx,
				ticket.ID,
				"customer_approval", // 客户审批阶段
				customerIDUint,      // 客户ID
				customerName,        // 客户名称
				"客户通过API接口授权",       // 备注信息
				map[string]interface{}{
					"approved": true,
					"comments": "客户通过API接口授权",
				},
			)
			if err != nil {
				// 记录错误，但不影响主流程
				workflowSignalFail++
				s.logger.Error("触发工作流信号失败，但不影响授权结果",
					zap.Error(err),
					zap.Uint("ticket_id", ticket.ID),
					zap.String("ticket_no", ticket.TicketNo),
				)
			} else {
				workflowSignalSuccess++
				s.logger.Info("成功触发工作流信号",
					zap.Uint("ticket_id", ticket.ID),
					zap.String("ticket_no", ticket.TicketNo),
					zap.String("stage", "customer_approval"),
				)
			}
		}

		// 记录最终结果
		s.logger.Info("批量授权工单完成",
			zap.Int("total", len(matchedTickets)),
			zap.Int("success", successCount),
			zap.Int("fail", failCount),
			zap.Int("approval_success", approvalSuccessCount),
			zap.Int("approval_fail", approvalFailCount),
			zap.Int("history_success", historySuccessCount),
			zap.Int("history_fail", historyFailCount),
			zap.Int("workflow_signal_success", workflowSignalSuccess),
			zap.Int("workflow_signal_fail", workflowSignalFail),
			zap.String("resource_ip", request.TicketVmIP),
			zap.String("client_ip", clientIP),
			zap.String("customer_id", customerID),
		)

		if successCount == 0 {
			return fmt.Errorf("所有工单授权失败")
		}

		return nil
	}
}

// mapStatusToInternal 将客户API状态映射到内部状态
func mapStatusToInternal(status model.TicketStatusEnum) string {
	switch status {
	case model.StatusOpen:
		return "waiting_accept"
	case model.StatusAuthority:
		return "waiting_approval"
	case model.StatusInProgress:
		// 返回逗号分隔的多个状态，ListFaultTickets会用IN查询处理
		return "investigating,repairing,restarting,migrating,software_fixing"
	case model.StatusClosed:
		return "completed"
	default:
		return ""
	}
}

// mapStatusToExternal 将内部状态映射到客户API状态
func mapStatusToExternal(status string) string {
	switch status {
	case "waiting_accept":
		return string(model.StatusOpen)
	case "waiting_approval":
		return string(model.StatusAuthority)
	case "investigating", "repairing", "restarting", "migrating", "software_fixing", "approved_waiting_action", "waiting_verification", "summarizing":
		return string(model.StatusInProgress)
	case "completed", "cancelled":
		return string(model.StatusClosed)
	default:
		return string(model.StatusInProgress)
	}
}

// BatchCreateTickets 批量创建工单
func (s *customerTicketService) BatchCreateTickets(ctx context.Context, request *model.BatchCreateTicketRequest) (*model.BatchCreateTicketResponse, error) {
	// 尝试从上下文获取客户端IP和客户ID
	clientIP := ""
	if ip, exists := ctx.Value("client_ip").(string); exists && ip != "" {
		clientIP = ip
	}

	customerID := ""
	if id, exists := ctx.Value("customerID").(string); exists && id != "" {
		customerID = id
	}

	s.logger.Info("开始批量创建工单",
		zap.Int("ticket_count", len(request.Tickets)),
		zap.String("client_ip", clientIP),
		zap.String("customer_id", customerID),
	)

	// 记录所有IP地址，便于问题追踪
	var ipList []string
	for _, ticket := range request.Tickets {
		ipList = append(ipList, ticket.TicketVmIP)
	}
	if len(ipList) > 0 {
		s.logger.Info("批量创建工单IP列表",
			zap.Strings("ip_list", ipList),
		)
	}

	response := &model.BatchCreateTicketResponse{
		Results: make([]model.BatchCreateTicketResult, 0, len(request.Tickets)),
	}

	// 遍历处理每个工单
	for i, ticketReq := range request.Tickets {
		s.logger.Info("处理工单",
			zap.Int("index", i+1),
			zap.Int("total", len(request.Tickets)),
			zap.String("ip", ticketReq.TicketVmIP),
		)

		result := model.BatchCreateTicketResult{
			TicketVmIP: ticketReq.TicketVmIP,
			Success:    false,
			Detail:     &ticketReq,
		}

		// 复制请求，避免修改原始请求
		reqCopy := ticketReq

		// 预处理IP地址
		reqCopy.TicketVmIP = strings.TrimSpace(reqCopy.TicketVmIP)

		// 尝试创建工单
		ticketRes, err := s.CreateTicket(ctx, &reqCopy)
		if err != nil {
			// 记录失败原因
			result.Error = err.Error()
			response.FailCount++
			s.logger.Error("创建工单失败",
				zap.String("ip", ticketReq.TicketVmIP),
				zap.Error(err),
			)
		} else {
			// 记录成功结果
			result.Success = true
			result.TicketID = ticketRes.TicketID // 直接使用工单号
			response.SuccessCount++
			s.logger.Info("创建工单成功",
				zap.String("ip", ticketReq.TicketVmIP),
				zap.String("ticket_id", ticketRes.TicketID),
			)

			// 注意：工作流已在CreateTicket方法中启动，此处无需重复启动
		}

		// 添加到结果列表
		response.Results = append(response.Results, result)
	}

	s.logger.Info("批量创建工单完成",
		zap.Int("success_count", response.SuccessCount),
		zap.Int("fail_count", response.FailCount),
		zap.String("client_ip", clientIP),
		zap.String("customer_id", customerID),
	)

	// 记录所有失败的IP和原因，方便后续问题排查
	if response.FailCount > 0 {
		var failedIPs []string
		var failReasons []string
		for _, result := range response.Results {
			if !result.Success {
				failedIPs = append(failedIPs, result.TicketVmIP)
				failReasons = append(failReasons, result.Error)
			}
		}
		s.logger.Warn("部分工单创建失败",
			zap.Strings("failed_ips", failedIPs),
			zap.Strings("fail_reasons", failReasons),
			zap.String("client_ip", clientIP),
		)
	}

	return response, nil
}
