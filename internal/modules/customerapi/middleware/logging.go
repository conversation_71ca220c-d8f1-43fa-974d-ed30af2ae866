package middleware

import (
	"backend/internal/modules/customerapi/model"
	"backend/internal/modules/customerapi/service"
	"bytes"
	"encoding/json"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// APILoggerMiddleware API操作日志中间件
type APILoggerMiddleware struct {
	logService service.CustomerAPILogService
	logger     *zap.Logger
}

// NewAPILoggerMiddleware 创建API日志中间件
func NewAPILoggerMiddleware(logService service.CustomerAPILogService, logger *zap.Logger) *APILoggerMiddleware {
	return &APILoggerMiddleware{
		logService: logService,
		logger:     logger,
	}
}

// LoggingMiddleware 日志记录中间件
func (m *APILoggerMiddleware) LoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始处理前捕获请求信息
		startTime := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method
		clientIP := c.ClientIP()

		// 将客户端IP存入上下文，方便后续业务逻辑使用
		c.Set("client_ip", clientIP)

		// 获取客户ID
		customerID, exists := c.Get("customerID")
		customerIDStr := ""
		if exists {
			var ok bool
			customerIDStr, ok = customerID.(string)
			if !ok {
				m.logger.Warn("customerID类型断言失败", zap.Any("customerID", customerID))
			}
		}

		// 读取请求体
		var requestBody string
		if c.Request.Body != nil && c.Request.ContentLength > 0 {
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err != nil {
				m.logger.Error("读取请求体失败", zap.Error(err))
			} else {
				requestBody = string(bodyBytes)
				// 重置请求体，以便后续处理
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}

		// 使用自定义响应写入器捕获响应
		responseBodyWriter := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = responseBodyWriter

		// 继续处理请求
		c.Next()

		// 处理完成后记录响应信息
		endTime := time.Now()
		latency := endTime.Sub(startTime)
		statusCode := responseBodyWriter.Status()
		responseBody := responseBodyWriter.body.String()

		// 提取操作类型
		action := extractAction(path)

		// 提取资源IP和工单号
		resourceIP, ticketNo := extractResourceInfo(c, requestBody)

		// 创建日志记录
		apiLog := &model.CustomerAPILog{
			Action:        action,
			OperationTime: startTime,
			SourceIP:      clientIP,
			CustomerID:    customerIDStr,
			RequestPath:   path,
			RequestMethod: method,
			RequestBody:   requestBody,
			ResourceIP:    resourceIP,
			TicketNo:      ticketNo,
			ResponseCode:  statusCode,
			ResponseMsg:   extractResponseMessage(responseBody),
			Success:       statusCode >= 200 && statusCode < 300,
		}

		// 记录日志
		if err := m.logService.LogAPIOperation(c.Request.Context(), apiLog); err != nil {
			m.logger.Error("记录API操作日志失败",
				zap.Error(err),
				zap.String("path", path),
				zap.String("client_ip", clientIP),
			)
		}

		// 记录请求处理时间（但不保存到数据库）
		m.logger.Info("API请求处理完成",
			zap.String("path", path),
			zap.String("method", method),
			zap.String("client_ip", clientIP),
			zap.Duration("latency", latency),
			zap.Int("status", statusCode),
		)
	}
}

// 自定义响应写入器，用于捕获响应内容
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func (w bodyLogWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

// 提取操作类型
func extractAction(path string) string {
	switch path {
	case "/event/api/create":
		return "create"
	case "/event/api/batch_create":
		return "batch_create"
	case "/event/api/query":
		return "query"
	case "/event/api/set_authority":
		return "set_authority"
	default:
		return "other"
	}
}

// 提取资源IP和工单号
func extractResourceInfo(c *gin.Context, requestBody string) (string, string) {
	var resourceIP, ticketNo string

	// 从URL参数获取
	if c.Request.Method == "GET" {
		resourceIP = c.Query("ticketVmIp")
		ticketNo = c.Query("ticketId")
	} else if requestBody != "" && c.Request.Method == "POST" {
		// 从请求体JSON中提取
		var requestMap map[string]interface{}
		if err := json.Unmarshal([]byte(requestBody), &requestMap); err == nil {
			// 尝试提取不同格式的字段名
			if ip, ok := requestMap["ticketVmIP"].(string); ok {
				resourceIP = ip
			} else if ip, ok := requestMap["ticketVmIp"].(string); ok {
				resourceIP = ip
			}

			if id, ok := requestMap["ticketId"].(string); ok {
				ticketNo = id
			} else if id, ok := requestMap["ticketID"].(string); ok {
				ticketNo = id
			}
		}
	}

	return resourceIP, ticketNo
}

// 提取响应消息
func extractResponseMessage(responseBody string) string {
	if responseBody == "" {
		return ""
	}

	var responseMap map[string]interface{}
	if err := json.Unmarshal([]byte(responseBody), &responseMap); err == nil {
		if msg, ok := responseMap["msg"].(string); ok {
			return msg
		}
	}

	return ""
}
