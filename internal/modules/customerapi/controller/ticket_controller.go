package controller

import (
	"backend/internal/modules/customerapi/model"
	"backend/internal/modules/customerapi/service"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CustomerTicketController 客户工单控制器
type CustomerTicketController struct {
	service service.CustomerTicketService
	logger  *zap.Logger
}

// NewCustomerTicketController 创建客户工单控制器
func NewCustomerTicketController(service service.CustomerTicketService, logger *zap.Logger) *CustomerTicketController {
	// 如果没有提供日志记录器，则使用noop logger
	if logger == nil {
		logger = zap.NewNop()
	}

	return &CustomerTicketController{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (c *CustomerTicketController) RegisterRoutes(router *gin.RouterGroup) {
	// 注册客户API路由
	router.POST("/create", c.CreateTicket)             // 创建工单
	router.POST("/batch_create", c.BatchCreateTickets) // 批量创建工单
	router.GET("/query", c.QueryTickets)               // 查询工单
	router.POST("/set_authority", c.SetAuthority)      // 设置授权

	// 添加签名认证说明文档路由
	//router.GET("/auth-help", c.GetAuthHelp)
}

// GetAuthHelp 获取认证帮助文档
func (c *CustomerTicketController) GetAuthHelp(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, model.APIResponse{
		Code: 0,
		Msg:  "签名认证说明",
		Data: map[string]interface{}{
			"authentication_method": "HMAC-SHA256签名",
			"required_headers": []string{
				"X-Customer-ID: 客户唯一标识",
				"X-Timestamp: 当前UNIX时间戳（秒）",
				"X-Signature: 签名",
			},
			"signature_algorithm": "HMAC-SHA256(customerID + timestamp, secretKey)",
			"signature_validity":  "5分钟",
			"ticket_id_format":    "工单ID格式为'i'开头加数字，如'i202503232626000'。设置授权时可以直接使用此格式ID，系统会自动处理前缀。",
			"batch_create_example": map[string]interface{}{
				"request": map[string]interface{}{
					"tickets": []map[string]string{
						{
							"ticketVmIP":    "*************",
							"ticketContent": "服务器CPU使用率过高",
						},
						{
							"ticketVmIP":    "*************",
							"ticketContent": "数据库连接异常",
						},
					},
				},
				"curl_example": "curl -X POST http://localhost:8090/event/api/batch_create -H 'X-Customer-ID: customer123' -H 'X-Timestamp: 1617189876' -H 'X-Signature: abc123signature' -H 'Content-Type: application/json' -d '{\"tickets\":[{\"ticketVmIP\":\"*************\",\"ticketContent\":\"服务器CPU使用率过高\"},{\"ticketVmIP\":\"*************\",\"ticketContent\":\"数据库连接异常\"}]}'",
			},
			"set_authority_example": map[string]interface{}{
				"request": map[string]interface{}{
					"ticketId": "i202503260192813",
				},
				"curl_example": "curl -X POST http://localhost:8090/event/api/set_authority -H 'X-Customer-ID: customer123' -H 'X-Timestamp: 1617189876' -H 'X-Signature: abc123signature' -H 'Content-Type: application/json' -d '{\"ticketId\":\"i202503260192813\"}'",
				"alternative_request": map[string]interface{}{
					"ticketVmIp": "*************",
				},
			},
			"authorization_fields": map[string]string{
				"requireApproval": "是否需要客户审批(从后台系统直接映射)",
				"isAuthorized":    "是否已授权(当不需要审批或已经超过待授权状态时为true)",
			},
			"example_code": map[string]string{
				"go": `
timestamp := fmt.Sprintf("%d", time.Now().Unix())
message := customerID + timestamp
mac := hmac.New(sha256.New, []byte(secretKey))
mac.Write([]byte(message))
signature := hex.EncodeToString(mac.Sum(nil))
`,
				"python": `
import hashlib
import hmac
import time

timestamp = str(int(time.time()))
message = customer_id + timestamp
signature = hmac.new(
    secret_key.encode('utf-8'),
    message.encode('utf-8'),
    hashlib.sha256
).hexdigest()
`,
				"java": `
long timestamp = System.currentTimeMillis() / 1000L;
String message = customerId + timestamp;
Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
SecretKeySpec secret_key = new SecretKeySpec(secretKey.getBytes("UTF-8"), "HmacSHA256");
sha256_HMAC.init(secret_key);
String signature = Hex.encodeHexString(sha256_HMAC.doFinal(message.getBytes("UTF-8")));
`,
			},
		},
	})
}

// respondError 通用错误响应处理
func (c *CustomerTicketController) respondError(ctx *gin.Context, code int, message string, err error) {
	errMsg := message
	if err != nil {
		errMsg = fmt.Sprintf("%s: %v", message, err)
		c.logger.Error(message, zap.Error(err))
	}

	ctx.JSON(http.StatusOK, model.APIResponse{
		Code: code,
		Msg:  errMsg,
	})
}

// respondSuccess 通用成功响应处理
func (c *CustomerTicketController) respondSuccess(ctx *gin.Context, message string, data interface{}) {
	ctx.JSON(http.StatusOK, model.APIResponse{
		Code: 0,
		Msg:  message,
		Data: data,
	})
}

// validateTicketRequest 验证工单创建请求
func (c *CustomerTicketController) validateTicketRequest(request *model.CustomerTicketRequest) error {
	if request.TicketVmIP == "" {
		return fmt.Errorf("ticketVmIP是必填字段")
	}
	if request.TicketContent == "" {
		return fmt.Errorf("ticketContent是必填字段")
	}
	return nil
}

// CreateTicket 创建工单
// @Summary 创建客户报障工单
// @Description 提交客户报障工单，默认影响SLA，提交时间为故障时间，授权时间为提交时间，一旦提交成功，默认客户已授权运维人员进行处理，不再单独申请客户授权
// @Tags 客户API
// @Accept json
// @Produce json
// @Param request body model.CustomerTicketRequest true "报障请求"
// @Success 200 {object} model.APIResponse{data=model.CustomerTicketResponse}
// @Failure 400 {object} model.APIResponse
// @Router /event/api/create [post]
func (c *CustomerTicketController) CreateTicket(ctx *gin.Context) {
	var request model.CustomerTicketRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		c.respondError(ctx, 1, "参数错误", err)
		return
	}

	// 参数验证
	if err := c.validateTicketRequest(&request); err != nil {
		c.respondError(ctx, 1, "参数错误", err)
		return
	}

	// 调用服务创建工单
	response, err := c.service.CreateTicket(ctx, &request)
	if err != nil {
		// 检查是否是已存在工单的情况
		if response != nil && response.ExistingTicket {
			// 使用特定格式返回已存在工单的信息
			c.respondSuccess(ctx, fmt.Sprintf("已存在IP为 %s 的重复工单", request.TicketVmIP), map[string]string{
				"ticketId": response.TicketID,
			})
			return
		}

		c.respondError(ctx, 1, "创建工单失败", err)
		return
	}

	// 返回成功响应
	c.respondSuccess(ctx, "创建成功", response)
}

// QueryTickets 查询工单
// @Summary 查询工单列表
// @Description 查询工单列表，支持多种筛选条件
// @Tags 客户API
// @Accept json
// @Produce json
// @Param ticketId query string false "事件单ID，多个用逗号分隔"
// @Param ticketStatus query string false "状态: open, authority, in_progress, closed, all(默认)"
// @Param ticketCreateAt query string false "创建时间，支持today, yesterday, thisweek, lastweek, thismonth, lastmonth, last7days或具体日期如2023-01-01"
// @Param ticketFrom query string false "来源: customer, cnhancloud, all(默认)"
// @Param pageSize query int false "每页条数，默认20，最大100"
// @Param page query int false "页码，默认1"
// @Param ticketVmIp query string false "虚机IP，多个用逗号分隔"
// @Success 200 {object} model.APIResponse{data=model.CustomerTicketQueryResponse}
// @Failure 400 {object} model.APIResponse
// @Router /event/api/query [get]
func (c *CustomerTicketController) QueryTickets(ctx *gin.Context) {
	// 解析查询参数
	var request model.CustomerTicketQueryRequest
	if err := ctx.ShouldBindQuery(&request); err != nil {
		c.respondError(ctx, 1, "参数错误", err)
		return
	}

	// 设置默认值
	if request.TicketStatus == "" {
		request.TicketStatus = model.StatusAll
	}
	if request.TicketFrom == "" {
		request.TicketFrom = model.FromAll
	}
	if request.Page <= 0 {
		request.Page = 1
	}
	if request.PageSize <= 0 {
		request.PageSize = 20
	}
	if request.PageSize > 100 {
		request.PageSize = 100
	}

	// 调用服务查询工单
	response, err := c.service.QueryTickets(ctx, &request)
	if err != nil {
		c.respondError(ctx, 1, "查询工单失败", err)
		return
	}

	// 返回成功响应
	c.respondSuccess(ctx, "查询成功", response)
}

// SetAuthority 设置授权
// @Summary 设置工单授权
// @Description 设置工单授权，可以通过工单ID或虚机IP授权
// @Tags 客户API
// @Accept json
// @Produce json
// @Param request body model.CustomerTicketAuthorityRequest true "授权请求"
// @Success 200 {object} model.APIResponse
// @Failure 400 {object} model.APIResponse
// @Router /event/api/set_authority [post]
func (c *CustomerTicketController) SetAuthority(ctx *gin.Context) {
	// 解析JSON参数
	var request model.CustomerTicketAuthorityRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		c.respondError(ctx, 1, "参数错误", err)
		return
	}

	// 参数验证
	if request.TicketID == "" && request.TicketVmIP == "" {
		c.respondError(ctx, 1, "参数错误", fmt.Errorf("ticketId和ticketVmIp至少需要提供一个"))
		return
	}

	// 调用服务设置授权
	if err := c.service.SetAuthority(ctx, &request); err != nil {
		c.respondError(ctx, 1, "设置授权失败", err)
		return
	}

	// 返回成功响应
	c.respondSuccess(ctx, "操作成功", nil)
}

// BatchCreateTickets 批量创建工单
// @Summary 批量创建客户报障工单
// @Description 一次提交多个客户报障工单，返回每个工单的创建结果
// @Tags 客户API
// @Accept json
// @Produce json
// @Param request body model.BatchCreateTicketRequest true "批量报障请求"
// @Success 200 {object} model.APIResponse{data=model.BatchCreateTicketResponse}
// @Failure 400 {object} model.APIResponse
// @Router /event/api/batch_create [post]
func (c *CustomerTicketController) BatchCreateTickets(ctx *gin.Context) {
	// 记录请求日志
	c.logger.Info("收到批量创建请求",
		zap.String("content_type", ctx.ContentType()),
		zap.String("client_ip", ctx.ClientIP()),
		zap.String("method", ctx.Request.Method),
		zap.String("path", ctx.Request.URL.Path))

	var request model.BatchCreateTicketRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		c.logger.Error("解析请求体失败", zap.Error(err))
		c.respondError(ctx, 1, "参数错误", err)
		return
	}

	// 参数验证
	if len(request.Tickets) == 0 {
		c.respondError(ctx, 1, "参数错误", fmt.Errorf("至少需要提供一个工单"))
		return
	}

	// 调用服务批量创建工单
	response, err := c.service.BatchCreateTickets(ctx, &request)
	if err != nil {
		c.respondError(ctx, 1, "批量创建工单失败", err)
		return
	}

	// 返回成功响应
	c.respondSuccess(ctx, fmt.Sprintf("批量创建完成，成功: %d，失败: %d", response.SuccessCount, response.FailCount), response)
}
