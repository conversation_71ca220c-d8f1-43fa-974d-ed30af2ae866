package repository

import (
	"backend/internal/modules/customerapi/model"
	"context"
	"time"

	"gorm.io/gorm"
)

// CustomerAPILogRepository 客户API日志仓库接口
type CustomerAPILogRepository interface {
	// Create 创建API操作日志
	Create(ctx context.Context, log *model.CustomerAPILog) error

	// ListByQuery 按条件查询API日志
	ListByQuery(ctx context.Context, customerID, resourceIP, action string, startTime, endTime time.Time, page, pageSize int) ([]*model.CustomerAPILog, int64, error)
}

// customerAPILogRepository 客户API日志仓库实现
type customerAPILogRepository struct {
	db *gorm.DB
}

// NewCustomerAPILogRepository 创建客户API日志仓库
func NewCustomerAPILogRepository(db *gorm.DB) CustomerAPILogRepository {
	return &customerAPILogRepository{
		db: db,
	}
}

// Create 创建API操作日志
func (r *customerAPILogRepository) Create(ctx context.Context, log *model.CustomerAPILog) error {
	return r.db.WithContext(ctx).Create(log).Error
}

// ListByQuery 按条件查询API日志
func (r *customerAPILogRepository) ListByQuery(ctx context.Context, customerID, resourceIP, action string, startTime, endTime time.Time, page, pageSize int) ([]*model.CustomerAPILog, int64, error) {
	var logs []*model.CustomerAPILog
	var total int64

	db := r.db.WithContext(ctx).Model(&model.CustomerAPILog{})

	// 应用过滤条件
	if customerID != "" {
		db = db.Where("customer_id = ?", customerID)
	}

	if resourceIP != "" {
		db = db.Where("resource_ip = ?", resourceIP)
	}

	if action != "" {
		db = db.Where("action = ?", action)
	}

	if !startTime.IsZero() {
		db = db.Where("operation_time >= ?", startTime)
	}

	if !endTime.IsZero() {
		db = db.Where("operation_time <= ?", endTime)
	}

	// 计算总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 应用排序和分页
	db = db.Order("operation_time DESC")

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 执行查询
	err = db.Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}
