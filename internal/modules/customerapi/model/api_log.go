package model

import (
	"time"

	"gorm.io/gorm"
)

// CustomerAPILog 客户API操作日志模型
type CustomerAPILog struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// 操作信息
	Action        string    `gorm:"type:varchar(50);not null" json:"action"`      // 操作类型：create/query/batch_create/set_authority
	OperationTime time.Time `gorm:"not null" json:"operation_time"`               // 操作时间
	SourceIP      string    `gorm:"type:varchar(50);not null" json:"source_ip"`   // 来源IP地址
	CustomerID    string    `gorm:"type:varchar(50);not null" json:"customer_id"` // 客户ID

	// 请求信息
	RequestPath   string `gorm:"type:varchar(255)" json:"request_path"`  // 请求路径
	RequestMethod string `gorm:"type:varchar(10)" json:"request_method"` // 请求方法
	RequestBody   string `gorm:"type:text" json:"request_body"`          // 请求体内容

	// 工单信息
	TicketNo   string `gorm:"type:varchar(50);index" json:"ticket_no"` // 关联的工单号
	ResourceIP string `gorm:"type:varchar(50)" json:"resource_ip"`     // 资源IP(虚机IP)

	// 响应信息
	ResponseCode int    `gorm:"" json:"response_code"`                 // 响应状态码
	ResponseMsg  string `gorm:"type:varchar(255)" json:"response_msg"` // 响应消息
	Success      bool   `gorm:"" json:"success"`                       // 操作是否成功
}

// TableName 表名
func (CustomerAPILog) TableName() string {
	return "customer_api_logs"
}
