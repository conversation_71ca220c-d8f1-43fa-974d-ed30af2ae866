package model

import (
	"time"
)

// CustomerTicketRequest 客户报障请求
type CustomerTicketRequest struct {
	TicketVmIP    string `json:"ticketVmIP" binding:"required"`    // 虚机IP
	TicketContent string `json:"ticketContent" binding:"required"` // 报障内容
}

// CustomerTicketResponse 客户报障响应
type CustomerTicketResponse struct {
	TicketID       string `json:"ticketId"`                 // 事件单ID，格式为i+数字
	ExistingTicket bool   `json:"existingTicket,omitempty"` // 是否为已存在的工单
}

// BatchCreateTicketRequest 批量创建工单请求
type BatchCreateTicketRequest struct {
	Tickets []CustomerTicketRequest `json:"tickets" binding:"required,min=1"` // 工单列表，至少需要一个
}

// BatchCreateTicketResponse 批量创建工单响应
type BatchCreateTicketResponse struct {
	SuccessCount int                       `json:"successCount"` // 成功创建数量
	FailCount    int                       `json:"failCount"`    // 失败数量
	Results      []BatchCreateTicketResult `json:"results"`      // 详细结果
}

// BatchCreateTicketResult 批量创建单个工单结果
type BatchCreateTicketResult struct {
	TicketVmIP string                 `json:"ticketVmIP"` // 虚机IP
	Success    bool                   `json:"success"`    // 是否成功
	TicketID   string                 `json:"ticketId"`   // 成功时的工单ID，格式为i+数字
	Error      string                 `json:"error"`      // 失败原因
	Detail     *CustomerTicketRequest `json:"detail"`     // 原始请求详情
}

// TicketStatusEnum 事件单状态枚举
type TicketStatusEnum string

const (
	StatusOpen       TicketStatusEnum = "open"        // 未开始
	StatusAuthority  TicketStatusEnum = "authority"   // 待授权
	StatusInProgress TicketStatusEnum = "in_progress" // 处理中
	StatusClosed     TicketStatusEnum = "closed"      // 已完成
	StatusAll        TicketStatusEnum = "all"         // 全部状态
)

// TicketFromEnum 事件单来源枚举
type TicketFromEnum string

const (
	FromCustomer   TicketFromEnum = "customer"   // 客户报障
	FromCnhancloud TicketFromEnum = "cnhancloud" // 瀚云内部报障
	FromAll        TicketFromEnum = "all"        // 所有来源
)

// CustomerTicketQueryRequest 客户查询请求
type CustomerTicketQueryRequest struct {
	TicketID       string           `form:"ticketId"`       // 事件单ID，多个用逗号隔离
	TicketStatus   TicketStatusEnum `form:"ticketStatus"`   // 事件单状态
	TicketCreateAt string           `form:"ticketCreateAt"` // 事件单创建时间
	TicketFrom     TicketFromEnum   `form:"ticketFrom"`     // 事件单来源
	PageSize       int              `form:"pageSize"`       // 每页展示条数
	Page           int              `form:"page"`           // 页码
	TicketVmIP     string           `form:"ticketVmIp"`     // 虚机IP，多个用逗号隔离
}

// CustomerTicketQueryResponse 客户查询响应
type CustomerTicketQueryResponse struct {
	Total    int64          `json:"total"`     // 总数
	Page     int            `json:"page"`      // 页码
	PageSize int            `json:"page_size"` // 每页条数
	Data     []TicketDetail `json:"data"`      // 数据
}

// TicketDetail 事件单详情
type TicketDetail struct {
	TicketID            string     `json:"ticketId"`                      // 事件单ID
	TicketContent       string     `json:"ticketContent"`                 // 事件单详情
	TicketVmIP          string     `json:"ticketVmIp"`                    // 虚拟机IP
	TicketFrom          string     `json:"ticketFrom"`                    // 事件单来源
	TicketStatus        string     `json:"ticketStatus"`                  // 事件单状态
	TicketCreateAt      time.Time  `json:"ticketCreateAt"`                // 事件单创建时间
	TicketUpdatedAt     time.Time  `json:"ticketUpdatedAt"`               // 事件单最近更新时间
	TicketCategory      string     `json:"ticketCategory"`                // 事件类型
	TicketSubcategory   string     `json:"ticketSubcategory,omitempty"`   // 事件子类型
	TicketRepairComment string     `json:"ticketRepairComment,omitempty"` // 维修备注
	TicketClosedAt      *time.Time `json:"ticketClosedAt,omitempty"`      // 事件单关闭时间
	IsColdmigration     bool       `json:"isColdmigration"`               // 是否冷迁移
	IsAuthorized        bool       `json:"isAuthorized"`                  // 是否已授权
	RequireApproval     bool       `json:"requireApproval"`               // 是否需要客户审批(从主系统直接映射)
}

// CustomerTicketAuthorityRequest 客户授权请求
type CustomerTicketAuthorityRequest struct {
	TicketID   string `json:"ticketId"`   // 事件单ID
	TicketVmIP string `json:"ticketVmIp"` // 虚机IP
}

// APIResponse API响应通用结构
type APIResponse struct {
	Code int         `json:"code"`           // 状态码 0:成功 1:失败
	Msg  string      `json:"msg"`            // 消息
	Data interface{} `json:"data,omitempty"` // 数据
}
