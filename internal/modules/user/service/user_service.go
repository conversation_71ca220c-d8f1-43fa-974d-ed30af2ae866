package service

import (
	"backend/internal/infrastructure/auth"
	"backend/internal/infrastructure/cache"
	systemRepo "backend/internal/modules/system/repository"
	userModel "backend/internal/modules/user/model"
	userRepo "backend/internal/modules/user/repository"
	"context"
	"errors"
	"fmt"
	"regexp"
	"time"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"golang.org/x/crypto/bcrypt"
)

// IUserService 用户服务接口
type IUserService interface {
	// Login 用户登录
	Login(ctx context.Context, req *userModel.LoginRequest) (*userModel.AuthResponse, error)
	// Register 用户注册
	Register(ctx context.Context, req *userModel.RegisterRequest) error
	// Logout 用户登出
	Logout(ctx context.Context, token string) error
	// GetUserInfo 获取用户信息
	GetUserInfo(ctx context.Context, userID uint) (*userModel.UserInfo, error)
	// GetAuthCodes 获取用户权限码
	GetAuthCodes(ctx context.Context, userID uint) ([]string, error)
	// ChangePassword 修改密码
	ChangePassword(ctx context.Context, userID uint, req *userModel.ChangePasswordRequest) error
	// GetUserList 获取用户列表
	GetUserList(ctx context.Context, query *userModel.UserListQuery) (*userModel.UserListResponse, error)
	// UpdateUser 更新用户信息
	UpdateUser(ctx context.Context, req *userModel.UpdateUserRequest) error
	// SoftDeleteUser 软删除用户
	SoftDeleteUser(ctx context.Context, userID uint) error
	// InitSuperAdmin 初始化超级管理员账号
	InitSuperAdmin(ctx context.Context) error
	// CreateUser 管理员创建用户
	CreateUser(ctx context.Context, req *userModel.CreateUserRequest) error
	// CheckUserExists 检查用户是否存在
	CheckUserExists(ctx context.Context, username string) (*userModel.User, error)
	// ResetUserPassword 超级管理员重置用户密码
	ResetUserPassword(ctx context.Context, adminID uint, userID uint, newPassword string) error
	// ClearAllAuthCodesCache 清除所有用户的权限码缓存
	ClearAllAuthCodesCache(ctx context.Context) error
	// CheckRolesPermissionUpdate 检查角色权限是否更新
	CheckRolesPermissionUpdate(ctx context.Context, roleIDs []string) (bool, int64, error)
	// ClearRolePermissionUpdateFlag 清除角色权限更新标志
	ClearRolePermissionUpdateFlag(ctx context.Context, roleIDs []string) error
}

type UserService struct {
	userRepo    *userRepo.UserRepository
	tokenSecret string
	tokenExpire time.Duration
	redisCache  *cache.RedisClient
	logger      *zap.Logger
}

// NewUserService 创建用户服务实例
func NewUserService(repo *userRepo.UserRepository, secret string, expire time.Duration, redisCache *cache.RedisClient, logger *zap.Logger) IUserService {
	return &UserService{
		userRepo:    repo,
		tokenSecret: secret,
		tokenExpire: expire,
		redisCache:  redisCache,
		logger:      logger,
	}
}

// validatePassword 验证密码是否符合要求（必须包含字母和数字）
func validatePassword(password string) error {
	if len(password) < 8 || len(password) > 20 {
		return errors.New("密码长度必须在8-20个字符之间")
	}

	// 检查是否包含字母
	hasLetter, err := regexp.MatchString(`[a-zA-Z]`, password)
	if err != nil {
		return errors.New("验证密码时发生错误: " + err.Error())
	}
	if !hasLetter {
		return errors.New("密码必须包含至少一个字母")
	}

	// 检查是否包含数字
	hasNumber, err := regexp.MatchString(`[0-9]`, password)
	if err != nil {
		return errors.New("验证密码时发生错误: " + err.Error())
	}
	if !hasNumber {
		return errors.New("密码必须包含至少一个数字")
	}

	// 检查是否包含不允许的特殊字符
	for _, char := range password {
		if char == '\t' || char == '\n' || char == '\r' {
			return errors.New("密码不能包含制表符或换行符")
		}
	}

	return nil
}

// 登录业务逻辑
// 登录业务逻辑
func (s *UserService) Login(ctx context.Context, req *userModel.LoginRequest) (*userModel.AuthResponse, error) {
	user, err := s.userRepo.FindByUsername(ctx, req.Username)
	if err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 检查用户状态是否为禁用
	if user.Status == userModel.UserStatusDisabled {
		return nil, errors.New("账户已被禁用，请联系管理员")
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 生成JWT
	token, expiresAt, err := auth.GenerateToken(*user, s.tokenSecret, auth.GetJWTExpireDuration())
	if err != nil {
		return nil, err
	}

	// 登录成功，异步预加载用户权限码到缓存
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()

		// 预加载权限码，记录日志但不中断流程
		if authCodes, err := s.GetAuthCodes(ctx, user.ID); err != nil {
			if s.logger != nil {
				s.logger.Warn("预加载用户权限码失败", zap.Error(err), zap.Uint("userID", user.ID))
			}
		} else if s.logger != nil {
			s.logger.Debug("成功预加载用户权限码",
				zap.Uint("userID", user.ID),
				zap.Int("authCodesCount", len(authCodes)))
		}
	}()

	return &userModel.AuthResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		UserID:    user.ID,
	}, nil
}

// 注册业务逻辑
func (s *UserService) Register(ctx context.Context, req *userModel.RegisterRequest) error {
	// 检查用户名是否已存在
	existingUser, err := s.userRepo.FindByUsername(ctx, req.Username)
	if err == nil && existingUser != nil {
		return errors.New("username already exists")
	}

	// 验证密码规则
	if err := validatePassword(req.Password); err != nil {
		return err
	}

	// 哈希密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return errors.New("failed to hash password")
	}

	// 创建用户并保存到数据库
	newUser := &userModel.User{
		UserName:    req.Username,
		Password:    string(hashedPassword), // 使用哈希后的密码
		Roles:       []string{"admin"},
		AccessCodes: []string{"AC_100001"},
	}

	if err := s.userRepo.CreateUser(ctx, newUser); err != nil {
		return errors.New("failed to create user")
	}

	return nil
}

// 登出业务逻辑
func (s *UserService) Logout(ctx context.Context, token string) error {
	// 无状态JWT无需服务端处理，直接返回成功
	return nil
}

// 获取用户信息业务逻辑，构造返回结构 UserInfo
func (s *UserService) GetUserInfo(ctx context.Context, userID uint) (*userModel.UserInfo, error) {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return nil, errors.New("failed to get user info")
	}

	userInfo := &userModel.UserInfo{
		Roles:    user.Roles,
		RealName: user.RealName,
		RoleName: user.RoleName,
		Email:    user.Email,
		UserName: user.UserName,
	}
	return userInfo, nil
}

// 获取按钮权限码
func (s *UserService) GetAuthCodes(ctx context.Context, userID uint) ([]string, error) {
	// 构建Redis缓存键
	cacheKey := fmt.Sprintf("auth:codes:user:%d", userID)

	// 首先尝试从缓存中获取
	if s.redisCache != nil {
		var authCodes []string
		err := s.redisCache.Get(ctx, cacheKey, &authCodes)
		if err == nil {
			// 缓存命中，直接返回
			return authCodes, nil
		}
		// 缓存未命中或发生错误，继续从数据库查询
	}

	// 获取用户信息及其角色
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return nil, errors.New("获取用户信息失败")
	}

	// 需要引入角色和菜单相关的仓库
	db := s.userRepo.GetDB()
	roleRepo := systemRepo.NewRoleRepository(db)
	menuRepo := systemRepo.NewMenuRepository(db)

	// 检查用户是否拥有super角色
	isSuperAdmin := false
	for _, roleID := range user.Roles {
		if roleID == "super" {
			isSuperAdmin = true
			break
		}
	}

	// 如果是super角色，获取所有按钮类型的权限码
	if isSuperAdmin {
		// 获取所有菜单，不进行状态过滤
		allMenus, err := menuRepo.GetAllMenusWithoutStatusFilter(ctx)
		if err != nil {
			return nil, errors.New("获取菜单失败")
		}

		// 提取所有按钮类型的权限码
		var allAuthCodes []string
		for _, menu := range allMenus {
			if menu.Type == "button" && menu.AuthCode != "" {
				allAuthCodes = append(allAuthCodes, menu.AuthCode)
			}
		}

		// 将结果存入Redis缓存
		if s.redisCache != nil {
			if err := s.redisCache.Set(ctx, cacheKey, allAuthCodes); err != nil {
				s.logger.Warn("缓存权限码失败", zap.Error(err), zap.Uint("userID", userID))
				// 继续执行，不中断流程
			}
		}

		// 获取权限码后，清除角色权限更新标志
		if err := s.ClearRolePermissionUpdateFlag(ctx, user.Roles); err != nil {
			s.logger.Warn("清除角色权限更新标志失败", zap.Error(err), zap.Strings("roleIDs", user.Roles))
			// 继续执行，不中断流程
		}

		return allAuthCodes, nil
	}

	// 获取用户所有角色的ID - 直接使用角色ID而非角色名称
	var roleIDs []string
	for _, roleIDStr := range user.Roles {
		// 用户角色字段中直接存储的是角色ID，不需要再查询
		roleIDs = append(roleIDs, roleIDStr)
	}

	// 如果用户没有角色，返回空结果
	if len(roleIDs) == 0 {
		return []string{}, nil
	}

	// 获取所有角色的权限
	var allPermissions []uint
	for _, roleID := range roleIDs {
		permissions, err := roleRepo.GetRolePermissionIDs(ctx, roleID)
		if err != nil {
			continue // 如果获取失败，跳过
		}
		allPermissions = append(allPermissions, permissions...)
	}

	// 去重
	permissionMap := make(map[uint]bool)
	for _, p := range allPermissions {
		permissionMap[p] = true
	}

	// 获取所有菜单，不进行状态过滤
	allMenus, err := menuRepo.GetAllMenusWithoutStatusFilter(ctx)
	if err != nil {
		return nil, errors.New("获取菜单失败")
	}

	// 筛选出类型为button且在用户权限列表中的菜单项的AuthCode
	var authCodes []string
	for _, menu := range allMenus {
		if menu.Type == "button" && permissionMap[menu.ID] && menu.AuthCode != "" {
			authCodes = append(authCodes, menu.AuthCode)
		}
	}

	// 将结果存入Redis缓存
	if s.redisCache != nil {
		if err := s.redisCache.Set(ctx, cacheKey, authCodes); err != nil {
			s.logger.Warn("缓存权限码失败", zap.Error(err), zap.Uint("userID", userID))
			// 继续执行，不中断流程
		}
	}

	// 获取权限码后，清除角色权限更新标志
	if err := s.ClearRolePermissionUpdateFlag(ctx, roleIDs); err != nil {
		s.logger.Warn("清除角色权限更新标志失败", zap.Error(err), zap.Strings("roleIDs", roleIDs))
		// 继续执行，不中断流程
	}

	return authCodes, nil
}

// 修改密码
func (s *UserService) ChangePassword(ctx context.Context, userID uint, req *userModel.ChangePasswordRequest) error {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.OldPassword)); err != nil {
		return errors.New("旧密码错误！")
	}

	// 验证新密码规则
	if err := validatePassword(req.NewPassword); err != nil {
		return err
	}

	// 哈希新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 更新密码
	return s.userRepo.UpdatePassword(ctx, userID, string(hashedPassword))
}

// 获取用户列表
func (s *UserService) GetUserList(ctx context.Context, query *userModel.UserListQuery) (*userModel.UserListResponse, error) {
	users, total, err := s.userRepo.GetUserList(ctx, query)
	if err != nil {
		return nil, err
	}

	userInfoList := make([]userModel.UserInfo, len(users))
	for i, user := range users {
		userInfoList[i] = userModel.UserInfo{
			ID:         user.ID,
			UserName:   user.UserName,
			Roles:      user.Roles,
			RealName:   user.RealName,
			RoleName:   user.RoleName,
			Email:      user.Email,
			Telephone:  user.Telephone,
			Department: user.Department,
			Status:     user.Status,
			CreatedAt:  user.CreatedAt,
			UpdatedAt:  user.UpdatedAt,
		}
	}

	return &userModel.UserListResponse{
		Total: total,
		List:  userInfoList,
	}, nil
}

// clearAuthCodesCache 清除用户的权限码缓存
func (s *UserService) clearAuthCodesCache(ctx context.Context, userID uint) {
	if s.redisCache == nil {
		return
	}

	cacheKey := fmt.Sprintf("auth:codes:user:%d", userID)
	err := s.redisCache.Del(ctx, cacheKey)
	if err != nil && s.logger != nil {
		s.logger.Warn("清除用户权限缓存失败", zap.Uint("userID", userID), zap.Error(err))
	} else if s.logger != nil {
		s.logger.Info("成功清除用户权限缓存", zap.Uint("userID", userID), zap.String("cacheKey", cacheKey))
	}
}

// 更新用户信息
func (s *UserService) UpdateUser(ctx context.Context, req *userModel.UpdateUserRequest) error {
	user, err := s.userRepo.FindByID(ctx, req.ID)
	if err != nil {
		return err
	}

	// 更新用户信息
	if req.RealName != "" {
		user.RealName = req.RealName
	}
	if req.Telephone != "" {
		user.Telephone = req.Telephone
	}
	if req.Email != "" {
		user.Email = req.Email
	}
	if req.Department != "" {
		user.Department = req.Department
	}
	if len(req.Roles) > 0 {
		user.Roles = req.Roles
		// 角色变更，清除权限码缓存
		s.clearAuthCodesCache(ctx, user.ID)
	}
	if req.RoleName != "" {
		user.RoleName = req.RoleName
	}
	if req.Status != "" {
		user.Status = req.Status
	}

	return s.userRepo.UpdateUser(ctx, user)
}

// 软删除用户
func (s *UserService) SoftDeleteUser(ctx context.Context, userID uint) error {
	// 清除用户权限缓存
	s.clearAuthCodesCache(ctx, userID)

	return s.userRepo.SoftDeleteUser(ctx, userID)
}

// InitSuperAdmin 初始化超级管理员账号
func (s *UserService) InitSuperAdmin(ctx context.Context) error {
	// 获取配置
	username := viper.GetString("superAdmin.username")
	password := viper.GetString("superAdmin.password")
	realname := viper.GetString("superAdmin.realname")
	roles := viper.GetStringSlice("superAdmin.roles")
	accesscodes := viper.GetStringSlice("superAdmin.accessCodes")
	email := viper.GetString("superAdmin.email")

	// 检查是否已存在超级管理员账号
	admin, err := s.userRepo.FindByUsername(ctx, username)
	if err == nil && admin != nil {
		// 超级管理员已存在，无需创建
		return nil
	}

	// 如果是其他错误，则返回错误
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 创建超级管理员账号
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	superAdmin := &userModel.User{
		UserName:    username,
		Password:    string(hashedPassword),
		RealName:    realname,
		Telephone:   "",
		Avatar:      "/assets/avatar/admin.png",
		Desc:        "系统超级管理员",
		RoleName:    "Super Admin",
		Roles:       userModel.StringArray(roles),
		AccessCodes: userModel.StringArray(accesscodes),
		HomePath:    "/dashboard/analysis",
		Email:       email,
		Department:  "系统管理部",
		Status:      "active",
	}

	return s.userRepo.CreateUser(ctx, superAdmin)
}

// 实现CreateUser方法
func (s *UserService) CreateUser(ctx context.Context, req *userModel.CreateUserRequest) error {
	// 检查用户名是否已存在
	existingUser, err := s.userRepo.FindByUsername(ctx, req.Username)
	if err == nil && existingUser != nil {
		return errors.New("用户名已存在")
	}

	// 验证密码规则
	if err := validatePassword(req.Password); err != nil {
		return err
	}

	// 哈希密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return errors.New("密码加密失败")
	}

	// 创建新用户
	newUser := &userModel.User{
		UserName:    req.Username,
		Password:    string(hashedPassword),
		RealName:    req.RealName,
		Email:       req.Email,
		Telephone:   req.Telephone,
		Department:  req.Department,
		RoleName:    req.RoleName,
		Roles:       req.Roles,
		AccessCodes: []string{"AC_100001"}, // 默认权限码
		Status:      req.Status,
	}

	return s.userRepo.CreateUser(ctx, newUser)
}

// CheckUserExists 检查用户是否存在
func (s *UserService) CheckUserExists(ctx context.Context, username string) (*userModel.User, error) {
	user, err := s.userRepo.FindByUsername(ctx, username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}
	// 检查用户是否被软删除
	if !user.DeletedAt.Time.IsZero() {
		return nil, errors.New("用户已被删除")
	}

	return user, nil
}

// ResetUserPassword 超级管理员重置用户密码
func (s *UserService) ResetUserPassword(ctx context.Context, adminID uint, userID uint, newPassword string) error {
	// 验证管理员权限
	admin, err := s.userRepo.FindByID(ctx, adminID)
	if err != nil {
		return errors.New("管理员信息获取失败")
	}

	// 检查是否有超级管理员权限
	hasPermission := false
	for _, role := range admin.Roles {
		if role == "super" {
			hasPermission = true
			break
		}
	}

	if !hasPermission {
		return errors.New("没有重置密码的权限")
	}

	// 检查目标用户是否存在
	_, err = s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return errors.New("用户不存在")
	}

	// 验证新密码规则
	if err := validatePassword(newPassword); err != nil {
		return err
	}

	// 哈希新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return errors.New("密码加密失败")
	}

	// 更新密码
	return s.userRepo.UpdatePassword(ctx, userID, string(hashedPassword))
}

// ClearAllAuthCodesCache 清除所有用户的权限码缓存
func (s *UserService) ClearAllAuthCodesCache(ctx context.Context) error {
	if s.redisCache == nil {
		return nil
	}

	pattern := "auth:codes:user:*"
	// 使用模式匹配删除所有用户权限缓存
	err := s.redisCache.DelByPattern(ctx, pattern)
	if err != nil && s.logger != nil {
		s.logger.Error("清除所有用户权限缓存失败", zap.Error(err))
	}

	return err
}

// CheckRolesPermissionUpdate 检查角色权限是否更新
func (s *UserService) CheckRolesPermissionUpdate(ctx context.Context, roleIDs []string) (bool, int64, error) {
	if s.redisCache == nil {
		// 没有Redis缓存，始终返回需要刷新
		return true, 0, nil
	}

	needRefresh := false
	var latestUpdateTime int64 = 0

	// 检查每个角色是否有权限更新
	for _, roleID := range roleIDs {
		// 检查角色权限更新标志
		updateFlagKey := fmt.Sprintf("role:permission:updated:%s", roleID)
		var updateFlag bool
		err := s.redisCache.Get(ctx, updateFlagKey, &updateFlag)
		if err == nil && updateFlag {
			// 已标记需要更新
			needRefresh = true

			// 获取更新时间戳
			updateTimeKey := fmt.Sprintf("role:permission:update_time:%s", roleID)
			var updateTime int64
			if err := s.redisCache.Get(ctx, updateTimeKey, &updateTime); err == nil {
				if updateTime > latestUpdateTime {
					latestUpdateTime = updateTime
				}
			}

			// 已确认需要刷新，可以提前结束检查
			break
		}
	}

	return needRefresh, latestUpdateTime, nil
}

// ClearRolePermissionUpdateFlag 清除角色权限更新标志
func (s *UserService) ClearRolePermissionUpdateFlag(ctx context.Context, roleIDs []string) error {
	if s.redisCache == nil {
		return nil
	}

	// 真正清除角色权限更新标志位
	for _, roleID := range roleIDs {
		updateFlagKey := fmt.Sprintf("role:permission:updated:%s", roleID)
		// 删除标志位而不是延迟执行
		err := s.redisCache.Del(ctx, updateFlagKey)
		if err != nil && s.logger != nil {
			s.logger.Warn("清除角色权限更新标志失败", zap.String("roleID", roleID), zap.Error(err))
		} else if s.logger != nil {
			s.logger.Info("成功清除角色权限更新标志", zap.String("roleID", roleID))
		}
	}

	return nil
}
