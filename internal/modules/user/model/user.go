package model

import (
	"time"

	"gorm.io/gorm"
)

// 添加用户状态常量
const (
	UserStatusActive   = "active"
	UserStatusDisabled = "disabled"
)

type User struct {
	gorm.Model
	UserName    string      `gorm:"column:username;uniqueIndex;size:50"`
	Password    string      `gorm:"size:100"`
	RealName    string      `gorm:"type:varchar(20);not null"`
	Telephone   string      `gorm:"type:varchar(11);default:''"`
	Avatar      string      `gorm:"type:varchar(255);default:'null'"`
	Desc        string      `gorm:"type:varchar(512);not null;default:'manager'"`
	RoleName    string      `gorm:"type:varchar(50);not null;default:'Super Admin'"` // 角色名称
	Roles       StringArray `gorm:"type:json;not null"`
	AccessCodes StringArray `gorm:"type:json;not null"`
	//Value       string      `gorm:"type:varchar(20);not null;default:'super'"`
	HomePath   string `gorm:"type:varchar(255);default:'/dashboard/analysis'"`
	Address    string `gorm:"type:varchar(255);default:''"`
	Email      string `gorm:"type:varchar(255);default:'<EMAIL>'"`
	Department string `gorm:"type:varchar(255);default:''"`
	Status     string `gorm:"type:varchar(20);not null;default:'active'"` // 用户状态
}

type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Password string `json:"password" binding:"required,min=8,max=20"`
}

type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type AuthResponse struct {
	Token     string `json:"accessToken"`
	ExpiresAt int64  `json:"expires_at"`
	UserID    uint   `json:"user_id"`
}

// 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword" binding:"required"`
	NewPassword string `json:"newPassword" binding:"required,min=8,max=20"`
}

// 更新用户信息请求
type UpdateUserRequest struct {
	ID         uint        `json:"id" binding:"required"`
	RealName   string      `json:"realName"`
	Telephone  string      `json:"telephone"`
	Email      string      `json:"email"`
	Department string      `json:"department"`
	Roles      StringArray `json:"roles"`
	RoleName   string      `json:"roleName"`
	Value      string      `json:"value"`
	Status     string      `json:"status"`
}

// 用户列表查询参数
type UserListQuery struct {
	Page     int    `form:"page" binding:"required,min=1"`
	PageSize int    `form:"pageSize" binding:"required,min=1,max=100"`
	Query    string `form:"query"`
	Status   string `form:"status"`
}

// 用户列表响应
type UserListResponse struct {
	Total int64      `json:"total"`
	List  []UserInfo `json:"list"`
}

// 扩展UserInfo结构体，添加更多字段
type UserInfo struct {
	ID         uint      `json:"id"`
	UserName   string    `json:"username"`
	Roles      []string  `json:"roles"`
	RealName   string    `json:"realName"`
	RoleName   string    `json:"roleName"`
	Email      string    `json:"email"`
	Telephone  string    `json:"telephone"`
	Department string    `json:"department"`
	Status     string    `json:"status"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

// 管理员创建用户请求
type CreateUserRequest struct {
	Username   string      `json:"username" binding:"required,min=3,max=50"`
	Password   string      `json:"password" binding:"required,min=8,max=20"`
	RealName   string      `json:"realName" `
	Email      string      `json:"email" `
	Telephone  string      `json:"telephone"`
	Department string      `json:"department"`
	RoleName   string      `json:"roleName" binding:"required"`
	Roles      StringArray `json:"roles" binding:"required"`
	Status     string      `json:"status" binding:"required"`
}

// ResetPasswordRequest 重置密码请求
// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	UserID      uint   `json:"userId" binding:"required"`
	NewPassword string `json:"newPassword" binding:"required,min=8,max=20"`
}
