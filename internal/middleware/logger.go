package middleware

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// ZapLoggerMiddleware 使用zap记录请求日志
func ZapLoggerMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		start := time.Now()

		// 为请求添加唯一ID，如果没有的话
		requestID, exists := c.Get("request_id")
		if !exists {
			requestID = uuid.New().String()
			c.Set("request_id", requestID)
		}

		// 处理请求
		c.Next()

		// 状态码和处理时间
		statusCode := c.Writer.Status()
		latency := time.Since(start)

		// 忽略一些常见的404请求
		if statusCode == 404 {
			path := c.Request.URL.Path
			// 忽略静态资源、图标等常见404
			ignorePaths := []string{
				"/favicon.ico",
				"/robots.txt",
				"/apple-touch-icon.png",
				"/apple-touch-icon-precomposed.png",
			}

			for _, ignorePath := range ignorePaths {
				if path == ignorePath {
					return
				}
			}
		}

		// 对于OPTIONS请求和成功的快速请求，不记录详细日志
		if statusCode < 400 && latency < 500*time.Millisecond && c.Request.Method != "OPTIONS" {
			return
		}

		// 根据状态码选择日志级别
		if statusCode >= 500 {
			reqIDStr := ""
			if reqIDVal, ok := requestID.(string); ok {
				reqIDStr = reqIDVal
			}

			logger.Error("HTTP错误",
				zap.String("req_id", reqIDStr),
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.String("client", c.ClientIP()),
				zap.Int("status", statusCode),
				zap.Duration("time", latency),
			)
		} else if statusCode >= 400 {
			reqIDStr := ""
			if reqIDVal, ok := requestID.(string); ok {
				reqIDStr = reqIDVal
			}

			logger.Warn("HTTP警告",
				zap.String("req_id", reqIDStr),
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.Int("status", statusCode),
				zap.Duration("time", latency),
			)
		} else if latency > 500*time.Millisecond {
			reqIDStr := ""
			if reqIDVal, ok := requestID.(string); ok {
				reqIDStr = reqIDVal
			}

			// 仅记录慢请求，保持精简
			logger.Info("慢请求",
				zap.String("req_id", reqIDStr),
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.Duration("time", latency),
			)
		}
	}
}
