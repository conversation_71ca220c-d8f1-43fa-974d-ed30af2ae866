package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"gopkg.in/yaml.v3"
)

// APIBasePath API路径公共前缀
const APIBasePath = "/api/v1"

// AuthConfig 权限配置结构体
type AuthConfig struct {
	Resources []ResourceConfig `yaml:"resources" json:"resources"`
}

// ResourceConfig 资源配置结构体
type ResourceConfig struct {
	Path       string            `yaml:"path" json:"path"`
	Operations []OperationConfig `yaml:"operations" json:"operations"`
	Children   []ResourceConfig  `yaml:"children" json:"children"`
	Whitelist  bool              `yaml:"whitelist" json:"whitelist"`
}

// OperationConfig 操作配置结构体
type OperationConfig struct {
	Method          string           `yaml:"method" json:"method"`
	AuthCode        string           `yaml:"auth_code" json:"auth_code"`
	ParamConditions []ParamCondition `yaml:"param_conditions" json:"param_conditions"`
}

// ParamCondition 参数条件配置
type ParamCondition struct {
	Params   map[string]string `yaml:"params" json:"params"`
	AuthCode string            `yaml:"auth_code" json:"auth_code"`
}

var (
	// AuthPathMethodMap 路径和HTTP方法到权限码的映射
	AuthPathMethodMap = map[string]map[string]string{}

	// AuthParamConditionsMap 路径和HTTP方法到参数条件的映射
	AuthParamConditionsMap = map[string]map[string][]ParamCondition{}

	// AuthWhiteList 白名单路径，不需要权限就可以访问
	AuthWhiteList = map[string]bool{}

	// 配置文件锁，用于并发安全
	configMutex sync.RWMutex
)

// LoadAuthConfig 从文件加载权限配置
func LoadAuthConfig(configPath string) error {
	configMutex.Lock()
	defer configMutex.Unlock()

	// 验证文件路径是否合法
	cleanPath := filepath.Clean(configPath)
	if !strings.HasPrefix(cleanPath, filepath.Join(".", "configs")) &&
		!strings.HasPrefix(cleanPath, filepath.Join("/", "etc", "api")) {
		return fmt.Errorf("配置文件路径不合法: %s", configPath)
	}

	// 读取文件
	data, err := os.ReadFile(cleanPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML配置
	var config AuthConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 构建权限映射和白名单
	newAuthMap := make(map[string]map[string]string)
	newParamConditionsMap := make(map[string]map[string][]ParamCondition)
	newWhiteList := make(map[string]bool)

	// 处理资源权限
	for _, res := range config.Resources {
		processResource(res, "", newAuthMap, newParamConditionsMap, newWhiteList)
	}

	// 更新全局变量
	AuthPathMethodMap = newAuthMap
	AuthParamConditionsMap = newParamConditionsMap
	AuthWhiteList = newWhiteList

	//// 调试输出
	//fmt.Println("=== 权限路径映射 ===")
	//for path, methods := range AuthPathMethodMap {
	//	fmt.Printf("%s => %v\n", path, methods)
	//}
	//
	//fmt.Println("\n=== 参数条件映射 ===")
	//for path, methods := range AuthParamConditionsMap {
	//	for method, conditions := range methods {
	//		fmt.Printf("%s %s => %v\n", path, method, conditions)
	//	}
	//}
	//
	//fmt.Println("\n=== 白名单路径 ===")
	//for path := range AuthWhiteList {
	//	fmt.Printf("%s\n", path)
	//}

	return nil
}

// processResource 递归处理资源权限配置
func processResource(res ResourceConfig, parentPath string, authMap map[string]map[string]string,
	paramConditionsMap map[string]map[string][]ParamCondition, whiteList map[string]bool) {
	// 构建完整路径，确保不会有重复的斜杠
	fullPath := joinPath(parentPath, res.Path)

	// 处理当前资源的操作权限
	if len(res.Operations) > 0 {
		if _, exists := authMap[fullPath]; !exists {
			authMap[fullPath] = make(map[string]string)
		}

		// 初始化参数条件映射
		if _, exists := paramConditionsMap[fullPath]; !exists {
			paramConditionsMap[fullPath] = make(map[string][]ParamCondition)
		}

		for _, op := range res.Operations {
			authMap[fullPath][op.Method] = op.AuthCode

			// 处理参数条件
			if len(op.ParamConditions) > 0 {
				paramConditionsMap[fullPath][op.Method] = op.ParamConditions
			}
		}
	}

	// 处理白名单
	if res.Whitelist {
		whiteList[fullPath] = true
	}

	// 递归处理子资源
	for _, child := range res.Children {
		processResource(child, fullPath, authMap, paramConditionsMap, whiteList)
	}
}

// joinPath 连接两个路径，确保不会有重复的斜杠
func joinPath(base, addition string) string {
	if base == "" {
		return addition
	}

	// 确保base不以/结尾，addition以/开头
	base = strings.TrimRight(base, "/")
	if !strings.HasPrefix(addition, "/") {
		addition = "/" + addition
	}

	return base + addition
}

// GetAuthPathMethodMap 获取权限映射
func GetAuthPathMethodMap() map[string]map[string]string {
	configMutex.RLock()
	defer configMutex.RUnlock()
	return AuthPathMethodMap
}

// GetAuthParamConditionsMap 获取参数条件映射
func GetAuthParamConditionsMap() map[string]map[string][]ParamCondition {
	configMutex.RLock()
	defer configMutex.RUnlock()
	return AuthParamConditionsMap
}

// GetParamConditions 获取特定路径和方法的参数条件
func GetParamConditions(path, method string) ([]ParamCondition, bool) {
	configMutex.RLock()
	defer configMutex.RUnlock()

	methodMap, exists := AuthParamConditionsMap[path]
	if !exists {
		// 检查通配符路径
		for configPath, methods := range AuthParamConditionsMap {
			if isWildcardMatch(path, configPath) {
				if conditions, ok := methods[method]; ok {
					return conditions, true
				}
			}
		}
		return nil, false
	}

	conditions, exists := methodMap[method]
	return conditions, exists
}

// GetAuthWhiteList 获取白名单
func GetAuthWhiteList() map[string]bool {
	configMutex.RLock()
	defer configMutex.RUnlock()
	return AuthWhiteList
}

// IsInWhiteList 检查路径是否在白名单中
func IsInWhiteList(path string) bool {
	configMutex.RLock()
	defer configMutex.RUnlock()

	// 检查精确匹配
	if AuthWhiteList[path] {
		return true
	}

	// 检查通配符匹配
	for whitelistPath := range AuthWhiteList {
		if isWildcardMatch(path, whitelistPath) {
			return true
		}
	}

	return false
}

// isWildcardMatch 检查路径是否匹配通配符
func isWildcardMatch(requestPath, configPath string) bool {
	if strings.HasSuffix(configPath, "/**") {
		prefix := strings.TrimSuffix(configPath, "/**")
		return strings.HasPrefix(requestPath, prefix)
	}
	return false
}

// GetAuthCode 获取指定路径和方法的权限码
func GetAuthCode(path, method string) (string, bool) {
	configMutex.RLock()
	defer configMutex.RUnlock()

	// 优先检查精确匹配
	methodMap, exists := AuthPathMethodMap[path]
	if exists {
		authCode, exists := methodMap[method]
		if exists {
			return authCode, true
		}
	}

	// 如果没有精确匹配，检查通配符匹配
	var longestMatchPath string
	var longestMatchLength int

	for configPath, methods := range AuthPathMethodMap {
		if _, methodExists := methods[method]; !methodExists {
			continue
		}

		if strings.HasSuffix(configPath, "/**") {
			prefix := strings.TrimSuffix(configPath, "/**")
			if strings.HasPrefix(path, prefix) && len(prefix) > longestMatchLength {
				longestMatchPath = configPath
				longestMatchLength = len(prefix)
			}
		}
	}

	// 如果找到匹配的通配符路径
	if longestMatchPath != "" {
		return AuthPathMethodMap[longestMatchPath][method], true
	}

	return "", false
}
