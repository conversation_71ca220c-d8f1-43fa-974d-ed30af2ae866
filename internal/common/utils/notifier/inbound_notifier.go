package notifier

import (
	"backend/internal/common/constants"
	"backend/internal/modules/cmdb/model/inbound"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"go.uber.org/zap"
)

// 飞书通知卡片模板
const (
	DismantledInboundTemplete     = "AAqICIWwpcvzS" // 拆机入库模板
	NewPurchaseInboundTemplate    = "AAqICxNtWRqOS" // 新购入库模板
	ReturnRepairedInboundTemplate = "AAqI91UCCTtfO" // 返修入库模板
)

// 模板版本
const (
	DismantledInboundTemplateVersion     = "1.0.2"
	NewPurchaseInboundTemplateVersion    = "1.0.4"
	ReturnRepairedInboundTemplateVersion = "1.0.0"
)

// 飞书机器人配置
type FeishuBotConfig struct {
	Name              string // 可选，方便标识
	WebhookURL        string
	Secret            string
	DetailURLTemplate string
}

// 入库机器人，提高后续拓展性
type InboundNotifier struct {
	Bots   map[string]FeishuBotConfig
	Logger *zap.Logger
}

// 初始化入库通知
func InitInboundNotifier(bots map[string]FeishuBotConfig, logger *zap.Logger) *InboundNotifier {
	return &InboundNotifier{
		Bots:   bots,
		Logger: logger,
	}
}

// 生成签名
func (n *InboundNotifier) generateSign(secret string) (string, string) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	stringToSign := fmt.Sprintf("%s\n%s", timestamp, secret)

	h := hmac.New(sha256.New, []byte(stringToSign))
	signBytes := h.Sum(nil)
	sign := base64.StdEncoding.EncodeToString(signBytes)

	return timestamp, sign
}

func (n *InboundNotifier) SendInboundNotificationV2(ticket *model.InboundTicket, details []model.InboundDetail, history []model.InboundHistory) error {
	var (
		tvb             TemplateVariable
		templateName    string
		templateVersion string
		tables          []table
	)
	url := map[string]string{
		"pc_url":      "",
		"android_url": "",
		"ios_url":     "",
		"url":         "http://cloud17.cnhancloud.com/asset-storage-detail/" + ticket.InboundNo,
	}
	// 选择不同的入库类型
	switch ticket.InboundType {
	case inbound.TypePartInbound:
		for _, detail := range details {
			tableArr := table{
				PN:           detail.Product.PN,
				SN:           detail.ComponentSN,
				MaterialType: detail.Product.MaterialType,
			}
			tables = append(tables, tableArr)
		}
		switch ticket.InboundReason {
		case constants.SourceTypeNewPurchase: // 新购入库相关通知
			tvb = TemplateVariable{
				"create_by":     ticket.CreateBy,
				"create_at":     ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"title":         ticket.InboundTitle,
				"ticket_no":     ticket.InboundNo,
				"type":          translations[ticket.InboundType],
				"reason":        translations[ticket.InboundReason],
				"project":       ticket.Project,
				"warehouse":     ticket.Warehouse.Name,
				"stage":         translations[ticket.Stage],
				"valid":         translations[ticket.Valid],
				"amount":        len(details),
				"may_arrive_at": ticket.MayArriveAt.Format("2006-01-02 15:04:05"),
				"tracking_info": ticket.TrackingInfo,
				"tables":        tables,
				"url":           url,
			}
			switch ticket.Stage {
			case common.StageAssetApproval:
				tvb["card_title"] = "🔔 您有一个新购入库单待填写SN"
			case common.StageVerify:
				tvb["card_title"] = "🔔 您有一个新购入库单待验证"
				tvb["asset_comments"] = history[0].Comment
			case common.StageCompleteInbound:
				tvb["card_title"] = "✅ 您有一个新购入库单已完成"
				switch len(history) {
				case 2:
					tvb["asset_comments"] = history[0].Comment
				case 3:
					tvb["engineer_comments"] = history[0].Comment
					tvb["asset_comments"] = history[1].Comment
				}
			case common.StageRejected:
				tvb["card_title"] = "❌ 新购入库单入库失败"
				switch len(history) {
				case 2:
					tvb["asset_comments"] = history[0].Comment
				case 3:
					tvb["engineer_comments"] = history[0].Comment
					tvb["asset_comments"] = history[1].Comment
				}
				tvb["asset_comments"] = history[0].Comment
			}
			templateName = NewPurchaseInboundTemplate
			templateVersion = NewPurchaseInboundTemplateVersion
		case constants.SourceTypeDismantled: // 拆机入库相关通知
			tvb = TemplateVariable{
				"create_by": ticket.CreateBy,
				"create_at": ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"title":     ticket.InboundTitle,
				"ticket_no": ticket.InboundNo,
				"type":      translations[ticket.InboundType],
				"reason":    translations[ticket.InboundReason],
				"project":   ticket.Project,
				"warehouse": ticket.Warehouse.Name,
				"return":    translations[ticket.NeedReturn],
				"stage":     translations[ticket.Stage],
				"amount":    len(details),
				"tables":    tables,
				"url":       url,
			}
			switch ticket.Stage {
			case common.StageAssetApproval:
				tvb["card_title"] = "🔔 您有一个拆机入库单待填写SN"
			case common.StageCompleteInbound:
				tvb["card_title"] = "✅ 您有一个拆机入库单已完成"
				tvb["asset_comments"] = history[0].Comment
			case common.StageRejected:
				tvb["card_title"] = "❌ 拆机入库单入库失败"
				tvb["asset_comments"] = history[0].Comment
			}
			templateName = DismantledInboundTemplete
			templateVersion = DismantledInboundTemplateVersion
		case constants.SourceTypeReturnRepair: // 返修入库相关通知
			tvb = TemplateVariable{
				"create_by": ticket.CreateBy,
				"create_at": ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"title":     ticket.InboundTitle,
				"ticket_no": ticket.InboundNo,
				"type":      translations[ticket.InboundType],
				"reason":    translations[ticket.InboundReason],
				"project":   ticket.Project,
				"warehouse": ticket.Warehouse.Name,
				"stage":     translations[ticket.Stage],
				"valid":     translations[ticket.Valid],
				"amount":    len(details),
				"tables":    tables,
				"url":       url,
			}
			switch ticket.Stage {
			case common.StageAssetApproval:
				tvb["card_title"] = "🔔 您有一个返修入库单待填写SN"
			case common.StageVerify:
				tvb["card_title"] = "🔔 您有一个返修入库单待验证"
				tvb["asset_comments"] = history[0].Comment
			case common.StageCompleteInbound:
				tvb["card_title"] = "✅ 您有一个返修入库单已完成"
				switch len(history) {
				case 2:
					tvb["asset_comments"] = history[0].Comment
				case 3:
					tvb["engineer_comments"] = history[0].Comment
					tvb["asset_comments"] = history[1].Comment
				}
			case common.StageRejected:
				tvb["card_title"] = "❌ 返修入库单入库失败"
				switch len(history) {
				case 2:
					tvb["asset_comments"] = history[0].Comment
				case 3:
					tvb["engineer_comments"] = history[0].Comment
					tvb["asset_comments"] = history[1].Comment
				}
				tvb["asset_comments"] = history[0].Comment
			}
			templateName = ReturnRepairedInboundTemplate
			templateVersion = ReturnRepairedInboundTemplateVersion
		}
	case inbound.TypeDeviceInbound:
		for _, detail := range details {
			tableArr := table{
				PN:           detail.Product.PN,
				SN:           detail.DeviceSN,
				MaterialType: detail.Product.MaterialType,
			}
			tables = append(tables, tableArr)
		}
		switch ticket.InboundReason {
		case constants.SourceTypeNewPurchase:
			tvb = TemplateVariable{
				"create_by":     ticket.CreateBy,
				"create_at":     ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"title":         ticket.InboundTitle,
				"ticket_no":     ticket.InboundNo,
				"type":          translations[ticket.InboundType],
				"reason":        translations[ticket.InboundReason],
				"project":       ticket.Project,
				"warehouse":     ticket.Warehouse.Name,
				"valid":         translations[ticket.Valid],
				"stage":         translations[ticket.Stage],
				"amount":        len(details),
				"may_arrive_at": ticket.MayArriveAt,
				"tracking_info": ticket.TrackingInfo,
				"tables":        tables,
				"url":           url,
			}
			switch ticket.Stage {
			case common.StageAssetApproval:
				tvb["card_title"] = "🔔 您有一个新购入库单待填写SN"
			case common.StageVerify:
				tvb["card_title"] = "🔔 您有一个新购入库单待验证"
				tvb["asset_comments"] = history[0].Comment
			case common.StageCompleteInbound:
				tvb["card_title"] = "✅ 您有一个新购入库单已完成"
				switch len(history) {
				case 2:
					tvb["asset_comments"] = history[0].Comment
				case 3:
					tvb["engineer_comments"] = history[0].Comment
					tvb["asset_comments"] = history[1].Comment
				}
			case common.StageRejected:
				tvb["card_title"] = "❌ 新购入库单入库失败"
				switch len(history) {
				case 2:
					tvb["asset_comments"] = history[0].Comment
				case 3:
					tvb["engineer_comments"] = history[0].Comment
					tvb["asset_comments"] = history[1].Comment
				}
				tvb["asset_comments"] = history[0].Comment
			}
			templateName = NewPurchaseInboundTemplate
			templateVersion = NewPurchaseInboundTemplateVersion
		}

	}
	botName := "QY_inbound"
	// 检查机器人配置是否存在
	bot, exists := n.Bots[botName]
	if !exists {
		n.Logger.Error("机器人配置不存在", zap.String("bot_name", botName))
		return fmt.Errorf("机器人%v不存在", botName)
	}
	n.sendPartNotifier(templateName, templateVersion, tvb, bot)
	return nil
}

func (n *InboundNotifier) SendInboundMsgToSecurityGuardV2(ticket *model.InboundTicket, details []model.InboundDetail, photoInfo string) error {
	var tables []table
	if ticket.Stage != common.StageCompleteInbound { // 直接跳过
		return nil
	}
	botName := "QY_security"
	// 检查机器人配置是否存在
	bot, exists := n.Bots[botName]
	if !exists {
		n.Logger.Error("机器人配置不存在", zap.String("bot_name", botName))
		return fmt.Errorf("机器人%v不存在", botName)
	}
	switch ticket.InboundType {
	case inbound.TypePartInbound:
		for _, detail := range details {
			tableArr := table{
				PN:           detail.Product.PN,
				SN:           detail.ComponentSN,
				MaterialType: detail.Product.MaterialType,
			}
			tables = append(tables, tableArr)
		}
	case inbound.TypeDeviceInbound:
		for _, detail := range details {
			tableArr := table{
				PN:           detail.Product.PN,
				SN:           detail.DeviceSN,
				MaterialType: detail.Product.MaterialType,
			}
			tables = append(tables, tableArr)
		}
	}
	tvb := TemplateVariable{
		"card_title":      "🔔 您有一个资产入室申请",
		"create_by":       ticket.CreateBy,
		"create_at":       ticket.UpdatedAt.Format("2006-01-02 15:04:05"),
		"type":            translations[ticket.InboundType],
		"reason":          translations[ticket.InboundReason],
		"amount":          len(details),
		"table_raw_array": tables,
		"location":        ticket.Warehouse.Name,
		"photo":           photoInfo,
	}
	n.sendOutboundPartNotifier(SecurityGuardTemplate, SecurityGuardVersion, tvb, bot)
	return nil
}

// SendInboundMsgToSecurityGuard 发送入库信息到保安群
func (n *InboundNotifier) SendInboundMsgToSecurityGuard(inboundInterface inbound.InboundInterface, inboundTicket model.InboundTicketInterface, photoURL string) error {
	var (
		createBy      string
		createAt      string
		componentSNs  []string
		amount        int
		inboundType   string
		inboundReason string
		location      string
		tables        []table
	)
	// 选择不同的入库类型
	switch Inbound := inboundInterface.(type) {
	case *inbound.NewInbound: // 新购入库相关通知
		Ticket, ok := inboundTicket.(*model.NewInboundTicket)
		if !ok {
			return fmt.Errorf("传入的Ticket类型错误")
		}
		if Ticket.Stage != common.StageCompleteInbound { // 直接跳过
			return nil
		}
		inboundType = "part_inbound"
		inboundReason = constants.ChangeReasonNewPurchase
		createBy = Inbound.CreateBy
		createAt = Ticket.UpdatedAt.Format("2006-01-02 15:04:05")
		location = Inbound.NewDetails[0].WarehouseName
		amount = len(Inbound.NewDetails)
		for _, detail := range Inbound.NewDetails {
			componentSNs = append(componentSNs, detail.SN)
		}
		for _, detail := range Inbound.NewDetails {
			tableArr := table{
				PN:           detail.Product.PN,
				SN:           detail.SN,
				MaterialType: detail.Product.MaterialType,
			}
			tables = append(tables, tableArr)
		}
		//SNs = strings.Join(componentSNs, ",")
	case *inbound.DismantledInbound:
		Ticket, ok := inboundTicket.(*model.DismantledPartInboundTicket)
		if !ok {
			return fmt.Errorf("传入的Ticket类型错误")
		}
		if Ticket.Stage != common.StageCompleteInbound { // 直接跳过
			return nil
		}
		inboundType = "part_inbound"
		inboundReason = constants.ChangeReasonDismantled
		createBy = Inbound.CreateBy
		createAt = Ticket.UpdatedAt.Format("2006-01-02 15:04:05")
		amount = len(Inbound.Details)
		for _, detail := range Inbound.Details {
			componentSNs = append(componentSNs, detail.ComponentSN)
		}
		for _, detail := range Inbound.Details {
			tableArr := table{
				PN:           detail.Product.PN,
				SN:           detail.ComponentSN,
				MaterialType: detail.Product.MaterialType,
			}
			tables = append(tables, tableArr)
		}
		//SNs = strings.Join(componentSNs, ",")
	case *inbound.RepairInbound:
		Ticket, ok := inboundTicket.(*model.RepairPartInboundTicket)
		if !ok {
			return fmt.Errorf("传入的Ticket类型错误")
		}
		if Ticket.Stage != common.StageCompleteInbound { // 直接跳过
			return nil
		}
		inboundType = "part_inbound"
		inboundReason = constants.ChangeReasonReturnRepair
		createBy = Inbound.CreateBy
		createAt = Ticket.UpdatedAt.Format("2006-01-02 15:04:05")
		location = Inbound.RepairDetails[0].WarehouseName
		amount = len(Inbound.RepairDetails)
		for _, detail := range Inbound.RepairDetails {
			componentSNs = append(componentSNs, detail.SN)
		}
		//SNs = strings.Join(componentSNs, ",")
		for _, detail := range Inbound.RepairDetails {
			tableArr := table{
				PN:           detail.PN,
				SN:           detail.SN,
				MaterialType: "",
			}
			tables = append(tables, tableArr)
		}
	}
	botName := "QY_security"
	// 检查机器人配置是否存在
	bot, exists := n.Bots[botName]
	if !exists {
		n.Logger.Error("机器人配置不存在", zap.String("bot_name", botName))
		return fmt.Errorf("机器人%v不存在", botName)
	}
	// 过渡
	if bot.WebhookURL == "" {
		n.Logger.Error("保安群飞书机器人webhook为空", zap.Any("bot", bot))
		return nil
	}
	//tvb := TemplateVariable{
	//	"create_by": createBy,
	//	"create_at": createAt,
	//	"type":      translations[inboundType],
	//	"reason":    translations[inboundReason],
	//	"amount":    amount,
	//	"sns":       SNs,
	//	"location":  location,
	//	"photo":     photoURL,
	//}

	tvb := TemplateVariable{
		"card_title":      "🔔 您有一个资产入室申请",
		"create_by":       createBy,
		"create_at":       createAt,
		"type":            translations[inboundType],
		"reason":          translations[inboundReason],
		"amount":          amount,
		"table_raw_array": tables,
		"location":        location,
		"photo":           photoURL,
	}
	n.sendOutboundPartNotifier(SecurityGuardTemplate, SecurityGuardVersion, tvb, bot)
	return nil
}

// sendPartNotifier 发送配件入库信息统一接口
func (n *InboundNotifier) sendPartNotifier(templateID, templateVersionName string, templateVariable map[string]interface{}, bot FeishuBotConfig) {
	// 构建消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"type": "template",
			"data": map[string]interface{}{
				"template_id":           templateID,
				"template_version_name": templateVersionName,
				"template_variable":     templateVariable,
			},
		},
	}
	// 添加签名
	timestamp, sign := n.generateSign(bot.Secret)
	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		n.Logger.Error("飞书通知JSON序列化失败", zap.Error(err))
		fmt.Println("序列化失败：", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(bot.WebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		n.Logger.Error("发送飞书通知失败",
			zap.String("bot", bot.Name),
			zap.Error(err))
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			n.Logger.Error("关闭飞书通知请求体失败",
				zap.String("bot", bot.Name),
				zap.Error(err))
		}
	}(resp.Body)
}
