package notifier

import (
	"backend/internal/common/constants"
	"backend/internal/modules/cmdb/model/outbound"
	"backend/internal/modules/cmdb/model/outbound/common"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"go.uber.org/zap"
)

// 模板
const (
	SellTemplate          = "AAqIq4IGdSugV" // 售卖出库模板
	SecurityGuardTemplate = "AAqdvlEug1O1L" // 保安群模板
	ReplaceTemplate       = "AAqdPYp0tFGsT" // 改配模板
	AllocateTemplate      = "AAqdQ179FXp33" // 调拨模板
	ReturnRepairTemplate  = "AAqdQkZsF4vJI" // 返修模板
	RepairTemplate        = "AAqIOEzE7pVod" // 维修模板
	RackOutboundTemplate  = "AAqI97O4FKfKj" // 上架出库模板
)

// 模板版本
const (
	OutboundSellVersion         = "1.0.1"
	SecurityGuardVersion        = "1.0.4"
	ReplaceTemplateVersion      = "1.0.4"
	AllocateTemplateVersion     = "1.0.2"
	ReturnRepairTemplateVersion = "1.0.2"
	RepairTemplateVersion       = "1.0.1"
	RackOutboundTemplateVersion = "1.0.0"
)

var translations = map[interface{}]string{
	// 入库类型
	"part_inbound":   "配件入库",
	"device_inbound": "设备入库",

	// 出库类型
	"part":   "配件出库",
	"device": "设备出库",

	//出库原因
	"repair":      "维修",
	"sell":        "售卖",
	"replacement": "改配",
	"allocate":    "调拨",

	// 入库原因
	constants.ChangeReasonNewPurchase:  "新购",
	constants.ChangeReasonDismantled:   "拆机",
	constants.ChangeReasonReturnRepair: "返修",

	// 阶段
	"asset_approval":      "资产管理员审批",
	"verify":              "专业工程师审批",
	"engineer_approval":   "专业工程师审批",
	"replace_approval":    "改配负责人确认",
	"asset_dest_approval": "收货方资产管理员审核",
	"complete_outbound":   "出库完成",
	"complete_inbound":    "入库完成",
	"rejected":            "出库失败",

	// 资产类型
	constants.AssetTypeSwitch:       "交换机",
	constants.AssetTypeRouter:       "路由器",
	constants.AssetTypeFirewall:     "防火墙",
	constants.AssetTypeStorage:      "存储设备",
	constants.AssetTypeGPUServer:    "GPU服务器",
	constants.AssetTypeServer:       "服务器",
	constants.AssetTypeLoadbalancer: "负载均衡器",

	// 布尔值
	true:  "是",
	false: "否",
}

type (
	TemplateVariable map[string]interface{}
)

// 采用入库机器人
func (n *InboundNotifier) SendOutboundNotification(ticket *outbound.SpareOutboundTicket, details []outbound.OutboundDetail, history []*outbound.OutboundTicketStatusHistory, amount uint) error {
	var tables []table
	url := map[string]string{
		"pc_url":      "",
		"android_url": "",
		"ios_url":     "",
		"url":         fmt.Sprintf("http://cloud17.cnhancloud.com/asset-out-warehouse-detail/%d", ticket.ID),
	}
	botName := "QY_inbound"
	bot, exists := n.Bots[botName]
	if !exists {
		n.Logger.Error("机器人配置不存在", zap.String("bot_name", botName))
		return fmt.Errorf("机器人%v不存在", botName)
	}
	switch ticket.OutboundType {
	case common.OutboundTypePart: // 配件
		for _, detail := range details {
			tableArr := table{
				PN:           detail.Product.PN,
				SN:           detail.ComponentSN,
				MaterialType: detail.Product.MaterialType,
			}
			tables = append(tables, tableArr)
		}
		switch ticket.OutboundReason {
		case common.OutboundReasonSell:
			tvb := TemplateVariable{
				"create_by":       ticket.ReporterName,
				"outbound_title":  ticket.OutboundTitle,
				"ticket_no":       ticket.TicketNo,
				"stage":           translations[ticket.Stage],
				"outbound_type":   translations[ticket.OutboundType],
				"outbound_reason": translations[ticket.OutboundReason],
				"project":         ticket.Project,
				"amount":          amount,
				"warehouse_name":  ticket.SourceLocation,
				"order_number":    ticket.OrderNumber,
				"buyer_info":      ticket.BuyerInfo,
				"create_at":       ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"tables":          tables,
				"url":             url,
			}
			switch ticket.Stage {
			case common.StageBuyerApproval:
				tvb["card_title"] = "🔔 您有一个售卖出库单待填写SN"
			case common.StageCompleteOutbound:
				tvb["card_title"] = "✅ 您有一个售卖出库单已完成"
				tvb["buyer_comments"] = history[0].Remarks
			case common.StageRejected:
				tvb["card_title"] = "❌ 售卖出库工单出库失败"
				tvb["buyer_comments"] = history[0].Remarks
			}
			n.sendOutboundPartNotifier(SellTemplate, OutboundSellVersion, tvb, bot)
		case common.OutboundReasonReturnRepair:
			tvb := TemplateVariable{
				"create_by":             ticket.ReporterName,
				"create_at":             ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"outbound_title":        ticket.OutboundTitle,
				"ticket_no":             ticket.TicketNo,
				"stage":                 translations[ticket.Stage],
				"outbound_type":         translations[ticket.OutboundType],
				"outbound_reason":       translations[ticket.OutboundReason],
				"project":               ticket.Project,
				"amount":                amount,
				"source_warehouse_name": ticket.SourceLocation,
				"dest_warehouse_name":   ticket.DestLocation,
				"tables":                tables,
				"url":                   url,
			}
			switch ticket.Stage {
			case common.StageAssetApproval:
				tvb["card_title"] = "🔔 您有一个返修出库单待填写SN"
			case common.StageCompleteOutbound:
				tvb["card_title"] = "✅ 您有一个返修出库单已完成"
				tvb["asset_comments"] = history[0].Remarks
			}
			n.sendOutboundPartNotifier(ReturnRepairTemplate, ReturnRepairTemplateVersion, tvb, bot)
		case common.OutboundReasonAllocate:
			tvb := TemplateVariable{
				"create_by":             ticket.ReporterName,
				"create_at":             ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"outbound_title":        ticket.OutboundTitle,
				"ticket_no":             ticket.TicketNo,
				"stage":                 translations[ticket.Stage],
				"outbound_type":         translations[ticket.OutboundType],
				"outbound_reason":       translations[ticket.OutboundReason],
				"project":               ticket.Project,
				"amount":                amount,
				"source_warehouse_name": ticket.SourceLocation,
				"dest_warehouse_name":   ticket.DestLocation,
				"tables":                tables,
				"url":                   url,
			}
			switch ticket.Stage {
			case common.StageAssetApproval:
				tvb["card_title"] = "🔔 你有一个调拨工单待补充SN"
			case common.StageAssetDestApproval:
				tvb["card_title"] = "🔔 你有一个调拨工单待审核"
				tvb["asset_comments"] = history[0].Remarks
			case common.StageRejected:
				tvb["card_title"] = "❌ 调拨工单出库失败"
				historyLen := len(history)
				switch historyLen {
				case 2:
					tvb["asset_comments"] = history[0].Remarks
				default:
					tvb["dest_asset_comments"] = history[0].Remarks
					tvb["asset_comments"] = history[1].Remarks
				}
			case common.StageCompleteOutbound:
				tvb["card_title"] = "✅ 调拨工单出库成功"
				tvb["dest_asset_comments"] = history[0].Remarks
				tvb["asset_comments"] = history[1].Remarks
			}
			n.sendOutboundPartNotifier(AllocateTemplate, AllocateTemplateVersion, tvb, bot)
		case common.OutboundReasonReplacement: // 改配
			tvb := TemplateVariable{
				"create_by":       ticket.ReporterName,
				"create_at":       ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"outbound_title":  ticket.OutboundTitle,
				"ticket_no":       ticket.TicketNo,
				"stage":           translations[ticket.Stage],
				"outbound_type":   translations[ticket.OutboundType],
				"outbound_reason": translations[ticket.OutboundReason],
				"project":         ticket.Project,
				"amount":          amount,
				"warehouse_name":  ticket.DestLocation,
				"tables":          tables,
				"url":             url,
			}
			switch ticket.Stage {
			case common.StageEngineerApproval: // 专业工程师审核
				tvb["card_title"] = "🔔 你有一个改配工单待审核"
				//tvb["engineer_comments"] = history[1].Remarks
			case common.StageAssetApproval: // 资产管理员审核
				tvb["card_title"] = "🔔 你有一个改配工单待补充配件SN"
				tvb["engineer_comments"] = history[0].Remarks
			case common.StageReplaceApproval: // 改配负责人审核
				tvb["card_title"] = "🔔 你有一个改配工单待补充设备SN"
				tvb["asset_comments"] = history[0].Remarks
				tvb["engineer_comments"] = history[1].Remarks

			case common.StageRejected: // 拒绝
				tvb["card_title"] = "❌ 改配工单出库失败"
				historyLen := len(history)
				switch historyLen {
				case 2:
					tvb["engineer_comments"] = history[0].Remarks
				case 3:
					tvb["asset_comments"] = history[0].Remarks
					tvb["engineer_comments"] = history[1].Remarks

				default:
					tvb["replace_comments"] = history[0].Remarks
					tvb["asset_comments"] = history[1].Remarks
					tvb["engineer_comments"] = history[2].Remarks
				}
			case common.StageCompleteOutbound: // 完成出库
				tvb["card_title"] = "✅ 改配工单出库成功"
				tvb["engineer_comments"] = history[2].Remarks
				tvb["asset_comments"] = history[1].Remarks
				tvb["replace_comments"] = history[0].Remarks
			}
			n.sendOutboundPartNotifier(ReplaceTemplate, ReplaceTemplateVersion, tvb, bot)
		case common.OutboundReasonRepair: // 维修
			tvb := TemplateVariable{
				"create_by":       ticket.ReporterName,
				"create_at":       ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"outbound_title":  ticket.OutboundTitle,
				"ticket_no":       ticket.TicketNo,
				"stage":           translations[ticket.Stage],
				"outbound_type":   translations[ticket.OutboundType],
				"outbound_reason": translations[ticket.OutboundReason],
				"project":         ticket.Project,
				"amount":          amount,
				"warehouse_name":  ticket.SourceLocation,
				"tables":          tables,
				"url":             url,
			}
			switch ticket.Stage {
			case common.StageEngineerApproval: // 专业工程师审核
				tvb["card_title"] = "🔔 你有一个维修出库工单待审核"
				//tvb["engineer_comments"] = history[1].Remarks
			case common.StageAssetApproval: // 资产管理员审核
				tvb["card_title"] = "🔔 你有一个维修出库工单待补充配件SN"
				tvb["engineer_comments"] = history[0].Remarks

			case common.StageRejected: // 拒绝
				tvb["card_title"] = "❌ 维修出库工单失败"
				historyLen := len(history)
				switch historyLen {
				case 2:
					tvb["engineer_comments"] = history[0].Remarks
				case 3:
					tvb["asset_comments"] = history[0].Remarks
					tvb["engineer_comments"] = history[1].Remarks
				}
			case common.StageCompleteOutbound: // 完成出库
				tvb["card_title"] = "✅ 维修出库工单成功"
				tvb["engineer_comments"] = history[1].Remarks
				tvb["asset_comments"] = history[0].Remarks
			}
			n.sendOutboundPartNotifier(RepairTemplate, RepairTemplateVersion, tvb, bot)
		}
	case common.OutboundTypeDevice: // 设备出库
		for _, detail := range details {
			tableArr := table{
				PN:           detail.Product.PN,
				SN:           detail.DeviceSN,
				MaterialType: detail.Product.MaterialType,
			}
			tables = append(tables, tableArr)
		}
		switch ticket.OutboundReason {
		case common.OutboundReasonRack:
			tvb := TemplateVariable{
				"create_by":       ticket.ReporterName,
				"create_at":       ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"outbound_title":  ticket.OutboundTitle,
				"ticket_no":       ticket.TicketNo,
				"stage":           translations[ticket.Stage],
				"outbound_type":   translations[ticket.OutboundType],
				"outbound_reason": translations[ticket.OutboundReason],
				"project":         ticket.Project,
				"amount":          amount,
				"warehouse_name":  ticket.SourceLocation,
				"tables":          tables,
				"url":             url,
			}
			switch ticket.Stage {
			case common.StageAssetApproval: // 资产管理员审核
				tvb["card_title"] = "🔔 你有一个上架出库工单待补充配件SN"
			case common.StageRejected: // 拒绝
				tvb["card_title"] = "❌ 上架出库工单失败"
				historyLen := len(history)
				switch historyLen {
				case 2:
					tvb["asset_comments"] = history[0].Remarks
				}
			case common.StageCompleteOutbound: // 完成出库
				tvb["card_title"] = "✅ 上架出库工单成功"
				tvb["asset_comments"] = history[0].Remarks
			}
			n.sendOutboundPartNotifier(RackOutboundTemplate, RackOutboundTemplateVersion, tvb, bot)
		case common.OutboundReasonAllocate:
			tvb := TemplateVariable{
				"create_by":             ticket.ReporterName,
				"create_at":             ticket.CreatedAt.Format("2006-01-02 15:04:05"),
				"outbound_title":        ticket.OutboundTitle,
				"ticket_no":             ticket.TicketNo,
				"stage":                 translations[ticket.Stage],
				"outbound_type":         translations[ticket.OutboundType],
				"outbound_reason":       translations[ticket.OutboundReason],
				"project":               ticket.Project,
				"amount":                amount,
				"source_warehouse_name": ticket.SourceLocation,
				"dest_warehouse_name":   ticket.DestLocation,
				"tables":                tables,
				"url":                   url,
			}
			switch ticket.Stage {
			case common.StageAssetApproval:
				tvb["card_title"] = "🔔 你有一个调拨工单待补充SN"
			case common.StageAssetDestApproval:
				tvb["card_title"] = "🔔 你有一个调拨工单待审核"
				tvb["asset_comments"] = history[0].Remarks
			case common.StageRejected:
				tvb["card_title"] = "❌ 调拨工单出库失败"
				historyLen := len(history)
				switch historyLen {
				case 2:
					tvb["asset_comments"] = history[0].Remarks
				default:
					tvb["dest_asset_comments"] = history[0].Remarks
					tvb["asset_comments"] = history[1].Remarks
				}
			case common.StageCompleteOutbound:
				tvb["card_title"] = "✅ 调拨工单出库成功"
				tvb["dest_asset_comments"] = history[0].Remarks
				tvb["asset_comments"] = history[1].Remarks
			}
			n.sendOutboundPartNotifier(AllocateTemplate, AllocateTemplateVersion, tvb, bot)
		}
	default:
		return fmt.Errorf("不支持的出库类型 %s", ticket.OutboundType)
	}

	switch ticket.Status {
	case common.StatusWaitingApproval: // 等待专业工程师审核
	case common.StatusOutbounding: // 等待资产管理员审核
	case common.StatusCompleted: // 出库完成
	case common.StatusRejected: // 拒绝出库
	}

	return nil
}

// 定义结构体
type table struct {
	PN           string `json:"pn"`
	SN           string `json:"sn"`
	MaterialType string `json:"material_type"`
}

// SendOutboundMsgToSecurityGuard 发送出库详情到保安群
func (n *InboundNotifier) SendOutboundMsgToSecurityGuard(ticket *outbound.SpareOutboundTicket, details []outbound.OutboundDetail, photoInfo string) error {
	var (
		tables        []table
		warehouseName string
	)
	if ticket.Stage != common.StageCompleteOutbound { // 直接跳过
		return nil
	}
	botName := "QY_security"
	// 检查机器人配置是否存在
	bot, exists := n.Bots[botName]
	if !exists {
		n.Logger.Error("机器人配置不存在", zap.String("bot_name", botName))
		return fmt.Errorf("机器人%v不存在", botName)
	}

	switch ticket.OutboundType {
	case common.OutboundTypePart:
		for _, detail := range details {
			tableArr := table{
				PN:           detail.Product.PN,
				SN:           detail.ComponentSN,
				MaterialType: detail.Product.MaterialType,
			}
			tables = append(tables, tableArr)
		}
		switch ticket.OutboundReason {
		//case common.OutboundReasonReplacement:
		//	warehouseName = ticket.DestLocation
		case common.OutboundReasonReturnRepair:
			warehouseName = ticket.SourceLocation
		case common.OutboundReasonAllocate:
			warehouseName = ticket.SourceLocation
		//case common.OutboundReasonRepair:
		//	warehouseName = ticket.SourceLocation
		case common.OutboundReasonSell:
			warehouseName = ticket.SourceLocation
		}
	case common.OutboundTypeDevice:
		for _, detail := range details {
			tableArr := table{
				PN:           "",
				SN:           detail.DeviceSN,
				MaterialType: translations[detail.Template.TemplateCategory],
			}
			tables = append(tables, tableArr)
		}
		switch ticket.OutboundReason {
		case common.OutboundReasonAllocate:
			warehouseName = ticket.SourceLocation
		}
	default:
		return fmt.Errorf("暂不支持的出库类型%s", ticket.OutboundType)
	}

	tvb := TemplateVariable{
		"card_title":      "🔔 您有一个资产出室申请",
		"create_by":       ticket.ReporterName,
		"create_at":       ticket.UpdatedAt.Format("2006-01-02 15:04:05"),
		"type":            translations[ticket.OutboundType],
		"reason":          translations[ticket.OutboundReason],
		"amount":          len(details),
		"table_raw_array": tables,
		"location":        warehouseName,
		"photo":           photoInfo,
	}
	n.sendOutboundPartNotifier(SecurityGuardTemplate, SecurityGuardVersion, tvb, bot)
	return nil
}

// sendPartNotifier 发送配件入库信息统一接口
func (n *InboundNotifier) sendOutboundPartNotifier(templateID, templateVersionName string, templateVariable map[string]interface{}, bot FeishuBotConfig) {
	// 构建消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"type": "template",
			"data": map[string]interface{}{
				"template_id":           templateID,
				"template_version_name": templateVersionName,
				"template_variable":     templateVariable,
			},
		},
	}
	// 添加签名
	timestamp, sign := n.generateSign(bot.Secret)
	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		n.Logger.Error("飞书通知JSON序列化失败", zap.Error(err))
		fmt.Println("序列化失败：", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(bot.WebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		n.Logger.Error("发送飞书通知失败",
			zap.String("bot", bot.Name),
			zap.Error(err))
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			n.Logger.Error("关闭飞书通知请求体失败",
				zap.String("bot", bot.Name),
				zap.Error(err))
		}
	}(resp.Body)
}
