package notifier

import (
	"backend/configs"
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"time"

	"go.uber.org/zap"
)

// FeishuNotifier 飞书机器人通知工具
type FeishuNotifier struct {
	WebhookURL        string
	Secret            string
	DetailUrlTemplate string
	Logger            *zap.Logger
	// 维修单相关配置
	RepairDetailUrlTemplate string
	// 维修单项目专用webhook映射（仅用于维修单待接单通知）
	RepairProjectWebhooks configs.ProjectWebhookMappings
}

// NewFeishuNotifier 创建飞书通知器实例
func NewFeishuNotifier(webhookURL, secret, detailUrlTemplate string, repairDetailUrlTemplate string, repairProjectWebhooks configs.ProjectWebhookMappings, logger *zap.Logger) *FeishuNotifier {
	// 记录原始输入参数
	logger.Debug("创建飞书通知器 - 输入参数",
		zap.String("webhookURL", webhookURL),
		zap.String("detailUrlTemplate", detailUrlTemplate),
		zap.String("repairDetailUrlTemplate", repairDetailUrlTemplate))

	// 如果URL模板为空，使用默认值
	defaultDetailUrl := "http://localhost:5666/fault-report-management/detail/%s"
	// 获取当前环境
	appEnv := os.Getenv("APP_ENV")

	// 如果是生产环境，使用生产环境默认URL
	if appEnv == "production" || appEnv == "prod" {
		defaultDetailUrl = "http://cloud17.cnhancloud.com/fault-report-management/detail/%s"
		logger.Info("检测到生产环境，使用生产环境默认URL",
			zap.String("APP_ENV", appEnv),
			zap.String("defaultDetailUrl", defaultDetailUrl))
	}

	if detailUrlTemplate == "" {
		logger.Info("工单详情URL模板为空，使用默认值",
			zap.String("default", defaultDetailUrl))
		detailUrlTemplate = defaultDetailUrl
	}

	// 如果维修单URL模板为空，使用通用模板
	if repairDetailUrlTemplate == "" {
		logger.Info("维修单URL模板为空，使用通用模板",
			zap.String("fallback", detailUrlTemplate))
		repairDetailUrlTemplate = detailUrlTemplate
	}

	return &FeishuNotifier{
		WebhookURL:              webhookURL,
		Secret:                  secret,
		DetailUrlTemplate:       detailUrlTemplate,
		Logger:                  logger,
		RepairDetailUrlTemplate: repairDetailUrlTemplate,
		RepairProjectWebhooks:   repairProjectWebhooks,
	}
}

// 生成签名
func (f *FeishuNotifier) generateSign() (string, string) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	stringToSign := fmt.Sprintf("%s\n%s", timestamp, f.Secret)

	h := hmac.New(sha256.New, []byte(stringToSign))
	signBytes := h.Sum(nil)
	sign := base64.StdEncoding.EncodeToString(signBytes)

	return timestamp, sign
}

// GetRepairProjectWebhookConfig 根据项目获取维修单专用webhook配置
func (f *FeishuNotifier) GetRepairProjectWebhookConfig(project string) (webhookURL, secret string) {
	f.Logger.Info("查找维修单项目webhook配置",
		zap.String("项目", project),
		zap.Int("可用配置数量", len(f.RepairProjectWebhooks)))

	// 打印所有可用的项目配置
	for key := range f.RepairProjectWebhooks {
		f.Logger.Info("可用维修单项目配置", zap.String("项目", key))
	}

	// 查找项目专用配置
	if project != "" {
		if config, exists := f.RepairProjectWebhooks[project]; exists {
			f.Logger.Info("找到维修单项目配置",
				zap.String("项目", project),
				zap.String("webhookURL", config.WebhookURL),
				zap.Bool("有secret", config.Secret != ""))
			if config.WebhookURL != "" && config.Secret != "" {
				return config.WebhookURL, config.Secret
			}
		} else {
			f.Logger.Warn("未找到维修单项目配置", zap.String("项目", project))
		}
	}

	// 如果没有找到项目专用配置，尝试使用default配置
	if defaultConfig, exists := f.RepairProjectWebhooks["default"]; exists {
		f.Logger.Info("使用default项目配置",
			zap.String("webhookURL", defaultConfig.WebhookURL),
			zap.Bool("有secret", defaultConfig.Secret != ""))
		if defaultConfig.WebhookURL != "" && defaultConfig.Secret != "" {
			return defaultConfig.WebhookURL, defaultConfig.Secret
		}
	}

	// 如果连default配置都没有，返回空值
	f.Logger.Error("未找到任何有效的维修单webhook配置", zap.String("项目", project))
	return "", ""
}

// SendTicketCreatedNotification 发送工单创建通知
func (f *FeishuNotifier) SendTicketCreatedNotification(ticketID, ticketNo, vmIP, content string) error {
	// 构建消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"config": map[string]interface{}{
				"wide_screen_mode": true,
			},
			"header": map[string]interface{}{
				"template": "yellow",
				"title": map[string]interface{}{
					"content": "⚠️ 新工单通知",
					"tag":     "plain_text",
				},
			},
			"elements": []map[string]interface{}{
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "<at id=all></at>",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "**⚠️ 新的客户报障工单已创建**",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": content,
						"tag":     "lark_md",
					},
				},
				{
					"tag": "hr",
				},
				{
					"tag": "div",
					"fields": []map[string]interface{}{
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**📝 工单编号：**\n" + ticketNo,
								"tag":     "lark_md",
							},
						},
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**💻 虚拟机IP：**\n" + vmIP,
								"tag":     "lark_md",
							},
						},
					},
				},

				{
					"tag": "hr",
				},

				{
					"tag": "hr",
				},
				{
					"tag": "action",
					"actions": []map[string]interface{}{
						{
							"tag": "button",
							"text": map[string]interface{}{
								"content": "🔍 查看详情",
								"tag":     "plain_text",
							},
							"type": "primary",
							"url":  fmt.Sprintf(f.DetailUrlTemplate, ticketID),
						},
					},
				},
			},
		},
	}

	// 添加签名
	timestamp, sign := f.generateSign()
	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		f.Logger.Error("飞书通知JSON序列化失败", zap.Error(err))
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(f.WebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		f.Logger.Error("发送飞书通知失败", zap.Error(err))
		return fmt.Errorf("发送通知失败: %w", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			f.Logger.Error("发送飞书通知失败", zap.Error(err))
		}
	}(resp.Body)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		f.Logger.Error("飞书通知API返回非200状态码",
			zap.Int("statusCode", resp.StatusCode))
		return fmt.Errorf("飞书API返回状态码: %d", resp.StatusCode)
	}

	f.Logger.Info("飞书通知发送成功")
	return nil
}

// SendTicketAuthorizedNotification 发送工单授权通知
func (f *FeishuNotifier) SendTicketAuthorizedNotification(ticketID, ticketNo, vmIP, _, _ string) error {
	// 构建消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"config": map[string]interface{}{
				"wide_screen_mode": true,
			},
			"header": map[string]interface{}{
				"template": "green",
				"title": map[string]interface{}{
					"content": "✅ 工单授权通知",
					"tag":     "plain_text",
				},
			},
			"elements": []map[string]interface{}{
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "<at id=all></at>",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "**✅ 客户已对工单进行授权**",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "工单可以进入处理流程",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "hr",
				},
				{
					"tag": "div",
					"fields": []map[string]interface{}{
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**📝 工单编号：**\n" + ticketNo,
								"tag":     "lark_md",
							},
						},
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**💻 虚拟机IP：**\n" + vmIP,
								"tag":     "lark_md",
							},
						},
					},
				},

				{
					"tag": "hr",
				},
				{
					"tag": "action",
					"actions": []map[string]interface{}{
						{
							"tag": "button",
							"text": map[string]interface{}{
								"content": "🔍 查看详情",
								"tag":     "plain_text",
							},
							"type": "primary",
							"url":  fmt.Sprintf(f.DetailUrlTemplate, ticketID),
						},
					},
				},
			},
		},
	}

	// 添加签名
	timestamp, sign := f.generateSign()
	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		f.Logger.Error("飞书通知JSON序列化失败", zap.Error(err))
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(f.WebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		f.Logger.Error("发送飞书通知失败", zap.Error(err))
		return fmt.Errorf("发送通知失败: %w", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			f.Logger.Error("发送飞书通知失败", zap.Error(err))
		}
	}(resp.Body)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		f.Logger.Error("飞书通知API返回非200状态码",
			zap.Int("statusCode", resp.StatusCode))
		return fmt.Errorf("飞书API返回状态码: %d", resp.StatusCode)
	}

	f.Logger.Info("飞书通知发送成功")
	return nil
}

// SendRepairTicketWaitingAcceptNotification 发送维修单待接单通知（使用default项目配置）
func (f *FeishuNotifier) SendRepairTicketWaitingAcceptNotification(repairTicketID, repairTicketNo, deviceSN, faultTicketNo string, reporterName string, cabinetName, roomName, dataCenterName string) error {
	// 获取default项目的webhook配置
	webhookURL, secret := f.GetRepairProjectWebhookConfig("default")
	if webhookURL == "" || secret == "" {
		return fmt.Errorf("未找到default项目的webhook配置")
	}

	// 使用自定义webhook方法发送通知
	return f.SendRepairTicketWaitingAcceptNotificationWithWebhook(
		repairTicketID, repairTicketNo, deviceSN, faultTicketNo,
		reporterName, cabinetName, roomName, dataCenterName,
		webhookURL, secret,
	)
}

// SendRepairTicketWaitingAcceptNotificationWithWebhook 发送维修单待接单通知（支持自定义webhook）
func (f *FeishuNotifier) SendRepairTicketWaitingAcceptNotificationWithWebhook(repairTicketID, repairTicketNo, deviceSN, faultTicketNo string, reporterName string, cabinetName, roomName, dataCenterName string, webhookURL, secret string) error {
	// 构建消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"config": map[string]interface{}{
				"wide_screen_mode": true,
			},
			"header": map[string]interface{}{
				"template": "blue",
				"title": map[string]interface{}{
					"content": "🔔 维修单待接单通知",
					"tag":     "plain_text",
				},
			},
			"elements": []map[string]interface{}{
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "**🔔 新维修单已授权，等待工程师接单**",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "请尽快安排工程师接单处理",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "hr",
				},
				{
					"tag": "div",
					"fields": []map[string]interface{}{
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**📝 维修单编号：**\n" + repairTicketNo,
								"tag":     "lark_md",
							},
						},
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**💻 设备SN：**\n" + deviceSN,
								"tag":     "lark_md",
							},
						},
					},
				},
				{
					"tag": "div",
					"fields": []map[string]interface{}{
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**📋 关联报障单：**\n" + faultTicketNo,
								"tag":     "lark_md",
							},
						},
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**👤 报修人：**\n" + reporterName,
								"tag":     "lark_md",
							},
						},
					},
				},
			},
		},
	}

	// 添加设备位置信息（如果有提供）
	cardMap, ok := msg["card"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("无法获取card字段")
	}
	elementsInterface, ok := cardMap["elements"]
	if !ok {
		return fmt.Errorf("无法获取elements字段")
	}
	elements, ok := elementsInterface.([]map[string]interface{})
	if !ok {
		return fmt.Errorf("elements字段格式错误")
	}

	// 只有当提供了位置信息时才添加
	if cabinetName != "" || roomName != "" {
		locationInfo := map[string]interface{}{
			"tag":    "div",
			"fields": []map[string]interface{}{},
		}

		fields := []map[string]interface{}{
			{
				"is_short": true,
				"text": map[string]interface{}{
					"content": "**🔧 机柜：**\n" + cabinetName,
					"tag":     "lark_md",
				},
			},
			{
				"is_short": true,
				"text": map[string]interface{}{
					"content": "**🚪 包间：**\n" + roomName,
					"tag":     "lark_md",
				},
			},
		}

		locationInfo["fields"] = fields
		elements = append(elements, locationInfo)
	}

	// 添加分隔线和按钮
	elements = append(elements,
		map[string]interface{}{
			"tag": "hr",
		},
		map[string]interface{}{
			"tag": "action",
			"actions": []map[string]interface{}{
				{
					"tag": "button",
					"text": map[string]interface{}{
						"content": "🔍 查看详情",
						"tag":     "plain_text",
					},
					"type": "primary",
					"url":  fmt.Sprintf(f.RepairDetailUrlTemplate, repairTicketID),
				},
			},
		},
	)

	// 更新elements
	cardMap["elements"] = elements

	// 生成自定义签名
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	stringToSign := fmt.Sprintf("%s\n%s", timestamp, secret)
	h := hmac.New(sha256.New, []byte(stringToSign))
	signBytes := h.Sum(nil)
	sign := base64.StdEncoding.EncodeToString(signBytes)

	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		f.Logger.Error("飞书通知JSON序列化失败", zap.Error(err))
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 发送HTTP请求到指定的webhook
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData)) // #nosec G107
	if err != nil {
		f.Logger.Error("发送飞书通知失败", zap.Error(err))
		return fmt.Errorf("发送通知失败: %w", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			f.Logger.Error("<UNK>", zap.Error(err))
		}
	}(resp.Body)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		f.Logger.Error("飞书通知API返回非200状态码",
			zap.Int("statusCode", resp.StatusCode))
		return fmt.Errorf("飞书API返回状态码: %d", resp.StatusCode)
	}

	f.Logger.Info("维修单待接单通知发送成功")
	return nil
}

// SendRepairTicketWaitingVerificationNotification 发送维修单待验证通知
func (f *FeishuNotifier) SendRepairTicketWaitingVerificationNotification(faultTicketID, repairTicketNo, deviceSN, faultTicketNo string, acceptorID string, repairSolution, repairSteps string) error {
	// 构建消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"config": map[string]interface{}{
				"wide_screen_mode": true,
			},
			"header": map[string]interface{}{
				"template": "orange",
				"title": map[string]interface{}{
					"content": "🔍 维修单待验证通知",
					"tag":     "plain_text",
				},
			},
			"elements": []map[string]interface{}{
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": func() string {
							if acceptorID != "" {
								return fmt.Sprintf("<at id=%s></at>", acceptorID)
							}
							return ""
						}(),
						"tag": "lark_md",
					},
				},
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "**🔍 维修工作已完成，等待验证结果**",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "请确认问题是否已解决",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "hr",
				},
				{
					"tag": "div",
					"fields": []map[string]interface{}{
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**📝 维修单编号：**\n" + repairTicketNo,
								"tag":     "lark_md",
							},
						},
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**💻 设备SN：**\n" + deviceSN,
								"tag":     "lark_md",
							},
						},
					},
				},
				{
					"tag": "div",
					"fields": []map[string]interface{}{
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**📋 关联报障单：**\n" + faultTicketNo,
								"tag":     "lark_md",
							},
						},
					},
				},
			},
		},
	}

	// 添加维修解决方案和维修步骤（如果有提供）
	cardMap, ok := msg["card"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("无法获取card字段")
	}
	elementsInterface, ok := cardMap["elements"]
	if !ok {
		return fmt.Errorf("无法获取elements字段")
	}
	elements, ok := elementsInterface.([]map[string]interface{})
	if !ok {
		return fmt.Errorf("elements字段格式错误")
	}

	if repairSolution != "" {
		elements = append(elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"content": "**🔧 维修解决方案：**\n" + repairSolution,
				"tag":     "lark_md",
			},
		})
	}

	if repairSteps != "" {
		elements = append(elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"content": "**📋 维修步骤：**\n" + repairSteps,
				"tag":     "lark_md",
			},
		})
	}

	// 添加分隔线和按钮
	elements = append(elements,
		map[string]interface{}{
			"tag": "hr",
		},
		map[string]interface{}{
			"tag": "action",
			"actions": []map[string]interface{}{
				{
					"tag": "button",
					"text": map[string]interface{}{
						"content": "🔍 查看详情",
						"tag":     "plain_text",
					},
					"type": "primary",
					"url":  fmt.Sprintf(f.DetailUrlTemplate, faultTicketID),
				},
			},
		},
	)

	// 更新elements
	cardMap["elements"] = elements

	// 添加签名
	timestamp, sign := f.generateSign()
	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		f.Logger.Error("飞书通知JSON序列化失败", zap.Error(err))
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(f.WebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		f.Logger.Error("发送飞书通知失败", zap.Error(err))
		return fmt.Errorf("发送通知失败: %w", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			f.Logger.Error("<UNK>", zap.Error(err))
		}
	}(resp.Body)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		f.Logger.Error("飞书通知API返回非200状态码",
			zap.Int("statusCode", resp.StatusCode))
		return fmt.Errorf("飞书API返回状态码: %d", resp.StatusCode)
	}

	f.Logger.Info("维修单待验证通知发送成功")
	return nil
}

// SendVerificationFailedNotification 发送验证不通过通知
func (f *FeishuNotifier) SendVerificationFailedNotification(faultTicketID, repairTicketID, repairTicketNo, deviceSN, faultTicketNo string, engineerName string, verificationComments string, cabinetName, roomName, dataCenterName string) error {
	// 获取default项目的webhook配置
	webhookURL, secret := f.GetRepairProjectWebhookConfig("default")
	if webhookURL == "" || secret == "" {
		return fmt.Errorf("未找到default项目的webhook配置")
	}
	// 构建消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"config": map[string]interface{}{
				"wide_screen_mode": true,
			},
			"header": map[string]interface{}{
				"template": "red",
				"title": map[string]interface{}{
					"content": "❌ 验证不通过通知",
					"tag":     "plain_text",
				},
			},
			"elements": []map[string]interface{}{
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": func() string {
							if engineerName != "" {
								return fmt.Sprintf("<at>%s</at>", engineerName)
							}
							return ""
						}(),
						"tag": "lark_md",
					},
				},
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "**❌ 维修验证不通过，需要重新处理**",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "div",
					"text": map[string]interface{}{
						"content": "请根据验证意见进行进一步处理",
						"tag":     "lark_md",
					},
				},
				{
					"tag": "hr",
				},
				{
					"tag": "div",
					"fields": []map[string]interface{}{
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**📝 维修单编号：**\n" + repairTicketNo,
								"tag":     "lark_md",
							},
						},
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**💻 设备SN：**\n" + deviceSN,
								"tag":     "lark_md",
							},
						},
					},
				},
				{
					"tag": "div",
					"fields": []map[string]interface{}{
						{
							"is_short": true,
							"text": map[string]interface{}{
								"content": "**📋 关联报障单：**\n" + faultTicketNo,
								"tag":     "lark_md",
							},
						},
					},
				},
			},
		},
	}

	// 添加验证意见（如果有提供）
	cardMap, ok := msg["card"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("无法获取card字段")
	}
	elementsInterface, ok := cardMap["elements"]
	if !ok {
		return fmt.Errorf("无法获取elements字段")
	}
	elements, ok := elementsInterface.([]map[string]interface{})
	if !ok {
		return fmt.Errorf("elements字段格式错误")
	}

	if verificationComments != "" {
		elements = append(elements, map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"content": "**⚠️ 验证意见：**\n" + verificationComments,
				"tag":     "lark_md",
			},
		})
	}

	// 添加设备位置信息（如果有提供）
	if cabinetName != "" || roomName != "" {
		locationInfo := map[string]interface{}{
			"tag":    "div",
			"fields": []map[string]interface{}{},
		}

		fields := []map[string]interface{}{
			{
				"is_short": true,
				"text": map[string]interface{}{
					"content": "**🔧 机柜：**\n" + cabinetName,
					"tag":     "lark_md",
				},
			},
			{
				"is_short": true,
				"text": map[string]interface{}{
					"content": "**🚪 包间：**\n" + roomName,
					"tag":     "lark_md",
				},
			},
		}

		locationInfo["fields"] = fields
		elements = append(elements, locationInfo)
	}

	// 添加分隔线和按钮
	elements = append(elements,
		map[string]interface{}{
			"tag": "hr",
		},
		map[string]interface{}{
			"tag": "action",
			"actions": []map[string]interface{}{
				{
					"tag": "button",
					"text": map[string]interface{}{
						"content": "🔍 查看详情",
						"tag":     "plain_text",
					},
					"type": "primary",
					"url":  fmt.Sprintf(f.RepairDetailUrlTemplate, repairTicketID),
				},
			},
		},
	)

	// 更新elements
	cardMap["elements"] = elements

	// 生成签名
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	stringToSign := fmt.Sprintf("%s\n%s", timestamp, secret)
	h := hmac.New(sha256.New, []byte(stringToSign))
	signBytes := h.Sum(nil)
	sign := base64.StdEncoding.EncodeToString(signBytes)

	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		f.Logger.Error("飞书通知JSON序列化失败", zap.Error(err))
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData)) // #nosec G107
	if err != nil {
		f.Logger.Error("发送飞书通知失败", zap.Error(err))
		return fmt.Errorf("发送通知失败: %w", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			f.Logger.Error("<UNK>", zap.Error(err))
		}
	}(resp.Body)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		f.Logger.Error("飞书通知API返回非200状态码",
			zap.Int("statusCode", resp.StatusCode))
		return fmt.Errorf("飞书API返回状态码: %d", resp.StatusCode)
	}

	f.Logger.Info("验证不通过通知发送成功")
	return nil
}
