package email

import (
	"backend/configs"
	"context"

	"go.uber.org/zap"
)

// GlobalEmailUtils 全局邮件工具实例
var GlobalEmailUtils *EmailUtils

// InitEmailUtils 初始化全局邮件工具
func InitEmailUtils(config *configs.Config, logger *zap.Logger) {
	GlobalEmailUtils = NewEmailUtilsFromConfig(config, logger)
}

// GetEmailUtils 获取邮件工具实例
func GetEmailUtils() *EmailUtils {
	return GlobalEmailUtils
}

// SendSimple 快速发送简单邮件
func SendSimple(ctx context.Context, to []string, subject, body string) error {
	if GlobalEmailUtils == nil {
		return nil // 如果未初始化，静默失败
	}
	return GlobalEmailUtils.SendSimpleEmail(ctx, to, subject, body)
}

// SendWelcome 快速发送欢迎邮件
func SendWelcome(ctx context.Context, to []string, name string) error {
	if GlobalEmailUtils == nil {
		return nil
	}
	return GlobalEmailUtils.SendWelcomeEmail(ctx, to, name)
}

// SendNotification 快速发送通知邮件
func SendNotification(ctx context.Context, to []string, title, message string, details map[string]string) error {
	if GlobalEmailUtils == nil {
		return nil
	}
	return GlobalEmailUtils.SendNotificationEmail(ctx, to, title, message, details)
}

// SendAlert 快速发送警告邮件
func SendAlert(ctx context.Context, to []string, title, message string, details map[string]string) error {
	if GlobalEmailUtils == nil {
		return nil
	}
	return GlobalEmailUtils.SendAlertEmail(ctx, to, title, message, details)
}
