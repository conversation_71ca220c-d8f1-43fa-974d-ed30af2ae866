package email

import (
	"backend/configs"
	"bytes"
	"context"
	"fmt"
	"html/template"
	"strings"
	"time"

	"go.uber.org/zap"
	"gopkg.in/gomail.v2"
)

type EmailTemplate struct {
	Subject string
	Body    string
}

type EmailConfig struct {
	Host     string
	Port     int
	Username string
	Password string
	From     string
}

type EmailUtils struct {
	config *EmailConfig
	logger *zap.Logger
}

type EmailRequest struct {
	To      []string
	Cc      []string
	Bcc     []string
	Subject string
	Body    string
	IsHTML  bool
}

type TemplateData map[string]interface{}

// 默认模板
const (
	DefaultWelcomeTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{.Subject}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 2px solid #007bff; padding-bottom: 15px; margin-bottom: 20px; }
        .content { line-height: 1.6; color: #333; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px; }
        .highlight { background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .button { display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{.Title}}</h1>
        </div>
        <div class="content">
            <p>亲爱的 {{.Name}}，</p>
            <div class="highlight">
                {{.Message}}
            </div>
            {{if .ActionURL}}
            <p>
                <a href="{{.ActionURL}}" class="button">{{.ActionText}}</a>
            </p>
            {{end}}
            <p>如有任何问题，请随时联系我们。</p>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发送，请勿直接回复。</p>
            <p>{{.CompanyName}} - {{.Timestamp}}</p>
        </div>
    </div>
</body>
</html>`

	DefaultNotificationTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{.Subject}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 2px solid #28a745; padding-bottom: 15px; margin-bottom: 20px; }
        .content { line-height: 1.6; color: #333; }
        .info-box { background-color: #f8f9fa; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{.Title}}</h1>
        </div>
        <div class="content">
            <p>您好，</p>
            <div class="info-box">
                <strong>{{.NotificationType}}:</strong> {{.Message}}
            </div>
            {{if .Details}}
            <div class="info-box">
                <strong>详细信息:</strong><br>
                {{range $key, $value := .Details}}
                <strong>{{$key}}:</strong> {{$value}}<br>
                {{end}}
            </div>
            {{end}}
            {{if .ActionURL}}
            <p>
                <a href="{{.ActionURL}}" style="color: #28a745; text-decoration: none; font-weight: bold;">查看详情</a>
            </p>
            {{end}}
        </div>
        <div class="footer">
            <p>{{.CompanyName}} - {{.Timestamp}}</p>
        </div>
    </div>
</body>
</html>`

	DefaultAlertTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{.Subject}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #fff3cd; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border: 1px solid #ffc107; }
        .header { border-bottom: 2px solid #dc3545; padding-bottom: 15px; margin-bottom: 20px; }
        .content { line-height: 1.6; color: #333; }
        .alert-box { background-color: #f8d7da; border: 1px solid #dc3545; color: #721c24; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚠️ {{.Title}}</h1>
        </div>
        <div class="content">
            <div class="alert-box">
                <strong>警告:</strong> {{.Message}}
            </div>
            {{if .Details}}
            <div style="margin: 20px 0;">
                <strong>相关详情:</strong><br>
                {{range $key, $value := .Details}}
                <strong>{{$key}}:</strong> {{$value}}<br>
                {{end}}
            </div>
            {{end}}
            {{if .ActionURL}}
            <p>
                <a href="{{.ActionURL}}" style="color: #dc3545; font-weight: bold;">立即处理</a>
            </p>
            {{end}}
        </div>
        <div class="footer">
            <p>这是一个重要的系统通知，请及时处理。</p>
            <p>{{.CompanyName}} - {{.Timestamp}}</p>
        </div>
    </div>
</body>
</html>`
)

// NewEmailUtils 创建一个新的EmailUtils实例
func NewEmailUtils(config *EmailConfig, logger *zap.Logger) *EmailUtils {
	return &EmailUtils{
		config: config,
		logger: logger,
	}
}

// NewEmailUtilsFromConfig 从配置文件创建EmailUtils
func NewEmailUtilsFromConfig(config *configs.Config, logger *zap.Logger) *EmailUtils {
	// 使用默认发件人账户
	if config.Email == nil || len(config.Email.SenderAccounts) == 0 {
		logger.Error("邮件配置未找到或发件人账户为空")
		return NewEmailUtils(&EmailConfig{}, logger)
	}

	// 获取默认发件人账户
	defaultAccount, ok := config.Email.SenderAccounts["default"]
	if !ok {
		// 如果没有default，使用第一个账户
		for _, account := range config.Email.SenderAccounts {
			defaultAccount = account
			break
		}
	}

	emailConfig := &EmailConfig{
		Host:     defaultAccount.Host,
		Port:     defaultAccount.Port,
		Username: defaultAccount.Username,
		Password: defaultAccount.Password,
		From:     defaultAccount.From,
	}
	return NewEmailUtils(emailConfig, logger)
}

// SendEmail 发送邮件
func (e *EmailUtils) SendEmail(ctx context.Context, req *EmailRequest) error {
	if len(req.To) == 0 {
		return fmt.Errorf("收件人不能为空")
	}

	m := gomail.NewMessage()
	m.SetHeader("From", e.config.From)
	m.SetHeader("To", req.To...)

	if len(req.Cc) > 0 {
		m.SetHeader("Cc", req.Cc...)
	}

	if len(req.Bcc) > 0 {
		m.SetHeader("Bcc", req.Bcc...)
	}

	m.SetHeader("Subject", req.Subject)

	if req.IsHTML {
		m.SetBody("text/html", req.Body)
	} else {
		m.SetBody("text/plain", req.Body)
	}

	dialer := gomail.NewDialer(e.config.Host, e.config.Port, e.config.Username, e.config.Password)

	if err := dialer.DialAndSend(m); err != nil {
		e.logger.Error("发送邮件失败", zap.Error(err))
		return fmt.Errorf("发送邮件失败: %w", err)
	}

	e.logger.Info("邮件发送成功",
		zap.Strings("to", req.To),
		zap.String("subject", req.Subject),
	)

	return nil
}

// SendEmailWithTemplate 使用模板发送邮件
func (e *EmailUtils) SendEmailWithTemplate(ctx context.Context, req *EmailRequest, templateStr string, data TemplateData) error {
	tmpl, err := template.New("email").Parse(templateStr)
	if err != nil {
		return fmt.Errorf("解析模板失败: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return fmt.Errorf("渲染模板失败: %w", err)
	}

	req.Body = buf.String()
	req.IsHTML = true

	return e.SendEmail(ctx, req)
}

// SendWelcomeEmail 发送欢迎邮件
func (e *EmailUtils) SendWelcomeEmail(ctx context.Context, to []string, name string) error {
	data := TemplateData{
		"Title":       "欢迎加入",
		"Name":        name,
		"Message":     "欢迎加入我们的系统！您的账户已成功创建。",
		"ActionText":  "开始使用",
		"ActionURL":   "https://cloud17.cnhancloud.com",
		"CompanyName": "Cloud17",
		"Timestamp":   time.Now().Format("2006-01-02 15:04:05"),
	}

	req := &EmailRequest{
		To:      to,
		Subject: "欢迎加入 Cloud17",
	}

	return e.SendEmailWithTemplate(ctx, req, DefaultWelcomeTemplate, data)
}

// SendNotificationEmail 发送通知邮件
func (e *EmailUtils) SendNotificationEmail(ctx context.Context, to []string, title, message string, details map[string]string) error {
	data := TemplateData{
		"Title":           title,
		"Message":         message,
		"NotificationType": "系统通知",
		"Details":         details,
		"CompanyName":     "Cloud17",
		"Timestamp":       time.Now().Format("2006-01-02 15:04:05"),
	}

	req := &EmailRequest{
		To:      to,
		Subject: title,
	}

	return e.SendEmailWithTemplate(ctx, req, DefaultNotificationTemplate, data)
}

// SendAlertEmail 发送警告邮件
func (e *EmailUtils) SendAlertEmail(ctx context.Context, to []string, title, message string, details map[string]string) error {
	data := TemplateData{
		"Title":       title,
		"Message":     message,
		"Details":     details,
		"CompanyName": "Cloud17",
		"Timestamp":   time.Now().Format("2006-01-02 15:04:05"),
	}

	req := &EmailRequest{
		To:      to,
		Subject: "⚠️ " + title,
	}

	return e.SendEmailWithTemplate(ctx, req, DefaultAlertTemplate, data)
}

// SendSimpleEmail 发送简单邮件
func (e *EmailUtils) SendSimpleEmail(ctx context.Context, to []string, subject, body string) error {
	req := &EmailRequest{
		To:      to,
		Subject: subject,
		Body:    body,
		IsHTML:  false,
	}

	return e.SendEmail(ctx, req)
}

// ValidateEmail 验证邮箱地址格式
func (e *EmailUtils) ValidateEmail(email string) bool {
	if email == "" {
		return false
	}

	// 简单的邮箱格式验证
	if !strings.Contains(email, "@") || !strings.Contains(email, ".") {
		return false
	}

	return true
}

// ValidateEmails 验证多个邮箱地址
func (e *EmailUtils) ValidateEmails(emails []string) []string {
	var invalidEmails []string
	for _, email := range emails {
		if !e.ValidateEmail(email) {
			invalidEmails = append(invalidEmails, email)
		}
	}
	return invalidEmails
}
