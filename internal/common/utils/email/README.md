# 邮件工具包 (Email Utils)

这是一个基于Go的邮件发送工具包，提供了简单易用的邮件发送功能，支持HTML模板和多种邮件类型。

## 功能特性

- ✅ 简单邮件发送
- ✅ HTML模板支持
- ✅ 预设常用邮件模板
- ✅ 邮件地址验证
- ✅ 上下文支持
- ✅ 结构化日志
- ✅ 抄送/密送支持

## 安装

确保你的项目已经包含以下依赖：

```bash
go get gopkg.in/gomail.v2
go get go.uber.org/zap
```

## 快速开始

### 1. 创建邮件工具实例

```go
import "backend/internal/common/utils/email"

// 方式1: 使用配置结构体
config := &email.EmailConfig{
    Host:     "smtp.example.com",
    Port:     587,
    Username: "your-username",
    Password: "your-password",
    From:     "<EMAIL>",
}

emailUtils := email.NewEmailUtils(config, logger)

// 方式2: 从配置文件创建
emailUtils := email.NewEmailUtilsFromConfig(appConfig, logger)
```

### 2. 发送邮件

#### 发送简单邮件
```go
err := emailUtils.SendSimpleEmail(ctx,
    []string{"<EMAIL>"},
    "测试邮件",
    "这是一封测试邮件")
```

#### 发送欢迎邮件
```go
err := emailUtils.SendWelcomeEmail(ctx,
    []string{"<EMAIL>"},
    "张三")
```

#### 发送通知邮件
```go	details := map[string]string{
    "工单编号": "INC-2024-001",
    "处理人":  "李四",
    "状态":   "已处理",
}

err := emailUtils.SendNotificationEmail(ctx,
    []string{"<EMAIL>"},
    "工单处理完成",
    "工单已处理完成",
    details)
```

#### 发送警告邮件
```go
alertDetails := map[string]string{
    "错误类型": "系统异常",
    "影响范围": "用户登录",
    "紧急程度": "高",
}

err := emailUtils.SendAlertEmail(ctx,
    []string{"<EMAIL>"},
    "系统异常警告",
    "检测到系统异常，请立即处理",
    alertDetails)
```

### 3. 使用自定义模板

```go
template := `
<!DOCTYPE html>
<html>
<head><title>{{.Subject}}</title></head>
<body>
    <h1>Hello {{.Name}}!</h1>
    <p>{{.Message}}</p>
</body>
</html>`

data := email.TemplateData{
    "Subject": "自定义模板",
    "Name":    "用户",
    "Message": "这是自定义模板内容",
}

req := &email.EmailRequest{
    To:      []string{"<EMAIL>"},
    Subject: "自定义模板邮件",
}

err := emailUtils.SendEmailWithTemplate(ctx, req, template, data)
```

## 预设模板

### 1. 欢迎模板 (DefaultWelcomeTemplate)
- 适用于新用户注册欢迎邮件
- 包含欢迎信息、操作按钮

### 2. 通知模板 (DefaultNotificationTemplate)
- 适用于系统通知
- 支持详细信息展示

### 3. 警告模板 (DefaultAlertTemplate)
- 适用于系统警告和异常通知
- 醒目的警告样式

## 邮件验证

```go
// 验证单个邮箱
isValid := emailUtils.ValidateEmail("<EMAIL>")

// 验证多个邮箱
invalidEmails := emailUtils.ValidateEmails([]string{
    "<EMAIL>",
    "invalid-email",
    "<EMAIL>",
})
```

## 错误处理

所有邮件发送函数都会返回错误，建议进行适当的错误处理：

```go
if err := emailUtils.SendEmail(ctx, req); err != nil {
    // 记录错误日志
    logger.Error("邮件发送失败", zap.Error(err))

    // 根据错误类型进行处理
    if strings.Contains(err.Error(), "SMTP") {
        // SMTP连接错误
    } else if strings.Contains(err.Error(), "template") {
        // 模板渲染错误
    }
}
```

##### 配置示例

在配置文件中添加邮件配置：

```yaml
email:
  senderAccounts:
    default:
      host: "smtp.gmail.com"
      port: 587
      username: "<EMAIL>"
      password: "your-app-password"
      from: "Cloud17 <<EMAIL>>"
    support:
      host: "smtp.gmail.com"
      port: 587
      username: "<EMAIL>"
      password: "support-app-password"
      from: "技术支持 <<EMAIL>>"
```

## 集成示例

### 在业务代码中使用

```go
// 在服务初始化时创建邮件工具
func NewUserService(db *gorm.DB, emailUtils *email.EmailUtils) *UserService {
    return &UserService{
        db:        db,
        emailUtils: emailUtils,
    }
}

// 注册用户时发送欢迎邮件
func (s *UserService) RegisterUser(ctx context.Context, user *User) error {
    // 保存用户到数据库...

    // 发送欢迎邮件
    if err := s.emailUtils.SendWelcomeEmail(ctx, []string{user.Email}, user.Name); err != nil {
        s.logger.Error("发送欢迎邮件失败", zap.Error(err))
        // 不返回错误，因为这不是关键操作
    }

    return nil
}
```

## 注意事项

1. **SMTP配置**：确保SMTP服务器配置正确，特别是端口和安全设置
2. **邮箱验证**：发送前建议验证邮箱格式，避免发送到无效地址
3. **错误处理**：邮件发送失败不应该影响主要业务流程
4. **模板变量**：使用模板时确保所有变量都有默认值，避免渲染错误
5. **频率限制**：注意邮件服务提供商的发送频率限制

## 测试

运行示例测试：

```bash
go test -v ./internal/common/utils/email/example_test.go
```

## 扩展

可以通过以下方式扩展邮件功能：

1. 添加新的邮件模板
2. 实现邮件队列功能
3. 添加邮件追踪功能
4. 支持多语言模板
5. 添加附件支持
