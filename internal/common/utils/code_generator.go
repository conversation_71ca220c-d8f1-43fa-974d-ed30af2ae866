package utils

import (
	"context"
	"crypto/rand"
	"fmt"
	"math"
	"math/big"
	"time"
)

// CodeExistChecker 是一个检查编码是否已存在的函数类型
type CodeExistChecker func(ctx context.Context, code string) (bool, error)

// CodeGeneratorOptions 编码生成器选项
type CodeGeneratorOptions struct {
	// Prefix 编码前缀，如P代表项目，A代表资产等
	Prefix string

	// DateFormat 日期格式，如"20060102"表示yyyyMMdd，"060102"表示yyMMdd
	DateFormat string

	// RandomLength 随机数长度，如4表示生成4位随机数
	RandomLength int

	// Delimiter 分隔符，如"-"则生成P-20230525-1234这样的格式
	Delimiter string
}

// DefaultCodeGeneratorOptions 默认的编码生成选项
var DefaultCodeGeneratorOptions = CodeGeneratorOptions{
	Prefix:       "CODE",
	DateFormat:   "20060102",
	RandomLength: 4,
	Delimiter:    "",
}

// 生成安全的随机整数，范围从 min 到 max (包含)
func secureRandomInt(min, max int) (int, error) {
	// 计算范围大小
	delta := max - min + 1

	// 生成随机数
	n, err := rand.Int(rand.Reader, big.NewInt(int64(delta)))
	if err != nil {
		return 0, err
	}

	// 将范围调整到 min-max
	return min + int(n.Int64()), nil
}

// GenerateUniqueCode 生成唯一编码
// prefix: 编码前缀
// existChecker: 检查编码是否已存在的函数
// options: 编码生成选项，可选，不提供则使用默认选项
func GenerateUniqueCode(ctx context.Context, existChecker CodeExistChecker, options ...CodeGeneratorOptions) (string, error) {
	// 使用提供的选项或默认选项
	opts := DefaultCodeGeneratorOptions
	if len(options) > 0 {
		opts = options[0]
	}

	// 生成随机数
	randomNumFormat := fmt.Sprintf("%%0%dd", opts.RandomLength)
	maxNum := int(math.Pow10(opts.RandomLength))
	minNum := int(math.Pow10(opts.RandomLength - 1))
	if minNum < 1 {
		minNum = 1
	}

	// 尝试最多10次生成唯一编码
	for i := 0; i < 10; i++ {
		// 获取当前时间
		now := time.Now()
		dateStr := now.Format(opts.DateFormat)

		// 生成随机数
		randomNum, err := secureRandomInt(minNum, maxNum-1)
		if err != nil {
			return "", fmt.Errorf("生成随机数失败: %w", err)
		}

		// 组合成编码
		var code string
		if opts.Delimiter == "" {
			code = fmt.Sprintf("%s%s%s", opts.Prefix, dateStr, fmt.Sprintf(randomNumFormat, randomNum))
		} else {
			code = fmt.Sprintf("%s%s%s%s%s", opts.Prefix, opts.Delimiter, dateStr, opts.Delimiter, fmt.Sprintf(randomNumFormat, randomNum))
		}

		// 检查编码是否存在
		if existChecker != nil {
			exists, err := existChecker(ctx, code)
			if err != nil {
				return "", fmt.Errorf("检查编码是否存在失败: %w", err)
			}

			if !exists {
				return code, nil
			}
		} else {
			// 如果没有提供存在性检查函数，直接返回生成的编码
			return code, nil
		}
	}

	return "", fmt.Errorf("无法生成唯一编码，请稍后重试")
}

// InitCodeGenerator 初始化编码生成器
func InitCodeGenerator() {

}
