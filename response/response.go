package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ResponseStruct 通用响应结构
type ResponseStruct struct {
	// HTTP 状态码
	// example: 200
	Code int `json:"code"`

	// 响应消息
	// example: 操作成功
	Message string `json:"message"`

	// 响应数据
	Data interface{} `json:"data"`
}

// PageResult 分页结果结构
type PageResult struct {
	List     interface{} `json:"list"`
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"page_size"`
}

// Response 统一响应格式
func Response(c *gin.Context, code int, data interface{}, msg string) {
	c.JSON(code, gin.H{
		"code":    code,
		"data":    data,
		"message": msg,
	})
}

// Success 成功响应
func Success(ctx *gin.Context, data interface{}, message string) {
	if message == "" {
		message = "操作成功"
	}
	ctx.JSON(http.StatusOK, ResponseStruct{
		Code:    http.StatusOK,
		Message: message,
		Data:    data,
	})
}

// Fail 失败响应
func Fail(ctx *gin.Context, code int, message string) {
	ctx.JSON(http.StatusOK, ResponseStruct{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

func InboundFail(c *gin.Context, code int, data interface{}, msg string) {
	Response(c, code, data, msg)
}
