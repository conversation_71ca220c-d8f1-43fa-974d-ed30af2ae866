# 设置变量
OUTPUT_DIR=../yuntu
GO_BUILD=CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo

# 编译所有服务
all: main customerapi worker

# 编译后端服务
main:
	$(GO_BUILD) -o $(OUTPUT_DIR)/main .

# 编译客户API服务
customerapi:
	$(GO_BUILD) -o $(OUTPUT_DIR)/customerapi ./cmd/customerapi/main.go

# 编译工作器服务
worker:
	$(GO_BUILD) -o $(OUTPUT_DIR)/worker ./cmd/worker/worker.go

# 清理编译产物
clean:
	rm -f $(OUTPUT_DIR)/backend $(OUTPUT_DIR)/customerapi $(OUTPUT_DIR)/worker
