# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [2.8.2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/compare/v2.8.1...v2.8.2) (2025-07-22)


### Features

* 🎸 使用常量替代硬编码审批动作和状态，优化代码可读性 ([f3058b9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f3058b98af207e90c430461d19cdf5b7e85643aa))
* 🎸 同步更新设备SN至资源表，确保一致性 ([df63d74](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/df63d74949c2a572ffad7dcc3c8a522fed6154cb))
* 🎸 增加对多种文件类型的支持，包括PDF、Word、Excel、PPT、文本、压缩、音频和视频文件 ([1f581a8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1f581a8c7872e17251404c21abf232ec532a2fda))
* 🎸 增强采购审批功能，添加当前阶段验证及用户真实姓名获取逻辑 ([44302f0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/44302f0be54f10e7d71b741a8bbbfafba5933d39))
* 🎸 完成采购申请模块，支持接收地址、接收人及电话字段，优化请求类型处理 ([ce2f29b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ce2f29bcd3f3c6ede90c3146c302ce1e23721c14))
* 🎸 新增采购询价模块，包括询价列表、详情、创建及审批功能，优化了相关数据结构和表单配置 ([3898f28](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3898f28e39f2a30c359e104d0383e4979bcf1352))
* 🎸 添加采购模块相关权限config配置 ([872d72f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/872d72f604fe7d8eae516ba6dc58b95cb6ba7d01))
* 🎸 添加采购申请明细询价状态接口，优化审批通知逻辑 ([8b2e357](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8b2e3579e3913f93f802ba61014cd162bd926509))
* 🎸 添加采购申请流程飞书通知功能，支持申请提交、阶段变更、审批通过和拒绝通知 ([4d2b7e5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4d2b7e58869dac31d9db402f29c974840274eddf))
* 🎸 添加采购询比价功能，包括工作流、状态管理及通知机制 ([d72fd43](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d72fd43aade844d4592b93b7d70c9df35140842d))
* 上线导入功能 ([8a1629d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8a1629de3954acc69345221b5a2b54a716bb951b))
* 修复报障单500错误 ([b73d396](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b73d3960d252fa381026b635d4225832e8870f77))
* 修改改配出库流程，资产管理员同意后将配件设置为使用中 ([24df0b7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/24df0b78df7c7ea1b24369bcc981451e776ef94c))
* 修改网络设备搜索相关SQL，修复可用区无法根据区域搜索的bug ([6edf4a3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6edf4a3bb55d28efb92973e816147d93520da002))
* 增加设备出入库飞书通知功能，更改配件入库飞书模板 ([596a401](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/596a401acbcf550013e2a68adb04a911c0735bee))
* 增加配件状态校验、库存校验以及更新库存信息说明 ([84f1d22](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/84f1d22492a02c21cf68f6ba91361befbe32a41a))
* 增加项目与仓库之间的关联 ([4b4e4fd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4b4e4fdb9b356ed351dc77ae28bcc83cbcf788fc))
* 更改cloud23校验逻辑，不需要绑定维修单 ([5e56627](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5e5662753fdb123f9509b1611c31646ae79fadee))
* 适配备件csv批量导入映射 ([e72d58d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e72d58daa1fb7838ba2ba53cfab7e546431d2dda))
* 重构入库数据结构，新增设备入库、设备调拨、上架出库 ([0b91b99](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0b91b99284942100b35a3d7483b6975b9db03725))
* 重构设备出入库,修改出入库库存更新逻辑 ([7a2746c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7a2746c0ef25bd7874b78b5280306f6c6f87f20f))
* 验收和上架新增支持导入设备列表 ([531ba58](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/531ba58a857c1728293577061bce74f77f1aa4eb))
* 验收和上架新增支持导入设备列表 ([be25724](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/be2572457e079f5dceaeb3e2300b99965eac684f))
* 验收模块修改解析网卡model ([639fd2f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/639fd2ff865554a27c9330ed00a9cd352c528629))
* 验收设备记录bmc mac和vpc mac信息 ([e2586a8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e2586a8b2c6956392630b508271d0c2002af9b3d))
* 验收设备记录bmc mac和vpc mac信息 ([ae973ac](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ae973ac2ec15346597865bd94dec87b3b8b95f5b))


### Bug Fixes

* 🐛 修复了故障工单模块中的空指针解引用问题 ([7380171](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7380171ac430ba4827d46f95987425295226b025))
* 🐛 修复故障单缺少sn加载dto pcnic问题 ([be07554](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/be0755442ddaed1882da842abe1ea532897f0a79))
* 修复合并错误 ([2af2085](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2af20858179fa1357dd6e504f5fe7b81a28d500b))
* 修复网络设备搜索bug ([b15b4d8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b15b4d855fcae19cd1f52a33f66a39add940c1a0))
* 修复设备出库更新CMDB失败的bug ([f7dc328](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f7dc32864ad0631045019cbe76a534016748cb53))
* 修复设备调拨出库飞书通知bug ([419bdc1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/419bdc17b12ab37b47567cbd9bf6f4bbc41d4709))
* 修复调拨出库模板导入问题 ([f4feb3e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f4feb3e6104f68581df1effbd39420ace3637992))
* 验收模块bug修复-07.21 ([66271d6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/66271d6ad6f848d656ab7d8565c26998c7a56cde))


### Others

* merge ([c66072f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c66072f6bb9074a95a93e570cb016a7ddaa34527))

## [2.8.0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/compare/v2.7.0...v2.8.0) (2025-07-08)


### Features

* 新增设备入库，重构拆机入库 ([64d7b08](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/64d7b082fc6e54b05b7ec8040d4ef04d6c2e07c3))


### Others

* **release:** 2.8.0 ([82b0559](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/82b05598d7cf29fa3454039398d3f8c3b64bfb40))

### [2.8.1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/compare/v2.7.0...v2.8.1) (2025-07-10)


### Features

* 🎸 修改字段省略空值以及修改维修单sn查询参数 ([42f4262](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/42f4262024c863ef6afe10e596d9b369b0a182c5))
* 🎸 修改字段省略空值以及修改维修单sn查询参数 ([8a9c7d0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8a9c7d09103c8938f22d3bd8c7ff33f2e91eb4c3))
* 🎸 修改维修选择/总结故障类型接收参数 ([3fda6cd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3fda6cd8e77cfd38a2e0bd899ba77282a69681e0))
* 🎸 修改维修选择/总结故障类型接收参数 ([1bd45a9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1bd45a9ffc7dcc3d050b8624bb9d936111ae9d6a))
* 🎸 修改资产验证list接口使其不返回具体item项 ([3b090ae](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3b090ae9b5baa19f17f8bdd3aa918d365dd0898e))
* 🎸 修改资产验证list接口使其不返回具体item项 ([37e098b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/37e098b40a1c4dbf48221a6cb79c1fc30706bcdb))
* 🎸 入库/出库类型时会同步加/减可用库存数量 ([e62ff07](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e62ff0727b1cd7e745279e0ecfd093c2826113cd))
* 🎸 创建上架工单增加保存cabinetID、跟U位信息 ([57b659e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/57b659e8cd59b33256163b5dfc73188245b8cc80))
* 🎸 升级go版本至1.24.3 ([f2fd864](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f2fd86404516b1cf78a91b4ec1ece906ef7a27ab))
* 🎸 升级go版本至1.24.3 ([e052d2e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e052d2e614d42a95efb9c3a331c81ac1b43f3f31))
* 🎸 增加data字段 ([27f127d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/27f127d3b40bf8ea1c30b2dc756570dd8c1f1ee8))
* 🎸 增加device多SN查询接口 ([ecd89aa](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ecd89aab25a524bf41315eafc3ebd0836059e5e3))
* 🎸 增加list iterm检测项接口 ([40fa9a5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/40fa9a5c0378f94646936828825df53d1737f906))
* 🎸 增加维修单接单响应计算 ([79f7fdb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/79f7fdb53874566c4145dc31ecce260db6a76ad1))
* 🎸 增加维修单接单响应计算 ([b6bba70](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b6bba70a470c46d0c72d38769d8bd542aecf3828))
* 🎸 支持多个查询关键词，优化设备资源查询逻辑 ([c807300](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c807300fe974d618600ef2122a81536300df5aae))
* 🎸 支持多个查询关键词，优化设备资源查询逻辑 ([cc54fb3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/cc54fb35957a1e7764e5ffecf4163fcde2fa0519))
* 🎸 更新仓库名称以反映最新的仓库信息 ([2fd8e15](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2fd8e15d8a5f8a85d7ab2d03333d95f3b3322213))
* 🎸 添加产品相关字段和DTO，支持产品创建和更新记录功能 ([f645815](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f645815aa3305674e96f15808e4dae2593ee1352))
* 🎸 添加产品相关字段和DTO，支持产品创建和更新记录功能 ([3c57df1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3c57df1b2246988dba270223ea7a6de726699292))
* 🎸 添加供应商管理模块，支持项目的创建、更新、查询和删除功能 ([e7282ea](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e7282ea95a49c36779f75073a09090385a666112))
* 🎸 添加供应商管理模块，支持项目的创建、更新、查询和删除功能 ([003193d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/003193de571272e90d5c7234d8db53d40e3c7694))
* 🎸 添加公司档案管理模块，支持公司信息的创建、更新、查询和删除功能 ([08d007f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/08d007ff0e405d65c4353b9a5ea3261e66bbb524))
* 🎸 添加公司档案管理模块，支持公司信息的创建、更新、查询和删除功能 ([fafdb35](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/fafdb350fb675727aab2df6019199dc4543ce0db))
* 🎸 添加文件服务支持到导入处理器，保存导入文件信息 ([cd3d277](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/cd3d277b789e1f8ef1260c8ee5c650a7ac403902))
* 🎸 添加文件服务支持到导入处理器，保存导入文件信息 ([293d54a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/293d54a8ec090819ed668e10312f516e66e217f3))
* 🎸 添加通过文件名获取文件信息的功能，并优化文件存储路径管理 ([6b432ac](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6b432acc97c23e5cb470538e0150503e81a3d9ad))
* 🎸 添加通过文件名获取文件信息的功能，并优化文件存储路径管理 ([15e3ea0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/15e3ea037c86a313185e2a773038bff5bbdebef5))
* 🎸 添加项目管理模块，支持项目的创建、更新、查询和删除功能 ([f4a4ecb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f4a4ecb877447b5cd2981a78fb14014324c0ea3a))
* 🎸 添加项目管理模块，支持项目的创建、更新、查询和删除功能 ([a39acfd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a39acfdbccef715d766fd1072a51bf0795646330))
* 🎸 禁用报障单的一些无用接口 ([3f1cdaa](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3f1cdaa529878e379afa64b61659d2ae69bea1de))
* 🎸 禁用报障单的一些无用接口 ([d015bde](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d015bdedb07719d1aa18f23f8c3baadbdf390962))
* 🎸 重命名 GPU 模块为 Server 模块，更新相关路径和引用 ([94227e5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/94227e5a405a7091ebfa2148a8758bad41ff6ec8))
* 🎸 重命名 GPU 模块为 Server 模块，更新相关路径和引用 ([0b34e4a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0b34e4a9ae0d032333a45bb6f2c2d8c916eb4fb1))
* 🎸 重命名采购模块为 purchase_old，更新相关路径和引用 ([c174b7d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c174b7d8ad87a95493b2c11e82a754e83b0f173f))
* 🎸 重命名采购模块为 purchase_old，更新相关路径和引用 ([1a2ac98](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1a2ac98a8116513d838130e0b9866fdbcb249e65))
* 🎸 重构通知器模块，替换 utils 包为 notifier 包 ([0ee4f11](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0ee4f11590634aae200196dc421344c8cf8d7220))
* 🎸 重构通知器模块，替换 utils 包为 notifier 包 ([53342a3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/53342a3dde152b8a898c7bfb14aaf30c8d8eb359))
* 🎸 隐藏一些用户日志隐私信息，将操作日志根据不同请求方式设置不同的保存时间 ([eb1407c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/eb1407cfdbb0a0941fd33aaec5b2189c8c07a7c4))
* 🎸 隐藏一些用户日志隐私信息，将操作日志根据不同请求方式设置不同的保存时间 ([5e84d59](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5e84d598da676267854e899938d2c715ab28288d))
* bebase ([c1f9f86](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c1f9f86bd00c81987a1f1b7c1d48b4af1bf0d9eb))
* inspectingComponentInfo表添加索引 ([f7075b9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f7075b90f7e60ac21093fa8a42d1c8ed5e59b8bd))
* merge ([a913694](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a9136941ffe315373e487cae240b965630f23ddc))
* 上ç下å上下线单命名规范â ([eb2bf6b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/eb2bf6b117e75eae1cb2e4df1f30104191eb582a))
* 上下é线注册位置改 ([cd4ce2e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/cd4ce2e357894dcb58adb4efcfa661b51c42efd9))
* 上架和验收添加获取历史接口 ([3162743](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3162743c7fa4398a5d15160cfab1e3460ba83bc6))
* 上架更改资产状态，（存疑惑） ([3b4d7e4](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3b4d7e4ec35a32fee65eacc3f3b1add98d4eaba0))
* 保存VPC BMC MAC ([560d5fd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/560d5fd9a2dcb0248fae4ca2fdf309288828e307))
* 修改工作流相关代码，使其能带出错误 ([b7afaec](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b7afaecfc5ee061ead1a5a9c62b401168f031459))
* 修改验收权限配置文件 ([39f05d2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/39f05d29753aee2f90934888307c069818fb516d))
* 入室人员管理创建修复 ([a634161](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a6341611609aa8a5cf479377e75181ac71359e70))
* 入室人员管理按钮 ([72512db](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/72512db5a3357cb0c7952f635bbb9dff1d0e20df))
* 创建验收工单 ([53b889c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/53b889c6b5cc6aabe819f16ca213ed383e38c3fb))
* 删除冗余的前端请求字段 ([6a577f3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6a577f385f511bd68d3152753aed320a7dd1931d))
* 合并代码 ([5f7aa99](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5f7aa9973c0fa32aef475c1a3335ff8ce637715f))
* 完结工单，往cmdb中导入通过的硬件信息 ([83d7e76](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/83d7e768d344c68f5b446fd2a8d321f2606b84c1))
* 完结工单，校验PN和SN ([ca9f583](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ca9f583db0dd21bc245765f9eb5d7400607756f1))
* 支持最新版的json数据解析 ([2c060e9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2c060e902f07f9acb3d48a4987c7ed3dc0659c2b))
* 支持手动填入CPU、GPU 缺省pn ([7906f32](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7906f32f6f4bf3dbb7cb557f47387b880636ffb2))
* 支持手动填入CPU、GPU 缺省pn ([e7bda6d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e7bda6d676c92abc8b838204c42078463bc45d3b))
* 新增正在验收的组件信息表，存储正在验收的组件信息 ([7c6d193](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7c6d1932ae15704700154b774c85614c0bd3141c))
* 新增正在验收的组件信息表，存储正在验收的组件信息 ([892579c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/892579cca3199963172ed2f6b21f6783048ad83a))
* 新增配件售卖出库 ([9dd195c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9dd195c16eb6e8ef94a6293fb3960264c24fa54d))
* 添加业务状态检查项表状态变更记录 ([1ce5b24](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1ce5b244ada51a78cb9a1902448ee925952f22af))
* 添加业务状态检查项表状态变更记录 ([1461ce7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1461ce7b466fa3f1625d0be4f8706346e6dcaae6))
* 添加文件上传 ([2af777b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2af777bc0684af7d3563a7ea59ff38439d75b5ce))
* 添加文件上传 ([4103cc2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4103cc26b8072ee5570a50a92a06dc1a6edcc8ad))
* 硬件排班飞书通知颜色改变 ([31dcf75](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/31dcf75985ec239d7715b0bb73a4b3e9d7caa156))
* 硬件资源上架，v1 ([83c7815](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/83c78152956c5301c9c437b8254588b7d16b9c65))
* 网络设备字段详情改 ([e0f89fe](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e0f89feb11fef8cfd5cdcd09bfaf1ba50354bac4))
* 调整流程结构，接收Moss导出文件 ([74e96e8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/74e96e8a03a9962855dfffd9bb40d2c226f544cf))
* 调整表结构 ([2956a5d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2956a5d812bb55060b23ac0d17dd0aad8ba8e40c))
* 资产验收错误文案调整 ([cb69928](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/cb699288093cde7a6deee713a2e10dff1ad9515e))
* 软件上下线权限配置 ([4cd89da](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4cd89da81ece21f97c343c2af850f06d980dd555))
* 软件上线和软件下线更新 ([95412d0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/95412d00c057d3cd34f64d0399b567bbbd5e8239))
* 软件上线搜索栏改正 ([c19b522](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c19b522298ae65e0095e49c0647ffc18f1eb28c8))
* 软件上线模块 ([8385924](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8385924a9e6aad450211ca4a513899d30d7e409e))
* 软件上线模块 ([18f4c4a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/18f4c4a508b12bd82f7947b0cd19bb29f1356fde))
* 软件下线后端接口 ([5c882a9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5c882a9875c3717de690f1db20f690c232ffe72e))
* 软件下线后端接口 ([aeec985](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/aeec985ac1a6f5e42b2fd17ff67d87e3bee8fa2b))
* 软件下线接口开发 ([f0aa577](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f0aa5776a17d609f05dbeb66ff31584102b4aae0))
* 飞书机器人发送私人消息，单元测试 ([2c6f51d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2c6f51d7bc3b592e539261000a4a1454a7298d3f))
* 飞书机器人应用模板test ([4f69d1d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4f69d1d721e3e17ed2dfd63cc862b3f09d7239a0))
* 验收去除多余的debug打印调试 ([bb3194f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/bb3194f91ceb558179249c930bd7f43e62d34c13))
* 验收工单整体流程 ([0ef0e2a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0ef0e2a2892404e39fb4fc4ce1dc665c05b86ff3))
* 验收新增更丰富的错误提示,以及支持未通过设备的SN导出 ([650e14c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/650e14c69e815eaaae071196ae641d9994709655))
* 验收暂时不严格对已存在组件信息进行报错 ([47aaf2e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/47aaf2e748031cdff13345ec422518f7b3c33b9a))
* 验收记录操作历史 ([7651fa6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7651fa60fc2202a2c6455e18a49f9ab68a8bafdb))


### Bug Fixes

* isPass 默认为空 ([3a9aebd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3a9aebd493b161ddd6ad5ec3d7da529a23cbb7fd))
* pn 和modelinfo错误 ([b65d8fb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b65d8fb617b70f3029b40b796e108cff4fb907de))
* 上架详情展示上架前的资产状态 ([b5cb31f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b5cb31f63ca397092903f42a6fd2de3559842202))
* 修复history重复创建、 ([61be3bc](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/61be3bc6788ffca2a75ac4d266663edd23bf6191))
* 修复history重复创建、 ([d69a4a1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d69a4a1e8a55c610dafbc2e6b63124b3112ea3df))
* 修复新购入库批量导入时发生的panic问题 ([1c6fe5c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1c6fe5c02ab4e9c4074c2b763901345d2cb23eac))
* 修复无法获取库存操作人的bug ([2c8227d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2c8227d608dbab2fd49bc04f26a3f26903269e08))
* 修复调拨出现的库存不足问题 ([7fa56f2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7fa56f21fc12f9a85f209b7864c378330bcdf97d))
* 修复调拨流程库存数量不足的bug ([e491545](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e4915459bd935f906b31eb531511013e20ada210))
* 修复飞书通知问题 ([a05ed86](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a05ed867c2686eff02f9607ab44a1722beca6a0b))
* 修改映射不正确问题 ([4c8707d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4c8707dd35441d3bb7e379aa76e525ca631b0741))
* 去除文件命名校验，增加新购入库导入模板条数到3000 ([d707eb1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d707eb128a19eff1dc1dd82241e55de53ddc6dbd))
* 状态流转 ([a7e2687](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a7e2687db0eb6bf34f2f1baa32928e79e219236d))
* 状态流转 ([eebd819](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/eebd819d81653077a51af6a1a6de63efc2527e30))
* 解决合并冲突 ([87c0fb3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/87c0fb3786ea87985f5150b5f9731ddd0ab265eb))


### Code Refactoring

* 💡 修改一些pagesize参数限制 ([5a9f3fa](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5a9f3faf7d0bb707b243d3f9c390dbc10fcc6c2a))
* 💡 操作日志修改为保存30天 ([9dbd211](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9dbd2111ffca0b0af9b1664bcf9b01171498cdd2))
* 💡 操作日志修改为保存30天 ([d0ab1ff](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d0ab1ff3145d4c0b22dba4e08d46b55e43987224))


### Others

* 🤖 merge ([36453f0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/36453f02a65c5155a3464b72f83a99e8bec26b80))
* 🤖 merge ([9619c94](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9619c94c03022eab4e26af39e88983294dfc77ce))
* **release:** 2.7.0 ([b72b310](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b72b310dcfe5e30359680608b32c35a9fdd01b27))
* **release:** 2.8.0 ([a13d324](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a13d324572c7075bce561b96c1195eee067b772d))

## [2.8.0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/compare/v2.7.0...v2.8.0) (2025-07-10)


### Features

* 🎸 修改字段省略空值以及修改维修单sn查询参数 ([42f4262](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/42f4262024c863ef6afe10e596d9b369b0a182c5))
* 🎸 修改字段省略空值以及修改维修单sn查询参数 ([8a9c7d0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8a9c7d09103c8938f22d3bd8c7ff33f2e91eb4c3))
* 🎸 修改维修选择/总结故障类型接收参数 ([3fda6cd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3fda6cd8e77cfd38a2e0bd899ba77282a69681e0))
* 🎸 修改维修选择/总结故障类型接收参数 ([1bd45a9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1bd45a9ffc7dcc3d050b8624bb9d936111ae9d6a))
* 🎸 修改资产验证list接口使其不返回具体item项 ([3b090ae](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3b090ae9b5baa19f17f8bdd3aa918d365dd0898e))
* 🎸 修改资产验证list接口使其不返回具体item项 ([37e098b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/37e098b40a1c4dbf48221a6cb79c1fc30706bcdb))
* 🎸 入库/出库类型时会同步加/减可用库存数量 ([e62ff07](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e62ff0727b1cd7e745279e0ecfd093c2826113cd))
* 🎸 创建上架工单增加保存cabinetID、跟U位信息 ([57b659e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/57b659e8cd59b33256163b5dfc73188245b8cc80))
* 🎸 升级go版本至1.24.3 ([f2fd864](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f2fd86404516b1cf78a91b4ec1ece906ef7a27ab))
* 🎸 升级go版本至1.24.3 ([e052d2e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e052d2e614d42a95efb9c3a331c81ac1b43f3f31))
* 🎸 增加data字段 ([27f127d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/27f127d3b40bf8ea1c30b2dc756570dd8c1f1ee8))
* 🎸 增加device多SN查询接口 ([ecd89aa](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ecd89aab25a524bf41315eafc3ebd0836059e5e3))
* 🎸 增加list iterm检测项接口 ([40fa9a5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/40fa9a5c0378f94646936828825df53d1737f906))
* 🎸 增加维修单接单响应计算 ([79f7fdb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/79f7fdb53874566c4145dc31ecce260db6a76ad1))
* 🎸 增加维修单接单响应计算 ([b6bba70](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b6bba70a470c46d0c72d38769d8bd542aecf3828))
* 🎸 支持多个查询关键词，优化设备资源查询逻辑 ([c807300](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c807300fe974d618600ef2122a81536300df5aae))
* 🎸 支持多个查询关键词，优化设备资源查询逻辑 ([cc54fb3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/cc54fb35957a1e7764e5ffecf4163fcde2fa0519))
* 🎸 更新仓库名称以反映最新的仓库信息 ([2fd8e15](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2fd8e15d8a5f8a85d7ab2d03333d95f3b3322213))
* 🎸 添加产品相关字段和DTO，支持产品创建和更新记录功能 ([f645815](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f645815aa3305674e96f15808e4dae2593ee1352))
* 🎸 添加产品相关字段和DTO，支持产品创建和更新记录功能 ([3c57df1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3c57df1b2246988dba270223ea7a6de726699292))
* 🎸 添加供应商管理模块，支持项目的创建、更新、查询和删除功能 ([e7282ea](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e7282ea95a49c36779f75073a09090385a666112))
* 🎸 添加供应商管理模块，支持项目的创建、更新、查询和删除功能 ([003193d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/003193de571272e90d5c7234d8db53d40e3c7694))
* 🎸 添加公司档案管理模块，支持公司信息的创建、更新、查询和删除功能 ([08d007f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/08d007ff0e405d65c4353b9a5ea3261e66bbb524))
* 🎸 添加公司档案管理模块，支持公司信息的创建、更新、查询和删除功能 ([fafdb35](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/fafdb350fb675727aab2df6019199dc4543ce0db))
* 🎸 添加文件服务支持到导入处理器，保存导入文件信息 ([cd3d277](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/cd3d277b789e1f8ef1260c8ee5c650a7ac403902))
* 🎸 添加文件服务支持到导入处理器，保存导入文件信息 ([293d54a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/293d54a8ec090819ed668e10312f516e66e217f3))
* 🎸 添加通过文件名获取文件信息的功能，并优化文件存储路径管理 ([6b432ac](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6b432acc97c23e5cb470538e0150503e81a3d9ad))
* 🎸 添加通过文件名获取文件信息的功能，并优化文件存储路径管理 ([15e3ea0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/15e3ea037c86a313185e2a773038bff5bbdebef5))
* 🎸 添加项目管理模块，支持项目的创建、更新、查询和删除功能 ([f4a4ecb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f4a4ecb877447b5cd2981a78fb14014324c0ea3a))
* 🎸 添加项目管理模块，支持项目的创建、更新、查询和删除功能 ([a39acfd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a39acfdbccef715d766fd1072a51bf0795646330))
* 🎸 禁用报障单的一些无用接口 ([3f1cdaa](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3f1cdaa529878e379afa64b61659d2ae69bea1de))
* 🎸 禁用报障单的一些无用接口 ([d015bde](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d015bdedb07719d1aa18f23f8c3baadbdf390962))
* 🎸 重命名 GPU 模块为 Server 模块，更新相关路径和引用 ([94227e5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/94227e5a405a7091ebfa2148a8758bad41ff6ec8))
* 🎸 重命名 GPU 模块为 Server 模块，更新相关路径和引用 ([0b34e4a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0b34e4a9ae0d032333a45bb6f2c2d8c916eb4fb1))
* 🎸 重命名采购模块为 purchase_old，更新相关路径和引用 ([c174b7d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c174b7d8ad87a95493b2c11e82a754e83b0f173f))
* 🎸 重命名采购模块为 purchase_old，更新相关路径和引用 ([1a2ac98](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1a2ac98a8116513d838130e0b9866fdbcb249e65))
* 🎸 重构通知器模块，替换 utils 包为 notifier 包 ([0ee4f11](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0ee4f11590634aae200196dc421344c8cf8d7220))
* 🎸 重构通知器模块，替换 utils 包为 notifier 包 ([53342a3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/53342a3dde152b8a898c7bfb14aaf30c8d8eb359))
* 🎸 隐藏一些用户日志隐私信息，将操作日志根据不同请求方式设置不同的保存时间 ([eb1407c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/eb1407cfdbb0a0941fd33aaec5b2189c8c07a7c4))
* 🎸 隐藏一些用户日志隐私信息，将操作日志根据不同请求方式设置不同的保存时间 ([5e84d59](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5e84d598da676267854e899938d2c715ab28288d))
* bebase ([c1f9f86](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c1f9f86bd00c81987a1f1b7c1d48b4af1bf0d9eb))
* inspectingComponentInfo表添加索引 ([f7075b9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f7075b90f7e60ac21093fa8a42d1c8ed5e59b8bd))
* merge ([a913694](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a9136941ffe315373e487cae240b965630f23ddc))
* 上ç下å上下线单命名规范â ([eb2bf6b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/eb2bf6b117e75eae1cb2e4df1f30104191eb582a))
* 上下é线注册位置改 ([cd4ce2e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/cd4ce2e357894dcb58adb4efcfa661b51c42efd9))
* 上架和验收添加获取历史接口 ([3162743](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3162743c7fa4398a5d15160cfab1e3460ba83bc6))
* 上架更改资产状态，（存疑惑） ([3b4d7e4](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3b4d7e4ec35a32fee65eacc3f3b1add98d4eaba0))
* 保存VPC BMC MAC ([560d5fd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/560d5fd9a2dcb0248fae4ca2fdf309288828e307))
* 修改工作流相关代码，使其能带出错误 ([b7afaec](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b7afaecfc5ee061ead1a5a9c62b401168f031459))
* 入室人员管理创建修复 ([a634161](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a6341611609aa8a5cf479377e75181ac71359e70))
* 入室人员管理按钮 ([72512db](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/72512db5a3357cb0c7952f635bbb9dff1d0e20df))
* 创建验收工单 ([53b889c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/53b889c6b5cc6aabe819f16ca213ed383e38c3fb))
* 删除冗余的前端请求字段 ([6a577f3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6a577f385f511bd68d3152753aed320a7dd1931d))
* 合并代码 ([5f7aa99](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5f7aa9973c0fa32aef475c1a3335ff8ce637715f))
* 完结工单，往cmdb中导入通过的硬件信息 ([83d7e76](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/83d7e768d344c68f5b446fd2a8d321f2606b84c1))
* 完结工单，校验PN和SN ([ca9f583](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ca9f583db0dd21bc245765f9eb5d7400607756f1))
* 支持最新版的json数据解析 ([2c060e9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2c060e902f07f9acb3d48a4987c7ed3dc0659c2b))
* 支持手动填入CPU、GPU 缺省pn ([7906f32](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7906f32f6f4bf3dbb7cb557f47387b880636ffb2))
* 支持手动填入CPU、GPU 缺省pn ([e7bda6d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e7bda6d676c92abc8b838204c42078463bc45d3b))
* 新增正在验收的组件信息表，存储正在验收的组件信息 ([7c6d193](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7c6d1932ae15704700154b774c85614c0bd3141c))
* 新增正在验收的组件信息表，存储正在验收的组件信息 ([892579c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/892579cca3199963172ed2f6b21f6783048ad83a))
* 新增配件售卖出库 ([9dd195c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9dd195c16eb6e8ef94a6293fb3960264c24fa54d))
* 添加业务状态检查项表状态变更记录 ([1ce5b24](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1ce5b244ada51a78cb9a1902448ee925952f22af))
* 添加业务状态检查项表状态变更记录 ([1461ce7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1461ce7b466fa3f1625d0be4f8706346e6dcaae6))
* 添加文件上传 ([2af777b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2af777bc0684af7d3563a7ea59ff38439d75b5ce))
* 添加文件上传 ([4103cc2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4103cc26b8072ee5570a50a92a06dc1a6edcc8ad))
* 硬件排班飞书通知颜色改变 ([31dcf75](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/31dcf75985ec239d7715b0bb73a4b3e9d7caa156))
* 硬件资源上架，v1 ([83c7815](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/83c78152956c5301c9c437b8254588b7d16b9c65))
* 网络设备字段详情改 ([e0f89fe](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e0f89feb11fef8cfd5cdcd09bfaf1ba50354bac4))
* 调整流程结构，接收Moss导出文件 ([74e96e8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/74e96e8a03a9962855dfffd9bb40d2c226f544cf))
* 调整表结构 ([2956a5d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2956a5d812bb55060b23ac0d17dd0aad8ba8e40c))
* 资产验收错误文案调整 ([cb69928](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/cb699288093cde7a6deee713a2e10dff1ad9515e))
* 软件上下线权限配置 ([4cd89da](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4cd89da81ece21f97c343c2af850f06d980dd555))
* 软件上线和软件下线更新 ([95412d0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/95412d00c057d3cd34f64d0399b567bbbd5e8239))
* 软件上线搜索栏改正 ([c19b522](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c19b522298ae65e0095e49c0647ffc18f1eb28c8))
* 软件上线模块 ([8385924](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8385924a9e6aad450211ca4a513899d30d7e409e))
* 软件上线模块 ([18f4c4a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/18f4c4a508b12bd82f7947b0cd19bb29f1356fde))
* 软件下线后端接口 ([5c882a9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5c882a9875c3717de690f1db20f690c232ffe72e))
* 软件下线后端接口 ([aeec985](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/aeec985ac1a6f5e42b2fd17ff67d87e3bee8fa2b))
* 软件下线接口开发 ([f0aa577](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f0aa5776a17d609f05dbeb66ff31584102b4aae0))
* 飞书机器人发送私人消息，单元测试 ([2c6f51d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2c6f51d7bc3b592e539261000a4a1454a7298d3f))
* 飞书机器人应用模板test ([4f69d1d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4f69d1d721e3e17ed2dfd63cc862b3f09d7239a0))
* 验收去除多余的debug打印调试 ([bb3194f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/bb3194f91ceb558179249c930bd7f43e62d34c13))
* 验收工单整体流程 ([0ef0e2a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0ef0e2a2892404e39fb4fc4ce1dc665c05b86ff3))
* 验收新增更丰富的错误提示,以及支持未通过设备的SN导出 ([650e14c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/650e14c69e815eaaae071196ae641d9994709655))
* 验收暂时不严格对已存在组件信息进行报错 ([47aaf2e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/47aaf2e748031cdff13345ec422518f7b3c33b9a))
* 验收记录操作历史 ([7651fa6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7651fa60fc2202a2c6455e18a49f9ab68a8bafdb))


### Bug Fixes

* isPass 默认为空 ([3a9aebd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3a9aebd493b161ddd6ad5ec3d7da529a23cbb7fd))
* pn 和modelinfo错误 ([b65d8fb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b65d8fb617b70f3029b40b796e108cff4fb907de))
* 上架详情展示上架前的资产状态 ([b5cb31f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b5cb31f63ca397092903f42a6fd2de3559842202))
* 修复history重复创建、 ([61be3bc](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/61be3bc6788ffca2a75ac4d266663edd23bf6191))
* 修复history重复创建、 ([d69a4a1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d69a4a1e8a55c610dafbc2e6b63124b3112ea3df))
* 修复新购入库批量导入时发生的panic问题 ([1c6fe5c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1c6fe5c02ab4e9c4074c2b763901345d2cb23eac))
* 修复无法获取库存操作人的bug ([2c8227d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2c8227d608dbab2fd49bc04f26a3f26903269e08))
* 修复调拨出现的库存不足问题 ([7fa56f2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7fa56f21fc12f9a85f209b7864c378330bcdf97d))
* 修复调拨流程库存数量不足的bug ([e491545](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e4915459bd935f906b31eb531511013e20ada210))
* 修复飞书通知问题 ([a05ed86](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a05ed867c2686eff02f9607ab44a1722beca6a0b))
* 去除文件命名校验，增加新购入库导入模板条数到3000 ([d707eb1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d707eb128a19eff1dc1dd82241e55de53ddc6dbd))
* 状态流转 ([a7e2687](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a7e2687db0eb6bf34f2f1baa32928e79e219236d))
* 状态流转 ([eebd819](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/eebd819d81653077a51af6a1a6de63efc2527e30))
* 解决合并冲突 ([87c0fb3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/87c0fb3786ea87985f5150b5f9731ddd0ab265eb))


### Others

* 🤖 merge ([36453f0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/36453f02a65c5155a3464b72f83a99e8bec26b80))
* 🤖 merge ([9619c94](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9619c94c03022eab4e26af39e88983294dfc77ce))
* **release:** 2.7.0 ([b72b310](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b72b310dcfe5e30359680608b32c35a9fdd01b27))


### Code Refactoring

* 💡 修改一些pagesize参数限制 ([5a9f3fa](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5a9f3faf7d0bb707b243d3f9c390dbc10fcc6c2a))
* 💡 操作日志修改为保存30天 ([9dbd211](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9dbd2111ffca0b0af9b1664bcf9b01171498cdd2))
* 💡 操作日志修改为保存30天 ([d0ab1ff](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d0ab1ff3145d4c0b22dba4e08d46b55e43987224))

## [2.7.0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/compare/v2.6.3...v2.7.0) (2025-06-25)


### Features

* 🎸 优化库存数量统计同步逻辑 ([2191b25](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2191b253bb5f5d0e23d80a4e144b150571f0ebd1))
* 🎸 修改了 GetStockHistory 服务方法 ([b365746](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b3657461f1fee83f66e127849b5a2c36ee85a765))
* 🎸 修改处理路径已配置但HTTP方法不匹配的情况 ([f08cbe5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f08cbe50eb50c5dce4f7d09c45280387c8279238))
* 🎸 入库/出库类型时会同步加/减可用库存数量 ([edb5a15](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/edb5a15a2672c49ecab14e9f6f2791b53af34cce))
* 🎸 升级cron库，库存定时任务修改为每小时执行一次 ([e96f2fe](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e96f2fec87914201845200fa6f9759f7db0af1a0))
* 🎸 增加后端权限控制，与原有前端权限码进行权限配置统一 ([8dc2a69](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8dc2a69bf2696da0f200e44f4913b0fd90099f67))
* 🎸 增加库存变更记录（根据仓库ID跟productID获取）API接口 ([c1bdbca](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c1bdbcadcc5bc57b9af73687a543f4d621dc6830))
* 🎸 增加库存统计查询参数类型 ([dc192be](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/dc192be34cfb490ada867e16e3d21be3e51b9225))
* 🎸 增加权限路由认证通配符，增加手动清除标志位接口 ([445e67e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/445e67e641a139ce3ac7210f76672495b7bcb52a))
* 🎸 增加用户角色权限标志位，前端根据标志位定时获取权限是否需要更新 ([04855de](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/04855de2b3a7d9f68c320f3f289d950d5ade0b32))
* 🎸 增加统一api地址根据不同参数控制相关权限（例如工作流不同流程节点权限控制） ([d5e66f4](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d5e66f44ee7f3d4d40daa55d197822f2b9fa2e67))
* 🎸 增加维修单维修次数接口增加本周故障次数数据返回 ([d19a8c6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d19a8c65024b52cb28d9063f7154d583a4cfc718))
* 🎸 增加角色管理、菜单管理相关接口 ([20f9a47](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/20f9a477bd6d709c2af684eac10f5ccc6025dd10))
* 🎸 增强用户密码校验 ([2dc3e55](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2dc3e55e23ea4a0f430575e931173deb7495c773))
* 🎸 备件导入后自动执行一次库存同步任务 ([2574f29](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2574f29e1ed6d73c9ee558ed0b68dc0c4694fd20))
* 🎸 备件状态调整或者仓库调整时记录具体SN以及存放仓库变动信息 ([5f900cd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5f900cd40e7109105a5e7e5c643e5d2b9545bf1f))
* 🎸 实现了维修单总维修时长的新计算方式。 ([02dcfaf](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/02dcfaf58f7de63b433de8267459594b54f91228))
* 🎸 客户报障api增加多ID多密钥匹配验证，报障人设置为所使用的报障name ([da47d0d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/da47d0d0c579432cf6202d25b9bad13dc1e9fd81))
* 🎸 将 /api/v1/auth/check-permission-status添加到操作日志中间件的排除路径列表 ([4e082a3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4e082a3e3c60f954a7ae315fe7689ec1d7bf2f21))
* 🎸 将view图片的api接口加入认证白名单 ([f867ecd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f867ecddcdd34d90abb5c782e4649766a6bd9610))
* 🎸 将权限认证中间件改为全局中间件，并且config未配置的api路径默认放行 ([a1d4546](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a1d45467045bbe966d2d0be987c67ec80883f1d8))
* 🎸 库存统计将不会返回所有数量都为0的数据 ([060a972](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/060a9726be7b8b105f4f25314ce2a6eeb5b72efd))
* 🎸 引入redis及其配置 ([1124bed](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1124bedf09c1f7fdb650152e30733566de69f2ed))
* 🎸 当按钮权限发生变化时，super角色的权限码缓存会被立即清除，下次用户访问时会重新从数据库加载最新的权限码。 ([941a8ef](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/941a8ef8e7707c44e16ab6fc9bc0f3af47b992c7))
* 🎸 根据角色的permission返回相应的按钮权限码 ([24ca751](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/24ca751d0808b5f333950773735c5f4fcd195ad5))
* 🎸 统一使用了role:permission:updated作为权限更新标志的键名前缀 ([a68c2d9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a68c2d920da809a20bbf3d8b0c1bfd9dd29ad90a))
* 🎸 角色权限管理实现了软删除恢复功能。当取消权限后再恢复权限时，不会创建新记录，而是恢复之前的记录。 ([2796759](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2796759ca9fb4dd7c46ff95a98e9ef4885e52cfe))
* 🎸 角色权限管理实现了软删除恢复功能。当取消权限后再恢复权限时，不会创建新记录，而是恢复之前的记录。 ([4f1b246](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4f1b246f801d238c1801fa7fcff6021879f63b35))
* 🎸 调整备件状态变更时，三个库存数量的库存同步逻辑 ([57ee002](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/57ee0026d61756deb34ab96bd0efdaf00d9f323d))
* 🎸 返回菜单列表根据meta.order字段顺序进行按序返回 ([e4aced4](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e4aced400aae82aa12d87c5270d092b114baf75f))
* 🎸 返回菜单去掉返回按钮类型数据 ([a72f6f0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a72f6f0aa302da5acc3c6bf9f7ec45733f09042d))
* 🎸 返回菜单路由数据时过滤掉status为false的路由不进行返回 ([ddda3bb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ddda3bb2a0d5d5e6412ac05de312277e43e73d20))
* 修改排班表和入室人员管理和申请 ([5af00f3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5af00f33fa51429d14c3e4f8ae8c2386ea61c7c8))
* 合并2.6.3最新变更 ([f52d6fc](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f52d6fc524f714dc321c616f6832963b084b8f9c))
* 合并最新代码 ([15a9d28](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/15a9d2828a1c56da269ca7592ccbbf41ce70139a))
* 合并库存更新代码 ([90fe437](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/90fe43739f229a6e666097127099cafcca139a23))
* 合并库存更新代码 ([1fbe7b8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1fbe7b8f5141849026d8b97cfe5634c7de66698c))
* 增加golanci ([0d750ec](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0d750ec559769b7eb6f7bdbf4495dd7fe40f2dda))
* 增加入库上传配件图片功能 ([49abb9b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/49abb9b5a254fc13b20222416efca1aef4463dcf))
* 增加改配、调拨批量导入功能 ([5569574](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5569574b79a517ca3a657baf71e1edf21b024e5c))
* 增加维修出库功能 ([c23e4d6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c23e4d655a508301b7392635409cfc6e6f3293f1))
* 增加资产出入库权限 ([9651ccb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9651ccb20242456085ac09388f7fd644f8e2539c))
* 新增golangci.yaml ([9f67583](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9f675832ecbfcf0813ef8617bf6a222bea93f9e1))
* 新增golangci.yaml ([a08f12f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a08f12f147d7052741d61bdbab4fd81ee924504c))
* 新增配件出库 ([4266bc8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4266bc81fd555bd4f0f49537bab687df05cc3f4d))
* 更新出库相关方法 ([991c98a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/991c98a2fff4255bb82935328dff9faf970cd2e1))
* 更新返修、调拨出库 ([fc6cd38](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/fc6cd38c2ede379e7112190ceca640ec6b557904))


### Bug Fixes

* 🐛 修复客户查询接口使用creation_time为时间基准而不是creation_at ([89946a0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/89946a08a424c1a457e3381dc79b014795a25293))
* 🐛 修复将软删除的备件也统计入库存数量的问题 ([151cb5e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/151cb5ecdb43e28cc44acf9169a9fedda9e0dd00))
* 🐛 修复故障单选择验证失败后续状态流转异常的问题 ([b4ff5c7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b4ff5c7a90c4ee2b4fb76b898fb04d8ded71c4c9))
* 🐛 修复无法正确返回authcode问题 ([0643566](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/06435668de2de3ed716314020668f39d4f896fc4))
* 🐛 修复菜单无法修改的问题 ([43f6967](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/43f69675cdca9e86e418ba476d724460e40d76f5))
* 🐛 读取casbin配置文件错误 ([575236b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/575236b33469f4b77595dc6d224a6e74ae742d9a))
* 修复出库查询bug ([dd88fe0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/dd88fe099c01aa3590f75f0b0de42112f4a7b2e6))
* 修复出库流程映射问题 ([216bb1d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/216bb1d8b26d75d2a8376e917802ba3e40907fb1))
* 修复合并bug ([a6cec05](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a6cec05e8ba5f5526b240ad40bd08ce87ac43795))
* 修复改配、调拨出库无法正确修改库存的bug，新增错误机制 ([bc14be7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/bc14be72f358755a2474559ebf47ce28d4a4f673))
* 修复新购出库details表数据不存在导致的导入失败问题 ([4c01823](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4c01823b2110b7a73d623c07dd6313d0eb55f11c))
* 修复飞书通知bug，修复部分展示bug ([a0db0a1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a0db0a135b1c1a643aeb2bfe3ee45fa072318f76))
* 兼容性修复，跳过调拨activity ([f908fe6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f908fe6a5a96a22bce81daeaf03881efddb741fd))
* 拆机入库 ([c51356c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c51356cd6e42ecbced8c8a529febb4a0055b2cdd))


### Reverts

* Revert "Revert "硬件飞书通知+上架接口"" ([9870db1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9870db1fe3e4059ebfe00cf769fd2108746927f5))


### Styling

* 💄 修改排班飞书通知版本号 ([88c01ac](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/88c01ac7e386d57e99718caf17b07e43faef39aa))
* 合并最新代码 ([fafe2e6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/fafe2e62263a1416d6a1e0f78817b52e6308f01d))


### Code Refactoring

* 💡 main入口函数重构 ([7cc2f3f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7cc2f3fef19d6fb94de2e7a136ffd6c70d44e4f5))
* 💡 修复静态检查报错 ([6f7b223](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6f7b223bc0d86ee3a8c8a35a5956531155049b9b))
* 💡 删除用户模型多余定义ID字段 ([fc482db](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/fc482dba17a087f155978af90e96e85afa289bf3))
* 💡 删除顶级icon字段 ([bfcafb7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/bfcafb769db1b77bec00ac8cd9d9d65b43e44ff7))
* 💡 菜单管理增加status、type字段 ([1fba5dc](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1fba5dc19333f83d04b98642748bdac132fdcc6a))
* 💡 重构temporal启动入口文 ([bd25d70](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/bd25d70801e9c2b060c38c7bfcbc87b4257a78a4))
* 增加所有err检测 ([7b60e7b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7b60e7b45b411ad8561faa589d9af66503b0158d))


### Docs

* ✏️ 修改golanci配置 ([1e2eee6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1e2eee637a809993689bf3a30423d3f2029d66bd))
* ✏️ 增加swag文档 ([ed41918](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ed41918ff135f464d077011e8ffd5819990016f4))


### Others

* 🤖 merge ([8a92837](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8a92837ef36431fe879fbb0440c0ca99535b971a))
* 🤖 合并2.6.3至角色开发分支 ([3e4889d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3e4889dcc04547657ced1e37af681ff3c1b8e950))
* 🤖 注视共有list权限限制 ([a04eeb8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a04eeb81ddbf6a2837cb78d99a64901910d3e0ff))
* merge增加所有err检测 ([048b4bb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/048b4bba89c007a8c6b0b7ada4624fbfd19efc8d))

### [2.6.3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/compare/v2.6.2...v2.6.3) (2025-06-16)


### Features

* 🎸 增加使用故障槽位统计故障卡数统计GPU故障比率 ([78153bc](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/78153bc6acde980cc1c14ae92af1a8d37ec7a1b0))
* faultTicketRepository.List 数据库查询 Preload Device相关信息 ([c51c9c7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c51c9c70855eb343d4603a86df37b0155ae79815))


### Bug Fixes

* 修复出库流程性bug ([2f57434](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2f57434137dadfd9e0fcc1521fcc24162b73efd3))


### Others

* **release:** 2.6.2 ([4f94cb7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4f94cb7b07624760c3f324a81a095739f9bf945c))

### [2.6.2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/compare/v2.6.1...v2.6.2) (2025-06-16)


### Features

* 🎸 增加获取服务器所有集群接口，跟集群查询参数 ([d532b48](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d532b48bd3f4b5ac4bf6b12282322f6d4a889c8e))
* 🎸 将NewSoftScheService函数的第二个参数修改为可选参数。 ([bfe8533](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/bfe853310274418f0f7159d29e13fd50b055c181))
* 🎸 服务器设备列表返回接口变更 ([12503b8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/12503b855123b7941d64a302a392268df3ac275b))
* 去掉SN确认环节, 支持表格导入方式 ([f157a7e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f157a7e68ef38d5c72312381e1490d7668b5a74d))
* 合并代码 ([eff8933](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/eff8933b6b997ceb4fb9f51cc1411e0691c675cb))
* 改变获取审批记录的方式 ([2691ada](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2691ada9ab0f6d1cd7dbfc61ba86f16df04c7fcf))
* 添加库存修改历史记录 ([eb19f3a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/eb19f3aee4aac8c8c6a6ea512d901dbf00e5016e))
* 添加库存修改历史记录 ([c4f7d0b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c4f7d0b868d892e2770b652ed90f10abde1c6ca2))
* 添加群聊机器人，修复新购入库机器人通知bug ([b9dc050](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b9dc0505efb4a5b027f724123992be8055a3a75a))


### Bug Fixes

* 🐛 修改网络设备更新相应接口，使得能够正确更新网络设备信息 ([13b15f2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/13b15f2926b7bd5dd93dc788886c5e50b1e46fab))
* 🐛 重构了GetDeviceTemplate方法,消除了所有可能导致unsupported data type: & ([f9d7368](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f9d736858c3cc3d743611b45ca7eb023ca002795))
* rejected状态修改 ([145753a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/145753a4b931e0bb8e8e24bf1118e92071d760b5))
* 修复导入报错 ([208a999](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/208a999304456976bb72ee84cd0202a89160f338))
* 支持rejected stage ([764e221](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/764e22133c784272a6df538de5f0dc359deeb818))
* 支持rejected状态转换 ([e8b1f0c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e8b1f0c3fe0dfa0944886b25793d95398fd5681c))
* 文案修改 ([416a7cb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/416a7cbe531910d91cfd035951a3d8259d539dc1))


### Others

* 🤖 删除exe编译包 ([c0c6995](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c0c6995e797894e1b80b1ffae60a487da6b570a5))


### Code Refactoring

* 更改机房机柜包间等相关接口格式 ([62c6a3b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/62c6a3b64224c259ec17ccf3b1eda4e6bb80e639))

### [2.6.1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/compare/v2.6.0...v2.6.1) (2025-06-03)


### Features

* 🎸 冷迁移验证失败时返回故障定位时回滚相应的故障机与备机的CMDB状态 ([e5bf0f2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e5bf0f204536448860165fd2eb4db0b91b7c5922))
* 🎸 冷迁移验证失败时返回故障定位时回滚相应的故障机与备机的CMDB状态 ([d4fc372](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d4fc372a54874ced218a4d514ef12b3e9ebbdeb9))
* 🎸 增加维修单待接单飞书通知显示报修人 ([5a76ee6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5a76ee60715d69fad30d49a3cb3d3822abb97b71))
* 修改活动类别 ([7711b38](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7711b38f7ea3840f5b5dbb86135a7bc834e5ab0e))
* 入室人员管理后端功能 ([7c6a407](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7c6a4073a94aaf5eebb295f7f35aca18462b3172))
* 去掉关闭工单的流程 ([8b42b3c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8b42b3c865cb04e2add87429928da65f1ae5732e))
* 合并代码 ([dc343d6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/dc343d639a28614b352d5b35c8756cb742f7ea26))
* 合并代码 ([2ffc013](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2ffc0130fe7b5cdf5c01abb8d1a55efd7bf201b0))
* 实现排班表功能相关接口 ([028b77a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/028b77aac776dc73bac36056e57ef91e86b881a8))
* 排班表开发中 ([e095e43](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e095e436c522c49ba297b996a71bf610771f4e88))
* 根据最新的产品沟通, 后端调整 ([088fc85](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/088fc855444422079362bfcf546d5a921fce47db))
* 调拨添加来源信息 ([68f3957](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/68f39570ea415b6beac411515d55923621d5ece9))
* 软件故障单创建时根据排班表自动设置接单人 -m 现在，通过客户API创建的报障单也会自动根据排班表分配给当天的主值班人员。同样遵循早上9点前分配给前一天值班人员，9点后分配给当天值班人员的规则 ([1888ca1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1888ca1a7aa3c12f0e2566b273e9e2c0d516d969))


### Bug Fixes

* 🐛 修复客户报障无创建报障单历史记录问题，导致前端报障单详情历史记录无法展示故障单分配信息 ([e2548a0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e2548a0213e012628ee74bf7f853d8c74154e63e))
* 🐛 修复高频故障统计会将冷迁移下来进行维修单的故障机重复统计入故障次数的问题 ([d0dd1a5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d0dd1a586e83aae5103087aed99527b676d4ffae))
* 🐛 维修单服务temporal未注册问题 ([00b526f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/00b526f7521a63d65c20ef327a2d642151afbec7))
* 修复工单历史操作人显示不正确的问题 ([8479e58](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8479e58d387fc9fd6b37a30c0aac728043c36ed6))
* 修复拆机入库获取数据不准确问题 ([7f57f7d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7f57f7d8aab1622bda439c3a3aa2a3ac819800f7))
* 修复拆机入库获取数据不准确问题 ([38c9e42](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/38c9e42c10782108edec657c50de862975a9b161))
* 修复新购入库信息重复问题，修正创建人字段，增加项目、入库原因字段 ([b642b71](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b642b712f34cf153dd63bf99ca7de4d993547d35))
* 修复新购入库工单备注问题 ([2896b09](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2896b098f7d4fe122c9e5fdafee1066d2c68b608))
* 修复新购入库获取数据不准确问题 ([bb03281](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/bb032818e59771227b1af51b28b334a86b7b1521))
* 修复添加入库类型、入库原因导致的无法创建问题 ([ce9719b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ce9719bd0a96ee4fb9af69b97e54b857825bc8d3))
* 更新入库状态、入库原因 ([5a5a7c7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5a5a7c7241c6877a0c4697764d211fcd6b2e2cbc))
* 添加路由 ([8ded6a7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8ded6a7a0ccc5f37992344ebb2d0dcbff6edf315))


### Styling

* 更新格式 ([83fb835](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/83fb835da16e1a31d67779eb2d2591fc76f98c92))
* 更新格式 ([f054f46](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f054f4661cfe3773dafca78ec48a82aced8f79c0))


### Others

* 🤖 修复审查报错 ([f42a19c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f42a19c9098ce0d9b314c3f026c2ff645e2bcd6e))
* 🤖 删除main.exe编译包 ([28e03ef](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/28e03ef5b377e0dbb5a26c9d90bf3629c5962a2b))
* 修改githook配置 ([f8e236d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f8e236d0cf25557d3a481563c9208ebdf963e769))

## [2.6.0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/compare/v2.5.3...v2.6.0) (2025-05-23)


### Features

* 🎸 修改库存统计逻辑 ([ad6ecf3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ad6ecf3b23c8d64210b6316b6eaa3b51ec94cd71))
* 🎸 修改提交维修方案时故障槽位为必填，以及数据库故障槽位字段长度 ([15492e4](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/15492e4a43a405b508321c7d42134333d09507dd))
* 🎸 在提交维修方案时同步设置是否计入SLA字段 ([01263c3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/01263c3a604b47eeb1ad8aa998dcb46f2bb1837f))
* 🎸 增加提交维修方案时编辑租户IP时检查是否有相同租户IP未完成的报障单功能 ([2a3666a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2a3666a7107f572bf45fb2163db22cd634c1a63c))
* 🎸 增加更新租户IP时验证IP以及清空前后多余的空格功能 ([0c235f4](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0c235f40b07b95935c121ee63d972fc3e536eff7))
* 🎸 实现了在冷迁移报障单完成时自动创建新报障单的功能 ([3b3bbdf](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3b3bbdf060c0dc25ed43dfe8ed0e254e6db90570))
* 🎸 客户报障接口创建报障单增加是否重复报障检测 ([faac71d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/faac71dfc88d972ae2ac1024122b1b70209c9cc6))
* 🎸 当遇到没有可用备机的情况时，用户可以选择返回排查阶段重新选择维修方式，而不是被迫继续 冷迁移流程 ([c982cc2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c982cc2561a651263de2741ee78a6866182f1938))
* 🎸 调整客户看板故障来源分布返回字段 ([c983b3c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c983b3c593401aed0cd41e26ea6e807897ed5c3d))
* List功能 ([4de1e28](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4de1e280e45594c5de034a3ec362a849288e337c))
* 代码合并 ([dce0b51](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/dce0b51d6c9e4d0537b15991d5e4bac1802837df))
* 入库功能 ([b261125](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b261125f913c8fcb6976cdd6c3964c7813329683))
* 入库工作流 ([386bbbe](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/386bbbec2ba84e225c6509010d9dd9650809f423))
* 入库工作流 ([a1b753c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a1b753cec8b1e09b4a1f9e7600b8ea3234926fc5))
* 合并test分支 ([3e717c7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3e717c7faccd4cf5947bec59fe5b824c1092cb01))
* 合并最新main分支 ([df4e07a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/df4e07a360050a0cccd7f5cf38851421cba6b931))
* 售卖出库工作流 ([c935f31](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c935f318312368bb15c000ebeeea40e186e5eab3))
* 增加inbound_type返回值，便于前端识别 ([e949d07](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e949d078f29464480bd0f802c608645168eeea9a))
* 增加入库模块 ([e768ed5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e768ed561549bef48e4a1d6557750a89e184b1fb))
* 增加维修件入库模块 ([9c4adb3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9c4adb3c8d30524870330c68308f4dda791ffc89))
* 备件出库工作流 ([dbf59c5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/dbf59c5bb87cee1db043986a15e6b1199c3399d5))
* 完善入库导入模块 ([63e9688](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/63e96882a00b4548d14ddfbd0cc69506e57f29b1))
* 完善备件出库工作流 ([ab5bed3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ab5bed318ed8fe573c5adca0ab26c954a45681f0))
* 完善新购入库流程代码 ([e2d998b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e2d998b55a6275392f44e06176bc923296399b5e))
* 完成入库模块CRUD功能 ([59d7fe8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/59d7fe82fa93f879d724b892023a67d7c9dfa409))
* 工作流增加出库完成状态 ([71291f7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/71291f7b1ee3711e1bf738b1d7b9c5ae51eb258c))
* 拆机配件入库，确保SN不存在于数据库时可以完成插入 ([2fff917](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2fff917f0836b147f69f5217224626894e4fd659))
* 支持多种类型 ([53582eb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/53582eb2e6c821979c4a2b96a368158e87e8f2bd))
* 新增拆机入库、返修入库功能 ([3c42750](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3c42750b7aab534b424de451b6d901792f4ad6aa))
* 新增新购入库、拆机入库、返修入库 ([b1a47b6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b1a47b66417fe2eb07c4d2dca85760c402258bc9))
* 新增新购入库解析CSV ([c33d485](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c33d4851eca897e12d48b3bf0b80670deb8642ef))
* 新增维修入库模块 ([43ee7b8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/43ee7b8ea49960c12bf67abeedac98abb75c8750))
* 新增维修单入库流程 ([f6daa02](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f6daa02728d2cdac1f6db79d4cbffb14f0aa5030))
* 新购入库功能 ([1a3604f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1a3604f357dae9678dcf92e0f8f676bf395deb71))
* 新购入库增量更新 ([c5d40bd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c5d40bd1330aedc98377b3229cc2293e8262ff42))
* 更新新购入库功能、修复维修入库清点bug ([a6c2342](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a6c2342e6c680fd5352ddeeb3a8bd64715a3f648))
* 根据最新产品调整的出库流程 ([8a317c3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8a317c37e4617f7379f17c5e3d11c12e98d69877))
* 维修入库增量更新 ([61f92bb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/61f92bbddb99f007202291e147031274dedd0c11))
* 维修入库工单增量更新 ([fd3917c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/fd3917c96cafb0412268710b244e23e03a77411e))


### Bug Fixes

* 修复入库列表查询BUG ([dc72797](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/dc7279790e63a5858545f4d6b8d38fe57f7b8438))
* 修复前端反馈的工单创建人无法显示的问题 ([ea04329](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ea04329757b51f64449ee84315186a227bdf0253))
* 修复外键关联报错 ([0ea8ba0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0ea8ba09077194b1f8a84b4c92b1bf3033ed1963))
* 修复导入异常BUG ([afdfbb6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/afdfbb6c1e2e9e6edc8ce7d921a0a7625280982f))
* 修复查询BUG ([3b3ad62](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3b3ad62c9c864059352726de507a2800d0175be8))
* 修复返修入库工作流更新数据表错误问题 ([8c7434d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8c7434d6c7489a953f53d829dccd11e2a9614554))
* 修复部分已知BUG ([4c68bcd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4c68bcda14722d8fcaecd6ac243ed132ab8b238b))
* 修改spare表结构,删去有关device外键,修改入库代码 ([7dbd494](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7dbd494710f7a28c9a70c451206c345134502f8e))
* 修改入库单相关方法 ([34e0c77](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/34e0c775fbbd5a653c5bcc1e1f8f107487a45380))
* 修改工单表结构解决工作流无法修改status字段问题 ([8152be5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8152be540587cc2a726414c305f2ea637ddeec41))
* 修正数据库 ([4056593](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/40565932c48a55bef972408955be729f75c30071))
* 修正路由导入 ([82e6030](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/82e6030623af21ba920e73bdbfa6eefd08af8375))
* 合并主分支bug修复 ([82bacd1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/82bacd130c88706c575394d5ae59332b663e59aa))


### Performance Improvements

* 优化temporalClient注入 ([6e7a306](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6e7a306abb62613e6c6ba93f12e2cb29cd608cf6))


### Others

* 修改为本地数据库 ([89af1bb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/89af1bb3232bcac722665f4dfa4882483bf75269))


### Styling

* 维修入库代码规范 ([0e51430](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0e51430a3cb10ef529a2cc33dd9154e9a0bfc25c))


### Docs

* ✏️ 增加swagger文档 ([52a39c8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/52a39c8a40296b0a1d99e7ea68b59bef825a4cc3))
* 修改swagger注释文档 ([a4a206b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a4a206bd0273e5ccd77084effc5484780f221b53))


### Code Refactoring

* 💡 事件单管理详情页整体重构 ([6ea8a63](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6ea8a631fb3ddcf82ccbed178581de5686b3a422))


### Tests

* 💍 增加客户报障api相应单元测试 ([7ede0ec](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7ede0ec12dc06c0cd293fe89bf77faca136ce718))
* 解决合并冲突 ([5137d91](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5137d91e662335e23c7f7e6d8a6c37447d097b11))
* 解决合并冲突 ([6a52294](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6a52294d6258f0a7b44b4db75b31de237f0e34e5))

### 2.5.3 (2025-05-14)


### Features

* 1. 从 FaultTicket 仓库方法中移除了对 Component 和 Device 关系的预加载 ([efe88a8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/efe88a8b0fae3848ebbe8e63116c9d24bdd76e05))
* 1. 修改了handleSummary函数，从信号数据中获取fault_type参数。 ([b3c28b6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b3c28b62f54e61e3112bfe9358921d93745e3f5b))
* 1. 在 FaultTicket 模型中添加了 CustomerApprovalTime 字段，用于记录客户审批时间。 ([517366f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/517366fb87de47e3e93ed7ac1a8da2a5db165527))
* 1. 在 getAvailableActions 函数中添加了条件判断，只有当工单有设备SN时才显示接单按钮 ([e528000](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e528000ffcfaf800f7231fead193b065d0364b38))
* 1. 在创建工单时，默认设置RequireApproval为true，也可以在请求中明确指定 ([8dacabf](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8dacabfe27001d794a0cdfc3e8405a85e02b0480))
* 1. 在创建报障单时： ([44ae10e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/44ae10e6ec51f750d3fc891aaca655a700fd1dea))
* 1. 在维修单工作流中修改了硬件维修完成后的流程，使其进入待验证状态（waiting_verification）而不是直接到工单完成状态 ([65cb60d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/65cb60d0b03626711b2b4044a8736ef2ff689020))
* 1. 增加对品牌字段的支持：从CSV中读取品牌字段信息 ([a82539a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a82539af4884d1e94022f27a1c0f314b9310ea6a))
* 1. 已经在 FaultTicketController 中更新了 ListFaultTickets 方法，支持新增的查询参数。 ([0f90e65](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0f90e655500c3537b7c4dc5eb06405856e9248b7))
* 修改了 List 方法（通用库存明细列表查询）： ([3e9055e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3e9055ec6c405f6d314d5cadb2eedd538b2131e5))
* 修改了代码，以避免重复记录状态变更日志。现在的流程是： ([c4f09c5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c4f09c5793df60eff43f9567f4d473bf8e7c8f5e))
* 修改报障单查询接口为精准匹配 ([b38f1dc](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b38f1dc27f38183b027bc76af983d5f5c7d4018c))
* 减少维修单状态流转阶段 ([20c202f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/20c202ffa1064e36d9d8a9834960f96478b6d33a))
* 创建了四个设备状态更新相关的活动函数： ([97394d3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/97394d36d1ba6d689145b3080e5e8cb8cf2bf8d0))
* 加入全局操作日志记录功能 ([27b8cb0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/27b8cb0425631f35db0169f9de7a35327c7dc3ea))
* 加入全局操作日志记录功能 ([1cf60f6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1cf60f6ebf8d88b55b2db5121651834c2a8a0871))
* 加入维修单子工作流 ([fc540c3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/fc540c34a064b8ff9a292925c43452efd713e275))
* 去除维修单工作流newtime定时器 ([7b7a6ec](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7b7a6ec9159ee325e6a94954cea5a7d906ca8b06))
* 取消工单时： ([750c376](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/750c376bc9997a322b3691260bcf7f0e4840caa3))
* 图表页面api框架搭建，增加相关数据查询接口 ([d4487de](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d4487defbbefdd04b637e97b53ba237886485b7d))
* 在 CabinetController.List 方法中添加了获取 roomID 参数的代码 ([8bef420](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8bef4206d8b5eb6bd36de5b5fc0ad51e42fa3bd3))
* 在 feishu_notifier.go 中添加了新的 SendVerificationFailedNotification 方法，用于发送验证不通过通知，通知使用红色模板，并包含验证失败的详细信息和验证意见。 ([6bc34c9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6bc34c93d46704a968121c3d5c7518acaf0a4779))
* 在 RoomController.List 方法中添加了获取 dataCenterId 参数的代码 ([8097069](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/809706923ec08eb9b93db0a6e9229ab207ce9788))
* 在backend/internal/modules/ticket/repository/fault_ticket_repository.go的Update方法中添加了customer_approval_time字段，确保当使用Update方法更新整个ticket对象时，这个字段也会被包含。 ([19d4fb4](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/19d4fb403a808d7671983e3422e70db6f82ad861))
* 在冷迁移流程中增加集群参数，优化备机选择逻辑，确保获取可用备机列表时考虑集群信息 ([818bf14](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/818bf14ae354f72abfff2dd5da8fba87b09706a0))
* 在故障单工作流中，修改了 handleCompleteVerification 函数，在向维修单发送验证结果信号时，添加了 verification_result 字段，根据验证结果设置为 passed 或 failed。 ([de55949](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/de559494b28d5b2915cc6af4e847f3a89913cba1))
* 在维修完成阶段，系统会尝试通过GetDeviceOriginalStatusBySNActivity函数获取设备维修前的原始状态。 ([8beed6c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8beed6c2f13f7be5b137bb1cf7f0e5a9cb0f1a1b))
* 增加config文件生产环境、开发环境分开读取，以及gorm日志是否在控制台输出等功能 ([e09d28d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e09d28da1047bb2fa1de7c80148eb09cd4ba7816))
* 增加config文件生产环境、开发环境分开读取，以及gorm日志是否在控制台输出等功能 ([a75c5c5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a75c5c54cd522ed049bbd9bd32d34c57c730e422))
* 增加GPU服务器品牌分布、包间分布、GPU卡型号统计接口 ([525d46b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/525d46b31bcb51e7fc96d850f50b9b7d56958bbc))
* 增加product的导入以及模版下载功能 ([7dc3d60](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7dc3d60a0a8b8d750c92af358f7c81d7e604e71c))
* 增加resouce-bysn查询接口 ([53d7982](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/53d79824dc5a0aaf07b8cecef93af1342ea5d745))
* 增加room以及机柜导入以及下载模版功能 ([bb8f8d8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/bb8f8d86ff830e9928192963e84cf0e91d81396e))
* 增加从排查中转向取消状态 ([dd902ea](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/dd902ea3b52d9bf1da85d8be3ad1f81e23f74ccf))
* 增加冷迁移与CMDB备机状态进行关联，增加冷迁移记录功能以及相关接口 ([27f0c30](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/27f0c302d2d827993828b574b2f64db325b13988))
* 增加各个报障单时间点计算活动1. 响应时长：使用AcknowledgeTime减去CreationTime计算 ([a6c6e64](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a6c6e64c332c746a48f9d713c226b6527294792e))
* 增加备件导入中文映射 ([8f0a7c8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8f0a7c828fa099d1367d5ab698b7e955e8349522))
* 增加备件导入功能以及导入下载模版 ([44719ab](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/44719ab9f30dd13e2f4944d1f95b4a41f231daba))
* 增加备件查询参数 ([2d19751](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2d19751806e627c3de68b6db106e1bcc2b9bf794))
* 增加客户报障、查询、授权接口 ([651c04c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/651c04cf5b6b96e778e0364922557f6e716e50ba))
* 增加客户报障以及授权飞书机器人通知功能 ([120fccc](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/120fccc43d89590162cc70f38feb4ae70cb6b08c))
* 增加客户报障客户ID的限制 ([a3fc582](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a3fc582d506bb60996feddbf67dec4dbdbad23e6))
* 增加客户报障接口日志记录功能 ([0b76094](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0b76094ab2a7d9391dc3d134b9285799127be851))
* 增加客户报障查询日期查询时间段、时间点工单查询功能 ([74b7dc7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/74b7dc74060ffdf40fa67e29ea2b02a7c66f8b1e))
* 增加工单系统触发资产状态、硬件状态、业务状态的变更日志记录 ([08e653d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/08e653d2f8e9a3e0d22afc8e70723bf8e7215b0e))
* 增加库存明细以及库存统计任务调度器，每天凌晨两点定时同步数据库数据 ([1ba2d9b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1ba2d9b84817580ded25a3f36650c5c029ebb828))
* 增加库存明细相关接口 ([3e29663](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3e29663d536f95e6a55d5cdf15696990d9810ce5))
* 增加报障单信息DTO，修复上传文件无法获取到是由谁上传的问题 ([36ea27b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/36ea27bbff26c2f5eadde579ef3dbb03fbed8701))
* 增加报障单信息部分更新接口 ([865128a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/865128aefe2d8cb6a84fd753ca395a12adede85a))
* 增加报障单标题、创建时间、故障槽位等创建报障单接口字段 ([1564c6f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1564c6fdcc5a489e31317ba572eb86e85c151b03))
* 增加报障接口相应的日志打印 ([f602b1f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f602b1f31eb53b129bb1eb0bd3d3299b2cf98537))
* 增加故障发生次数/总时长，硬件故障发生次数接口分组聚合功能 ([19395db](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/19395dbb8beb187c02942d86572ec41b7feb3ab2))
* 增加故障总台数统计接口 ([4eb6dde](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4eb6dde9cf495dbe9f6d98c564627edac81e8915))
* 增加文件上传以及预览接口功能 ([5d9885e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5d9885e2a406b3a5d1a27acdb8e67b2b69d31e48))
* 增加是否计入SLA的query参数方便内部运维看板复用接口，默认不传为true， ([e3abdf0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e3abdf01a6bf1684f4077e96a2f0bada7168cc4f))
* 增加服务器资产管理接口以及资产状态相关接口 ([8dc49cd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8dc49cd6f77ebcc15eb9834bbc89d20a03a2c2b0))
* 增加服务器资产管理接口以及资产状态相关接口（未完善） ([f6f476b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f6f476b01e701f7df5e9d68d57fd2098a4ce2b98))
* 增加服务器资产管理接口以及资产状态相关接口（未完善） ([aa4af4b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/aa4af4b55bd30201599a288f2e0d24790888591b))
* 增加服务器页面查询参数 ([59f00bc](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/59f00bcbfbf9a1d3efced7e87dc162c2fb0dd044))
* 增加服务器项目获取接口 ([80b9c3c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/80b9c3cb0394db6dd843aca60e5b8ab55825f769))
* 增加组件变更记录功能 ([c137d68](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c137d6847a3fa2e92337c49f93ac6f5f6edaafcc))
* 增加维修单历史记录接口 ([165f309](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/165f3094a0f19ccea891fa26e950ee2f1c1df521))
* 增加维修单接单、维修待验证飞书通知 ([de18579](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/de18579c08abbe716d95e539cdec1b527c6ae23b))
* 增加维修单转单更新记录功能 ([f7c19d4](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f7c19d4d73da3403f617c48c006c5e3c2175fa6b))
* 增加维修选择时具体故障类型、故障槽位的选择 ([88bb751](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/88bb751dc5eac0f2ae1be1ce216dc06a01e884cf))
* 增加网络设备以及下载模版的api ([8831f02](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8831f02745a05b5fd9a457b3eadc8965a1f0b02e))
* 增加网络设备单独相关接口 ([3101da7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/3101da79cc04c3569410b63784ac389b7ee6933c))
* 增加规格/pn的物料类型与产品类型映射 ([326cad4](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/326cad4f0e1c3a257db5a4a7c324b538617deca4))
* 增加规格信息导入功能以及导入下载模版 ([6b2cb01](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6b2cb015b2276f599af19a1447799933d84d75d3))
* 增加运维看板人均软件/硬件的响应/修复时长统计接口 ([35a5244](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/35a5244346aec3d24819ca67a50494ba271a3197))
* 增加运维看板故障厂商分布统计接口 ([18a4dea](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/18a4dea5d5a1399f3e6f27b0e87fe8e0cfec7e4b))
* 增加运维看板每天故障处理总时长趋势统计接口 ([90dfe8e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/90dfe8e686eb21499b8d3cdd133dae8736c2e352))
* 增加运维看板每天硬件故障趋势统计接口 ([f4527fd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f4527fd0ade378cbb9c8de85f59bd93b4dad3a91))
* 增加运维看板硬件平均响应时间统计 ([0a19c62](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0a19c626ddd7fa064766a4f7c553a5c8a90802f5))
* 增加选择数据导出以及全量导出excel功能接口 ([857d1fd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/857d1fd744ba878152accfa83876e54101169379))
* 增加配件库存接库 ([4143d66](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4143d66cfc092cf99533df585d75837c55de0799))
* 备件增加规格查询参数 ([c6710a5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c6710a5d9fc6661a58251e2d888ae71158d98602))
* 多IP查询： ([279a9cb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/279a9cba5383549a3b80be831ee7aefc9afbe569))
* 实现SLA看板的所有接口功能 ([4637151](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/46371516b6c7254ca46519c820a911e712a1a7eb))
* 实现报障单整体流程分部控制 ([796435a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/796435a714feb31cd1825448fe4b15852ca1f582))
* 客户报障以及授权接口接入到现有工作流系统中 ([f8c28b8](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f8c28b8b4896d1051518c20a1972f5ebea2d13c4))
* 对客户报障api的ip格式做严格限制 ([12a2773](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/12a27739e761a47c4f06c7c554649019404491c9))
* 将操作人由当前账号登录的realname所决定 ([50fdc08](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/50fdc08dc66f51e3138c424ee49f006d922e936a))
* 报障单list采用创建时间倒叙排序返回 ([ce890ee](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ce890eecadcef32fb5c505485cc6684b6bea1681))
* 报障单status支持多个状态同时查询 ([fcb7061](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/fcb7061017ed63188f65bafdeb44ea781381f0df))
* 报障单工作流流程整体初步跑通 ([55f6f9e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/55f6f9e5947011069b6d20f77446a4973c21c280))
* 更换维修通知至报障单的开始维修流转阶段 ([d399f1e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d399f1ebd7083e3c0a85e9e8804dad21e57365cd))
* 更改备件类型查询映射 ([87d6545](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/87d6545667dafeb5436bec8634e00ec8bb6b5ca0))
* 测试 ([8aa6c6f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8aa6c6fae1dc265ebcdbcbb04d48c24797fd9f46))
* 添加了判断条件，当操作类型为query时： ([1c97bc0](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1c97bc0dd4e9bfa274755a0429db6ed791490734))
* 清理创建报障单或者修改报障单时device_sn前后的空格以及换行 ([efe5523](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/efe55235d0bc53c2313838914dbbc4435fb82c1f))
* 简化和优化了handleCompleteVerification函数中的信号发送逻辑 ([b7921c5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b7921c58ff40aaa62d71963f17a3ef1c51b1657b))
* 维修单工作流加入待授权状态，通过报障单授权信号来自动触发进入待接单状态 ([ab71c0a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ab71c0ac2af4b87e5b7805db695006a9afad572e))
* 维修单飞书通知增加机柜跟包间的展示 ([6ec5b7b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6ec5b7b39b8c0243c1022cdeff902ad46ea5f00c))
* 维修待验证通知不发向维修群 ([48a0b86](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/48a0b86766e0393ba0696b5a90c96a30d2346a06))
* 页码参数(Page)异常值处理： ([63aa5c2](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/63aa5c245ebc83f4dce44874378e814ef0a43781))


### Bug Fixes

* resource表asset_id出现重复数据导致前端无法正确显示数据问题 ([8508528](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/85085284f80ead4d99514872cbe5c0a7eabcf409))
* resource表asset_id出现重复数据导致前端无法正确显示数据问题 ([ca0b3db](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ca0b3dbdffeec9c9ff20cef1cc2abe2966758cc5))
* SLA看板模块将未计入SLA的也统计至当前状态的问题 ([136eeba](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/136eeba69422433bc6212744e49e30ccdeb797f5))
* 仓库查询没有预加载包间数据问题 ([ade28b1](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/ade28b1678a3b311049ba5b75621e54a72b83c75))
* 修复component组件信息路由问题 ([93aadd4](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/93aadd4ef73b147411fc7c6e01707152d9d812b3))
* 修复产品时无法自动创建inventory_detail表相关数据而报错，增加自动创建功能 ([8a0851b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8a0851b6913f06654ce0d65fec41587725f020c1))
* 修复创建报障单的字段 ([9cb6337](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9cb63378f55ff1412a5c78c1c4d4059d8441795d))
* 修复套餐模版无法正确显示的问题 ([7149e2d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7149e2daa4d4fff66e55f2620281265bbd0f911a))
* 修复客户审批、维修选择的返回错误问题 ([f881663](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f8816636287eeb0afa97a771d20b844c090f69d3))
* 修复客户报障api  isAuthorized: true的问题 ([e108e6b](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e108e6bd2810dbf4281da2054c176758edca85b5))
* 修复客户报障接口total总量显示不是符合条件的总量数而是查询当前page的总量数问题 ([9ecfa74](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9ecfa74ae6e29880546aa757505219d45d862d17))
* 修复客户报障接口授权请求是query而不是json请求 ([5b62666](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5b62666290e88cbd896cf8c57efbdcb019585787))
* 修复工作流在工单结束状态下会重新启动的问题 ([911acea](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/911aceaeadd011379eb3027bfc6053aa962e5d01))
* 修复待验证通知没有使用报障单的ID问题 ([833e262](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/833e2628df2e510d57a14ec992b4d7bab6ed4ca1))
* 修复报障单进行维修选择时部分选择会导致创建硬件维修单的问题 ([a187ec3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/a187ec32461bc75df36502782acc13537981be4d))
* 修复按工单id、时间查询时不匹配会返回所有数据问题 ([fd92f03](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/fd92f03c94a9d915d180399852f845c47b4bb498))
* 修复无法对服务器是否是备机的状态进行修改的问题 ([4d4e4df](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4d4e4df41bdbe82fc1d7a8d199f84635c4edc8c8))
* 修复服务器页面无法导入问题、增加机房机柜来确定唯一机柜以及增加主机名以及租户IP字段 ([5c3502a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5c3502a27f1d0dddc2d4df1efa864c660ac48fdd))
* 修复维修选择时无法正确更新报障单表里的维修单号数据问题 ([0bb5c6f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0bb5c6f16f9ada1c034b094281150ade626202db))
* 修复维修飞书通知无法获取到设备SN的问题 ([4075d6d](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/4075d6d5f95657c9cee1ebd845a51549df201669))
* 修复规格信息list列表过小问题 ([e69238a](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/e69238ad8f1a461c30f981ad37790c6a1f8d468e))
* 修复飞书通知没有正确使用生产环境变量问题 ([6e24727](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6e2472776aaba7a5d7a1d573c83c26567082f276))
* 修改了SendVerificationFailedNotification函数签名，添加了repairTicketID参数并将engineerID改为engineerName ([d7144fa](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/d7144fa54d29d4703e200ae31c6d12afdb0453bf))
* 修改了时间过滤逻辑，取消了条件判断，确保无论pageSize大小如何，都执行相同的过滤过程： ([5a8e370](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5a8e3704499d23c3f7f06b350c3670783c226241))
* 修改仓库问题 ([6d5dad9](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/6d5dad981997f6a1e62ca17a929eb2379f030754))
* 停止全局审计钩子（严重bug） ([2f5b5dd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2f5b5dd9fe0fa3ee680dfb2f3def5afd4f561e20))
* 删除了handleCustomerApproval函数中在客户批准维修方案后立即发送授权信号的代码 ([1c88ea6](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1c88ea64afefa160ee0e15cf636b0856e5a02970))
* 前端传的JSON 数据，依赖任何 base64 编码/解码，导致添加模版失败问题 ([f01b5ef](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/f01b5efc8c5f0a4745546cc8c7f92cffdf679f26))
* 审计日志过长插入失败问题 ([2f86032](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2f860327220239b316c70af8c212c584d51f6c1b))
* 客户报障接口授权后status能够正常流转至approved_waiting_action ([bbd1ba5](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/bbd1ba55011a5465698e2b281696f8d8c500a1f9))
* 客户报障查询无法根据工单id进行查询 ([2ed3b95](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/2ed3b950878330fee68aa99e96ed941bb6310c7d))
* 客户授权接口无法正确触发工作流流转问题、查询total数量计算不准问题 ([b3e35bb](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b3e35bbf85d5a85eef0c44f90754da8042f82f5e))
* 导入数据时选择覆盖无法覆盖相同SN的数据问题，包括软删除状态 ([b150280](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b150280be5cbbeb3520a5c3406256ad7cbff7ae5))
* 工作流信号触发失败时工作流状态不更新而数据库字段却更新的问题 ([8509974](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/8509974e2f855e91a6785dd1a5767ac0acd50241))
* 接单人无法正确赋值问题 ([c2dca05](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/c2dca056d9ba36a7b42968d98d99f55375d4a1e2))
* 新增服务器数据时asset_id已存在无法新增的问题 ([0cf042e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0cf042ed985478d96fe32938bde2f806eb80b0d4))
* 机柜存在同名名称无法导入的问题 ([b79d327](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/b79d327ee0011e95cd2446c549acd2990d1bb9ff))
* 维修单历史变更记录表无法正确显示操作人姓名问题 ([9d6f140](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/9d6f14092822965c00242e9883dea5b93824fda2))
* 维修单点击完成时报障状态错误流转至完成状态 ([56b82d3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/56b82d3b4717ea4802ee4c06bed4cad2b6cb67ff))
* 验证记录无法正确记录 ([0d12b6c](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/0d12b6c199b2de6044c3540330249d6e1c51698f))


### Performance Improvements

* 优化日志显示 ([5b7867e](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/5b7867ed3e01637a07ed39fed723c2fcc8bf3e84))
* 优化日志显示 ([1c3d81f](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/1c3d81fd7b92e8f778c414d44d2893f90cf0fa38))


### Code Refactoring

* 产品规格导入去除品牌、型号必选 ([274bcfc](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/274bcfca9c3fbda615f813f55d74a85a553b127d))
* 客户报障接口返回工单ID改为i开头的工单id ([034cfb3](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/034cfb322a10f4ce2a3649324da28466857b5906))
* 报障接口最大长度修改为2000字符 ([32e7789](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/32e77899db9073c6ff0fe94851864bc46ff94b11))
* 服务器套餐模版id从asset_device表获取,而不是从server_asset获取 ([7ea1251](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/7ea1251018287c334121c39f8724de378c2aea8c))
* 重构资产设备接口设计逻辑 ([bd8cd67](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/bd8cd67caa1e0fb42e1666071f5980f38aef0388))


### Styling

* 代码风格优化 ([692a4dd](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/692a4ddbd59b0d9086fef902b3e1b7aede6a69ed))


### Docs

* ✏️ 增加changelog显示以及生成插件 ([fb17fc7](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/fb17fc795fba73ebef4ed61dc24a635edefb4e6b))
* 修改swag注释文档 ([cde5141](http://gitlab.cnhancloud.com:9090/intune/intunesystem-backend/commit/cde514173970c02ae27f4767f984d913e4cf50ff))
